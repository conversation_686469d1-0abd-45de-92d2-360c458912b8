var gulp = require("gulp")
var ts = require("gulp-typescript")
var tsProject = ts.createProject("tsconfig.json", { typescript: require('typescript') })
var nodemon = require("gulp-nodemon")
var del = require('del')
var sourcemaps = require('gulp-sourcemaps')


gulp.task("build.ts", function () {
    gulp.src('src/server/**/*.json')
        .pipe(gulp.dest('dist/server/'))

    return tsProject.src()
        .pipe(sourcemaps.init())
        .pipe(tsProject())
        .pipe(sourcemaps.write('.', { includeContent: false, sourceRoot: __dirname }))
        .pipe(gulp.dest("dist"))
})

gulp.task("build", gulp.series("build.ts"))

gulp.task("watch", gulp.series("build"), function () {
    // return gulp.watch("src/**/*.*", ["build.ts", "webpack"])
    return gulp.watch("src/**/*.*", gulp.series("build.ts"))
})

gulp.task("default", gulp.series("build", function(){
    process.env.APP_NAME = 'iris'
    nodemon({ script: "dist/server/index.js", verbose: true, watch: "src/**/*.*" })
}))


gulp.task('clean', function (cb) {
    return del('dist', cb)
})
