# Iris 
[![Curefit](https://static.cure.fit/assets/images/curefit-v-man.svg)]()

**<PERSON>** was the goddess of the rainbow and the messenger of the Olympian gods. She was often described as the handmaiden and personal messenger of <PERSON><PERSON>.
For curefit, **iris** controls all the communications send out to the users

It provides you with capability to:
  
- Send communications
- Checking delivery status of communications

# New Features!

- We now support Functions in Notifications! [Ref](https://docs.google.com/document/d/1KJY1CD-VwefsIQra1zTRktBXA2L5okwTVTwvW6PnvHs/edit?usp=sharing)
- Send Multimedia(Image/PDF) WhatsApp notifications!
- Hurray! Now you can send a template attachment
- Entire history of notification sends to user is available with filter options (vertical, channel, campaignId, creativeId)

Communication Channels supported:
---

| **Communication Channel**| **creativeType**|
| ------ | ------ |
| Calling | CLICK_TO_CALL |
| Electronic mails | EMAIL |
| In app notifications | IN_APP_NOTIFICATION |
| IVR | OBD |
| Messaging | SMS |
| Push notifications | PUSH_NOTIFICATION |
| WhatsApp | WHATSAPP |

# Quick Start

1. Create a campaign and creative. For Push Notifs, SMS and Emails use [cerebrum](https://cerebrum.curefit.co/#/curefit/assets/create) else, use 
   [cyclops](https://cyclops.curefit.co/#/creatives).
2. Use the apis below to trigger notifications.


Local Setup
---

Clone [iris](https://github.com/curefit/iris.git) repository

Install the dependencies and devDependencies and start the server.

```sh
$ yarn
$ gulp
```
- read about [gulp][GulpLink]

Verify the installation...
```sh
curl -XGET http://localhost:3010/status
```
Expected Response: Status is OK

Guide to use iris to send your communications
---

> Configure the campaign in [cyclops](https://cyclops.curefit.co/#/) / [cerebrum](https://cerebrum.curefit.co/#/curefit/campaigns?page=1)

> Configure the creative in [cyclops](https://cyclops.curefit.co/#/creatives) / [cerebrum](https://cerebrum.curefit.co/#/curefit/assets)

Refer to [cyclops][cyclopsReadme] documentation

Voila! start sending your communications using [iris-client][irisClientNpm], here is the [github][irisClientGit]

Sample APIs
---

1.0 send notification request
```sh
curl -L -X POST 'http://localhost:3010/campaigns/send' -H 'Content-Type: application/json' --data-raw '{
    "campaignId":"CUREFIT_PLATFORMS_TEST",
    "creativeIds":["SMS_PLATFORMS_TEAM_TEST"],
    "userContexts":[
        {"userId":"123",
        "tags":{
            "testCustomerName":"Test name",
            "testCustomer": {
            	"sex":"Male",
            	"age":"23"
            	}
            }
        }]
}'
```

1.1 send notification response
```
{
    "123": [
        {
            "creativeId": "SMS_PLATFORMS_TEAM_TEST",
            "notificationId": "973d61f1-34a1-4521-afbd-9b701556be64",
            "sent": true,
            "failReason": null,
            "scheduled": false
        }
    ]
}
```

2.0 get notification status
```sh
curl -L -X GET 'http://localhost:3010/notifications/:notificationId'
```

2.1
```
{
    "id": 92406691,
    "notificationId": "18486324-c05e-4473-a691-1f49300961e1",
    "campaignId": "CUREFIT_PLATFORMS_TEST",
    "creativeId": "SMS_PLATFORMS_TEAM_TEST",
    "userId": "123",
    "taskId": "ba0ae530-baaf-4d29-8bb5-5dc73fb94e6f",
    "bumbleTokenId": null,
    "failReason": null,
    "sentAt": "2020-04-07T09:07:42.000Z",
    "processedAt": null,
    "receivedAt": "2020-04-07T09:07:44.000Z",
    "openedAt": null,
    "convertedAt": null,
    "convertedEntityId": null,
    "convertedEntityType": null,
    "createdAt": "2020-04-07T09:07:41.000Z",
    "updatedAt": "2020-04-07T09:07:45.000Z",
    "userIdType": "CUREFIT_USER_ID",
    "status": "DELIVERED",
    "externalId": "SMS_Solution_Infini_10058588938-1",
    "userTags": "{\"userId\":\"123\",\"tags\":{\"testCustomerName\":\"Test Name\",\"testCustomer\":{\"sex\":\"Male\",\"age\":\"23\"}},\"attachments\":[]}",
    "scheduledAt": null
}
```

Exiting Tags present with PII which can be used
 ```
 _|*user_email*|_
 _|*user_phone*|_
 _|*user_name*|_
 _|*user_subscribed*|_
 ```

 
Whitelisting process for testing
---

As iris is a tool to send communications, it is important that we restrict the communications on non-production environments.
For testing purposes, the whitelisted communications can be sent. 
Below are the whitelisting criteria:
- All users with internal email domains are whitelisted ("@curefit.com". "v.curefit.com")
- phone numbers are whitelisted manually

Sending WhatsApp Messages
---

Whatsapp notifications need to be approved from providers before they can be used. Below is the guide for the same:
1. [Value First](https://docs.google.com/document/d/1D7ywKlyK-mxD-jn0NFyd63oEtwBi6b0HNn1FbjLjR60/edit
   ) (Will be Deprecated soon)
2. [Kaleyra](https://docs.google.com/document/d/1K8CSElLWtAjoDiG6o0S4NHmXomk68TO135vVJDw8zIc/edit)


Attachment templates supported formats:
---

- pdf
- csv

#### Jenkins
https://jenkins.curefit.co/job/iris-k8s/

#### Kubernetes
https://k8s.curefit.co/#/overview?namespace=iris

#### Monitoring Dashboards
- [notification send - grafana][irisGrafanaMain]
- [service provider metrics - grafana][irisGrafanaProvider]
- [CRM dashboard - metabase][irisMetabaseCrm]
- [Delivery delay - metabase][irisMetabaseDeliveryDelay]

### Coming Soon!

 - Support for Sending Calender Invites

**Support: For more information reach out to platforms team**

[//]: # (references)

   [cyclopsCreatives]: <http://cyclops.stage.curefit.co/#/creatives>
   [cyclopsCampaigns]: <http://cyclops.stage.curefit.co/#/>
   [cyclopsTemplates]: <http://cyclops.stage.curefit.co/#/templates>
   [cyclopsReadme]: <https://github.com/curefit/cyclops/blob/master/README.md>
   [GulpLink]: <http://gulpjs.com>
   
   [irisClientNpm]: <https://www.npmjs.com/package/@curefit/iris-client>
   [irisClientGit]: <https://github.com/curefit/iris-client>
   
   
   [irisGrafanaMain]: <https://monitoring.curefit.co/grafana/d/DMni7ftZz/curefit-iris?orgId=1&refresh=5s>
   [irisGrafanaProvider]: <https://monitoring.curefit.co/grafana/d/MBRxkg3Zk/curefit-iris-service-provider?orgId=1>
   [irisMetabaseCrm]: <https://metabase.curefit.co/dashboard/723?startdate=2020-02-01&enddate=2020-02-01>
   [irisMetabaseDeliveryDelay]: <https://metabase.curefit.co/dashboard/878?startdate=2020-05-03&enddate=2020-05-04&campaignid=CUREFIT_OTP&status=DELIVERED>

   [cyclopsReadme]: <https://github.com/curefit/cyclops/blob/master/README.md>
   [PlGh]: <https://github.com/joemccann/dillinger/tree/master/plugins/github/README.md>
   [PlGd]: <https://github.com/joemccann/dillinger/tree/master/plugins/googledrive/README.md>
   [PlOd]: <https://github.com/joemccann/dillinger/tree/master/plugins/onedrive/README.md>
   [PlMe]: <https://github.com/joemccann/dillinger/tree/master/plugins/medium/README.md>
   [PlGa]: <https://github.com/RahulHP/dillinger/blob/master/plugins/googleanalytics/README.md>
