pipeline {
  agent {
    label "k8s-kaniko-amd64"
    }
  parameters {
    booleanParam(name: 'EnableCanary', defaultValue: false, description: '')
    }
  environment {
    ORG = 'curefit'
    APP_NAME = 'iris'
    NPM_TOKEN = credentials('npm-token')
    GITHUB_NPM_TOKEN = credentials('github-npm-token')
    }
  stages {
    stage('Prepare Docker Image for Stage Environment') {
      when { branch 'stage' }
      environment {
        VERSION = "$BUILD_NUMBER-$BRANCH_NAME".replaceAll('_','-')
      } // environment
      parallel {
        stage('Prepare arm64 Docker Image for Stage Environment') {
          agent {
            label 'k8s-kaniko-arm64'
          } // agent
          environment {
            PREVIEW_VERSION = "$BUILD_NUMBER-$BRANCH_NAME-arm64".replaceAll('_', '-')
          } // environment
          steps {
            container(name: 'kaniko', shell: '/busybox/sh') {
              buildAndPushDockerImage("stage", "${PREVIEW_VERSION}")
              updateArtifact("${DOCKER_REGISTRY}/${ORG}/stage/${APP_NAME}", "${VERSION}", "stage")
            } // container
          } // steps
        } // stage('Prepare arm64 Docker Image for Stage Environment')
        stage('Prepare amd64 Docker Image for Stage Environment') {
          environment {
            PREVIEW_VERSION = "$BUILD_NUMBER-$BRANCH_NAME-amd64".replaceAll('_', '-')
          } // environment
          steps {
            container(name: 'kaniko', shell: '/busybox/sh') {
              buildAndPushDockerImage("stage", "${PREVIEW_VERSION}")
              updateArtifact("${DOCKER_REGISTRY}/${ORG}/stage/${APP_NAME}", "${VERSION}", "stage")
            } // container
          } // steps
        } // stage('Prepare amd64 Docker Image for Stage Environment')
      } // parallel
    }; // stage('Prepare Docker Image for Stage Environment') 
    stage('Prepare Docker Image for Alpha Environment') {
      when { branch 'alpha'}
      environment {
          VERSION = "$BUILD_NUMBER-$BRANCH_NAME".replaceAll('_', '-')
      } // environment
      parallel {
        stage('Prepare arm64 Docker Image for Alpha Environment') {
          agent {
            label 'k8s-kaniko-arm64'
          } // agent
          environment {
            PREVIEW_VERSION = "$BUILD_NUMBER-$BRANCH_NAME-arm64".replaceAll('_', '-')
          } // environment
          steps {
            container(name: 'kaniko', shell: '/busybox/sh') {
              buildAndPushDockerImage("alpha", "${PREVIEW_VERSION}")
              updateArtifact("${DOCKER_REGISTRY}/${ORG}/alpha/${APP_NAME}", "${VERSION}", "alpha")
            } // container
          } // steps
        } // stage('Prepare arm64 Docker Image for Alpha Environment')
        stage('Prepare amd64 Docker Image for Alpha Environment') {
          environment {
            PREVIEW_VERSION = "$BUILD_NUMBER-$BRANCH_NAME-amd64".replaceAll('_', '-')
          } // environment
          steps {
            container(name: 'kaniko', shell: '/busybox/sh') {
              buildAndPushDockerImage("alpha", "${PREVIEW_VERSION}")
              updateArtifact("${DOCKER_REGISTRY}/${ORG}/alpha/${APP_NAME}", "${VERSION}", "alpha")
            } // container
          } // steps
        } // ('Prepare amd64 Docker Image for Alpha Environment')
      } // parallel
    }; // stage('Prepare Docker Image for Alpha Environment')
    stage('Prepare Docker Image for Production Environment') {
      when{ branch 'master' }
      environment {
        VERSION = "$BUILD_NUMBER-$BRANCH_NAME".replaceAll('_','-')
      } // environment
      parallel {
        stage('Prepare arm64 Docker Image for Production Environment') {
          agent {
            label 'k8s-kaniko-arm64'
          } // agent
          environment {
            PREVIEW_VERSION = "$BUILD_NUMBER-$BRANCH_NAME-arm64".replaceAll('_', '-')
          } // environment
          steps {
            container(name: 'kaniko', shell: '/busybox/sh') {
              buildAndPushDockerImage("prod", "${PREVIEW_VERSION}")
              updateArtifact("${DOCKER_REGISTRY}/${ORG}/prod/${APP_NAME}", "${VERSION}", "prod")
            } // container
          } // steps
        } // stage('Prepare arm64 Docker Image for Production Environment')
        stage('Prepare amd64 Docker Image for Production Environment') {
          environment {
            PREVIEW_VERSION = "$BUILD_NUMBER-$BRANCH_NAME-amd64".replaceAll('_', '-')
          } // environment
          steps {
            container(name: 'kaniko', shell: '/busybox/sh') {
              buildAndPushDockerImage("prod", "${PREVIEW_VERSION}")
              updateArtifact("${DOCKER_REGISTRY}/${ORG}/prod/${APP_NAME}", "${VERSION}", "prod")
            } // container
          } // steps
        } // stage('Prepare amd64 Docker Image for Production Environment')
      } // parallel
    }; // stage('Prepare Docker Image for Production Environment')
    stage('Build Docker Manifest and push') {
      when { anyOf { branch 'master'; branch 'stage'; branch 'alpha'; } }
      agent {
        label 'teleport-db-agent'
      } // agent
      environment {
        VERSION = "$BUILD_NUMBER-$BRANCH_NAME".replaceAll('_', '-')
        VERSION_ARM = "$BUILD_NUMBER-$BRANCH_NAME-arm64".replaceAll('_', '-')
        VERSION_AMD = "$BUILD_NUMBER-$BRANCH_NAME-amd64".replaceAll('_', '-')
      } // environment
      steps {
        script {
          env = "$BRANCH_NAME" == 'master' ? 'prod' : ("$BRANCH_NAME" == 'alpha' ? 'alpha' : 'stage')
          def URL = "${DOCKER_REGISTRY}/${ORG}/${env}/${APP_NAME}:${VERSION}"
          def URL_ARM = "${DOCKER_REGISTRY}/${ORG}/${env}/${APP_NAME}:${VERSION_ARM}"
          def URL_AMD = "${DOCKER_REGISTRY}/${ORG}/${env}/${APP_NAME}:${VERSION_AMD}"
          dockerManifest(URL, URL_ARM, URL_AMD)
        } // script
      } // steps
    }; // stage('Build Docker Manifest and push')    

    stage ('Preparing Docker Image for Dev testing Environment') {
        when {
          not {
            anyOf {
              expression { params.branchName == null }
              expression { params.branchName == "stage" }
              expression { params.branchName == "alpha" }
              expression { params.branchName == "master" }
            }
          }
        }
        environment {
          PREVIEW_VERSION = "$BUILD_NUMBER-$virtualClusterName".replaceAll('_','-')
          APP_VERSION = "$GIT_COMMIT"
          VOYAGER_URL = 'http://voyager.production.cure.fit.internal/echidna/deployment'
        }
        steps {
            container(name: 'kaniko', shell: '/busybox/sh') {
              sh "echo building ${virtualClusterName} ${DOCKER_REGISTRY}/${ORG}/${APP_NAME}:${PREVIEW_VERSION}"

              buildDockerfileDevspace("stage", "${PREVIEW_VERSION}")
              updateArtifact("${DOCKER_REGISTRY}/${ORG}/stage/${APP_NAME}", "${PREVIEW_VERSION}", "stage")

              sh "curl -sf -X POST \"${VOYAGER_URL}/${params.deploymentId}/trigger\" -H 'Content-Type: application/json;charset=UTF-8' --data-raw '{\"appName\": \"${APP_NAME}\", \"repoName\": \"${params.repoName}\", \"virtualClusterName\": \"${params.virtualClusterName}\", \"imageUrl\": \"${DOCKER_REGISTRY}/${ORG}/stage/${APP_NAME}\", \"imageTag\": \"${PREVIEW_VERSION}\"}'"
            }
          }
       };
    }
  post {
    success {
      cleanWs()
      }
    }
  }

void buildAndPushDockerImage(env, tag) {
    sh """
    #!/busybox/sh -xe
      /kaniko/executor \
      --dockerfile Dockerfile \
      --context `pwd`/ \
      --build-arg NPM_TOKEN=${NPM_TOKEN} \
      --build-arg GITHUB_NPM_TOKEN=${GITHUB_NPM_TOKEN} \
      --build-arg ENVIRONMENT=${env} \
      --build-arg APP_NAME=${APP_NAME} \
      --destination ${DOCKER_REGISTRY}/${ORG}/${env}/${APP_NAME}:${tag}
  """
}

void updateArtifact(repo, tag, env) {
    sh """
    touch build.properties
    echo repo=${repo} >> build.properties
    echo tag=${tag} >> build.properties
    echo env=${env} >> build.properties
    echo enableCanary=${params.EnableCanary} >> build.properties
    """
    archiveArtifacts 'build.properties'
}

void buildDockerfileDevspace(env, tag){
  sh "VIRTUAL_CLUSTER=${virtualClusterName} PREVIEW_VERSION=${tag} DOCKER_REGISTRY=${DOCKER_REGISTRY} ORG=${ORG} APP_NAME=${APP_NAME} GITHUB_NPM_TOKEN=${GITHUB_NPM_TOKEN} NPM_TOKEN=${NPM_TOKEN} ENVIRONMENT=${env} devspace build"
}

void dockerManifest(tag, tag_arm64, tag_amd64) {
  sh "aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ${DOCKER_REGISTRY}"
  sh "docker manifest create ${tag} ${tag_arm64} ${tag_amd64}"
  sh "docker manifest annotate --arch arm64 ${tag} ${tag_arm64}"
  sh "docker manifest annotate --arch amd64 ${tag} ${tag_amd64}"
  sh "docker manifest push ${tag}"
}