import tracer from "dd-trace"
import * as express from "express"
import * as opentracing from "opentracing"


let sampleRate = 0.005
if (process.env.APP_ENV === "ALPHA" || process.env.APP_ENV === "STAGE") {
    sampleRate = 0.05
}

tracer.init({
    runtimeMetrics: process.env.APP_ENV !== "PRODUCTION",
    sampleRate: sampleRate,
    flushInterval: 10_000,
    startupLogs: true,
    profiling: false
}) // initialized in a different file to avoid hoisting.

tracer.use("http", {
    client: {
        hooks: {
            request: (span, req, res) => {
                // the request hook is invoked for both .on 'data' and .on 'end' events
                // the response and it's metadata will exist for 'data' events but not '.end' events
                // configure your hook accordingly, this is an example
                if (res && res.statusCode >= 400) {
                    span.setTag("error.msg", res.statusMessage)
                }
            }
        }
    }
})
let StatsD = require("node-dogstatsd").StatsD
let dogstatsd = new StatsD()
export default tracer
