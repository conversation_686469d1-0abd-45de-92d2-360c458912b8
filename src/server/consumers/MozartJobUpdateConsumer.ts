import {BaseDelayedBatchedQueue<PERSON>and<PERSON>, IQueueService, Message, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {INotificationSegmentTaskReadWriteDao, IRIS_MODELS_TYPES} from "../../iris-models"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {JobStatus} from "@curefit/mozart-client/dist"
import {CampaignManagerService} from "../service/CampaignManagerService"

import {inject, injectable} from "inversify"

import Constants from "../constants"
import {
    NotificationSegmentTask,
    NotificationSegmentTaskJobTypeEnum,
    NotificationSegmentTaskStatusEnum,
    QueueConstants
} from "../../iris-common"
import TYPES from "../ioc/types"
import MozartService from "../service/MozartService"

@injectable()
class MozartJobUpdateConsumer extends BaseDelayedBatchedQueueHandler {

    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(TYPES.MozartService) private mozartService: MozartService,
        @inject(SQS_CLIENT_TYPES.QueueService) protected queueService: IQueueService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.CampaignManagerService) private campaignManagerService: CampaignManagerService,
        @inject(IRIS_MODELS_TYPES.NotificationSegmentTaskReadWriteDao) private notificationSegmentTaskReadWriteDao: INotificationSegmentTaskReadWriteDao
    ) {
        super(QueueConstants.getQueueName(Constants.MOZART_JOB_STATUS_UPDATES_QUEUE), Constants.SQS_BATCH_SIZE, queueService)
        this.init()
    }

    private init() {
        this.logger.info(`MozartJobUpdateConsumer::init Started consumer for ` +
            `${QueueConstants.getQueueName(Constants.MOZART_JOB_STATUS_UPDATES_QUEUE)}`)
    }

    /*
        Used for failures on transient segment download
        segment_download_user use cases are deprecated in segmentation downloadv2 flow
    */
    public async handle(messages: Message[]): Promise<boolean[]> {
        const successfulProcess: boolean[] = []

        for (const message of messages) {
            try {
                const mozartJobUpdate = JSON.parse(message.data)
                const { id: mozartJobId, status, successFile } = mozartJobUpdate
                const jobConfigId = message.attributes["jobConfigId"].StringValue

                this.logger.info(`MozartJobUpdateConsumer::handle ${jobConfigId} ${mozartJobId} ${status} ${successFile}`)

                switch (jobConfigId) {
                    case MozartService.MOZART_JOB_CONFIG_ID_DOWNLOAD_SEGMENT: {
                        await this.handleSegmentDownloadJobUpdate(mozartJobId, status, successFile)
                        break
                    }
                    // call from mozart on job success
                    case MozartService.MOZART_JOB_CONFIG_ID_SEND_USER_TO_QUEUE_TRANSIENT_SEGMENT: {
                        // this mozart job should only be used for transient segment downloads
                        await this.handleTransientSegmentDownloadJobUpdate(mozartJobId, status)
                        break
                    }
                    default: {
                        const errorMessage = `MozartJobUpdateConsumer::handle Unknown job config id ${jobConfigId}`
                        this.logger.error(errorMessage)
                        this.rollbarService.sendError(new Error(errorMessage))
                    }
                }

                successfulProcess.push(true)
            } catch (error) {
                const errorMessage = `MozartJobUpdateConsumer::handle error while processing message ` +
                    `${JSON.stringify(message)}. Error: ${error.toString()}`
                this.logger.error(errorMessage)
                this.rollbarService.sendError(new Error(errorMessage))
                successfulProcess.push(false)
            }
        }
        return successfulProcess
    }

    // Handle segment_download_user
    private async handleSegmentDownloadJobUpdate(mozartJobId: string, status: string, successFile: string): Promise<any> {
        this.logger.info(`MozartJobUpdateConsumer::handleSegmentDownloadJobUpdate Entering with ` +
            `mozartJobId: ${mozartJobId} status: ${status} successFile: ${successFile}`)

        const notificationSegmentTask = await this.notificationSegmentTaskReadWriteDao.findOne({
            "jobs": { $elemMatch: {
                "jobId": String(mozartJobId),
                "type" : NotificationSegmentTaskJobTypeEnum.SEGMENT_DOWNLOAD
            }}})

        // Process only those jobs which are relevant. Allow state transition only from current state = PROCESSING
        if (!notificationSegmentTask || notificationSegmentTask.status !== NotificationSegmentTaskStatusEnum.PROCESSING) {
            this.logger.info(`MozartJobUpdateConsumer::handleSegmentDownloadJobUpdate Not processing job. ` +
                `notificationSegmentTask: ${JSON.stringify(notificationSegmentTask)}`)
            return
        }

        this.logger.info(`MozartJobUpdateConsumer::handleSegmentDownloadJobUpdate Received update from mozart for notification ` +
            `segment task id ${notificationSegmentTask.taskId}. status: ${status} successFile: ${successFile}`)

        if (!status || !successFile) {
            const errorMessage = `MozartJobUpdateConsumer::handleSegmentDownloadJobUpdate status or successFile ` +
                `missing for taskId ${notificationSegmentTask.taskId}`
            this.logger.error(errorMessage)
            throw new Error(errorMessage)
        }

        switch (status) {
            case JobStatus.SUCCESS: {
                this.logger.info(`MozartJobUpdateConsumer::handleSegmentDownloadJobUpdate mozart job successful for ` +
                    `task ${notificationSegmentTask.taskId}`)
                await this.handleSegmentDownloadSuccess(notificationSegmentTask, mozartJobId, successFile)
                break
            }
            case JobStatus.FAILURE: {
                this.logger.info(`MozartJobUpdateConsumer::handleSegmentDownloadJobUpdate mozart job failed`)
                await this.handleSegmentDownloadFailure(notificationSegmentTask)
                break
            }
            default: {
                this.logger.info(`MozartJobUpdateConsumer::handleSegmentDownloadJobUpdate received status ${status}. Ignoring.`)
                break
            }
        }
    }

    // transient segment download mozart job update 
    private async handleTransientSegmentDownloadJobUpdate(mozartJobId: string, status: string) {
        this.logger.info(`MozartJobUpdateConsumer::handleTransientSegmentDownloadJobUpdate Entering with ` +
            `mozartJobId: ${mozartJobId} status: ${status}`)

        const notificationSegmentTask = await this.notificationSegmentTaskReadWriteDao.findOne({
            "jobs": { $elemMatch: {
                "jobId": String(mozartJobId),
                "type" : NotificationSegmentTaskJobTypeEnum.BATCH_PROCESS_QUEUE
            }}})
        
        if(!notificationSegmentTask) {
            let errorMsg: string = `MozartJobUpdateConsumer::handleTransientSegmentDownloadJobUpdate didn't find any associated entry mozartJobId: ${mozartJobId}`
            this.logger.error(errorMsg)
            this.rollbarService.sendError(new Error(errorMsg))
            return
        }

        if(!status || (status !== "SUCCESS" && status !== "FAILURE")) {
            let errorMsg: string = `MozartJobUpdateConsumer::handleTransientSegmentDownloadJobUpdate didn't find any appropriate status with the message received for mozartJobId: ${mozartJobId}, notificationSegmentTaskId: ${notificationSegmentTask.taskId} status: ${status}`
            this.logger.error(errorMsg)
            this.rollbarService.sendError(new Error(errorMsg))
            return
        }

        if(status === "FAILURE"){
            this.handleSegmentDownloadFailure(notificationSegmentTask)
        }
    }


    private async handleSegmentDownloadSuccess(notificationSegmentTask: NotificationSegmentTask, successJobId: string,
                                               successFile: string): Promise<any> {

        // Not catching error here. Failure will send message back to queue for retry
        const batchProcessQueueJob = await this.mozartService.startJob(
            MozartService.MOZART_JOB_CONFIG_ID_SEND_USER_TO_QUEUE,
            `${successJobId}/${successFile}`,
            { "taskId": notificationSegmentTask.taskId },
            notificationSegmentTask.userId
        )

        await this.notificationSegmentTaskReadWriteDao.findOneAndUpdatePartial(
            { taskId: notificationSegmentTask.taskId },
            { $set: { status: NotificationSegmentTaskStatusEnum.SENDING },
                $push: { jobs: { jobId: String(batchProcessQueueJob.id), type: NotificationSegmentTaskJobTypeEnum.BATCH_PROCESS_QUEUE }}})
    }

    private async handleSegmentDownloadFailure(notificationSegmentTask: NotificationSegmentTask): Promise<any> {
        let errMsg : string = "Segment download failed from Mozart";
        await this.notificationSegmentTaskReadWriteDao.findOneAndUpdatePartial(
            { taskId: notificationSegmentTask.taskId },
            { $set: { status: NotificationSegmentTaskStatusEnum.FAILED }})
        await this.campaignManagerService.campaignStatusUpdateOnSegmentDownloadFailure(errMsg, notificationSegmentTask);
    }
}

export default MozartJobUpdateConsumer