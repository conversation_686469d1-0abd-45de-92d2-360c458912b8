import {inject, injectable} from "inversify"

import {BASE_TYPES, Configuration, ILogger} from "@curefit/base"
import {ClickToCallCreative, CommunicationCampaign, IRIS_QUEUE_NAMES, NotificationWrapper, } from "../../iris-common"
import Constants from "../constants"
import TYPES from "../ioc/types"
import {IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {IClickToCallNotificationHelper} from "../helpers/ClickToCallNotificationHelper"
import {BaseConsumer} from "./BaseConsumer"


@injectable()
class CTCNotificationConsumer extends BaseConsumer {
    constructor( @inject(BASE_TYPES.ILogger) logger: ILogger,
                 @inject(SQS_CLIENT_TYPES.QueueService) queueService: IQueueService,
                 @inject(BASE_TYPES.Configuration) appConfig: Configuration,
                 @inject(TYPES.ClickToCallNotificationHelper) private clickToCallNotificationHelper: IClickToCallNotificationHelper
    ) {
        super(logger, queueService, <IRIS_QUEUE_NAMES>Constants.CTC_PR_JOB_QUEUE, Constants.CTC_WORKER_BATCH_SIZE, Constants.CTC, appConfig,
            Constants.CTC_WORKER_DELAY , Constants.CTC_PER_MIN_LIMIT)
        this.sendNotificationsWrapped = this.limiter.wrap((creative: ClickToCallCreative,
                                                           notificationWrappers: NotificationWrapper[],
                                                           isTransactional: boolean,
                                                           campaign: CommunicationCampaign,
                                                           appId: string,
                                                           dryRun?: boolean) =>
            this.clickToCallNotificationHelper.sendNotifications(creative, notificationWrappers, isTransactional, dryRun))
    }
}

export default CTCNotificationConsumer
