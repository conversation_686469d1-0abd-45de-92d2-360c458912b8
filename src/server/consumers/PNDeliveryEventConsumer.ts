import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {BaseDelayedBatchedQueueHandler, IQueueService, Message, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {IBasicNotificationDao, IRIS_MODELS_TYPES} from "../../iris-models"

import {inject, injectable} from "inversify"
import * as _ from "lodash"

import TYPES from "../ioc/types"
import {MetricUtils} from "../utils/MetricUtils"
import constants from "../constants"
import Constants from "../constants"
import {QueueConstants} from "../../iris-common"
import NotificationStatusUpdatePublisher from "../publishers/NotificationStatusUpdatePublisher"
import FeatureUtils from "../utils/FeatureUtils"

@injectable()
class PNDeliveryEventConsumer extends BaseDelayedBatchedQueueHandler {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(SQS_CLIENT_TYPES.QueueService)  queueService: IQueueService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(TYPES.FeatureUtils) private featureUtils: FeatureUtils,
        @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
        @inject(TYPES.NotificationStatusUpdatePublisher) private notificationStatusUpdatePublisher: NotificationStatusUpdatePublisher
    ) {
        /* TODO:
         * This queue subscribes to a topic which receives events from GCP (Big Query).
         * Currently the topic receives messages for both production and stage.
         * We need to figure out a way to bifurcate them into two different topics (stage and prod).
         * Integrity is ensured since we use externalId. Stage external ids would never exist in production
         */
        super(QueueConstants.getQueueName("IRIS_PN_DELIVERY_EVENTS"), constants.SQS_BATCH_SIZE, queueService)
        this.init()
    }

    private init() {
        this.logger.info(`PNDeliveryEventConsumer::init Starting queue handler for ` +
            `${QueueConstants.getQueueName("IRIS_PN_DELIVERY_EVENTS")}`)
    }

    async handle(messages: Message[]): Promise<boolean[]> {
        const ret = []
        const callbackReceivedAt = new Date()

        for (const message of messages) {
            // If PN delivery not enabled via firebase
            if (!this.featureUtils.isPushNotificationDeliveryReportViaFirebaseEnabled()) {
                ret.push(true)
                continue
            }

            try {
                this.logger.debug(`PNDeliveryEventConsumer::handle received message ${JSON.stringify(message)}`)
                const data = JSON.parse(message.data)
                this.logger.debug(`PNDeliveryEventConsumer::handle data is ${JSON.stringify(data)}`)
                this.logger.debug(`PNDeliveryEventConsumer::handle data.Message is ` +
                    `${_.get(data, "Message", "[]")}`)

                // Message attribute in data is being sent as a string
                for (const deliveryEvent of data) {
                    const {
                        event_timestamp : { value: receivedAt },
                        event,
                        app_name: appName,
                        message_id: messageId } = deliveryEvent
                    this.logger.debug(`PNDeliveryEventConsumer::handle extracted ${event} ${receivedAt} ` +
                        `${appName} ${messageId}`)

                    // We need only accepted and delivered events
                    if (event === Constants.FIREBASE_MESSAGE_ACCEPTED_EVENT ||
                        event === Constants.FIREBASE_MESSAGE_DELIVERED_EVENT) {
                        const externalId = Constants.FIREBASE_EXTERNALID_PREFIX + messageId
                        this.logger.debug(`PNDeliveryEventConsumer::handle update ${externalId} with ${receivedAt}`)

                        const notification = await this.notificationDao.getNotificationsByExternalId(externalId)

                        if (!notification) {
                            // Did not find notification. Ignore event
                            continue
                        }

                        this.metricUtils.incrementAppEventTypeCounter(event)
                        this.logger.debug(`PNDeliveryEventConsumer::handle updating ${externalId} with ${receivedAt}`)
                        this.metricUtils.reportCallbackLatency(event.toUpperCase(),
                            callbackReceivedAt.getTime() - new Date(receivedAt).getTime())

                        // If already updated, ignore
                        if (notification.receivedAt) {
                            continue
                        }

                        const updateProps: any = { receivedAt }
                        // Update status only if not read
                        if (notification.status !== "READ") {
                            updateProps.status = "DELIVERED"
                        }
                        await this.notificationDao.updatePropsByExternalId(externalId, updateProps)
                        await this.notificationStatusUpdatePublisher.publish([{ notification,
                            status: "DELIVERED", timestamp: new Date(receivedAt).getTime() }])
                        this.metricUtils.incrementNotificationStatusCounter(
                            notification.campaignId, notification.creativeId, "DELIVERED", "FIREBASE")
                    } else {
                        this.logger.debug(`PNDeliveryEventConsumer::handle received impertinent message ` +
                            `${JSON.stringify(deliveryEvent)}`)
                    }
                }

                ret.push(true)
            } catch (err) {
                const errorMessage = `PNDeliveryEventConsumer::handle Error while processing event: ${err.toString()}`
                this.logger.error(errorMessage, err)
                this.rollbarService.sendError(new Error(errorMessage))
                ret.push(false)
            }
        }
        return ret
    }
}

export default PNDeliveryEventConsumer