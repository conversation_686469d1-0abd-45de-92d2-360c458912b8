import {inject, injectable} from "inversify"

import {BASE_TYPES, Configuration, ILogger} from "@curefit/base"
import {
    CommunicationCampaign,
    InAppNotificationCreative,
    IRIS_QUEUE_NAMES,
    NotificationWrapper,
} from "../../iris-common"
import Constants from "../constants"
import TYPES from "../ioc/types"
import {IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {IInAppNotificationHelper} from "../helpers/InAppNotificationHelper"
import {BaseConsumer} from "./BaseConsumer"


@injectable()
class IANNotificationConsumer extends BaseConsumer {
    constructor( @inject(BASE_TYPES.ILogger) logger: ILogger,
                 @inject(SQS_CLIENT_TYPES.QueueService) queueService: IQueueService,
                 @inject(BASE_TYPES.Configuration) appConfig: Configuration,
                 @inject(TYPES.InAppNotificationHelper) private inAppNotificationHelper: IInAppNotificationHelper
    ) {
        super(logger, queueService, <IRIS_QUEUE_NAMES>Constants.IAN_PR_JOB_QUEUE, Constants.IAN_WORKER_BATCH_SIZE, Constants.IAN, appConfig,
            Constants.IAN_WORKER_DELAY , Constants.IAN_PER_MIN_LIMIT)

        this.sendNotificationsWrapped = this.limiter.wrap((creative: InAppNotificationCreative,
                                                           notificationWrappers: NotificationWrapper[],
                                                           isTransactional: boolean,
                                                           campaign: CommunicationCampaign,
                                                           appId: string,
                                                           dryRun?: boolean) =>
            this.inAppNotificationHelper.persistNotifications(creative, notificationWrappers, isTransactional, campaign, appId, dryRun))
    }
}

export default IANNotificationConsumer
