import {BaseDelayedBatchedQ<PERSON>ue<PERSON><PERSON><PERSON>, IQueueService, Message} from "@curefit/sqs-client"
import {Configuration, ILogger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

import Bottleneck from "@maselious/bottleneck"
import * as _ from "lodash"
import {inject} from "inversify"
import moment = require("moment-timezone")
import {ObjectID} from "mongodb"

import TYPES from "../ioc/types"
import Constants from "../constants"
import FeatureUtils from "../utils/FeatureUtils"
import {CommonUtils} from "../utils/CommonUtils"
import {TimezoneUtils} from "../utils/timezoneUtils"
import UserContextHelper from "../helpers/UserContextHelper"
import {MetricUtils, ProfilerType} from "../utils/MetricUtils"
import {HourMin} from "@curefit/base-common/dist/src/models/Common"
import {MozartHelper} from "../helpers/MozartHelper"
import UserProfileAttributeUtils from "../utils/UserProfileAttributeUtils"
import {BumbleService, DimensionValue} from "../../bumble/services/BumbleService"
import NotificationStatusUpdatePublisher from "../publishers/NotificationStatusUpdatePublisher"
import {IBasicNotificationDao, INotificationTaskReadWriteDao, IRIS_MODELS_TYPES} from "../../iris-models"
import {
    BaseCreative, CommunicationCampaign,
    Country,
    IRIS_QUEUE_NAMES,
    NotificationTask,
    NotificationWrapper,
    QueueConstants
} from "../../iris-common"
import NotificationWrapperCacheService from "../service/NotificationWrapperCacheService"
import {Device} from "@curefit/device-common"
import {DEVICE_MODELS_TYPES, IDeviceReadonlyDao} from "@curefit/device-models"

export interface DNDDetails {
    country: Country,
    forceSend: boolean,
    dndWindowStart: HourMin,
    dndWindowEnd: HourMin
}

export class BaseConsumer extends BaseDelayedBatchedQueueHandler {
    protected limiter: Bottleneck
    protected sendNotificationsWrapped: any
    @inject(IRIS_MODELS_TYPES.NotificationTaskReadWriteDao) private notificationTaskDao: INotificationTaskReadWriteDao
    @inject(TYPES.BumbleService) private bumbleService: BumbleService
    @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao
    @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService
    @inject(TYPES.MozartHelper) private mozartHelper: MozartHelper
    @inject(TYPES.MetricUtils) private metricUtils: MetricUtils
    @inject(TYPES.NotificationStatusUpdatePublisher) private notificationStatusUpdatePublisher: NotificationStatusUpdatePublisher
    @inject(TYPES.FeatureUtils) private featureUtils: FeatureUtils
    @inject(TYPES.UserProfileAttributeUtils) private userProfileAttributeUtils: UserProfileAttributeUtils
    @inject(TYPES.UserContextHelper) private userContextHelper: UserContextHelper
    @inject(TYPES.NotificationWrapperCacheService) private notificationWrapperCacheService: NotificationWrapperCacheService
    @inject(DEVICE_MODELS_TYPES.DeviceReadonlyDao) private deviceDao: IDeviceReadonlyDao

    private logger: ILogger

    constructor(
        logger: ILogger,
        queueService: IQueueService,
        queueName: IRIS_QUEUE_NAMES,
        batchSize: number,
        workerTag: string,
        appConfig: Configuration,
        minTime: number,
        reservoirSize: number,
        reservoirRefreshInterval?: number
    ) {
        super(QueueConstants.getQueueName(queueName), batchSize , queueService, Constants.SQS_INIT_WAIT_TIME)
        this.logger = logger
        const redisConfigs = _.get(appConfig.getConfiguration(), "redisConf.connections")
        const redisConfig: any = _.find(redisConfigs, {"entitySpace": "REDIS_CACHE"})
        this.logger.info(`trying to connect worker ${workerTag} to REDIS using host ${redisConfig.hosts[0].host}, port ${redisConfig.hosts[0].port}`)
        this.limiter = new Bottleneck({
            datastore: "redis",
            clientOptions: {
                host: redisConfig.hosts[0].host,
                port: redisConfig.hosts[0].port
            },
            id: workerTag,
            clearDatastore: false,
            minTime: minTime,
            reservoir: reservoirSize,
            maxConcurrent: reservoirSize,                   // strict rate limiting is needed for emails, can be removed for PNs if required
            reservoirRefreshAmount: reservoirSize,
            reservoirRefreshInterval: (!!reservoirRefreshInterval ? reservoirRefreshInterval : Constants.THROTTLE_RESERVOIR_REFRESH_INTERVAL)
        })
        this.logger.debug(`BaseConsumer::constructor queueName, reservoirRefreshAmount, reservoirRefreshInterval:` +
        `${queueName} ${reservoirSize} ${reservoirRefreshInterval}`)
        this.limiter.ready()
            .then(() => {
                this.logger.info(`${workerTag} worker connected to REDIS`)
            })
        this.limiter.on("error", (error: any) => {
            this.logger.error(error)
            this.rollbarService.sendError(error)
        })
        this.limiter.on("failed", (error: any, jobInfo: any) => {
            this.logger.error(error, jobInfo)
            this.rollbarService.sendError(error)
        })
    }

    private getDNDDetails(nt: NotificationTask): DNDDetails {
        let country: Country = "INDIA"
        let forceSend = false

        let dndWindowStart = this.featureUtils.getConfigValue(Constants.GLOBAL_DND_START_CONFIG_KEY, Constants.GLOBAL_DND_START_DEFAULT)
        let dndWindowEnd = this.featureUtils.getConfigValue(Constants.GLOBAL_DND_END_CONFIG_KEY, Constants.GLOBAL_DND_START_DEFAULT)

        this.logger.debug(`BaseConsumer::getDNDDetails dndWindowStart: ${JSON.stringify(dndWindowStart)} ` +
            `dndWindowEnd: ${JSON.stringify(dndWindowEnd)}`)

        if (nt && nt.forceSend !== undefined && nt.forceSend !== null) {
            forceSend = nt.forceSend
        }
        if (nt && nt.dndWindow && nt.dndWindow.startingHours && nt.dndWindow.closingHours && nt.country) {
            country = nt.country
            dndWindowStart = nt.dndWindow.startingHours
            dndWindowEnd = nt.dndWindow.closingHours
        }
        return {
            country: country,
            forceSend: forceSend,
            dndWindowStart: dndWindowStart,
            dndWindowEnd: dndWindowEnd
        }
    }

    // overridden in child consumers
    getCountry(notificationWrapper: NotificationWrapper, dndDetails: DNDDetails, appId?: string): Country {
        return dndDetails.country
    }

    private validateJob(isBulk: boolean, nt: NotificationTask, taskId: string, notifications: NotificationWrapper[]): boolean {
        if (isBulk && nt && nt.status === "CANCELLED") {
            this.logger.error(`failed to process a chunk of taskId ${taskId} - CANCELLED task.`)
            notifications.map((nw: NotificationWrapper) => {
                nw.notification.failReason = "CANCELLED"
                nw.notification.status = "FAILED"
            })
            return false
        }
        return true
    }

    /**
     *  Parse dates again if present.
     *  When passed via queues they become strings!!
     */

    private fixNotificaitonWrapperDates(notificationWrapper: NotificationWrapper) {
        notificationWrapper.notification.createdAt = new Date(notificationWrapper.notification.createdAt)
        notificationWrapper.notification.scheduledAt = notificationWrapper.notification.scheduledAt ?
            new Date(notificationWrapper.notification.scheduledAt) : undefined
    }

    /**
     * While pushing to this queue, we ensure that each message has a max of 5 notifications.
     * Each of these notifications are processed asynchronously
     * We set our batch size as 10, and process each message asynchronously as well
     * This ensures that at any given point; a max of 50 notifications are being processed async at each worker
     * Underlying SQS lib waits on handle; as a result, only one instance of handle runs at any given point of time
     */
    async handle(messages: Message[]): Promise<boolean[]> {
        // All messages are processed async
        return Promise.all(messages.map(async message => {
            let success = true
            const isTransactional: boolean = false

            let bodyJson = JSON.parse(message.data)
            const {
                creative,
                campaign,
                chunk_progress,
                isBulk = false,
                taskId,
                dryRun,
                forceSend
            } = bodyJson
            const appId = bodyJson["appId"] || Constants.APP_ID_CUREFIT

            let notificationIds: string[] = _.get(bodyJson, "notificationIds", [])
            let notificationWrappers: NotificationWrapper[] = _.get(bodyJson, "notifications", [])

            // If both notifications and notificationIds are empty, there is nothing to process
            if (notificationIds.length === 0 && notificationWrappers.length === 0) {
                return success
            }

            /**
             * If we received notificationIds in the message;
             * use cache with a fallback to DB to fetch notificationWrapper.
             */
            if (notificationWrappers.length === 0) {
                notificationWrappers = await Promise.all(notificationIds.map(async notificationId => {
                    let notificationWrapper =
                        await this.notificationWrapperCacheService.getCachedNotification(notificationId)

                    this.logger.debug(`BaseConsumer::handle notificationId: ${notificationId} ` +
                        `Cached notification: ${JSON.stringify(notificationWrapper)}`)

                    if (!notificationWrapper) {
                        // Fetch notification from db
                        notificationWrapper = {
                            notification: await this.notificationDao.get(notificationId),
                            userContext: null
                        }

                        // Evaluate user context
                        notificationWrapper.userContext =
                            this.userContextHelper.decodeUserContext(notificationWrapper.notification.userTags)

                        this.logger.debug(`BaseConsumer::handle notificationId: ${notificationId} ` +
                            `Notification from DB: ${JSON.stringify(notificationWrapper)}`)
                    }

                    this.logger.debug(`BaseConsumer::handle notificationId: ${notificationId} ` +
                        `notificationWrapper: ${JSON.stringify(notificationWrapper)}`)
                    return notificationWrapper
                }))
            }

            /**
             * We will populate this map with notificationId -> notificationWrapper
             */
            const notificationsMap: {[key: string]: NotificationWrapper} = {}

            notificationWrappers.forEach(notificationWrapper => {
                this.fixNotificaitonWrapperDates(notificationWrapper)
                notificationsMap[notificationWrapper.notification.notificationId] = notificationWrapper
            })

            const notificationTask: NotificationTask = isBulk ? await this.notificationTaskDao.findById(taskId) : null

            /**
             * This is used only for the bulk job option via cyclops.
             * We should deprecate this and move to cerebrum. It'll make this flow cleaner as well
             * For the bulk send flow, notificationWrappers would always be present
             */
            if (!this.validateJob(isBulk, notificationTask, taskId, notificationWrappers)) {
                notificationWrappers.map((nw: NotificationWrapper) => {
                    this.notificationDao.updateProps(nw.notification.notificationId,
                        {
                            failReason: nw.notification.failReason,
                            status: nw.notification.status
                        }
                    )
                })
                this.logger.error(`failed to process a chunk of taskId ${taskId} as it was cancelled or is invalid.`)
                return true
            }

            try {

                // Reschedule if in DND. TODO: Deprecate nt flow. forceSend should only come through queue message
                const dndDetails: DNDDetails = this.getDNDDetails(notificationTask)
                dndDetails.forceSend = dndDetails.forceSend || forceSend
                notificationWrappers = await this.filterOutDNDNotifications(notificationWrappers,
                    dndDetails, appId, dryRun)

                notificationWrappers = await this.validateAppVersionForCampaign(notificationWrappers, campaign, appId)

                // Assign bumble token
                await this.assignBumbleToken(notificationWrappers, creative, campaign, dryRun, appId)

                // Filter out final list of notifications to be sent
                const notificationWrappersToBeSent = await this.filterNotificationsToBeSent(notificationWrappers)

                // Send notifications
                let timerContext
                timerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF,
                    "sendNotificationsWrapped.withOptions", notificationWrappersToBeSent.length)
                await this.sendNotificationsWrapped.withOptions({ weight: notificationWrappersToBeSent.length },
                    creative, notificationWrappersToBeSent, isTransactional, campaign, bodyJson["appId"], dryRun)
                this.metricUtils.endProfilingTimer(timerContext)

                // Post process; each notification async
                // - Update DB, Profile notification, Reschedule if throttled
                await Promise.all(notificationWrappers.map((notificationWrapper) => {
                    return this.postProcessNotificationWrapper(notificationWrapper, creative, campaign, dryRun)
                }))

                // Publish callbacks
                if (!dryRun) {
                    timerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF,
                        "publishCallback", notificationWrappers.length)
                    await this.notificationStatusUpdatePublisher.publish(
                        notificationWrappers.map((notificationWrapper) => {
                            return {
                                notification: notificationWrapper.notification,
                                status: notificationWrapper.notification.status,
                                // sent at will be available only for sent, for all other states use current time
                                timestamp: (notificationWrapper.notification.sentAt || new Date()).getTime()
                            }
                        })
                    )
                    this.metricUtils.endProfilingTimer(timerContext)
                }

                if (isBulk) {
                    await this.notificationTaskDao.increment({_id: new ObjectID(taskId)}, "progress", chunk_progress, 100)
                    this.notificationTaskDao.findById(taskId).then(nt => {
                        if (Math.ceil(nt.progress) >= 100) {
                            nt.status = "COMPLETED"
                            nt.progress = 100
                            this.notificationTaskDao.update(taskId, nt)
                        }
                    })
                }
            } catch (err) {
                success = false
                this.logger.error(`failed to process a chunk of taskId ${taskId}. Error: ${err.message}`)
                this.rollbarService.sendError(err)
            }
            return success
        }))
    }

    private async validateAppVersionForCampaign(notificationWrappers: NotificationWrapper[], campaign: CommunicationCampaign,
                                                appId: string): Promise<NotificationWrapper[]> {
        if (campaign.requiredAppVersion &&
            (campaign.requiredAppVersion.minAppVersion || campaign.requiredAppVersion.maxAppVersion)) {
                const userIds: string[] = notificationWrappers.map(wrapper => wrapper.notification.userId)
                const findQuery = { condition: { userId: {$in: userIds} }}
                let devices: Device[] = await this.deviceDao.find(findQuery)

                const isUserAppVersionValid: {[userId: string]: boolean} = {}
                const minAppVersion = campaign.requiredAppVersion.minAppVersion || -1
                const maxAppVersion = campaign.requiredAppVersion.maxAppVersion || 10000000000000000000
                for (let device of devices) {
                    if (device.activeDevice && device.activeDevice.tenant === appId && device.activeDevice.appVersion &&
                    device.activeDevice.appVersion > minAppVersion && device.activeDevice.appVersion < maxAppVersion) {
                        this.logger.debug(`BaseConsume::validateAppVersionForCampaign  found device with relevant app` +
                        `version for user: ${device.userId} deviceId: ${device.deviceId}`)
                        isUserAppVersionValid[device.userId] = true
                    }
                }

                for (let wrapper of notificationWrappers) {
                    if (!isUserAppVersionValid[wrapper.notification.userId]) {
                        wrapper.notification.status = "FAILED"
                        wrapper.notification.failReason = "INVALID_APP_VERSION"
                    }
                }
        }
        return notificationWrappers
    }

    private async postProcessNotificationWrapper(notificationWrapper: NotificationWrapper, creative: BaseCreative,
                                                 campaign: CommunicationCampaign, dryRun: boolean): Promise<any> {
        if (notificationWrapper.notification.bumbleTokenId) {
            if (!dryRun && notificationWrapper.notification.sentAt) {
                this.logger.debug(`BaseConsumer::handle consuming token for ` +
                    `${notificationWrapper.notification.notificationId}`)
                await this.bumbleService.tokenConsumed(notificationWrapper.notification.bumbleTokenId)
            } else {
                this.logger.debug(`BaseConsumer::handle marking token as not consumed for ` +
                    `${notificationWrapper.notification.notificationId}`)
                await this.bumbleService.tokenUnused(notificationWrapper.notification.bumbleTokenId)
            }
        }

        // If throttled, reschedule
        if (notificationWrapper.notification.failReason === "THROTTLED") {
            const scheduleDelayInMinutes =
                this.featureUtils.getThrottledNotificationScheduleDelayInMinutes()
            const scheduleAt = new Date()
            scheduleAt.setMinutes(scheduleAt.getMinutes() + scheduleDelayInMinutes)
            if (CommonUtils.isDateAfterNotificationExpiry(notificationWrapper.userContext, scheduleAt)) {
                notificationWrapper.notification.failReason = "THROTTLED_SCHEDULE_EXPIRED"
                notificationWrapper.notification.status = "FAILED"
                notificationWrapper.notification.scheduledAt = scheduleAt
            } else {
                try {
                    await this.mozartHelper.scheduleNotification(
                        scheduleAt.toISOString(),
                        notificationWrapper.notification.campaignId,
                        notificationWrapper.notification.creativeId,
                        [ notificationWrapper.notification.notificationId ])
                    notificationWrapper.notification.failReason = undefined
                    notificationWrapper.notification.status = "SCHEDULED"
                    notificationWrapper.notification.scheduledAt = scheduleAt
                    // New bumble token will be allocated on reattempt
                    notificationWrapper.notification.bumbleTokenId = undefined

                    this.logger.debug(`BaseConsumer::handle successfully scheduled throttled notification` +
                        `${notificationWrapper.notification.notificationId} after ${scheduleDelayInMinutes} ` +
                        `minutes at ${scheduleAt.toISOString()}`)
                } catch (err) {
                    notificationWrapper.notification.failReason = "THROTTLED_SCHEDULE_FAILURE"
                    notificationWrapper.notification.status = "FAILED"
                    this.logger.error(`BaseConsumer::handle error while scheduling ` +
                        `${notificationWrapper.notification.notificationId}. Error: ${err.toString()}`)
                }
            }
        }

        this.metricUtils.profileNotification(notificationWrapper.notification, campaign, creative)

        const timerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF,
            "UpdateProps::CONSUMER")
        await this.notificationDao.updateProps(notificationWrapper.notification.notificationId,
            {
                sentAt: notificationWrapper.notification.sentAt,
                bumbleTokenId: notificationWrapper.notification.bumbleTokenId,
                failReason: notificationWrapper.notification.failReason,
                status: notificationWrapper.notification.status,
                externalId: notificationWrapper.notification.externalId,
                scheduledAt: notificationWrapper.notification.scheduledAt,
                userTags: this.userContextHelper.encodeUserContext(notificationWrapper.userContext),
                deviceInfo: notificationWrapper.notification.deviceInfo ?
                    notificationWrapper.notification.deviceInfo : undefined
            }
        )
        this.metricUtils.endProfilingTimer(timerContext)
    }

    private async filterOutDNDNotifications(notificationWrappers: NotificationWrapper[], dndDetails: DNDDetails,
                                            appId: string, dryRun: boolean): Promise<NotificationWrapper[]> {
        const isNotificationInDND: boolean[] = []

        for (const notificationWrapperChunk of _.chunk(notificationWrappers, 5)) {
            isNotificationInDND.push(
                ...(await Promise.all(notificationWrapperChunk.map(notificationWrapper => {
                    return this.checkInDNDAndScheduleIfNecessary({
                        // getting notification specific country
                        country: this.getCountry(notificationWrapper, dndDetails, appId),
                        forceSend: dndDetails.forceSend,
                        dndWindowStart: dndDetails.dndWindowStart,
                        dndWindowEnd: dndDetails.dndWindowEnd
                    }, notificationWrapper, dryRun, appId)
                }))))
        }

        return notificationWrappers.filter(
            (nw, index) => !isNotificationInDND[index])
    }

    private async assignBumbleToken(notificationWrappers: NotificationWrapper[], creative: BaseCreative,
                                    campaign: CommunicationCampaign, dryRun: boolean, appId: string): Promise<any> {

        let dimensions: DimensionValue[] = [
            {
                dimensionName: Constants.BUMBLE_DIMENSION_CHANNEL,
                dimensionValue: creative.type
            }
        ]
        if (campaign.vertical) {
            dimensions.push({
                dimensionName: Constants.BUMBLE_DIMENSION_VERTICAL,
                dimensionValue: campaign.vertical
            })
        }
        if (creative.objective) {
            dimensions.push({
                dimensionName: Constants.BUMBLE_DIMENSION_OBJECTIVE,
                dimensionValue: creative.objective
            })
        }

        const batchSize = 5

        for (const chunk of _.chunk(notificationWrappers, batchSize)) {
            const timerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF,
                "assignTokenIfSendPermitted", chunk.length)

            await Promise.all(chunk.map(nw => {
                return this.assignTokenIfSendPermitted(nw, dimensions, dryRun, appId)
            }))

            this.metricUtils.endProfilingTimer(timerContext)
        }
    }

    private async checkInDNDAndScheduleIfNecessary(dndDetails: DNDDetails,
                                                   notificationWrapper: NotificationWrapper,
                                                   dryRun: boolean, appId: string): Promise<boolean> {
        let inDND = false

        // Create dnd window
        // First attempt to get timezone from Rashi
        // If not present, try using the provided country (defaults to India)
        let timezone = null
        if (this.featureUtils.useRashiForUserTimezone()) {
            this.logger.debug(`BaseConsumer::checkInDNDAndScheduleIfNecessary Using Rashi for timezone`)
            const userAttributes = await this.userProfileAttributeUtils.getCachedUserAttributes(
                notificationWrapper.notification.userId,
                [ Constants.USER_PROFILE_ATTRIBUTE_KEY_TIMEZONE ], appId)
            timezone = userAttributes.attributes.get(Constants.USER_PROFILE_ATTRIBUTE_KEY_TIMEZONE) || null
            this.logger.debug(`BaseConsumer::checkInDNDAndScheduleIfNecessary received timezone ` +
                `${timezone} for user ${notificationWrapper.notification.userId}`)
        }

        // If rashi sent back a timezone, but it is not interpretable; set as null
        if (timezone && !moment.tz.zone(timezone)) {
            const errorMessage = `BaseConsumer::checkInDNDAndScheduleIfNecessary fetched uninterpretable timezone ` +
                `${timezone} for user ${notificationWrapper.notification.notificationId}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            timezone = null
        }
        // For null timezones, default to current inference
        timezone = timezone ? timezone : TimezoneUtils.getCountryString(dndDetails.country)
        this.logger.debug(`BaseConsumer::checkInDNDAndScheduleIfNecessary timezone for user ${timezone}`)

        const now = moment.tz(timezone)
        let dndWindowStart = moment(now)
            .set("hours", dndDetails.dndWindowStart.hour)
            .set("minutes", dndDetails.dndWindowStart.min)
            .set("seconds", 0)
        let dndWindowEnd = moment(now)
            .set("hours", dndDetails.dndWindowEnd.hour)
            .set("minutes", dndDetails.dndWindowEnd.min)
            .set("seconds", 0)

        const adjustedBoundaries = TimezoneUtils.adjustDNDBoundaries(now, dndWindowStart, dndWindowEnd)
        dndWindowStart = adjustedBoundaries.dndWindowStart
        dndWindowEnd = adjustedBoundaries.dndWindowEnd

        // forceSend overrides DND
        if (!dndDetails.forceSend) {
            inDND = !TimezoneUtils.isAppropriateTime(now, dndWindowStart, dndWindowEnd)
        }

        if (dryRun) {
            this.logger.debug(`BaseConsumer::checkDNDAndScheduleIfNecessary Marking dnd false for dry run ` +
                `notification ${notificationWrapper.notification.notificationId}`)
            inDND = false
        }

        // Reschedule if in DND
        if (inDND) {
            this.logger.info(`BaseConsumer::checkDNDAndScheduleIfNecessary Inside DND. Scheduling notification ` +
                `${notificationWrapper.notification.notificationId} at ${dndWindowEnd.toISOString()}`)
            try {
                await this.mozartHelper.scheduleNotification(
                    dndWindowEnd.toISOString(),
                    notificationWrapper.notification.campaignId,
                    notificationWrapper.notification.creativeId,
                    [ notificationWrapper.notification.notificationId ])
                await this.notificationDao.updateProps(notificationWrapper.notification.notificationId,
                    { status: "SCHEDULED", scheduledAt: dndWindowEnd.toDate() })
                this.metricUtils.incrementCampaignCreativeDNDUsageCounter(notificationWrapper.notification.campaignId,
                    notificationWrapper.notification.creativeId)
            } catch (err) {
                this.logger.error(`BaseConsumer::checkDNDAndScheduleIfNecessary error while scheduling ` +
                    `${notificationWrapper.notification.notificationId}. Error: ${err.toString()}`)
                await this.notificationDao.updateProps(notificationWrapper.notification.notificationId,
                    { status: "FAILED", failReason: "DND_SCHEDULE_FAILURE" })
            }
        }
        return inDND
    }

    private async assignTokenIfSendPermitted(notification: NotificationWrapper, dimensions: DimensionValue[],
                                             dryRun?: boolean, appId?: string) {
        /*
        This flag is populated by the issubscribed attribute in Rashi, which is populated by the email unsubscription
         link. Hence we use this value to block only emails. Other Creative types need to be blocked from the
          communicationslimits attributes in Rashi used by Bumble.
         */
        if (notification.notification.creativeId.startsWith(Constants.EMAIL) &&
            ((notification.userContext.tags[Constants.USER_SUBSCRIBED_OLD] ||
            notification.userContext.tags[Constants.USER_SUBSCRIBED]) === "false")) {
            notification.notification.failReason = "UNSUBSCRIBED"
            notification.notification.status = "FAILED"
        } else if (!notification.notification.userId) {
            notification.notification.failReason = "UNKNOWN"
            notification.notification.status = "FAILED"
        } else {
            const bumbleNamespace = appId || Constants.APP_ID_CUREFIT
            const { token, unsubscribed } = await this.bumbleService.fetchUnsubscribedStatusAndToken(bumbleNamespace,
                notification.notification.userId, dimensions, dryRun, false, appId)
            if (token) {
                notification.notification.bumbleTokenId = token
            } else if (unsubscribed) {
                notification.notification.failReason = "UNSUBSCRIBED"
                notification.notification.status = "FAILED"
            } else {
                notification.notification.failReason = "RATE_LIMIT"
                notification.notification.status = "FAILED"
            }
        }
    }

    // Filter out notifications which are to be sent. We exclude:
    //      1. Notifications that have expired
    //      2. Notifications that cannot get a bumble token
    private async filterNotificationsToBeSent(notificationWrappers: NotificationWrapper[]): Promise<NotificationWrapper[]> {
        return notificationWrappers.filter(notificationWrapper => {
            if (CommonUtils.hasNotificationExpired(notificationWrapper.userContext)) {
                this.logger.debug(`BaseConsumer::handle ${notificationWrapper.notification.notificationId} has expired`)
                notificationWrapper.notification.status = "FAILED"
                notificationWrapper.notification.failReason = "EXPIRED"
                return false
            } else if (notificationWrapper.notification.status === "FAILED") {
                this.logger.debug(`BaseConsumer::filterNotificationsToBeSent ${notificationWrapper.notification.notificationId}` +
                    `is already marked failed with reason: ${notificationWrapper.notification.failReason}`)
                return false
            } else {
                this.logger.debug(`BaseConsumer::handle ${notificationWrapper.notification.notificationId} has token ` +
                  `${notificationWrapper.notification.bumbleTokenId}`)
                return !!notificationWrapper.notification.bumbleTokenId
            }
        })
    }
}


