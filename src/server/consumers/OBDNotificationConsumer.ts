import {inject, injectable} from "inversify"

import {BASE_TYPES, Configuration, ILogger} from "@curefit/base"
import {CommunicationCampaign, IRIS_QUEUE_NAMES, NotificationWrapper, OBDCreative, } from "../../iris-common"
import Constants from "../constants"
import TYPES from "../ioc/types"
import {IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {IOBDNotificationHelper} from "../helpers/OBDNotificationHelper"
import {BaseConsumer} from "./BaseConsumer"


@injectable()
class OBDNotificationConsumer extends BaseConsumer {
    constructor( @inject(BASE_TYPES.ILogger) logger: ILogger,
                 @inject(SQS_CLIENT_TYPES.QueueService) queueService: IQueueService,
                 @inject(BASE_TYPES.Configuration) appConfig: Configuration,
                 @inject(TYPES.OBDNotificationHelper) private obdNotificationHelper: IOBDNotificationHelper,
    ) {
        super(logger, queueService, <IRIS_QUEUE_NAMES>Constants.OBD_PR_JOB_QUEUE, Constants.OBD_WORKER_BATCH_SIZE, Constants.OBD, appConfig,
            Constants.OBD_WORKER_DELAY , Constants.OBD_PER_MIN_LIMIT)

        this.sendNotificationsWrapped = this.limiter.wrap((creative: OBDCreative,
                                                           notificationWrappers: NotificationWrapper[],
                                                           isTransactional: boolean,
                                                           campaign: CommunicationCampaign,
                                                           appId: string,
                                                           dryRun?: boolean) =>
            this.obdNotificationHelper.sendNotifications(creative, notificationWrappers, isTransactional, dryRun))
    }
}

export default OBDNotificationConsumer
