import {inject, injectable} from "inversify"

import {BASE_TYPES, Configuration, ILogger} from "@curefit/base"
import {CommunicationCampaign, Country, IRIS_QUEUE_NAMES, NotificationWrapper, SMSCreative, } from "../../iris-common"
import Constants from "../constants"
import TYPES from "../ioc/types"
import {IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {BaseConsumer, DNDDetails} from "./BaseConsumer"
import {PhoneNumberUtils} from "../utils/PhoneNumberUtils"
import SMSNotificationHelper from "../helpers/SMS/SMSNotificationHelper"


@injectable()
class SMSNotificationConsumer extends BaseConsumer {
    constructor( @inject(BASE_TYPES.ILogger) logger: ILogger,
                 @inject(SQS_CLIENT_TYPES.QueueService) queueService: IQueueService,
                 @inject(BASE_TYPES.Configuration) appConfig: Configuration,
                 @inject(TYPES.SMSNotificationHelper) private smsNotificationHelper: SMSNotificationHelper,
    ) {
        super(logger, queueService, <IRIS_QUEUE_NAMES>Constants.SMS_PR_JOB_QUEUE, Constants.SMS_WORKER_BATCH_SIZE, Constants.SMS, appConfig,
            Constants.SMS_WORKER_DELAY , Constants.SMS_PER_MIN_LIMIT)

        this.sendNotificationsWrapped = this.limiter.wrap((creative: SMSCreative,
                                                           notificationWrappers: NotificationWrapper[],
                                                           isTransactional: boolean,
                                                           campaign: CommunicationCampaign,
                                                           appId: string,
                                                           dryRun?: boolean) =>
            this.smsNotificationHelper.sendNotifications(creative, notificationWrappers, isTransactional, dryRun))
    }

    getCountry(notificationWrapper: NotificationWrapper, dndDetails: DNDDetails): Country {
        const phone = notificationWrapper.userContext.phone ||
            notificationWrapper.userContext.tags[Constants.USER_PHONE_TAG_OLD] ||
            notificationWrapper.userContext.tags[Constants.USER_PHONE_TAG]
        if (phone) {
            return PhoneNumberUtils.getCountry(phone)
        }
        return dndDetails.country
    }
}

export default SMSNotificationConsumer
