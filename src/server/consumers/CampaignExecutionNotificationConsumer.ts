import {BASE_TYPES, ILogger} from "@curefit/base"
import {BaseDelayedBatchedQueueHandler, IQueueService, Message, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

import {inject, injectable} from "inversify"

import {QueueConstants} from "../../iris-common"
import Constants from "../constants"
import TYPES from "../ioc/types"
import ScaleService from "../service/ScaleService"

@injectable()
class CampaignExecutionNotificationConsumer extends BaseDelayedBatchedQueueHandler {

    /** We don't want to wait (long polling) in this case. Scale as soon as we get a message */
    static batchSize: number = 1

    /** We do not scale out when audience size is less than minAudienceSizeToScaleOut */
    static minAudienceSizeToScaleOut: number = 20000

    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(TYPES.ScaleService) private scaleService: ScaleService,
        @inject(SQS_CLIENT_TYPES.QueueService)  queueService: IQueueService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService
    ) {
        super(QueueConstants.getQueueName(Constants.CAMPAIGN_EXECUTION_NOTIFICATION_QUEUE),
            CampaignExecutionNotificationConsumer.batchSize, queueService)
        this.init()
    }

    private init() {
        this.logger.info(`CampaignExecutionNotificationConsumer::init Starting queue handler for ` +
            `${QueueConstants.getQueueName(Constants.CAMPAIGN_EXECUTION_NOTIFICATION_QUEUE)}`)
    }

    // We currently filter for only "FAILED" status updates (attribute)
    async handle(messages: Message[]): Promise<boolean[]> {
        const processStatus: boolean[] = []

        for (const message of messages) {
            let successfulProcess = true

            this.logger.info(`CampaignExecutionNotificationConsumer::handle ` +
                `CHANNEL: ${message.attributes["CHANNEL"]}`)
            this.logger.info(`CampaignExecutionNotificationConsumer::handle ` +
                `TARGET_COUNT: ${message.attributes["TARGET_COUNT"]}`)

            const channel = message.attributes["CHANNEL"].StringValue
            const targetAudienceCount = +message.attributes["TARGET_COUNT"].StringValue

            /** PROMO | TRANSACTION */
            const { campaignType } = JSON.parse(message.data)
            this.logger.info(`CampaignExecutionNotificationConsumer::handle channel: ${channel} ` +
                `targetAudienceCount: ${targetAudienceCount} campaignType: ${campaignType}`)

            if (targetAudienceCount >=  CampaignExecutionNotificationConsumer.minAudienceSizeToScaleOut) {

                try {
                    /** Server will always be scaled */
                    await this.scaleService.scaleOutChannel("SERVER")

                    /** Scale worker as well if campaign is promotional */
                    campaignType === "PROMO" && await this.scaleService.scaleOutChannel(channel)

                } catch (err) {
                    const errorMessage = `CampaignExecutionNotificationConsumer::handle Error: ${err.toString()}`
                    this.logger.error(errorMessage)
                    this.rollbarService.sendError(new Error(errorMessage))
                    successfulProcess = false
                }
            }

            processStatus.push(successfulProcess)
        }

        return processStatus
    }
}

export default CampaignExecutionNotificationConsumer