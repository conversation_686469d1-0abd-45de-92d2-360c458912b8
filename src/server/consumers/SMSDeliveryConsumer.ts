import {BASE_TYPES, ILogger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {BaseDelayedBatchedQueueHandler, IQueueService, Message, SQS_CLIENT_TYPES} from "@curefit/sqs-client"

import {inject, injectable} from "inversify"

import TYPES from "../ioc/types"
import Constants from "../constants"
import {QueueConstants, ServiceProvider} from "../../iris-common"
import SMSNotificationHelper from "../helpers/SMS/SMSNotificationHelper"

export interface SMSCallbackParams {
    serviceProvider: ServiceProvider,
    callbackStatus: string,
    notificationId: string,
    deliveredAt: Date,
    callbackReceivedAt: Date,
    errorCause?: string
}

@injectable()
class SMSDeliveryConsumer extends BaseDelayedBatchedQueueHandler {

    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(SQS_CLIENT_TYPES.QueueService)  queueService: IQueueService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.SMSNotificationHelper) private smsNotificationHelper: SMSNotificationHelper
    ) {
        super(QueueConstants.getQueueName(Constants.SMS_CALLBACKS), Constants.SQS_BATCH_SIZE, queueService)
        this.init()
    }

    private init() {
        this.logger.info(`SMSDeliveryConsumer::init Starting queue handler for ` +
            `${QueueConstants.getQueueName(Constants.SMS_CALLBACKS)}`)
    }

    async handle(messages: Message[]): Promise<boolean[]> {
        const processStatus: boolean[] = []

        for (const message of messages) {
            let successfulProcess = true
            try {
                this.logger.info(`SMSDeliveryConsumer::handle received message ${JSON.stringify(message)}`)
                const parsedMessage: SMSCallbackParams = JSON.parse(message.data)
                const deliveredAt: Date = new Date(parsedMessage.deliveredAt)
                const callbackReceivedAt: Date = new Date(parsedMessage.callbackReceivedAt)
            await this.smsNotificationHelper.processSMSCallback(parsedMessage.serviceProvider, parsedMessage.callbackStatus,
                    parsedMessage.notificationId, deliveredAt, callbackReceivedAt, parsedMessage.errorCause)
            } catch (err) {
                const errorMessage = `SMSDeliveryConsumer::handle Error while processing ` +
                    `${JSON.stringify(message)}. Error: ${err.toString()}`
                this.logger.error(errorMessage)
                this.rollbarService.sendError(new Error(err))
                successfulProcess = false
            }
            processStatus.push(successfulProcess)
        }

        return processStatus
    }

}
export default SMSDeliveryConsumer