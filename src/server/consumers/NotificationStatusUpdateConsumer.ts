import {BASE_TYPES, ILogger} from "@curefit/base"
import {BaseDelayedBatchedQueueHandler, IQueueService, Message, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

import {inject, injectable} from "inversify"

import {
    QueueConstants,
    SendCampaignNotificationsRequest,
    SendCampaignNotificationsResponse,
    UserTags
} from "../../iris-common"
import constants from "../constants"
import Constants from "../constants"
import TYPES from "../ioc/types"
import CampaignRequestCacheService from "../service/CampaignRequestCacheService"
import {IBasicNotificationDao, IRIS_MODELS_TYPES} from "../../iris-models"
import CampaignHelper from "../helpers/CampaignHelper"

@injectable()
class NotificationStatusUpdateConsumer extends BaseDelayedBatchedQueueHandler {

    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(SQS_CLIENT_TYPES.QueueService)  queueService: IQueueService,
        @inject(TYPES.CampaignHelper) private campaignHelper: CampaignHelper,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
        @inject(TYPES.CampaignRequestCacheService) private campaignRequestCacheService: CampaignRequestCacheService
    ) {
        super(QueueConstants.getQueueName(Constants.NOTIFICATION_STATUS_UPDATE_QUEUE),
            constants.SQS_BATCH_SIZE, queueService)
        this.init()
    }

    private init() {
        this.logger.info(`NotificationStatusUpdateConsumer::init Starting queue handler for ` +
            `${QueueConstants.getQueueName(Constants.NOTIFICATION_STATUS_UPDATE_QUEUE)}`)
    }

    // We currently filter for only "FAILED" status updates (attribute)
    async handle(messages: Message[]): Promise<boolean[]> {
        const processStatus: boolean[] = []
        for (const message of messages) {
            let successfulProcess = true

            const status = message.attributes["status"].StringValue

            // Failsafe
            if (status !== "FAILED") {
                this.logger.debug(`NotificationStatusUpdateConsumer::handle Ignoring message with status ${status}`)
                processStatus.push(successfulProcess)
                continue
            }
            const statusUpdates = JSON.parse(message.data)
            this.logger.debug(`NotificationStatusUpdateConsumer::handle parsed statusUpdates:${JSON.stringify(statusUpdates)}`)
            for (const statusUpdate of statusUpdates) {
                const { notificationId, taskId } = statusUpdate
                this.logger.debug(`NotificationStatusUpdateConsumer::handle notificationId: ${notificationId} ` +
                    `taskId: ${taskId}`)

                let sendCampaignNotificationsRequest: SendCampaignNotificationsRequest =
                    await this.campaignRequestCacheService.getSingleCreativeCampaignRequest(taskId)
                let isFallbackPresentInCreativeList: boolean = !!sendCampaignNotificationsRequest

                // check in all campaignNotificationRequest if not present in the single creative flow
                if (!sendCampaignNotificationsRequest) {
                    sendCampaignNotificationsRequest = await  this.campaignRequestCacheService.getCampaignRequest(taskId)
                    isFallbackPresentInCreativeList = false
                }

                // Only send via one creative flow caches for failure retries
                // Ignore message if send request not found
                if (!sendCampaignNotificationsRequest) {
                    this.logger.info(`NotificationStatusUpdateConsumer::handle No send request found for ` +
                        `notificationId: ${notificationId} taskId: ${taskId}. Skipping.`)
                    continue
                }

                this.logger.debug(`NotificationStatusUpdateConsumer::handle Cached send request found for ` +
                    `notificationId: ${notificationId} taskId: ${taskId}. Trying via remaining creatives.`)
                try {
                    await this.sendNotificationThroughNextCreative(sendCampaignNotificationsRequest, statusUpdate, isFallbackPresentInCreativeList)
                } catch (error) {
                    const errorMessage = `NotificationStatusUpdateConsumer::handle Failed while calling ` +
                        `sendNotificationThroughNextCreative with error ${error.toString()}`
                    this.logger.error(errorMessage)
                    this.rollbarService.sendError(new Error(errorMessage))
                }
            }

            processStatus.push(successfulProcess)
        }

        return processStatus
    }

    private async sendNotificationThroughNextCreative(sendCampaignNotificationsRequest: SendCampaignNotificationsRequest,
                                                      statusUpdate: any, isFallbackPresentInCreativeList: boolean): Promise<any> {
        // Filter user context for current failed notification
        const userContexts: UserTags[] = sendCampaignNotificationsRequest.userContexts
            .filter((userContext) => { return statusUpdate.userId && statusUpdate.userId.toString() === this.getUserIdFromUserContext(statusUpdate.userIdType, userContext)})

        // User context should be present
        if (userContexts.length === 0 && isFallbackPresentInCreativeList) {
            const errorMessage = `NotificationStatusUpdateConsumer::sendNotificationThroughNextCreative No user ` +
                `context found. statusUpdate: ${JSON.stringify(statusUpdate)}` +
                `sendCampaignNotificationsRequest: ${JSON.stringify(sendCampaignNotificationsRequest)}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            return
        }

        const previousCreativeId = statusUpdate.creativeId

        // Create the new send request. Differences would be:
        // - userContext being passed
        // - no atSchedule, since this would always be a retry flow
        sendCampaignNotificationsRequest.userContexts = userContexts
        sendCampaignNotificationsRequest.atSchedule = undefined

        this.logger.debug(`NotificationStatusUpdateConsumer::sendNotificationThroughNextCreative `
            + `Created new send campaign request: ${JSON.stringify(sendCampaignNotificationsRequest)}`)
        try {
            if (isFallbackPresentInCreativeList && sendCampaignNotificationsRequest.creativeIds
                && sendCampaignNotificationsRequest.creativeIds.length > 1) {
                await this.campaignHelper.sendCampaignMessagesThroughOneCreative(sendCampaignNotificationsRequest, previousCreativeId)
            } else if (!isFallbackPresentInCreativeList && sendCampaignNotificationsRequest.fallbackCreativeIds
                && sendCampaignNotificationsRequest.fallbackCreativeIds[previousCreativeId]) {
                const sendNotificationResponse: SendCampaignNotificationsResponse = await this.campaignHelper.sendCampaignNotificationsWithFallback(sendCampaignNotificationsRequest, true, previousCreativeId)
                const fallbackNotificationId = sendNotificationResponse[statusUpdate.userId][0].notificationId
                this.logger.info(`NotificationStatusUpdateConsumer::sendNotificationThroughNextCreative triggerd fallbackNotificationId:${fallbackNotificationId} for notificationId:${statusUpdate.notificationId}`)
                //TODO: Capture fallbackNotification in the database
            }
        } catch (error) {
            this.logger.info(`NotificationStatusUpdateConsumer::sendNotificationThroughNextCreative error `
                + `while calling campaignHelper: ${error.toString()} with previousCreativeId: ${previousCreativeId} `
                + `sendCampaignNotificationsRequest: ${JSON.stringify(sendCampaignNotificationsRequest)} `
                + `triggered by statusUpdate: ${JSON.stringify(statusUpdate)}`)
        }
    }

    getUserIdFromUserContext(userIdType: string, userContext: UserTags): string {
        switch (userIdType) {
            case Constants.USER_ID_TYPES.EMAIL_ID: return userContext.emailId ? userContext.emailId.toString() : ""
            case Constants.USER_ID_TYPES.PHONE_NUMBER: return userContext.phone ? userContext.phone.toString() : ""
            case Constants.USER_ID_TYPES.CUREFIT_USER_ID:
            default: return userContext.userId ? userContext.userId.toString() : ""
        }
    }
}

export default NotificationStatusUpdateConsumer