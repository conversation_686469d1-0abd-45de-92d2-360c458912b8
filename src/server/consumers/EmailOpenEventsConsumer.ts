import {BaseDelayedBatchedQueue<PERSON><PERSON><PERSON>, IQueueService, Message, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {BASE_TYPES, ILogger} from "@curefit/base"

import {inject, injectable} from "inversify"

import constants from "../constants"
import Constants from "../constants"
import {QueueConstants} from "../../iris-common"
import TYPES from "../ioc/types"
import {MetricUtils} from "../utils/MetricUtils"
import {IBasicNotificationDao, IRIS_MODELS_TYPES} from "../../iris-models"
import NotificationStatusUpdatePublisher from "../publishers/NotificationStatusUpdatePublisher"

export interface EmailOpenEvent {
    "eventType": "Open",
    "mail": {
        "commonHeaders": any,
        "destination": [string],
        "headers": any,
        "headersTruncated": boolean,
        "messageId": string,
        "sendingAccountId": string,
        "source": string,
        "tags": any,
        "timestamp": Date
    },
    "open": {
        "ipAddress": string,
        "timestamp": Date,
        "userAgent": string
    }
}

@injectable()
class EmailOpenEventsConsumer extends BaseDelayedBatchedQueueHandler {

    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(SQS_CLIENT_TYPES.QueueService)  queueService: IQueueService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
        @inject(TYPES.NotificationStatusUpdatePublisher) private notificationStatusUpdatePublisher: NotificationStatusUpdatePublisher
    ) {
        super(QueueConstants.getQueueName("IRIS_EMAIL_OPEN_EVENTS"), constants.SQS_BATCH_SIZE, queueService)
        this.init()
    }

    private init() {
        this.logger.info(`EmailOpenEventsConsumer::init Starting queue handler for ` +
            `${QueueConstants.getQueueName("IRIS_EMAIL_OPEN_EVENTS")}`)
    }

    async handle(messages: Message[]): Promise<boolean[]> {
        let result: boolean[] = []
        const messageReceivedAt = new Date()

        for (let message of messages) {
            this.logger.debug(`EmailOpenEventsConsumer::handle Event received: ${message.data}`)
            try {
                const emailOpenEvent: EmailOpenEvent = JSON.parse(message.data)
                const externalId = Constants.EMAIL_EXTERNALID_PREFIX + emailOpenEvent.mail.messageId
                const notification = await this.notificationDao.getNotificationsByExternalId(externalId)

                if (!notification) {
                    this.logger.warn(`EmailOpenEventsConsumer::handle Received event for unknown external id ` +
                        `${emailOpenEvent.mail.messageId}`)
                } else if (notification.openedAt) {
                    this.logger.info(`EmailOpenEventsConsumer::handle Received event for notification ` +
                        `where openedAt exists. ${notification.notificationId} ${JSON.stringify(emailOpenEvent)}`)
                } else {
                    const openedAt: Date = new Date(emailOpenEvent.open.timestamp)
                    await this.notificationDao.updatePropsByExternalId(externalId,
                        { status: "READ", openedAt })
                    await this.notificationStatusUpdatePublisher.publish([{ notification, status: "READ",
                        timestamp: openedAt.getTime() }])
                    this.metricUtils.reportCallbackLatency("EMAIL_SES_OPEN",
                        messageReceivedAt.getTime() - openedAt.getTime())
                    this.metricUtils.incrementNotificationStatusCounter(
                        notification.campaignId, notification.creativeId, "READ", "SES")
                }

                result.push(true)
            }
            catch (err) {
                this.rollbarService.sendError(new Error(err))
                this.logger.error(`EmailOpenEventsConsumer::handle Unable to process open email `
                    + `event due to ${err.toString()}`)
                result.push(false)
            }
        }
        return result
    }

}

export default EmailOpenEventsConsumer
