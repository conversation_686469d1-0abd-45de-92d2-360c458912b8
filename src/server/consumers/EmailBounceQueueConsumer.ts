import {BASE_TYPES, ILogger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {BaseDelayedBatchedQueueHandler, IQueueService, Message, SQS_CLIENT_TYPES} from "@curefit/sqs-client"

import {inject, injectable} from "inversify"
import Constants from "../constants"
import {AWSUtils} from "../utils/AWSUtils"
import {snsTopic} from "../enums"
import {QueueConstants} from "../../iris-common"
import {EventData, EVENTS_TYPES, EventsService} from "@curefit/events-util"

@injectable()
class EmailBounceQueueConsumer extends BaseDelayedBatchedQueueHandler {

    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(SQS_CLIENT_TYPES.QueueService)  queueService: IQueueService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(EVENTS_TYPES.EventsService) private eventsService: EventsService
    ) {
        super(QueueConstants.getQueueName(Constants.SQS_EMAIL_BOUNCE_ALL), Constants.SQS_BATCH_SIZE, queueService)
        this.init()
    }

    private init() {
        this.logger.info(`EmailBounceQueueConsumer::init Starting queue handler for ` +
            `${QueueConstants.getQueueName(Constants.SQS_EMAIL_BOUNCE_ALL)}`)
    }

    async handle(messages: Message[]): Promise<boolean[]> {
        const processStatus: boolean[] = []

        for (const message of messages) {
            let successfulProcess = true
            try {
                this.logger.debug(`EmailBounceQueueConsumer::handle received message ${JSON.stringify(message)}`)
                const parsedMessage = JSON.parse(JSON.parse(message.data).Message)
                let bounceType: string = parsedMessage.bounce.bounceType
                if (bounceType.toUpperCase() !== "PERMANENT") {
                    processStatus.push(successfulProcess)
                    continue
                }
                const emails = parsedMessage.bounce.bouncedRecipients
                for (let email of emails) {
                    const source: Map<string, any> = new Map<string, any>([["source", Constants.SQS_EMAIL_BOUNCE_ALL]])
                    this.logger.debug(`EmailBounceQueueConsumer::handle publishing for email ${email.emailAddress}`)
                    const eventData: EventData<any> = new EventData<any>(
                        Constants.IRIS + "_" + Constants.SNS_EMAIL_BOUNCE_REPORT, null,
                        Date.now(), {email: email.emailAddress} , source)
                    try {
                        this.eventsService.publishMessageAsync(
                            AWSUtils.getAwsSNSTopicName(snsTopic.INVALID_EMAILS, false), eventData)
                    } catch (err) {
                        this.logger.error(`EmailBounceQueueConsumer::handle Failed publishing to SQS for event data ` +
                            `${JSON.stringify(eventData)} with error ${err.toString()}`)
                        throw err
                    }
                }

            } catch (err) {
                const errorMessage = `EmailBounceQueueConsumer::handle Error while processing ` +
                    `${JSON.stringify(message)}. Error: ${err.toString()}`
                this.logger.error(errorMessage)
                this.rollbarService.sendError(new Error(err))
                successfulProcess = false
            }
            processStatus.push(successfulProcess)
        }

        return processStatus
    }

}
export default EmailBounceQueueConsumer