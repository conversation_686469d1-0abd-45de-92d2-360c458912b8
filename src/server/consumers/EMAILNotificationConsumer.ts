import {inject, injectable} from "inversify"

import {BASE_TYPES, Configuration, ILogger} from "@curefit/base"
import {CommunicationCampaign, EmailCreative, IRIS_QUEUE_NAMES, NotificationWrapper, } from "../../iris-common"
import Constants from "../constants"
import TYPES from "../ioc/types"
import {IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {IEmailNotificationHelper} from "../helpers/EmailNotificationHelper"
import {BaseConsumer} from "./BaseConsumer"


@injectable()
class EmailNotificationConsumer extends BaseConsumer {
    constructor( @inject(BASE_TYPES.ILogger) logger: ILogger,
                 @inject(SQS_CLIENT_TYPES.QueueService) queueService: IQueueService,
                 @inject(BASE_TYPES.Configuration) appConfig: Configuration,
                 @inject(TYPES.EmailNotificationHelper) private emailNotificationHelper: IEmailNotificationHelper,
    ) {
        super(logger, queueService, <IRIS_QUEUE_NAMES>Constants.EMAIL_PR_JOB_QUEUE, Constants.EMAIL_WORKER_BATCH_SIZE, Constants.EMAIL, appConfig,
            Constants.EMAIL_WORKER_DELAY , Constants.EMAIL_PER_SEC_LIMIT, Constants.EMAIL_THROTTLE_RESERVOIR_REFRESH_INTERVAL)

        this.sendNotificationsWrapped = this.limiter.wrap((creative: EmailCreative,
                                                           notificationWrappers: NotificationWrapper[],
                                                           isTransactional: boolean,
                                                           campaign: CommunicationCampaign,
                                                           appId: string,
                                                           dryRun?: boolean) =>
            this.emailNotificationHelper.sendNotifications(creative, notificationWrappers, isTransactional, dryRun))
    }
}

export default EmailNotificationConsumer
