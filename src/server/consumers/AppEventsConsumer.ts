import {BASE_TYPES, Configuration, ILogger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

import * as _ from "lodash"
import {inject, injectable} from "inversify"
import {<PERSON><PERSON>ka, <PERSON>fkaMessage} from "kafkajs"

import {IBasicNotificationDao, IRIS_MODELS_TYPES} from "../../iris-models"
import {IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import TYPES from "../ioc/types"
import {MetricUtils} from "../utils/MetricUtils"
import FeatureUtils from "../utils/FeatureUtils"
import NotificationStatusUpdatePublisher from "../publishers/NotificationStatusUpdatePublisher"
import {KafkaUtils} from "../utils/KafkaUtils"

const enum AppEventType {
    PN_IMPRESSION = "pn_impression",
    PN_CLICKED = "pn_clicked"
}

@injectable()
class AppEventsConsumer {

    private readonly topic: string = "branch_io_topic"
    private readonly groupId: string = "iris-app-events-consumer"
    private readonly appEventsConsumerConf: any = undefined

    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(TYPES.FeatureUtils) private featureUtils: FeatureUtils,
        @inject(BASE_TYPES.Configuration) private appConfig: Configuration,
        @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
        @inject(TYPES.NotificationStatusUpdatePublisher) private notificationStatusUpdatePublisher: NotificationStatusUpdatePublisher,
        @inject(TYPES.KafkaUtils) private kafkaUtils: KafkaUtils
    ) {

        // Load config
        this.appEventsConsumerConf = _.get(this.appConfig.getConfiguration(),
            "appEventsConsumerConf", undefined)
        if (!this.appEventsConsumerConf) {
            const errorMessage = `AppEventsConsumer::startConsumer missing conf`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            return
        }

        this.logger.info(`AppEventsConsumer::constructor starting consumer`)
        this.startConsumer()
            .catch((error) => {
                const errorMessage = `AppEventsConsumer::constructor error in consumer: ${error.toString()}`
                this.logger.error(errorMessage)
                this.rollbarService.sendError(new Error(errorMessage))
            })
    }

    private async startConsumer(): Promise<any> {
        const clientId = _.get(this.appEventsConsumerConf, "clientId")
        const brokers = _.get(this.appEventsConsumerConf, "brokers")
        const kafka = new Kafka({ clientId, brokers })

        const consumer = kafka.consumer({
            groupId: this.groupId,
            maxWaitTimeInMs: _.get(this.appEventsConsumerConf, "maxWaitTimeInMs", 200),
            sessionTimeout: _.get(this.appEventsConsumerConf, "sessionTimeoutInMs", 300000),
            heartbeatInterval: _.get(this.appEventsConsumerConf, "heartbeatInterval", 3000),
            maxBytes: _.get(this.appEventsConsumerConf, "maxBytes", 262144)
        })

        try {
            await consumer.connect()
            this.logger.info(`AppEventsConsumer::startConsumer Connection to broker established`)
            await consumer.subscribe({ topic: this.topic, fromBeginning: false })
            this.logger.info(`AppEventsConsumer::startConsumer Subscribed to topic ${this.topic}`)
        } catch (error) {
            this.logger.error(`AppEventsConsumer::startConsumer Error while connecting consumer ${error.toString()}`)
            return
        }

        this.logger.info(`AppEventsConsumer::startConsumer Beginning to receive messages`)
        const queueResource = _.get(this.appEventsConsumerConf, "dlq")

        await consumer.run({
            partitionsConsumedConcurrently: 1,
            eachBatchAutoResolve: false,
            eachBatch: async ({batch, resolveOffset, heartbeat, isRunning, isStale}) => {
                for (const message of batch.messages) {

                    if (!isRunning() || isStale()) {
                        this.logger.info(`AppEventsConsumer::startConsumer Consumer is shutting down / batch` +
                            `is stale. isRunning: ${isRunning()} isStale: ${isStale()}`)
                        break
                    }

                    const callbackReceivedAt = new Date()
                    try {
                        await this.handleMessage(message, callbackReceivedAt)
                    } catch (error) {
                        this.logger.error(`AppEventsConsumer::startConsumer Error in handleMessage: ${error.toString()} ` +
                            `Event: ${JSON.stringify(message)}`)
                        await this.kafkaUtils.handleMessageFailure(message, queueResource)
                    }
                    await heartbeat()
                    await resolveOffset(message.offset)
                }
            }
        })
    }


    private async handleMessage(message: KafkaMessage, callbackReceivedAt: Date) {
        const eventData = JSON.parse(message.value.toString())
        const eventType = _.get(eventData, "name", "").toLowerCase()
        this.metricUtils.incrementAppEventTypeCounter(eventType)

        if (eventType === AppEventType.PN_IMPRESSION || eventType === AppEventType.PN_CLICKED) {
            await this.processPushNotificationEvent(eventData, eventType, callbackReceivedAt)
        }
    }

    private async processPushNotificationEvent(eventData: any, eventType: AppEventType,
                                               callbackReceivedAt: Date): Promise<any> {
        const eventTimestamp = this.getTimestamp(eventData)
        if (!eventTimestamp) {
            this.logger.error(`AppEventsConsumer::processPushNotificationEvent Event does not have timestamp ` +
                `Event: ${JSON.stringify(eventData)}`)
            return
        }
        const eventTime = new Date(eventTimestamp)

        const notificationId = this.getNotificationId(eventData)
        if (!notificationId) {
            this.logger.error(`AppEventsConsumer::processPushNotificationEvent Event does not have notificationId ` +
                `Event: ${JSON.stringify(eventData)}`)
            return
        }

        this.logger.info(`AppEventsConsumer::processPushNotificationEvent ${eventType} message for ` +
            `notificationId: ${notificationId} at eventTimestamp: ${eventTimestamp}`)

        // If PN delivery not enabled via app events
        if (eventType === AppEventType.PN_IMPRESSION &&
            !this.featureUtils.isPushNotificationDeliveryReportViaAppEventsEnabled()) {
            return
        }

        const notification = await this.notificationDao.get(notificationId)
        if (!notification) {
            this.logger.info(`AppEventsConsumer::processPushNotificationEvent Received event for unknown ` +
                `notificationId ${notificationId} Event: ${JSON.stringify(eventData)}`)
            return
        }

        // Do not update if already updated
        switch (eventType) {
            case AppEventType.PN_IMPRESSION: {
                if (notification.receivedAt) {
                    return
                }
                break
            }
            case AppEventType.PN_CLICKED: {
                if (notification.openedAt) {
                    return
                }
                break
            }
        }

        const updateProps: any = {}
        switch (eventType) {
            case AppEventType.PN_IMPRESSION: {
                updateProps.receivedAt = eventTime

                // Events can be out of order. Mark as delivered if not already marked as READ
                // Race conditions can arise, hence notification status is not guaranteed to be accurate
                // receivedAt, openedAt should be used for accurate analysis
                // P.S. We do not take locks due to perf reasons
                if (notification.status !== "READ") {
                    updateProps.status = "DELIVERED"
                }
                this.metricUtils.incrementNotificationStatusCounter(
                    notification.campaignId, notification.creativeId, "DELIVERED", "APP_EVENT")
                break
            }
            case AppEventType.PN_CLICKED: {
                updateProps.openedAt = eventTime
                updateProps.status = "READ"
                this.metricUtils.incrementNotificationStatusCounter(
                    notification.campaignId, notification.creativeId, "READ", "APP_EVENT")
                break
            }
        }

        this.logger.info(`AppEventsConsumer::processPushNotificationEvent updating ${notificationId} ` +
            `with props: ${JSON.stringify(updateProps)}`)
        await this.notificationDao.updateProps(notificationId, updateProps)
        await this.notificationStatusUpdatePublisher.publish([{ notification, status: updateProps.status,
            timestamp: eventTime.getTime() }])
        this.metricUtils.reportCallbackLatency(eventType.toUpperCase(),
            callbackReceivedAt.getTime() - eventTime.getTime())
    }

    private getNotificationId(eventData: any): string {
        // Existing spelling issue in app side logic. Try for both spellings
        let notificationId = _.get(eventData, "custom_data.notification_id", undefined)
        if (!notificationId) {
            notificationId = _.get(eventData, "custom_data.notifcation_id")
        }
        return notificationId
    }

    private getTimestamp(eventData: any): number {
        return _.get(eventData, "event_timestamp")
    }
}

export default AppEventsConsumer