import {inject, injectable} from "inversify"
import {BaseDelayedQueueHandler, IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {BASE_TYPES, ILogger} from "@curefit/base"
import TYPES from "../ioc/types"
import ClevertapService from "../helpers/ClevertapService"
import {AppName} from "@curefit/base-utils"
import {
    APP_NAMES,
    NOTIFICATION_TYPES,
    NotificationParams,
    NotificationType,
    QUEUE_NAME
} from "../helpers/INotificationService"
import {DataError} from "@curefit/error-client"
import {QueueConstants} from "../../iris-common"

// TODO: Deprecate this flow
@injectable()
class NotificationProcessorQueueConsumer extends BaseDelayedQueueHandler {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(TYPES.INotificationService) private notificationService: ClevertapService,
        @inject(SQS_CLIENT_TYPES.QueueService) protected queueService: IQueueService
    ) {
        super(QueueConstants.getQueueName(QUEUE_NAME), queueService)
        this.init()
    }

    private init() {
        this.logger.info(`Starting queue handler for ${QUEUE_NAME}`)
    }

    async handle(data: string, attributes: { [key: string]: string }): Promise<boolean> {
        const appName: AppName = (<any>(attributes["appName"] || {})).StringValue
        const notificationType: NotificationType = (<any>(attributes["notificationType"] || {})).StringValue

        this.logger.info(`NotificationProcessorQueueListener data: ${data}, props: appName ${appName}, notificationType: ${notificationType}`)

        if (!appName || APP_NAMES.indexOf(appName) === -1) {
            throw new DataError("Invalid parameter appName")
        }

        if (!notificationType || NOTIFICATION_TYPES.indexOf(notificationType)) {
            throw new DataError("Invalid parameter notificationType")
        }

        try {
            const notificationParams: NotificationParams = JSON.parse(data)
            await this.notificationService.sendNotification(notificationType, notificationParams, appName)
            this.logger.info(`Sent notification with ${data} successfully`)
            return Promise.resolve(true)
        } catch (err) {
            this.logger.error(`Unable to process event. Error: ${err.message}, data: ${data}`)
            return Promise.reject(err)
        }
    }
}

export default NotificationProcessorQueueConsumer