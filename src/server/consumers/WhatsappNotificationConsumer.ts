import {inject, injectable} from "inversify"

import {BASE_TYPES, Configuration, ILogger} from "@curefit/base"
import {
    CommunicationCampaign,
    Country,
    IRIS_QUEUE_NAMES,
    NotificationWrapper,
    WhatsappCreative,
} from "@curefit/iris-common"
import Constants from "../constants"
import TYPES from "../ioc/types"
import {IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {BaseConsumer, DNDDetails} from "./BaseConsumer"
import {IWhatsappNotificationHelper} from "../helpers/whatsapp/WhatsappNotificationHelper"
import {PhoneNumberUtils} from "../utils/PhoneNumberUtils"


@injectable()
class WhatsappNotificationConsumer extends BaseConsumer {
    constructor( @inject(BASE_TYPES.ILogger) logger: ILogger,
                 @inject(SQS_CLIENT_TYPES.QueueService) queueService: IQueueService,
                 @inject(BASE_TYPES.Configuration) appConfig: Configuration,
                 @inject(TYPES.WhatsappNotificationHelper) private whatsappNotificationHelper: IWhatsappNotificationHelper,
    ) {
        super(logger, queueService, <IRIS_QUEUE_NAMES>Constants.WHATSAPP_PR_JOB_QUEUE, Constants.WHATSAPP_WORKER_BATCH_SIZE, Constants.WHATSAPP, appConfig,
            Constants.WHATSAPP_WORKER_DELAY , Constants.WHATSAPP_PER_SEC_LIMIT, Constants.WHATSAPP_THROTTLE_RESERVOIR_REFRESH_INTERVAL)

        this.sendNotificationsWrapped = this.limiter.wrap((creative: WhatsappCreative,
                                                           notificationWrappers: NotificationWrapper[],
                                                           isTransactional: boolean,
                                                           campaign: CommunicationCampaign,
                                                           appId: string,
                                                           dryRun?: boolean,
                                                           testRun?: boolean) =>
            this.whatsappNotificationHelper.sendNotifications(creative, notificationWrappers, isTransactional, dryRun, testRun))
    }

    getCountry(notificationWrapper: NotificationWrapper, dndDetails: DNDDetails): Country {
        const phone = notificationWrapper.userContext.phone ||
            notificationWrapper.userContext.tags[Constants.USER_PHONE_TAG_OLD] ||
            notificationWrapper.userContext.tags[Constants.USER_PHONE_TAG]
        if (phone) {
            return PhoneNumberUtils.getCountry(phone)
        }
        return dndDetails.country
    }
}

export default WhatsappNotificationConsumer
