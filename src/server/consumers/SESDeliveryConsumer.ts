import {BASE_TYPES, ILogger} from "@curefit/base"
import {BaseDelayedBatchedQueueHandler, IQueueService, Message, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

import {inject, injectable} from "inversify"

import {QueueConstants} from "../../iris-common"
import constants from "../constants"
import TYPES from "../ioc/types"
import NotificationHelper from "../helpers/NotificationHelper"
import {MetricUtils} from "../utils/MetricUtils"
import CampaignHelper from "../helpers/CampaignHelper"
import {IBasicNotificationDao, IRIS_MODELS_TYPES} from "../../iris-models"
import NotificationStatusUpdatePublisher from "../publishers/NotificationStatusUpdatePublisher"

interface SESDeliveryMessageMeta {
    Type: string,
    MessageId: string,
    TopicArn: string,
    Message: string,
    Timestamp: string,
    SignatureVersion: string,
    Signature: string,
    SigningCertURL: string,
    UnsubscribeURL: string
}

interface SESMessage {
    notificationType: string,
    delivery: Delivery,
    mail: Mail
}

export interface Delivery {
    timestamp: Date,
    processingTimeMillis: number,
    recipients: string[],
    smtpResponse: string,
    remoteMtaIp: string,
    reportingMTA: string,
}
export interface Mail {
    timestamp: Date,
    source: string,
    sourceArn: string,
    sourceIp: string,
    sendingAccountId: string,
    messageId: string,
    destination: string[],
    headersTruncated: boolean,
}

@injectable()
class SESDeliveryQueueConsumer extends BaseDelayedBatchedQueueHandler {

    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(SQS_CLIENT_TYPES.QueueService)  queueService: IQueueService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.NotificationHelper) private notificationHelper: NotificationHelper,
        @inject(TYPES.CampaignHelper) private campaignHelper: CampaignHelper,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
        @inject(TYPES.NotificationStatusUpdatePublisher) private notificationStatusUpdatePublisher: NotificationStatusUpdatePublisher
    ) {
        super(QueueConstants.getQueueName("SES-DELIVERY-REPORT"), constants.SQS_BATCH_SIZE, queueService)
        this.init()
    }

    private init() {
        this.logger.info(`Starting queue handler for ${QueueConstants.getQueueName("SES-DELIVERY-REPORT")}`)
    }

    async handle(messages: Message[]): Promise<boolean[]> {
        let result: boolean[] = []
        const messageReceivedAt = new Date()

        for (let message of messages) {
            try {
                const sesDeliveryMessage: SESDeliveryMessageMeta = JSON.parse(message.data)
                if (sesDeliveryMessage.MessageId && sesDeliveryMessage.Message) {
                    const sesMsg: SESMessage = JSON.parse(sesDeliveryMessage.Message)
                    if (sesMsg && sesMsg.mail && sesMsg.delivery) {

                        const externalId = constants.EMAIL_EXTERNALID_PREFIX + sesMsg.mail.messageId
                        const notification = await this.notificationDao.getNotificationsByExternalId(externalId)

                        if (!notification) {
                            this.logger.warn(`SESDeliveryQueueConsumer::handle Received event for unknown ` +
                                `external id ${externalId}`)
                        } else if (notification.receivedAt) {
                            this.logger.debug(`SESDeliveryQueueConsumer::handle Received event for notification ` +
                                `where receivedAt exists. ${notification.notificationId} ${JSON.stringify(sesMsg)}`)
                        } else {
                            this.logger.debug(`SESDeliveryQueueConsumer::handle Updating delivery time of ` +
                                `notification with external id ${externalId}`)

                            const deliveredAt: Date = new Date(sesMsg.delivery.timestamp)
                            const updateProps: any = { receivedAt: deliveredAt }

                            // Do not overwrite if current status is not sent.
                            // Possible race condition with opened email events.
                            if (notification.status === "SENT") {
                                updateProps.status = "DELIVERED"
                            }
                            await this.notificationDao.updatePropsByExternalId(externalId, updateProps)
                            await this.notificationStatusUpdatePublisher.publish([{ notification,
                                status: "DELIVERED", timestamp: deliveredAt.getTime() }])

                            const processingDelay = messageReceivedAt.getTime() - deliveredAt.getTime()
                            this.metricUtils.reportCallbackLatency("EMAIL_SES_DELIVER", processingDelay)
                            this.metricUtils.incrementNotificationStatusCounter(
                                notification.campaignId, notification.creativeId, "DELIVERED", "SES")
                        }
                    }
                }
                result.push(true)
            } catch (err) {
                this.rollbarService.sendError(new Error(err))
                this.logger.error(`SESDeliveryQueueConsumer::handle error while handling ${message}. ` +
                    `Error: ${err.toString()}`)
                result.push(false)
            }
        }
        return result

    }

}

export default SESDeliveryQueueConsumer
