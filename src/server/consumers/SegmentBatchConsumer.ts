import {BaseDelayedBatchedQueue<PERSON>and<PERSON>, IQueueService, Message, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {INotificationSegmentTaskReadWriteDao, IRIS_MODELS_TYPES} from "../../iris-models"

import {inject, injectable} from "inversify"
import * as _ from "lodash"

import TYPES from "../ioc/types"
import MozartService from "../service/MozartService"
import Constants from "../constants"
import {
    NotificationSegmentTaskJobTypeEnum,
    NotificationSegmentTaskStatusEnum,
    QueueConstants,
    SendCampaignNotificationsRequest,
    UserTags
} from "../../iris-common"
import CampaignHelper from "../helpers/CampaignHelper"

/**
 * Each batch has 100 notifications to process.
 * A batch size of 2 ensures each poll results in not more than 200 notifications
 * <PERSON> receives of the queue IRIS_CAMPAIGN is set to 1; We do not want to reattempt failed messages
 */
const SEGMENT_BATCH_QUEUE_BATCH_SIZE: number = 2

@injectable()
class SegmentBatchConsumer extends BaseDelayedBatchedQueueHandler {

    static MAX_USER_CONTEXT_SIZE: number = 100

    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(TYPES.MozartService) private mozartService: MozartService,
        @inject(TYPES.CampaignHelper) private campaignHelper: CampaignHelper,
        @inject(SQS_CLIENT_TYPES.QueueService) protected queueService: IQueueService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(IRIS_MODELS_TYPES.NotificationSegmentTaskReadWriteDao) private notificationSegmentTaskReadWriteDao: INotificationSegmentTaskReadWriteDao
    ) {
        super(QueueConstants.getQueueName(Constants.MOZART_SEGMENT_BATCH_QUEUE), SEGMENT_BATCH_QUEUE_BATCH_SIZE, queueService)
        this.init()
    }

    private init() {
        this.logger.info(`SegmentBatchConsumer::init Started consumer for ` +
            `${QueueConstants.getQueueName(Constants.MOZART_SEGMENT_BATCH_QUEUE)}`)
    }


    public async handle(messages: Message[]): Promise<boolean[]> {
        const successfulProcess: boolean[] = []
        for (const message of messages) {

            this.logger.debug(`SegmentBatchConsumer::handle message: ${JSON.stringify(message)}`)
            this.logger.debug(`SegmentBatchConsumer::handle data: ${message.data}`)
            this.logger.debug(`SegmentBatchConsumer::handle attributes: ${JSON.stringify(message.attributes)}`)

            const userIds = _.map(JSON.parse(message.data), data => data.userId)
            const taskId = message.attributes["taskId"].StringValue
            const isSegmentBatchFlow: boolean = "SEGMENT_STREAM_ID" in message.attributes
            try {
                this.logger.info(`SegmentBatchConsumer::handle taskId: ${taskId} has ${userIds.length} userIds`)
                /**
                 * If the message has more than 100 contexts, alert immediately and find the source;
                 * This can impact service performance
                 */
                if (userIds.length > SegmentBatchConsumer.MAX_USER_CONTEXT_SIZE) {
                    const errorMessage = `SegmentBatchConsumer::handle Message size too large; possible performance` +
                        `degradation! Message size: ${userIds.length} taskId: ${taskId}`
                    this.logger.error(errorMessage)
                    this.rollbarService.sendError(new Error(errorMessage))
                }

                if (!taskId) {
                    const errorMessage = `SegmentBatchConsumer::handle received message without taskId.` +
                        `Message: ${JSON.stringify(message)}`
                    this.logger.error(errorMessage)
                    this.rollbarService.sendError(new Error(errorMessage))
                    successfulProcess.push(true)
                    continue
                }

                let notificationSegmentTask

                notificationSegmentTask = await this.notificationSegmentTaskReadWriteDao.findOne({ taskId })

                if (!notificationSegmentTask) {
                    const errorMessage = `SegmentBatchConsumer::handle taskId ${taskId} Does not exist. ` +
                        `Message: ${JSON.stringify(message)}`
                    this.logger.error(errorMessage)
                    await this.rollbarService.sendError(new Error(errorMessage))
                    successfulProcess.push(true)
                    continue
                }

                if (notificationSegmentTask.status === NotificationSegmentTaskStatusEnum.KILLED) {
                    this.logger.info(`SegmentBatchConsumer::handle taskId: ${taskId} killed. Not processing batch`)
                    successfulProcess.push(true)
                    continue
                }

                if (!notificationSegmentTask.totalBatches) {

                    // segmentation based batching flow
                    if (isSegmentBatchFlow) {
                        throw new Error(`TotalBatches not present in notificationSegmentTask for segmentationFlow`)
                    }

                    // mozart based download flow
                    this.logger.info(`SegmentBatchConsumer::handle taskId: ${taskId} updating totalBatches`)
                    // Find right job
                    const queueProcessJob = _.find(notificationSegmentTask.jobs,
                        job => job.type === NotificationSegmentTaskJobTypeEnum.BATCH_PROCESS_QUEUE)
                    try {
                        const mozartQueueProcess = await this.mozartService.getJob(queueProcessJob.jobId)
                        notificationSegmentTask = await this.notificationSegmentTaskReadWriteDao.findOneAndUpdatePartial(
                            { taskId: notificationSegmentTask.taskId },
                            { $set: { totalBatches: mozartQueueProcess.totalSteps }})
                    } catch (error) {
                        let errorMessage: string = `SegmentBatchConsumer::handle taskId: ${taskId} error for fetching total batches via mozart job Error: ${error.toString()}`
                        this.logger.error(errorMessage)
                        await this.rollbarService.sendError(new Error(errorMessage))
                    }
                }

                if (userIds.length === 0) {
                    successfulProcess.push(true)
                    continue
                }

                // Form userContext array
                const userContexts: UserTags[] = _.map(userIds, userId => {
                    const userContext: any = { userId }
                    userContext.tags = []
                    return userContext
                })

                const sendCampaignNotificationsRequest: SendCampaignNotificationsRequest = {
                    userContexts,
                    taskId: notificationSegmentTask.taskId,
                    campaignId: notificationSegmentTask.campaignId,
                    creativeIds: notificationSegmentTask.creativeIds,
                    globalTags: notificationSegmentTask.globalTags,
                    globalAttachments: notificationSegmentTask.globalAttachments,
                    forceSend: notificationSegmentTask.forceSend,
                    expiry: notificationSegmentTask.expiry,
                    globalMetaTags: notificationSegmentTask.globalMetaTags,
                    creativeSpecificMetaTags: notificationSegmentTask.creativeSpecificMetaTags,
                    appId: notificationSegmentTask.appId || Constants.APP_ID_CUREFIT,
                    fallbackCreativeIds: notificationSegmentTask.fallbackCreativeIds
                }

                await this.campaignHelper.sendCampaignNotificationsWithFallback(sendCampaignNotificationsRequest, true)
                this.logger.info(`SegmentBatchConsumer::handle taskId: ${taskId} ` +
                    `Successfully sent notifications. Incrementing`)
                await this.notificationSegmentTaskReadWriteDao.increment(
                    { taskId }, "batchesProcessed", 1)
                this.logger.info(`SegmentBatchConsumer::handle taskId: ${taskId} Successfully incremented`)
                notificationSegmentTask = await this.notificationSegmentTaskReadWriteDao.findOne({ taskId })
                this.logger.info(`SegmentBatchConsumer::handle taskId: ${taskId} Current count: ` +
                    `${notificationSegmentTask.batchesProcessed}`)

                if (notificationSegmentTask.batchesProcessed >= notificationSegmentTask.totalBatches) {
                    notificationSegmentTask = await this.notificationSegmentTaskReadWriteDao.findOneAndUpdatePartial(
                        { taskId: notificationSegmentTask.taskId },
                        { $set: { status: NotificationSegmentTaskStatusEnum.COMPLETED }})

                }
                successfulProcess.push(true)
            } catch (error) {
                const errorMessage = `SegmentBatchConsumer::handle taskId: ${taskId} Something went wrong while ` +
                    `sending campaign messages. Error: ${error.toString()}`
                this.logger.error(errorMessage)
                this.rollbarService.sendError(new Error(errorMessage))
                successfulProcess.push(false)
            }
        }

        return successfulProcess
    }

}

export default SegmentBatchConsumer
