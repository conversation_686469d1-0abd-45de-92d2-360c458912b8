import {BaseDelayedBatchedQueueHandler, IQueueService, Message, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {DEVICE_MODELS_TYPES, IDeviceReadWriteDao} from "@curefit/device-models"

import {inject, injectable} from "inversify"

import {NotificationSegmentTaskStatusEnum, QueueConstants} from "../../iris-common"
import constants from "../constants"
import {IFindQuery} from "@curefit/mongo-utils"

/*
   TODO: Move this to user service once device has been migrated to it
   Iris should not be writing to device dao
 */

@injectable()
class FirebaseAppRemoveEventsConsumer extends BaseDelayedBatchedQueueHandler {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(SQS_CLIENT_TYPES.QueueService)  queueService: IQueueService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(DEVICE_MODELS_TYPES.DeviceReadwriteDao) private deviceDao: IDeviceReadWriteDao,
    ) {
        /* TODO:
         * This queue subscribes to a topic which receives events from GCP (Big Query).
         * Currently the topic receives messages for both production and stage.
         * We need to figure out a way to bifurcate them into two different topics (stage and prod).
         * Integrity is ensured since we use externalId. Stage external ids would never exist in production
         */
        super(QueueConstants.getQueueName("IRIS_FIREBASE_APP_REMOVE_EVENTS"), constants.SQS_BATCH_SIZE, queueService)
        this.init()
    }

    private init() {
        this.logger.info(`FirebaseAppRemoveEventsConsumer::init Starting queue handler for ` +
            `${QueueConstants.getQueueName("IRIS_FIREBASE_APP_REMOVE_EVENTS")}`)
    }

    async handle(messages: Message[]): Promise<boolean[]> {

        const ret = []

        for (const message of messages) {

            try {
                const data = JSON.parse(message.data)

                for (const firebaseEvent of data) {
                    const {
                        event_timestamp: eventTimestamp,
                        advertising_id: advertiserId
                    } = firebaseEvent

                    this.logger.info(`FirebaseAppRemoveEventsConsumer::handle received event with advertiserId: ` +
                        `${advertiserId} as uninstalled. Timestamp: ${eventTimestamp}`)
                    // Find devices with this advertiser id with createdDate < event time
                    const condition = {
                        "activeDevice.advertiserId": advertiserId,
                        createdDate: { $lt: (new Date(Math.floor(eventTimestamp / 1000))).toISOString() }}
                    const devices = await this.deviceDao.find({ condition })

                    if (devices.length === 0) {
                        this.logger.info(`FirebaseAppRemoveEventsConsumer::handle No device found for ` +
                            `advertiserId: ${advertiserId} `)
                    }

                    for (const device of devices) {
                        this.logger.info(`FirebaseAppRemoveEventsConsumer::handle marking deviceId ` +
                            `${device.deviceId} as uninstalled`)
                        await this.deviceDao.findOneAndUpdatePartial(
                            { deviceId: device.deviceId }, { $set: { "activeDevice.uninstalled": true }})
                    }
                }

                ret.push(true)
            } catch (err) {
                const errorMessage = `FirebaseAppRemoveEventsConsumer::handle Error while processing event: ${err.toString()}`
                this.logger.error(errorMessage, err)
                this.rollbarService.sendError(new Error(errorMessage))
                ret.push(false)
            }
        }

        return ret
    }
}

export default FirebaseAppRemoveEventsConsumer