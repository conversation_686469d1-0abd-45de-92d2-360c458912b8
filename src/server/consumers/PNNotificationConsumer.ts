import {inject, injectable} from "inversify"

import {BASE_TYPES, Configuration, ILogger} from "@curefit/base"
import {CommunicationCampaign, Country, IRIS_QUEUE_NAMES, NotificationWrapper, PNCreative, } from "../../iris-common"
import Constants from "../constants"
import TYPES from "../ioc/types"
import {IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {IPushNotificationHelper} from "../helpers/PushNotificationHelper"
import {BaseConsumer, DNDDetails} from "./BaseConsumer"
import {TimezoneUtils} from "../utils/timezoneUtils"


@injectable()
class PNNotificationConsumer extends BaseConsumer {
    constructor( @inject(BASE_TYPES.ILogger) logger: ILogger,
                 @inject(SQS_CLIENT_TYPES.QueueService) queueService: IQueueService,
                 @inject(BASE_TYPES.Configuration) appConfig: Configuration,
                 @inject(TYPES.PushNotificationHelper) private pushNotificationHelper: IPushNotificationHelper,
    ) {
        super(logger, queueService, <IRIS_QUEUE_NAMES>Constants.PN_PR_JOB_QUEUE, Constants.PN_WORKER_BATCH_SIZE, Constants.PN, appConfig,
            Constants.PN_WORKER_DELAY , Constants.PN_PER_MIN_LIMIT)

        this.sendNotificationsWrapped = this.limiter.wrap((creative: PNCreative,
                                                           notificationWrappers: NotificationWrapper[],
                                                           isTransactional: boolean,
                                                           campaign: CommunicationCampaign,
                                                           appId: string,
                                                           dryRun?: boolean) =>
            this.pushNotificationHelper.sendNotifications(creative, notificationWrappers, isTransactional, appId, dryRun))
    }

    getCountry(notificationWrapper: NotificationWrapper, dndDetails: DNDDetails, appId: string): Country {
        return TimezoneUtils.getCountry(notificationWrapper.userContext.appId || appId)
    }
}

export default PNNotificationConsumer
