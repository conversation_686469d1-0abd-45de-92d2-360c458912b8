import {BASE_TYPES, ILogger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {BaseDelayedBatchedQueueHandler, IQueueService, Message, SQS_CLIENT_TYPES} from "@curefit/sqs-client"

import {inject, injectable} from "inversify"

import {QueueConstants, SendCampaignNotificationsRequest} from "../../iris-common"
import TYPES from "../ioc/types"
import CampaignHelper from "../helpers/CampaignHelper"

/**
 * We don't know how many notifications we'll receive per message.
 * We assume it to be a maximum of 100.
 * A batch size of 2 ensures each poll results in no more than 200 notifications in each batch
 *
 * Max receives of the queue IRIS_CAMPAIGN is set to 1; We do not want to reattempt failed messages
 */
const QUEUE_BATCH_SIZE: number = 2

@injectable()
class NotificationQueueConsumer extends BaseDelayedBatchedQueueHandler {

    static MAX_USER_CONTEXT_SIZE: number = 100

    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(SQS_CLIENT_TYPES.QueueService)  queueService: IQueueService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.CampaignHelper) private campaignHelper: CampaignHelper
    ) {
        super(QueueConstants.getQueueName("IRIS_CAMPAIGN"), QUEUE_BATCH_SIZE, queueService)
        this.init()
    }

    private init() {
        this.logger.info(`NotificationQueueConsumer::init Starting queue handler for ` +
            `${QueueConstants.getQueueName("IRIS_CAMPAIGN")}`)
    }

    async handle(messages: Message[]): Promise<boolean[]> {
        let result: boolean[] = []
        for (const message of messages) {

            try {
                const sendNotifications: SendCampaignNotificationsRequest = JSON.parse(message.data)
                if (sendNotifications.campaignId === "CULTSPORT_CEREBRUM_CULTSPORT_-_CLPVISITORS_796") {
                    this.logger.info(`NotificationQueueConsumer::handle Message received in NotificationQueueConsumer:${message.data}`)
                }

                /**
                 * If the message has more than 100 contexts, alert immediately and find the source;
                 * This can impact service performance
                 */
                if (sendNotifications.userContexts.length > NotificationQueueConsumer.MAX_USER_CONTEXT_SIZE) {
                    const errorMessage = `NotificationQueueConsumer::handle Message size too large; possible performance` +
                        `degradation! Message size: ${sendNotifications.userContexts.length} ` +
                        `campaign: ${sendNotifications.campaignId}`
                    this.logger.error(errorMessage)
                    this.rollbarService.sendError(new Error(errorMessage))
                }

                await this.campaignHelper.sendCampaignMessages(sendNotifications)
                result.push(true)
            } catch (err) {
                const errorMessage = `NotificationQueueConsumer::handle Error while processing batch; ` +
                    `error: ${err.toString()}`
                this.logger.error(errorMessage)
                this.rollbarService.sendError(new Error(errorMessage))
                result.push(false)
            }
        }
        return result
    }
}

export default NotificationQueueConsumer