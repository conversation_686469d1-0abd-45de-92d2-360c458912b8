import {BASE_TYPES, ILogger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {BaseDelayedBatchedQueueHandler, IQueueService, Message, SQS_CLIENT_TYPES} from "@curefit/sqs-client"

import {inject, injectable} from "inversify"

import TYPES from "../ioc/types"
import constants from "../constants"
import Constants from "../constants"
import {IBasicNotificationDao, IRIS_MODELS_TYPES} from "../../iris-models/"
import CampaignHelper from "../helpers/CampaignHelper"
import {QueueConstants} from "../../iris-common"
import {MetricUtils} from "../utils/MetricUtils"

@injectable()
class ScheduledNotificationConsumer extends BaseDelayedBatchedQueueHandler {

    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(SQS_CLIENT_TYPES.QueueService)  queueService: IQueueService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.CampaignHelper) private campaignHelper: CampaignHelper,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
    ) {
        super(QueueConstants.getQueueName(Constants.SCHEDULED_NOTIFICATIONS_QUEUE), constants.SQS_BATCH_SIZE, queueService)
        this.init()
    }

    private init() {
        this.logger.info(`ScheduledNotificationConsumer::init Starting queue handler for ` +
            `${QueueConstants.getQueueName(Constants.SCHEDULED_NOTIFICATIONS_QUEUE)}`)
    }

    async handle(messages: Message[]): Promise<boolean[]> {
        const processStatus: boolean[] = []
        const messageReceivedAt = new Date()

        for (const message of messages) {
            let successfulProcess = true
            try {
                this.logger.debug(`ScheduledNotificationConsumer::handle received message ${JSON.stringify(message)}`)
                const data = JSON.parse(message.data)
                const { creativeId, campaignId, notificationIds} = data

                this.logger.debug(`ScheduledNotificationConsumer::handle received message with creativeId: ` +
                    `${creativeId} campaignId: ${campaignId} notificationIds: ${JSON.stringify(notificationIds)}`)

                await this.campaignHelper.processScheduledNotifications({
                    campaignId, creativeId, notificationIds}, messageReceivedAt)
            } catch (err) {
                const errorMessage = `ScheduledNotificationConsumer::handle Error while processing ` +
                    `${JSON.stringify(message)}. Error: ${err.toString()}`
                this.logger.error(errorMessage)
                this.rollbarService.sendError(new Error(err))
                successfulProcess = false
            }
            processStatus.push(successfulProcess)
        }

        return processStatus
    }

}

export default ScheduledNotificationConsumer