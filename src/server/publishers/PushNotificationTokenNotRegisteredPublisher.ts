import {inject, injectable} from "inversify"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {AWSUtils} from "../utils/AWSUtils"
import Constants from "../constants"
import {snsTopic} from "../enums"
import {EventData, EVENTS_TYPES, EventsService} from "@curefit/events-util"

@injectable()
class PushNotificationTokenNotRegisteredPublisher {

    readonly topicName: string
    readonly eventType: string

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(EVENTS_TYPES.EventsService) private eventsService: EventsService,
    ) {
        this.eventType = Constants.IRIS + "_" + Constants.SNS_PUSH_NOTIFICATION_TOKEN_NOT_REGISTERED
        this.topicName = AWSUtils.getAwsSNSTopicName(snsTopic.PUSH_NOTIFICATION_TOKEN_NOT_REGISTERED, false)
        this.logger.info(`PushNotificationTokenNotRegisteredPublisher::init Starting publisher for topic ${this.topicName}`)
    }

    public async publish(appId: string, deviceId: string, userId: string,
                         pushNotificationToken: string, timestamp: Date): Promise<any> {
        const eventAttributes = new Map<string, any>()
        eventAttributes.set("appId", appId)

        const payload = { appId, deviceId, userId, pushNotificationToken, timestamp: timestamp.getTime() }
        const eventData: EventData<any> = new EventData<any>(this.eventType, null, Date.now(),
            payload, eventAttributes)
        try {
            this.eventsService.publishMessageAsync(this.topicName, eventData)
        } catch (err) {
            this.logger.error(`PushNotificationTokenNotRegisteredPublisher::publish Failed for event data ` +
                `${JSON.stringify(eventData)} with error ${err.toString()}`)
        }
    }
}

export default PushNotificationTokenNotRegisteredPublisher