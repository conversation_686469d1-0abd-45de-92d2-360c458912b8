import {BASE_TYPES, ILogger} from "@curefit/base"

import {inject, injectable} from "inversify"

import TYPES from "../ioc/types"
import {AWSUtils} from "../utils/AWSUtils"
import Constants from "../constants"
import {snsTopic} from "../enums"
import {Notification} from "../../iris-common"
import UserContextHelper from "../helpers/UserContextHelper"
import {EventData, EVENTS_TYPES, EventsService} from "@curefit/events-util"

@injectable()
class UserInteractionPublisher {

    readonly topicName: string
    readonly eventType: string

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(EVENTS_TYPES.EventsService) private eventsService: EventsService,
        @inject(TYPES.UserContextHelper) private userContextHelper: UserContextHelper
    ) {
        this.eventType = Constants.IRIS + "_" + Constants.SNS_EVENT_USER_INTERACTION
        this.topicName = AWSUtils.getAwsSNSTopicName(snsTopic.USER_INTERACTIONS, false)
        this.logger.info(`UserInteractionPublisher::init Starting publisher for topic ${this.topicName}`)
    }

    public async publishPhoneInteraction(notification: Notification, interactionTimestamp: Date,
                                         status: string,  meta?: any, customText?: any): Promise<any> {
        let phone
        if (notification.userIdType === "PHONE_NUMBER") {
            phone = notification.userIdType
        } else {
            const userContext = this.userContextHelper.decodeUserContext(notification.userTags)
            phone = userContext.tags[Constants.USER_PHONE_TAG_OLD]
                || userContext.tags[Constants.USER_PHONE_TAG] || userContext.phone
        }

        this.logger.debug(`UserInteractionPublisher::publish Sending latest user interaction for ` +
            `notificationId: ${notification.notificationId} userId: ${notification.userId} phone: ${phone}`)

        const eventAttributes = new Map<string, any>()
        eventAttributes.set("creativeId", notification.creativeId)
        eventAttributes.set("campaignId", notification.campaignId)
        eventAttributes.set("appTenant", notification.appId)

        const payload = {
            userId: notification.userId,
            notificationId: notification.notificationId,
            lastInteractionTs: interactionTimestamp,
            phone, status, customText,
            meta: meta ? meta : {}
        }

        const eventData: EventData<any> = new EventData<any>(this.eventType, null, Date.now(), payload, eventAttributes)
        try {
            await this.eventsService.publishMessage(this.topicName, eventData)
        } catch (err) {
            this.logger.error(`UserInteractionPublisher::publish Failed for event data ${JSON.stringify(eventData)} ` +
                `with error ${err.toString()}`)
        }
    }
}

export default UserInteractionPublisher