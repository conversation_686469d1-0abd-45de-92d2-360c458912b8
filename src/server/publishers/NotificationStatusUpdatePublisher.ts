import {BASE_TYPES, Configuration, ILogger} from "@curefit/base"

import {inject, injectable} from "inversify"
import * as _ from "lodash"

import {Notification, NotificationStatus} from "../../iris-common"
import {AWSUtils} from "../utils/AWSUtils"
import Constants from "../constants"
import {snsTopic} from "../enums"
import TYPES from "../ioc/types"
import UserContextHelper from "../helpers/UserContextHelper"
import {EventType, UserEvent} from "@curefit/rashi-client"
import {Kafka, Producer} from "kafkajs"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {CommonUtils} from "../utils/CommonUtils"
import {KafkaUtils} from "../utils/KafkaUtils"
import {EventData, EVENTS_TYPES, EventsService} from "@curefit/events-util"
import {IFirebaseAnalyticsService} from "../service/FirebaseAnalyticsService"
import {FirebaseNotificationEvent} from "../../iris-common"
import {Tenant} from "@curefit/user-common"

export interface StatusUpdate {
    status: NotificationStatus,
    notification: Notification,
    timestamp: number,
    //meta data to be passed along with status update to Attribute store
    metaData?: any
}

@injectable()
class NotificationStatusUpdatePublisher {

    readonly topicName: string
    readonly eventType: string
    readonly notificationEventsKafkaConf: any = undefined
    kafkaTopic: string
    producer: Producer
    kafkaDlq: string
    lastProducerNetworkRequestTime: Date = new Date()
    lastPublishRequestTime: Date = new Date()
    explicitlyCalledDisconnect: boolean = false
    lastPublishedMessage: any = {}

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(EVENTS_TYPES.EventsService) private eventsService: EventsService,
        @inject(BASE_TYPES.Configuration) private appConfig: Configuration,
        @inject(TYPES.UserContextHelper) private userContextHelper: UserContextHelper,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.KafkaUtils) private kafkaUtils: KafkaUtils,
        @inject(TYPES.FirebaseAnalyticsService) private firebaseAnalyticsService: IFirebaseAnalyticsService
    ) {
        // Load config
        this.notificationEventsKafkaConf = _.get(this.appConfig.getConfiguration(),
            "notificationEventsKafkaConf", undefined)

        this.eventType = Constants.IRIS + "_" + Constants.SNS_EVENT_NOTIFICATION_STATUS_UPDATE
        this.topicName = AWSUtils.getAwsSNSTopicName(snsTopic.NOTIFICATION_STATUS_UPDATE, true)
        this.logger.info(`NotificationStatusUpdatePublisher::init Starting publisher for topic ${this.topicName}`)

        this.kafkaDlq = _.get(this.notificationEventsKafkaConf, "dlq")

        this.kafkaTopic = _.get(this.notificationEventsKafkaConf, "topic")
        this.producer = this.createKafkaProducer()
        this.logger.info(`Kafka Producer ${JSON.stringify(this.producer)} initialized for notification events `)
        this.startKafkaProducer()
        this.startNetworkRequestIntervalCheck()
    }

    private createKafkaProducer() {
        const clientId = _.get(this.notificationEventsKafkaConf, "clientId")
        const brokers = _.get(this.notificationEventsKafkaConf, "brokers")
        const kafka = new Kafka({clientId, brokers, retry: {retries: 3}})
        return kafka.producer()
    }

    private startKafkaProducer = async () => {
        this.logger.info(`Kafka Producer awaiting connection for notification events`)

        //log events
        this.producer.on("producer.connect", () => {
            this.logger.info(`Kafka Producer connected for notification events`)
            this.lastPublishRequestTime = new Date()
            this.lastProducerNetworkRequestTime = new Date()
            this.lastPublishedMessage = {}
        })
        this.producer.on("producer.disconnect", () => {
            this.logger.info(`Kafka Producer disconnected - notification events`)
            if (!this.explicitlyCalledDisconnect) {
                this.logger.info(`Since Kafka Producer not disconnected explicitly trying to reconnect`)
                this.producer.connect()
            }
        })
        this.producer.on("producer.network.request", () => {
            this.lastProducerNetworkRequestTime = new Date()
            this.logger.debug(`NotificationStatusUpdatePublisher:: Kafka Producer sent producer.network.request`)
        })
        await this.producer.connect()
    }

    private startNetworkRequestIntervalCheck() {
        setInterval(async () => {
            const minFromLastNetworkRequest = this.lastProducerNetworkRequestTime
                ? Math.floor((new Date().getTime() - this.lastProducerNetworkRequestTime.getTime()) / 60000) : 0
            const secFromLastSendRequest = this.lastPublishRequestTime
                ? Math.floor((new Date().getTime() - this.lastPublishRequestTime.getTime()) / 1000) : 0
            if ((this.lastPublishRequestTime.getTime() - this.lastProducerNetworkRequestTime.getTime()) > 2000) {
                this.logger.error(`NotificationStatusUpdatePublisher::NetworkRequestIntervalCheck: No network request sent event after 2s of publish request` +
                `, Publish request sent at ${this.lastPublishRequestTime} and last network request sent at: ${this.lastProducerNetworkRequestTime}.`)
                if (this.producer && minFromLastNetworkRequest > 5) {
                    this.logger.error(`No network request for ${minFromLastNetworkRequest} min from kafka producer, last request sent ${secFromLastSendRequest} sec back. Last request:${JSON.stringify(this.lastPublishedMessage)}`)
                    await this.producer.connect()
                }
            }

        }, 30000)
    }

    private async restartProducer() {
        try {
            this.explicitlyCalledDisconnect = true
            await this.producer.disconnect()
            this.createKafkaProducer()
            this.explicitlyCalledDisconnect = false
        } catch (e) {
            this.logger.error(`NotificationStatusUpdatePublisher::restartProducer failed to disconnect and created a new publisher. Error: ${JSON.stringify(e)}`, e)
        }
        try {
            await this.startKafkaProducer()
        } catch (e) {
            this.logger.error(`NotificationStatusUpdatePublisher::restartProducer failed to start the producer. Error: ${JSON.stringify(e)}`, e)
        }
    }

    public async publish(statusUpdates: StatusUpdate[]): Promise<any> {

        const uniqueStatus = _.uniq(_.map(statusUpdates, "status"))
        this.logger.info(`NotificationStatusUpdatePublisher::publish uniqueStatus derived:${uniqueStatus}`
            + `for statusUpdates:${JSON.stringify(statusUpdates)}`)

        // Publish one message for each type of status update.
        // Do these for all status concurrently
        // Allows us to set status in attribute
        await Promise.all(uniqueStatus.map(async status => {
            const filteredUpdates = _.filter(statusUpdates, (update) => {
                return update.status === status
            })
            return this.publishMessageForStatus(filteredUpdates, status)
        }))

        this.publishFirebaseAnalyticsEvents(statusUpdates)
        return this.publishRashiEvents(statusUpdates)
    }

    public async publishMessageForStatus(statusUpdates: StatusUpdate[], status: NotificationStatus) {
        if (statusUpdates.length === 0) {
            this.logger.warn(`NotificationStatusUpdatePublisher::publishMessageForStatus updates has zero messages`)
            return
        }

        const eventAttributes = new Map<string, any>()
        eventAttributes.set("status", status)

        const payload = statusUpdates.map((statusUpdate) => {
            return {
                notificationId: statusUpdate.notification.notificationId,
                userId: statusUpdate.notification.userId,
                userIdType: statusUpdate.notification.userIdType,
                creativeId: statusUpdate.notification.creativeId,
                campaignId: statusUpdate.notification.campaignId,
                timestamp: statusUpdate.timestamp,
                status: statusUpdate.status,
                taskId: statusUpdate.notification.taskId,
                metaTags: statusUpdate.notification.userTags ? this.userContextHelper.decodeUserContext(statusUpdate.notification.userTags).metaTags : {},
                deviceType: !!statusUpdate.notification.deviceInfo?.osName ? statusUpdate.notification.deviceInfo.osName : undefined,
                metaData: statusUpdate.metaData ? statusUpdate.metaData : {}
            }
        })

        const eventData: EventData<any> = new EventData(this.eventType, null, Date.now(),
            payload, eventAttributes)

        this.logger.info(`NotificationStatusUpdatePublisher::publish eventData: ${JSON.stringify(eventData)}`)

        try {
            this.eventsService.publishMessageAsync(this.topicName, eventData)
            this.logger.info(`message published for ${this.topicName} and eventData = ${JSON.stringify(eventData)}`)
        } catch (err) {
            this.logger.error(`NotificationStatusUpdatePublisher::publishMessageForStatus Failed for event data ` +
                `${JSON.stringify(eventData)} with error ${err.toString()}`)
        }
    }

    /*
    Transforms userEvent according to requirements of segmentation
    We are choosing to do the transformation at iris end because magnitude of change at segmentation will be much large if we handle the transformation there
     */
    private transformRashiPayloadForDataLake(userEvent: UserEvent): object {

        const DATA_LAKE_PAYLOAD_BODY_PREFIX = "body_"

        const dataLakePayload: any = {
            "userid": userEvent.userId,
            "name": userEvent.eventName,
            "occuredat_pk_epoch": userEvent.eventTime,
            "type": EventType.USER_ACTIVITY_EVENT
        }

        for (const key of Object.keys(userEvent.body)) {
            dataLakePayload[DATA_LAKE_PAYLOAD_BODY_PREFIX + key.toLowerCase()] = userEvent.body[key]
        }

        return dataLakePayload

    }

    public async publishRashiEvents(statusUpdates: StatusUpdate[]) {
        this.lastPublishRequestTime = new Date()
        this.lastPublishedMessage = statusUpdates
        let userEvents: { [appId: string]: UserEvent[] } = {}
        for (const statusUpdate of statusUpdates) {

            // Do not publish events which do not correspond to a curefit user id and have no userTags
            if (statusUpdate.notification.userIdType !== Constants.USER_ID_TYPES.CUREFIT_USER_ID
                || !statusUpdate.notification.userTags) {
                continue
            }

            const userTags = this.userContextHelper.decodeUserContext(statusUpdate.notification.userTags)
            if (!userTags.appId) {
                userTags.appId = Constants.APP_ID_CUREFIT
            }

            let eventName
            switch (statusUpdate.status) {
                case "SENT": {
                    eventName = "notification_sent"
                    break
                }
                case "DELIVERED": {
                    eventName = "notification_received"
                    break
                }
                case "READ": {
                    eventName = "notification_opened"
                    break
                }
                default: {
                    // We do not publish other events to rashi
                    return
                }
            }

            const userEvent: UserEvent = {
                type: EventType.USER_ACTIVITY_EVENT,
                userId: +statusUpdate.notification.userId,
                eventTime: statusUpdate.timestamp,
                eventName,
                body: {
                    irisCampaignId: statusUpdate.notification.campaignId,
                    irisCreativeId: statusUpdate.notification.creativeId,
                    channel: statusUpdate.notification.creativeId.split("_")[0],
                    vertical: statusUpdate.notification.campaignId.split("_")[0],
                    ...userTags.metaTags,
                    ...statusUpdate.metaData

                },
            }

            if (statusUpdate.notification.deviceInfo && statusUpdate.notification.deviceInfo.deviceId) {
                userEvent.body["deviceid"] = statusUpdate.notification.deviceInfo.deviceId
            }

            //These conditions are required in validateRequest() in Rashi to publish
            if (!!userEvent.userId && !_.isEmpty(userEvent.body) && !!userEvent.eventTime) {
                if (!userEvents[userTags.appId]) {
                    userEvents[userTags.appId] = []
                }
                userEvents[userTags.appId].push(userEvent)
            }
        }

        if (_.keys(userEvents).length === 0) {
            this.logger.debug(`NotificationStatusUpdatePublisher::publishRashiEvents No user events to publish. ` +
                `Original updates: ${JSON.stringify(statusUpdates)}`)
        }


        try {
            this.logger.debug(`NotificationStatusUpdatePublisher::publishRashiEvents Publishing event ` +
                `${JSON.stringify(userEvents)} Original updates: ${JSON.stringify(statusUpdates)}`)
            for (let appId of _.keys(userEvents)) {
                try {
                    for (let userEvent of userEvents[appId]) {

                        const kafkaMessage = {
                                "value": JSON.stringify({
                                    "eventName": "user_event",
                                    "payload": JSON.stringify({
                                        ...this.transformRashiPayloadForDataLake(userEvent),
                                        apptenant: CommonUtils.convertAppIdtoAppTenant(appId),
                                        occurredat_pk_tz: new Date(userEvent.eventTime)
                                    }),
                                    "ts_ms": new Date().getTime(),
                                    "op": "c"
                                })
                            }


                        try {

                            await this.producer.send({
                                topic: this.kafkaTopic,
                                messages: [kafkaMessage]
                            })

                            this.logger.debug(`NotificationStatusUpdatePublisher Published kafka message ${JSON.stringify(kafkaMessage)} to topic ${this.kafkaTopic}`)

                        } catch (error) {
                            this.logger.error(`NotificationStatusUpdatePublisher Error in publishing event to producer: ${error.toString()} ` +
                                `Event: ${JSON.stringify(kafkaMessage)}`)
                            await this.kafkaUtils.handleMessageFailure(kafkaMessage, this.kafkaDlq)
                        }


                    }



                } catch (e) {
                    let erromsg = `NotificationStatusUpdatePublisher::publishRashiEvents error in publishing events 
                    for appId ${appId} because of error ${e}`
                    this.logger.error(erromsg)
                    this.rollbarService.sendError(new Error(erromsg))
                }
            }

        } catch (err) {
            this.logger.error(`NotificationStatusUpdatePublisher::publishRashiEvents Failed for event data ` +
                `${JSON.stringify(userEvents)} with error ${err.toString()}`)
        }
    }

    public async publishFirebaseAnalyticsEvents(statusUpdates: StatusUpdate[]) {
        try {
            this.logger.debug(`NotificationStatusUpdatePublisher::publishFirebaseAnalyticsEvents Publishing ${statusUpdates.length} events to Firebase Analytics`)

            for (const statusUpdate of statusUpdates) {
                // Only publish events for valid user IDs
                if (statusUpdate.notification.userIdType !== Constants.USER_ID_TYPES.CUREFIT_USER_ID) {
                    continue
                }

                // Map notification status to Firebase event type
                let eventType: 'notification_sent' | 'notification_opened' | 'notification_dismissed' | null = null
                switch (statusUpdate.status) {
                    case "SENT":
                        eventType = "notification_sent"
                        break
                    case "DELIVERED":
                        eventType = "notification_sent" // Firebase doesn't distinguish between sent and delivered
                        break
                    case "READ":
                        eventType = "notification_opened"
                        break
                    case "FAILED":
                        eventType = "notification_dismissed" // Treat failed as dismissed
                        break
                    default:
                        // Skip unknown statuses
                        continue
                }

                // Get tenant from app ID
                const tenant = this.getTenantFromAppId(statusUpdate.notification.appId)

                const firebaseEvent: FirebaseNotificationEvent = {
                    notification_id: statusUpdate.notification.notificationId,
                    campaign_id: statusUpdate.notification.campaignId,
                    creative_id: statusUpdate.notification.creativeId,
                    event_type: eventType,
                    timestamp: statusUpdate.timestamp,
                    user_id: statusUpdate.notification.userId,
                    app_id: statusUpdate.notification.appId
                }

                // Send Firebase Analytics event asynchronously
                this.firebaseAnalyticsService.sendNotificationEvent(
                    statusUpdate.notification.userId,
                    firebaseEvent,
                    tenant
                ).catch(error => {
                    this.logger.error(`NotificationStatusUpdatePublisher::publishFirebaseAnalyticsEvents Failed to send Firebase event: ${error.toString()}`)
                })
            }
        } catch (error) {
            this.logger.error(`NotificationStatusUpdatePublisher::publishFirebaseAnalyticsEvents Error: ${error.toString()}`)
        }
    }

    private getTenantFromAppId(appId: string): Tenant {
        switch (appId) {
            case Constants.APP_ID_LIVEFIT:
                return Tenant.LIVEFIT_APP
            case Constants.APP_ID_CULTWATCH:
                return Tenant.CULTWATCH
            case Constants.APP_ID_CUREFIT:
            default:
                return Tenant.CUREFIT_APP
        }
    }

}

export default NotificationStatusUpdatePublisher
