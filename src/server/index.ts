// datadog
process.env.DD_TRACE_DEBUG = "false"
process.env.DD_TRACE_ENABLED = "true"
process.env.DD_TRACE_STARTUP_LOGS = "true"

require("./tracer")

import "isomorphic-fetch"

import kernel from "./ioc/kernel"
import TYPES from "./ioc/types"


import {LogUtil} from "@curefit/util-common"
import {CFBaseServer, SERVER_TYPES, ServerConfig} from "@curefit/server"

import {CommonUtils} from "./utils/CommonUtils"
import NotificationQueueConsumer from "./consumers/NotificationQueueConsumer"
import ScheduledNotificationConsumer from "./consumers/ScheduledNotificationConsumer"
import NotificationStatusUpdateConsumer from "./consumers/NotificationStatusUpdateConsumer"
import SegmentBatchConsumer from "./consumers/SegmentBatchConsumer"
import FirebaseAppRemoveEventsConsumer from "./consumers/FirebaseAppRemoveEventsConsumer"
import NotificationProcessorQueueConsumer from "./consumers/NotificationProcessorQueueConsumer"
import EmailOpenEventsConsumer from "./consumers/EmailOpenEventsConsumer"
import MozartJobUpdateConsumer from "./consumers/MozartJobUpdateConsumer"
import SESDeliveryQueueConsumer from "./consumers/SESDeliveryConsumer"
import EmailNotificationConsumer from "./consumers/EMAILNotificationConsumer"
import PNNotificationConsumer from "./consumers/PNNotificationConsumer"
import CTCNotificationConsumer from "./consumers/CTCNotificationConsumer"
import IANNotificationConsumer from "./consumers/IANNotificationConsumer"
import OBDNotificationConsumer from "./consumers/OBDNotificationConsumer"
import SMSNotificationConsumer from "./consumers/SMSNotificationConsumer"
import WhatsappNotificationConsumer from "./consumers/WhatsappNotificationConsumer"
import NotificationEventProcessingService from "./service/NotificationEventProcessingService"
import CampaignExecutionNotificationConsumer from "./consumers/CampaignExecutionNotificationConsumer"
import EmailBounceQueueConsumer from "./consumers/EmailBounceQueueConsumer"
import SMSDeliveryConsumer from "./consumers/SMSDeliveryConsumer"


const serverConfigOptions = kernel.get<ServerConfig>(SERVER_TYPES.ServerConfig)
const server = new CFBaseServer(kernel, serverConfigOptions)
server.start()

switch (process.env.DEPLOYMENT_TYPE) {
    case "server": {
        LogUtil.info("Iris starting")

        if (!CommonUtils.isEnvironmentLocal()) {

            /**
             * These consumers are enabled on every env (stage, alpha, production)
             */
            const notificationQueueConsumer =
                kernel.get<NotificationQueueConsumer>(TYPES.NotificationQueueConsumer) // Send async flow.
            server.registerShutDownCallback(
                notificationQueueConsumer.shutDownConsumer.bind(notificationQueueConsumer))

            const scheduledNotificationConsumer =
                kernel.get<ScheduledNotificationConsumer>(TYPES.ScheduledNotificationConsumer)
            server.registerShutDownCallback(
                scheduledNotificationConsumer.shutDownConsumer.bind(scheduledNotificationConsumer))

            const notificationStatusUpdateConsumer =
                kernel.get<NotificationStatusUpdateConsumer>(TYPES.NotificationStatusUpdateConsumer)
            server.registerShutDownCallback(
                notificationStatusUpdateConsumer.shutDownConsumer.bind(notificationStatusUpdateConsumer))

            const notificationEventProcessingService =
                kernel.get<NotificationEventProcessingService>(TYPES.NotificationEventProcessingService)
            server.registerShutDownCallback(
                notificationEventProcessingService.shutDownConsumer.bind(notificationEventProcessingService))

            const emailBounceQueueConsumer =
                kernel.get<EmailBounceQueueConsumer>(TYPES.EmailBounceQueueConsumer)
            server.registerShutDownCallback(
                emailBounceQueueConsumer.shutDownConsumer.bind(emailBounceQueueConsumer))

            const smsDeliveryConsumer =
                kernel.get<SMSDeliveryConsumer>(TYPES.SMSDeliveryConsumer)
            server.registerShutDownCallback(
                smsDeliveryConsumer.shutDownConsumer.bind(smsDeliveryConsumer))
            // These consumers are disabled on alpha
            if (!CommonUtils.isEnvironmentAlpha()) {
                const notificationProcessorQueueConsumer =
                    kernel.get<NotificationProcessorQueueConsumer>(TYPES.NotificationProcessorQueueConsumer) // Old send async flow. Deprecate this
                server.registerShutDownCallback(
                    notificationProcessorQueueConsumer.shutDownConsumer.bind(notificationProcessorQueueConsumer))

                const segmentBatchConsumer =
                    kernel.get<SegmentBatchConsumer>(TYPES.SegmentBatchConsumer)
                server.registerShutDownCallback(
                    segmentBatchConsumer.shutDownConsumer.bind(segmentBatchConsumer))

                const mozartJobUpdateConsumer =
                    kernel.get<MozartJobUpdateConsumer>(TYPES.MozartJobUpdateConsumer)
                server.registerShutDownCallback(
                    mozartJobUpdateConsumer.shutDownConsumer.bind(mozartJobUpdateConsumer))

                const _SESDeliveryQueueConsumer =
                    kernel.get<SESDeliveryQueueConsumer>(TYPES.SESDeliveryQueueConsumer)
                server.registerShutDownCallback(
                    _SESDeliveryQueueConsumer.shutDownConsumer.bind(_SESDeliveryQueueConsumer))

                const emailOpenEventsConsumer =
                    kernel.get<EmailOpenEventsConsumer>(TYPES.EmailOpenEventsConsumer)
                server.registerShutDownCallback(
                    emailOpenEventsConsumer.shutDownConsumer.bind(emailOpenEventsConsumer))

                const firebaseAppRemoveEventsConsumer =
                    kernel.get<FirebaseAppRemoveEventsConsumer>(TYPES.FirebaseAppRemoveEventsConsumer)
                server.registerShutDownCallback(
                    firebaseAppRemoveEventsConsumer.shutDownConsumer.bind(firebaseAppRemoveEventsConsumer))

                const campaignExecutionNotificationConsumer =
                    kernel.get<CampaignExecutionNotificationConsumer>(TYPES.CampaignExecutionNotificationConsumer)
                server.registerShutDownCallback(
                    campaignExecutionNotificationConsumer.shutDownConsumer.bind(campaignExecutionNotificationConsumer))
        }

        }

        // App events consumer enabled only on production
        if (CommonUtils.isEnvironmentProduction()) {
            kernel.get<any>(TYPES.AppEventsConsumer)
            kernel.get<any>(TYPES.PNDeliveryEventConsumer)
        }

        break
    }
    case "sms": {
        LogUtil.info("IRIS SMS worker initializing")
        const _SMSNotificationConsumer =
            kernel.get<SMSNotificationConsumer>(TYPES.SMSNotificationConsumer)
        server.registerShutDownCallback(
            _SMSNotificationConsumer.shutDownConsumer.bind(_SMSNotificationConsumer))
        break
    }
    case "email": {
        LogUtil.info("IRIS EMAIL worker initializing")
        const _EmailNotificationConsumer =
            kernel.get<EmailNotificationConsumer>(TYPES.EmailNotificationConsumer)
        server.registerShutDownCallback(
            _EmailNotificationConsumer.shutDownConsumer.bind(_EmailNotificationConsumer))
        break
    }
    case "pn": {
        LogUtil.info("IRIS PN worker initializing")
        const _PNNotificationConsumer =
            kernel.get<PNNotificationConsumer>(TYPES.PNNotificationConsumer)
        server.registerShutDownCallback(
            _PNNotificationConsumer.shutDownConsumer.bind(_PNNotificationConsumer))
        break
    }
    case "ctc": {
        LogUtil.info("IRIS CTC worker initializing")
        const _CTCNotificationConsumer =
            kernel.get<CTCNotificationConsumer>(TYPES.CTCNotificationConsumer)
        server.registerShutDownCallback(
            _CTCNotificationConsumer.shutDownConsumer.bind(_CTCNotificationConsumer))
        break
    }
    case "ian": {
        LogUtil.info("IRIS IAN worker initializing")
        const _IANNotificationConsumer =
            kernel.get<IANNotificationConsumer>(TYPES.IANNotificationConsumer)
        server.registerShutDownCallback(
            _IANNotificationConsumer.shutDownConsumer.bind(_IANNotificationConsumer))
        break
    }
    case "obd": {
        LogUtil.info("IRIS OBD worker initializing")
        const _OBDNotificationConsumer =
            kernel.get<OBDNotificationConsumer>(TYPES.OBDNotificationConsumer)
        server.registerShutDownCallback(
            _OBDNotificationConsumer.shutDownConsumer.bind(_OBDNotificationConsumer))
        break
    }
    case "whatsapp": {
        LogUtil.info("IRIS WHATSAPP worker initializing")
        const _WhatsappNotificationConsumer =
            kernel.get<WhatsappNotificationConsumer>(TYPES.WhatsappNotificationConsumer)
        server.registerShutDownCallback(
            _WhatsappNotificationConsumer.shutDownConsumer.bind(_WhatsappNotificationConsumer))
        break
    }
}

