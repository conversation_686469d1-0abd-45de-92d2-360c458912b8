import {inject, injectable} from "inversify"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {CommunicationCampaign} from "../../iris-common"
import TYPES from "../ioc/types"

export interface CommunicationCampaignEventPublisher {
    publishCommunicationCampaignEvent(campaign: CommunicationCampaign): Promise<boolean>
}

@injectable()
export class CommunicationCampaignEventPublisherImpl implements CommunicationCampaignEventPublisher {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,
        @inject(TYPES.FalconEntityRefreshQueueName) private queueName: string
    ) {}

    public async publishCommunicationCampaignEvent(
        campaign: CommunicationCampaign
    ): Promise<boolean> {

        const messageAttributes = new Map<string, string>()
        messageAttributes.set("vertical", "CRM")
        messageAttributes.set("subVertical", "CAMPAIGN_MANAGER")
        messageAttributes.set("entityType", "CAMPAIGN")

        try {
            return await this.queueService.sendMessage(this.queueName, campaign, messageAttributes)
        } catch (error) {
            this.logger.error(`Failed to publish communication campaign event: ${error.message}`, error)
            return false
        }
    }
}
