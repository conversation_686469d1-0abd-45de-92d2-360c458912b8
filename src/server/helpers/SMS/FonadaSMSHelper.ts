import { BASE_TYPES, FetchUtilV2, ILogger } from "@curefit/base"
import {DataErrorV2} from "@curefit/error-client"

import * as mustache from "mustache"
import {inject, injectable} from "inversify"

import {ISMSProviderHelper} from "./ISMSProviderHelper"
import {NotificationStatus, NotificationWrapper, SMSCreative} from "../../../iris-common"
import {UserSMSInfo} from "./SMSUtils"
import Constants from "../../constants"
import {MetricUtils, ProfilerType} from "../../utils/MetricUtils"
import {BillingUtils} from "../../utils/BillingUtils"
import TYPES from "../../ioc/types"
import { Agent } from "http"
import _ = require("lodash")
import { CommonUtils } from "../../utils/CommonUtils"

const https = require("https")
const fetch = require("node-fetch")

@injectable()
class FonadaSMSHelper implements ISMSProviderHelper {

    private fetchAgent: Agent

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(BASE_TYPES.FetchUtilV2) private fetchUtil: FetchUtilV2,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
    ) {
        this.fetchAgent = new https.Agent({
            keepAliveMsecs: 50,
            keepAlive: true,
            maxSockets: 50,
            maxFreeSockets: 2,
            timeout: 300000
        })
    }


    // different account configs basis send type: Transactional/Promotional messages and OTP based uses
    private static getFonadaSMSTransactionalOrPromotionalCommsAccount(phone: string, email: string): {"id": string, "password": string} {
        let account = null
        if (CommonUtils.isEnvironmentProductionOrAlpha() || Constants.isWhitelisted(phone, email)) {
            account = Constants.FONADA_SMS_CONFIG.ACCOUNT_CREDS.TRANSACTIONAL_PROMOTIONAL_COMMS
        }
        return account
    }

    private static getFonadaSmsOTPRouteAccount(phone: string, email: string) {
        let account = null
        if (CommonUtils.isEnvironmentProductionOrAlpha() || Constants.isWhitelisted(phone, email)) {
            account = Constants.FONADA_SMS_CONFIG.ACCOUNT_CREDS.OTP_ROUTE
        }
        return account
    }

    private getAccountDetails(creativeId: string, isTransactional: boolean, phone: string, email: string ): {"id": string, "password": string} {
        const isOTP = Constants.isOtpRouteCreative(creativeId)
        if (isOTP && isTransactional) return FonadaSMSHelper.getFonadaSmsOTPRouteAccount(phone, email)
        else return FonadaSMSHelper.getFonadaSMSTransactionalOrPromotionalCommsAccount(phone, email)
    }

    private getSmsUrl(creative: SMSCreative, userSMSInfo: UserSMSInfo, body: string, isTransactional: boolean, sender: string) {
        const {phone, email, country} = userSMSInfo
        
        if (country != "INDIA") {
            throw new DataErrorV2("Internation messages not supported through Fonada", {meta: "INTERNATION_MESSAGES_NOT_SUPPORTED"})
        }
        let urlForUser: string = ""
        const accountDetails = this.getAccountDetails(creative.creativeId , isTransactional, phone , email)
        if (!accountDetails) {
            throw new DataErrorV2("Unauthorized", {meta: "UNAUTHORIZED"})
        }
        const templateUrl = Constants.FONADA_SMS_CONFIG.API_URL
        urlForUser = mustache.render(templateUrl, {
            "from": sender,
            "to": phone.replace(/\+/g, ""),
            "text": body,
            "username": accountDetails.id,
            "password": accountDetails.password,
            "templateId": creative.smsParams.templateId
        })
        return urlForUser
    }

    public async sendSMS(creative: SMSCreative, notificationWrapper: NotificationWrapper, userSMSInfo: UserSMSInfo,
                         body: string, isTransactional: boolean, dryRun?: boolean): Promise<boolean> {

        const { name, country } = userSMSInfo
        const { smsParams } = creative

        notificationWrapper.userContext.tags.name = name

        const sender: string = encodeURIComponent(smsParams.sender)

        // get fonada url
        const urlForUser: string = this.getSmsUrl(creative, userSMSInfo, body, isTransactional, sender)


        this.logger.info(`Fonada::sendSMS notification ${notificationWrapper.notification.notificationId} and userId ${notificationWrapper.userContext.userId} request url: ${urlForUser}`)
        const apiLatencyTimerContext =
            this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "FONADA_SMS")
        const response = await fetch(urlForUser, this.fetchUtil.get({ agent: this.fetchAgent }))
        this.metricUtils.endProfilingTimer(apiLatencyTimerContext)

        const fonadaResponse = await response.json()

        if (response.status >= 500) {
            let errorMsg: string = `Fonada Error: request failed with status ${response.status} , response: ${JSON.stringify(fonadaResponse)} `
            throw new DataErrorV2(errorMsg, {})
        }


        this.logger.info(`FonadaSMSHelper::sendSMS response: ${fonadaResponse}`)

        const sent: boolean = fonadaResponse.state === "SUBMIT_ACCEPTED"

        if (sent) {
            notificationWrapper.notification.externalId = `${Constants.SMS_FONADA_EXTERNALID_PREFIX}${fonadaResponse.transactionId}`
            notificationWrapper.notification.sentAt = new Date()
            notificationWrapper.notification.status = "SENT"
            notificationWrapper.notification.billableUnits = BillingUtils.getSMSUnitsConsumed(body, country, "FONADA")
            this.logger.debug(`FonadaSMSHelper::sendSMS SMS sent via Fonada. ` +
                `notificationId: ${notificationWrapper.notification.notificationId}`)
        } else {
            let providerFailReason = fonadaResponse.description
            const errorMessage = `FonadaSMSHelper::sendSMS Failed to send ` +
                `notificationId: ${notificationWrapper.notification.notificationId} ` +
                `Fail reason: ${providerFailReason}.`
            this.logger.error(errorMessage)
            throw new DataErrorV2("Fonada failure", { meta: providerFailReason})
        }
        return sent
    }

    public notificationStatusMapper(callbackStatus: string): NotificationStatus {
        switch (callbackStatus) {
            case "SUBMISSION_ACCEPTED":
                return "SENT"
            case "DELIVERY_SUCCESS":
                return "DELIVERED"
            default:
                return "FAILED"
        }
    }

    public validateFonadaCallbackRequest(txid: any, deliverystatus: any, deliverydt: any, errCode: any): any {
        
        if (!txid) return {status: false, message: "Transaction ID is required."}
        if (!deliverystatus) return {status: false, message: "Delivery status is required."}
        const callbackStatus = this.notificationStatusMapper(deliverystatus)
        if (callbackStatus == "DELIVERED" && !deliverydt) return {status: false, message: "Delivery time is required with successful deliveries."}
        if (callbackStatus === "FAILED" && !errCode) return {status: false, message: "Error Code is required with failed deliveries"}
        return {status: true, message: "Validation Successful"}
    }
}

export default FonadaSMSHelper