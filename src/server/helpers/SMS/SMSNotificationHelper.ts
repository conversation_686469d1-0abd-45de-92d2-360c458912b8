import {BASE_TYPES, ILogger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

import {inject, injectable} from "inversify"
import * as _ from "lodash"

import TYPES from "../../ioc/types"
import {MetricUtils} from "../../utils/MetricUtils"
import SolutionsInfiniSMSHelper from "./SolutionsInfiniSMSHelper"
import NotificationLogService from "../../service/NotificationLogService"
import {
    Notification, NotificationStatus,
    NotificationWrapper,
    ServiceProvider,
    SMSCreative,
    UserNotificationStatus
} from "../../../iris-common"
import SMSUtils from "./SMSUtils"
import {ISMSProviderHelper} from "./ISMSProviderHelper"
import GupshupSMSHelper from "./GupshupSMSHelper"
import NotificationReportHelper from "../NotificationReportHelper"
import {IBasicNotificationDao, IRIS_MODELS_TYPES} from "../../../iris-models"
import {NotFoundError} from "@curefit/error-client"
import NotificationStatusUpdatePublisher from "../../publishers/NotificationStatusUpdatePublisher"
import UserInteractionPublisher from "../../publishers/UserInteractionPublisher"
import Constants from "../../constants"
import {CommonUtils} from "../../utils/CommonUtils"
import {SMSServiceProvider} from "../../../iris-common/src/creatives"
import FeatureUtils from "../../utils/FeatureUtils"
import TagUtils from "../../utils/TagUtils"
import {AWSUtils} from "../../utils/AWSUtils"
import {SMSCallbackParams} from "../../consumers/SMSDeliveryConsumer"
import UserContextHelper from "../UserContextHelper"
import {IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import FonadaSMSHelper from "./FonadaSMSHelper"

interface SmsProviderConfig {
    enableCreativeProviderMap: boolean
    creativeProviderMap: {[creative: string]: SMSServiceProvider}
    enableTrafficSplit: boolean
    solutionsInfiniTrafficPercentage: number,
    gupshupTrafficPercentage: number,
    fonadaTrafficPercentage: number
}

@injectable()
class SMSNotificationHelper {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.NotificationLogService) private notificationLogService: NotificationLogService,
        @inject(TYPES.NotificationReportHelper) private notificationReportHelper: NotificationReportHelper,
        @inject(TYPES.SolutionsInfiniSMSHelper) private solutionsInfiniSMSHelper: SolutionsInfiniSMSHelper,
        @inject(TYPES.FonadaSMSHelper) private fonadaSMSHelper: FonadaSMSHelper,
        @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
        @inject(TYPES.GupshupSMSHelper) private gupshupSMSHelper: GupshupSMSHelper,
        @inject(TYPES.UserInteractionPublisher) private userInteractionPublisher: UserInteractionPublisher,
        @inject(TYPES.NotificationStatusUpdatePublisher) private notificationStatusUpdatePublisher: NotificationStatusUpdatePublisher,
        @inject(TYPES.FeatureUtils) private featureUtils: FeatureUtils,
        @inject(TYPES.AWSUtils) private awsUtils: AWSUtils,
        @inject(TYPES.UserContextHelper) private userContextHelper: UserContextHelper,
        @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,

    ) { }

    public async sendNotifications(creative: SMSCreative, notifications: NotificationWrapper[],
                                   isTransactional: boolean, dryRun?: boolean): Promise<UserNotificationStatus[]> {
        const allTags = TagUtils.allTagsUsed([ creative.body ])
        const userNotificationStatuses: UserNotificationStatus[] = []

        for (const chunk of _.chunk(notifications, 10)) {
            userNotificationStatuses.push(...(await Promise.all(chunk.map(notificationWrapper => {
                    return this.sendNotification(creative, notificationWrapper, allTags, isTransactional, dryRun)
                        .then(() => {
                            return {
                                userId: notificationWrapper.userContext.userId,
                                creativeId: creative.creativeId,
                                notificationId: notificationWrapper.notification.notificationId,
                                sent: !!notificationWrapper.notification.sentAt,
                                failedReason: notificationWrapper.notification.failReason
                            }
                        })
                })
            )))
        }
        return userNotificationStatuses
    }

    private async sendNotification(creative: SMSCreative, notificationWrapper: NotificationWrapper, allTags: string[],
                                   isTransactional: boolean, dryRun?: boolean) {
                                    
        const userSMSInfo = await SMSUtils.fetchUserInformation(notificationWrapper)

        if (!userSMSInfo.phone) {
            notificationWrapper.notification.status = "FAILED"
            notificationWrapper.notification.failReason = "PHONE_NOT_FOUND"
            notificationWrapper.notification.billableUnits = 0
            return
        }

        let body: string
        try {
            body = SMSUtils.parseBody(notificationWrapper, creative, allTags)
        } catch (error) {
            this.logger.error(`SMSNotificationHelper::sendNotification SMS send failure. TEMPLATE_FAIL. ` +
                `notificationId: ${JSON.stringify(notificationWrapper.notification.notificationId)} and ` +
                `userContext: ${JSON.stringify(notificationWrapper.userContext)} due to ${JSON.stringify(error)}`)
            notificationWrapper.notification.status = "FAILED"
            notificationWrapper.notification.failReason = "TEMPLATE_FAIL"
            notificationWrapper.notification.billableUnits = 0
            return
        }

        try {
            this.userContextHelper.checkTagLengths(creative.creativeId, notificationWrapper.userContext.tags, new Set(allTags))
        } catch (error) {
            this.logger.error(`SMSNotificationHelper::sendNotification SMS send failure. TAG_LENGTH_EXCEEDED. ` +
                `notificationId: ${JSON.stringify(notificationWrapper.notification.notificationId)} and ` +
                `userContext: ${JSON.stringify(notificationWrapper.userContext)} due to ${JSON.stringify(error)}`)
            notificationWrapper.notification.status = "FAILED"
            notificationWrapper.notification.failReason = "TAG_LENGTH_EXCEEDED"
            notificationWrapper.notification.billableUnits = 0
            return
        }

        if (!Constants.avoidDuplicateCheckCreative(creative.creativeId) &&
            !Constants.isOtpRouteCreative(creative.creativeId) && await this.notificationLogService.isDuplicate({ body }, userSMSInfo.phone, dryRun)) {
            notificationWrapper.notification.status = "FAILED"
            notificationWrapper.notification.failReason = "DUPLICATE"
            notificationWrapper.notification.billableUnits = 0
            return
        }

        if (dryRun) {
            notificationWrapper.notification.sentAt = new Date()
            notificationWrapper.notification.status = "SENT"
            notificationWrapper.notification.billableUnits = 0
            return
        }

        let smsProviderHelp: ISMSProviderHelper

        let providerConfig: SmsProviderConfig = this.featureUtils.getSmsProviderConfig({enableCreativeProviderMap: false, enableTrafficSplit: false})

        if (["DUBAI", "USA-CANADA"].includes(userSMSInfo.country)) {
            //international sms through solutions inifni only
            smsProviderHelp = this.solutionsInfiniSMSHelper
        } else if (providerConfig.enableCreativeProviderMap && creative.creativeId in providerConfig.creativeProviderMap) {
            // if a particular provider is fixed for some creative
            this.logger.info(`SMSNotificationHelper::creativeProviderMap creativeId: ${creative.creativeId} 
            provider:  ${providerConfig.creativeProviderMap[creative.creativeId]}`)
            switch (providerConfig.creativeProviderMap[creative.creativeId]) {
                case "GUPSHUP": {
                    smsProviderHelp = this.gupshupSMSHelper
                    break
                }
                case "SOLUTIONS_INFINI": {
                    smsProviderHelp = this.fonadaSMSHelper
                    break
                }
                case "FONADA": {
                    smsProviderHelp = this.fonadaSMSHelper
                    break
                }
                default: {
                    notificationWrapper.notification.status = "FAILED"
                    notificationWrapper.notification.failReason = "INVALID_SERVICE_PROVIDER"
                    notificationWrapper.notification.billableUnits = 0
                    const errorMessage = `SMSNotificationHelper::sendNotification Unsupported ` +
                        `provider: ${creative.smsParams.serviceProvider}`
                    this.logger.error(errorMessage)
                    this.rollbarService.sendError(new Error(errorMessage))
                    return
                }
            }
        } else if ((providerConfig.enableTrafficSplit && !!creative.smsParams.templateId) || Constants.isOtpRouteCreative(creative.creativeId)) {
            // split by traffic percentage
            const randomInt = CommonUtils.getRandomNumber1to100()
            this.logger.info(`SMSNotificationHelper::enableTrafficSplit randomVal: ${randomInt} gupshup percentage: ${providerConfig.gupshupTrafficPercentage}`)
            if (randomInt <= providerConfig.solutionsInfiniTrafficPercentage) {
                smsProviderHelp = this.fonadaSMSHelper
            }
            else if (randomInt <= providerConfig.solutionsInfiniTrafficPercentage + providerConfig.gupshupTrafficPercentage) {
                smsProviderHelp = this.gupshupSMSHelper
            }
            else {
                smsProviderHelp = this.fonadaSMSHelper
            }
        } else {
            this.logger.info("SMSNotificationHelper:: executing switch stmt")
            switch (creative.smsParams.serviceProvider) {
                case "GUPSHUP": {
                    smsProviderHelp = this.gupshupSMSHelper
                    break
                }
                case "SOLUTIONS_INFINI": {
                    smsProviderHelp = this.fonadaSMSHelper
                    break
                }
                case "FONADA": {
                    smsProviderHelp = this.fonadaSMSHelper
                    break
                }
                default: {
                    notificationWrapper.notification.status = "FAILED"
                    notificationWrapper.notification.failReason = "INVALID_SERVICE_PROVIDER"
                    notificationWrapper.notification.billableUnits = 0
                    const errorMessage = `SMSNotificationHelper::sendNotification Unsupported ` +
                        `provider: ${creative.smsParams.serviceProvider}`
                    this.logger.error(errorMessage)
                    this.rollbarService.sendError(new Error(errorMessage))
                    return
                }
            }
        }

        let success: boolean
        try {
            success = await smsProviderHelp.sendSMS(creative, notificationWrapper,
                userSMSInfo, body, isTransactional, dryRun)
        } catch (error) {
        const failReason = _.get(error, "meta", "SEND_FAIL")
        notificationWrapper.notification.failReason = failReason
            notificationWrapper.notification.status = "FAILED"
            notificationWrapper.notification.billableUnits = 0

            if (failReason === "SEND_FAIL") {
                const errorMessage = `SMSNotificationHelper::sendNotification Error while sending SMS ` +
                    `notificationId: ${notificationWrapper.notification.notificationId}. Error: ${error.toString()}`
                this.logger.error(errorMessage)
                this.rollbarService.sendError(new Error(errorMessage))
            }
        }

        if (success) {
            await this.notificationLogService.logNotification(
                { body }, notificationWrapper.notification.userId,
                "SMS", isTransactional, dryRun)
        }
    }

    public async processSMSCallback(serviceProvider: ServiceProvider, callbackStatus: string, notificationId: string,
                                    deliveredAt: Date, callbackReceivedAt: Date, errorDetails?: string): Promise<any> {
        this.logger.info(`SMSNotificationHelper::processCallback received callback request serviceProvider: ${serviceProvider} callbackstatus: ${callbackStatus} notifId: ${notificationId}`)
        const status = await this.notificationReportHelper.findNotificationStatus(serviceProvider,
            "SMS", callbackStatus)
        this.logger.info(`SMSNotificationHelper::processCallback value ${this.getMeaningfulError(serviceProvider, callbackStatus, errorDetails)}`)
        const failReason = status === "FAILED" ? this.getMeaningfulError(serviceProvider, callbackStatus, errorDetails) : null
        const updateProps = { status, failReason, receivedAt: status !== "FAILED" ? deliveredAt : null }
        const notification: Notification = await this.notificationDao.get(notificationId)

        if (!notification) {
            const errorMessage = `SMSNotificationHelper::processCallback Unable to find notification: ${notificationId}`
            this.logger.error(errorMessage)
            throw new NotFoundError(errorMessage)
        }

        await this.notificationDao.updateProps(notificationId, updateProps)
        await this.notificationStatusUpdatePublisher.publish([{
            notification, status: updateProps.status, timestamp: deliveredAt.getTime()
        }])
        this.metricUtils.incrementCallbackFailReasonCounter("SMS", notification.campaignId,
            notification.creativeId, failReason ? failReason : "none", serviceProvider)

        //TODO: This sends too many Rollbars because sms Callbacks come in before IRIS is able to update DB.

        if (!notification.sentAt) {
            const errorMessage = `NotificationsController::updateSmsDelivery no sentAt for ` +
                `notification ${notification.notificationId}`
            this.logger.error(errorMessage)

            //sampling rollbars
            const randomVal = Math.random()
            if (randomVal < 0.01) {
                this.rollbarService.sendError(new Error(errorMessage))
            }
        } else {
            this.metricUtils.reportCallbackLatency(`SMS_${serviceProvider}`,
                callbackReceivedAt.getTime() - deliveredAt.getTime())
        }
        await this.userInteractionPublisher.publishPhoneInteraction(notification, deliveredAt, status)
    }

    public async publishSMSCallback(serviceProvider: ServiceProvider, callbackStatus: string, notificationId: string,
                                    deliveredAt: Date, callbackReceivedAt: Date, errorDetails?: string): Promise<any> {
        const payload: SMSCallbackParams = {
            serviceProvider: serviceProvider,
            callbackStatus: callbackStatus,
            notificationId: notificationId,
            deliveredAt: deliveredAt,
            callbackReceivedAt: callbackReceivedAt,
            errorCause: errorDetails
        }
        try {
            await this.queueService.sendMessage(Constants.getSMSCallbackSQSQueueName(), payload, new Map<string, any>())
        } catch (err) {
            const errMsg = `SMSNotificationHelper::publishCallback Unable to publish data ${JSON.stringify(payload)}  to sqs ${Constants.getSMSCallbackSQSQueueName()} due to ${err} `
            this.logger.error(errMsg)
            this.rollbarService.sendError(new Error(errMsg))
        }
    }

    public async processSOIPCallback(externalId: string, callbackStatus: string, failReason: string, callbackReceivedAt: Date) {
        let status: NotificationStatus = failReason === "DELIVRD" || callbackStatus.toUpperCase() === "DELIVERED" ? "DELIVERED" : "FAILED"
        failReason = status === "DELIVERED" ? undefined : failReason

        const notification: Notification = await this.notificationDao.getNotificationsByExternalId(externalId)
        if (!notification) {
            const errorMessage = `SMSNotificationHelper::processSOIPCallback Unable to find notification with external Id: ${externalId}`
            this.logger.error(errorMessage)
            throw new NotFoundError(errorMessage)
        }

        const updateProps = {status, failReason, receivedAt: status !== "FAILED" ? callbackReceivedAt : undefined}

        this.logger.info(`SMSNotificationHelper::processSOIPCallback Updating notification ${notification.notificationId} with external Id ${notification.externalId} with properties ${JSON.stringify(updateProps)}`)
        await this.notificationDao.updateProps(notification.notificationId, updateProps)
        this.logger.info(`SMSNotificationHelper::processSOIPCallback successfully updated notification ${notification.notificationId} with external Id ${notification.externalId} with properties ${JSON.stringify(updateProps)}`)

        await this.notificationStatusUpdatePublisher.publish([{
            notification, status: updateProps.status, timestamp: callbackReceivedAt.getTime()
        }])
        await this.userInteractionPublisher.publishPhoneInteraction(notification, callbackReceivedAt, status)
    }

    public getMeaningfulError(serviceProvider: ServiceProvider, callbackStatus: string, errorDetails?: string): string {
        let errorToDescription: Record<string, string>
        switch (serviceProvider) {
            case "SOLUTIONS_INFINI": {
                errorToDescription = Constants.KALEYRA_ERRORS_TO_DESCRIPTION_MAP
                break
            }
            case "GUPSHUP": {
                errorToDescription = Constants.GUPSHUP_ERRORS_TO_DESCRIPTION_MAP
                break
            }
            case "FONADA": {
                errorToDescription = Constants.FONADA_ERRORS_TO_DESCRIPTION_MAP
                break
            }
            default: {
                this.logger.info(`Unable to find map for service Provider ${serviceProvider}`)
                return `${serviceProvider}_${callbackStatus}${errorDetails ? `_${errorDetails}` : ""}`
            }
        }
        this.logger.debug(`map ${JSON.stringify(errorToDescription)}`)
        if (callbackStatus in errorToDescription && !_.isEmpty(errorToDescription[callbackStatus])) {
            this.logger.debug(`Entered if returning ${serviceProvider}_${errorToDescription[callbackStatus].toUpperCase().split(" ").join("_")}`)
            return `${serviceProvider}_${errorToDescription[callbackStatus].toUpperCase().split(" ").join("_")}`
        }
        return `${serviceProvider}_${callbackStatus}${errorDetails ? `_${errorDetails}` : ""}`
    }

}

export default SMSNotificationHelper