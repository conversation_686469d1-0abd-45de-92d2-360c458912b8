import {BASE_TYPES, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ogger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {DataErrorV2} from "@curefit/error-client"

import {inject, injectable} from "inversify"
import * as mustache from "mustache"
const fetch = require("node-fetch")

import TYPES from "../../ioc/types"
import {MetricUtils, ProfilerType} from "../../utils/MetricUtils"
import NotificationLogService from "../../service/NotificationLogService"
import {NotificationWrapper, SMSCreative} from "../../../iris-common"
import Constants from "../../constants"
import {BillingUtils} from "../../utils/BillingUtils"
import {UserSMSInfo} from "./SMSUtils"
import {ISMSProviderHelper} from "./ISMSProviderHelper"
import {CommonUtils} from "../../utils/CommonUtils"

interface KaleyraSMSResponse {
    status: string, // "sent"
    data: {
        [num: string]: {
            id: string,
            customid: string,
            customid1: string,
            customid2: string,
            mobile: string,
            status: string // should be "AWAITED-DLR" for successful send
        }
    }
}

interface KaleyraSOIPResponse {
    body?: string,
    callback_profile_id?: string,
    campaign_name?: string,
    data?: {
        message_id: string,
        recepient: string
    }[],
    from?: string,
    kid?: string,
    type?: string,
    route?: string
    error?: {
        code: string,
        type: string,
        parameter: string,
        message: string,
        reference: string
    }
}

@injectable()
class SolutionsInfiniSMSHelper implements ISMSProviderHelper {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(BASE_TYPES.FetchUtil) private fetchUtil: FetchUtil,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.NotificationLogService) private notificationLogService: NotificationLogService
    ) {
    }

    private getApiKey(creativeId: string, isTransactional: boolean, country: string, phone: string, email: string ) {
        const isOTP = Constants.isOtpRouteCreative(creativeId)
        if (isOTP && isTransactional) return Constants.getSolInfiniSmsOTPRouteApiKey(country, phone, email)
        if (isTransactional) return Constants.getSolInfiniSMSTransactionalApiKey(country, phone, email)
        return Constants.getSolInfiniSMSPromotionalApiKey(country, phone, email)
    }

    public async sendSMS(creative: SMSCreative, notificationWrapper: NotificationWrapper, userSMSInfo: UserSMSInfo,
                         body: string, isTransactional: boolean, dryRun?: boolean): Promise<boolean> {

        const { phone, name, email, country } = userSMSInfo
        const { smsParams } = creative

        notificationWrapper.userContext.tags.name = name

        const sender = encodeURIComponent(smsParams.sender)

        if (!!creative.smsParams.soipProperties) {
            return this.sendSOIP(creative, notificationWrapper, userSMSInfo, body, isTransactional )
        }

        let templateUrl = Constants.getSolInfiniSendSMSTemplateUrl(country)
        let apiKey = this.getApiKey(creative.creativeId, isTransactional, country, phone, email)
        if (!apiKey || 0 === apiKey.length) {
            throw new DataErrorV2("UNAUTHORIZED", { meta: "UNAUTHORIZED"})
        }
        apiKey = encodeURIComponent(apiKey)

        if (!!creative.smsParams.templateId) {
            templateUrl += `&template_id=${encodeURIComponent(creative.smsParams.templateId)}`
        }

        const deliveryReportUrl = encodeURIComponent(mustache.render(Constants.getSolInfiniDeliveryUrl(),
            {"id": notificationWrapper.notification.notificationId}))
        const urlForUser = mustache.render(templateUrl, {
            "to": phone,
            "message": body,
            "apiKey": apiKey,
            "sender": sender,
            "dlrurl": deliveryReportUrl
        })

        this.logger.info(`SolutionsInfiniSMSHelper::sendSMS notification ${notificationWrapper.notification.notificationId} and userId ${notificationWrapper.userContext.userId} kaleyra url: ${urlForUser} `)

        const apiLatencyTimerContext =
            this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "SOLUTIONS_INFINI_SMS")
        const response = await fetch(urlForUser, this.fetchUtil.get())
        this.metricUtils.endProfilingTimer(apiLatencyTimerContext)

        const solInfiniResponse = await this.fetchUtil.parseResponse<KaleyraSMSResponse>(response)
        this.logger.info(`SolutionsInfiniSMSHelper::sendSMS response: ${JSON.stringify(solInfiniResponse)}`)

        const sent = solInfiniResponse.status === "OK" &&
            solInfiniResponse.data &&
            solInfiniResponse.data[0] &&
            solInfiniResponse.data[0].status === "AWAITED-DLR"

        if (sent) {
            notificationWrapper.notification.externalId = solInfiniResponse.data[0].id ?
                Constants.SMS_SI_EXTERNALID_PREFIX + solInfiniResponse.data[0].id : null
            notificationWrapper.notification.sentAt = new Date()
            notificationWrapper.notification.status = "SENT"
            notificationWrapper.notification.billableUnits = BillingUtils.getSMSUnitsConsumed(body, country,
                "SOLUTIONS_INFINI")
            this.logger.debug(`SolutionsInfiniSMSHelper::sendSMS SMS sent via Solution Infini. ` +
                `notificationId: ${notificationWrapper.notification.notificationId} ` +
                `response: ${JSON.stringify(solInfiniResponse)}`)
        } else {
            // Solution Infini level error
            let { status } = solInfiniResponse

            // SMS level error
            if (status === "OK") {
                status = solInfiniResponse.data && solInfiniResponse.data[0] ?
                    solInfiniResponse.data[0].status : undefined
            }

            let providerFailReason = status ? `${Constants.SOLUTIONS_INFINI}_${status}` : "SEND_FAIL"
            const errorMessage = `SolutionsInfiniSMSHelper::sendSMS Failed to send ` +
                `notificationId: ${notificationWrapper.notification.notificationId} ` +
                `Fail reason: ${providerFailReason}. SolInfini response: ${JSON.stringify(solInfiniResponse)}`
            this.logger.error(errorMessage)
            throw new DataErrorV2("Solutions Infini failure", { meta: providerFailReason})
        }

        return sent
    }

    public async sendSOIP(creative: SMSCreative, notificationWrapper: NotificationWrapper, userSMSInfo: UserSMSInfo,
                         body: string, isTransactional: boolean): Promise<boolean> {

        let templateUrl = Constants.getSOIPTemplateUrl()
        let apiKey = Constants.getSOIPApiKey()
        if (!apiKey || 0 === apiKey.length) {
            throw new DataErrorV2("UNAUTHORIZED", { meta: "UNAUTHORIZED"})
        }
        apiKey = encodeURIComponent(apiKey)
        const headers = {"api-key": apiKey}

        const requestBody = {
            "to": userSMSInfo.phone,
            "from": "CULTFT",
            "body": decodeURIComponent(body),
            "source": "API",
            "type": "RMC",
            "route": isTransactional ? "TXN" : "MKT",
            "campaign_name": notificationWrapper.notification.campaignId,
            "title": [creative.smsParams.soipProperties.title],
            "category": creative.smsParams.soipProperties.category,
            "rich_media": [
                {
                    "media_type": 1,
                    "url": creative.smsParams.soipProperties.imageUrl,
                    "display_ratio": "tall"
                }
            ],
            "actions": [
                {
                    "action_type": 1,
                    "action_content": creative.smsParams.soipProperties.actionText,
                    "action_rank": "1",
                    "action_to": creative.smsParams.soipProperties.actionUrl
                }
            ],
            "callback_profile_id": CommonUtils.isEnvironmentProductionOrAlpha() ? "IN_659a6d8a-2289-451a-a865-2fd74ea90a4a" : "IN_184cbbd7-f703-4512-ad43-5aaa2d0f5fa1"
        }

        this.logger.info(`SolutionsInfiniSMSHelper::sendSOIP notification ${notificationWrapper.notification.notificationId} and userId ${notificationWrapper.userContext.userId} kaleyra url: ${templateUrl} and request body ${JSON.stringify(requestBody)} `)
        const apiLatencyTimerContext =
            this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "KALEYRA_SOIP")
        const response = await fetch(templateUrl, this.fetchUtil.post(requestBody, headers))
        this.metricUtils.endProfilingTimer(apiLatencyTimerContext)

        this.logger.info(`SolutionsInfiniSMSHelper::sendSOIP response1: ${JSON.stringify(response)}`)
        const kaleyraResponse = await this.fetchUtil.parseResponse<KaleyraSOIPResponse>(response)
        this.logger.info(`SolutionsInfiniSMSHelper::sendSOIP response: ${JSON.stringify(kaleyraResponse)}`)

        const sent = !kaleyraResponse.error
        if (sent) {
            notificationWrapper.notification.externalId = kaleyraResponse.kid
            notificationWrapper.notification.sentAt = new Date()
            notificationWrapper.notification.status = "SENT"
            notificationWrapper.notification.billableUnits = BillingUtils.getSOIPUnitsConsumed()
            this.logger.debug(`SolutionsInfiniSMSHelper::sendSMS SMS sent via Kaleyra. ` +
                `notificationId: ${notificationWrapper.notification.notificationId} ` +
                `response: ${JSON.stringify(kaleyraResponse)}` + `externalId : ${notificationWrapper.notification.externalId}`)
        } else {
            // Solution Infini level error
            let { error } = kaleyraResponse

            let providerFailReason = !!error ? `${Constants.KALEYRA}_${error.type}` : "SEND_FAIL"
            const errorMessage = `SolutionsInfiniSMSHelper::sendSMS Failed to send ` +
                `notificationId: ${notificationWrapper.notification.notificationId} ` +
                `Fail reason: ${providerFailReason}. Kaleyra response: ${JSON.stringify(kaleyraResponse)}`
            this.logger.error(errorMessage)
            throw new DataErrorV2("Kaleyra failure", { meta: providerFailReason})
        }

        return sent
    }
}

export default SolutionsInfiniSMSHelper