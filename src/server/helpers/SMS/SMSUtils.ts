import {Country, NotificationWrapper, SMSCreative} from "../../../iris-common"
import {MustacheUtils} from "../../utils/MustacheUtils"
import HandlebarsUtils from "../../utils/HandlebarsUtils"
import Constants from "../../constants"
import {PhoneNumberUtils} from "../../utils/PhoneNumberUtils"
import TagUtils from "../../utils/TagUtils"

export interface UserSMSInfo {
    phone?: string
    name?: string
    email?: string
    country?: Country
}

class SMSUtils {

    public static fetchUserInformation(notification: NotificationWrapper): UserSMSInfo {
        const phone = notification.userContext.tags[Constants.USER_PHONE_TAG_OLD] ||
            notification.userContext.tags[Constants.USER_PHONE_TAG] || notification.userContext.phone
        const name = notification.userContext.tags[Constants.USER_NAME_TAG_OLD] ||
            notification.userContext.tags[Constants.USER_NAME_TAG]
        const email = notification.userContext.tags[Constants.USER_EMAIL_TAG_OLD] ||
            notification.userContext.tags[Constants.USER_EMAIL_TAG]
        const country: Country = phone ? PhoneNumberUtils.getCountry(phone) : null
        return { phone, name, email, country }
    }

    public static parseBody(notification: NotificationWrapper, creative: SMSCreative, allTags: string[]): string {
        const { smsParams } = creative
        const isHandlebars = smsParams.templateEngine === "handlebars"
        const bodyTemplate = creative.body.replace(/\\n/g, "\n")
        if (!isHandlebars && !MustacheUtils.contextHasAllTags(allTags, notification.userContext.tags)) {
            throw new Error(`SMSUtils:parseBody using Mustache- UserContext does not have all tags`)
        }
        const renderLibrary = isHandlebars ? HandlebarsUtils : MustacheUtils

        /* encodeURIComponent() does not replace single quotes ('), therefore we are doing that separately since it
         was causing invalid requests when hitting external APIs
         */
        return encodeURIComponent(TagUtils.render(bodyTemplate, notification.userContext.tags, renderLibrary)).replace(/'/g, "%27")
    }
}

export default SMSUtils