import { BASE_TYPES, FetchUtilV2, ILogger } from "@curefit/base"
import {DataErrorV2} from "@curefit/error-client"

import * as mustache from "mustache"
import {inject, injectable} from "inversify"

import {ISMSProviderHelper} from "./ISMSProviderHelper"
import {NotificationWrapper, SMSCreative} from "../../../iris-common"
import {UserSMSInfo} from "./SMSUtils"
import Constants from "../../constants"
import {MetricUtils, ProfilerType} from "../../utils/MetricUtils"
import {BillingUtils} from "../../utils/BillingUtils"
import TYPES from "../../ioc/types"
import {CommonUtils} from "../../utils/CommonUtils"
import { Agent } from "http"

const https = require("https")
const fetch = require("node-fetch")

@injectable()
class GupshupSMSHelper implements ISMSProviderHelper {

    private fetchAgent: Agent

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(BASE_TYPES.FetchUtilV2) private fetchUtil: FetchUtilV2,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
    ) {
        this.fetchAgent = new https.Agent({
            keepAliveMsecs: 50,
            keepAlive: true,
            maxSockets: 50,
            maxFreeSockets: 2,
            timeout: 300000
        })
    }

    private static getGupShupSMSTransactionalAccount(phone: string, email: string): {"id": string, "password": string} {
        let account = null
        if (CommonUtils.isEnvironmentProductionOrAlpha() || Constants.isWhitelisted(phone, email)) {
            account = Constants.GUPSHUP_SMS_CONFIG.ACCOUNT_CREDS.TRANSACTIONAL.INDIA
        }
        return account
    }

    private static getGupShupSMSPromotionalAccount(phone: string, email: string): {"id": string, "password": string} {
        let account = null
        if (CommonUtils.isEnvironmentProductionOrAlpha() || Constants.isWhitelisted(phone, email)) {
            account = Constants.GUPSHUP_SMS_CONFIG.ACCOUNT_CREDS.PROMOTIONAL.INDIA
        }
        return account
    }

    private static getGupShupSmsOTPRouteAccount(phone: string, email: string) {
        let account = null
        if (CommonUtils.isEnvironmentProductionOrAlpha() || Constants.isWhitelisted(phone, email)) {
            account = Constants.GUPSHUP_SMS_CONFIG.ACCOUNT_CREDS.OTP_ROUTE.INDIA
        }
        return account
    }

    private getAccountDetials(creativeId: string, isTransactional: boolean, phone: string, email: string ): {"id": string, "password": string} {
        const isOTP = Constants.isOtpRouteCreative(creativeId)
        if (isOTP && isTransactional) return GupshupSMSHelper.getGupShupSmsOTPRouteAccount(phone, email)
        if (isTransactional) return GupshupSMSHelper.getGupShupSMSTransactionalAccount(phone, email)
        return GupshupSMSHelper.getGupShupSMSPromotionalAccount(phone, email)
    }

    public async sendSMS(creative: SMSCreative, notificationWrapper: NotificationWrapper, userSMSInfo: UserSMSInfo,
                         body: string, isTransactional: boolean, dryRun?: boolean): Promise<boolean> {
        const { name, country } = userSMSInfo
        const { smsParams } = creative

        notificationWrapper.userContext.tags.name = name

        const sender = encodeURIComponent(smsParams.sender)

        const urlForUser = this.getSmsmUrl(creative, notificationWrapper, userSMSInfo, body, isTransactional, sender)

        this.logger.info(`GupshupSMSHelper::sendSMS notification ${notificationWrapper.notification.notificationId} and userId ${notificationWrapper.userContext.userId} request url: ${urlForUser}`)
        const apiLatencyTimerContext =
            this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "GUPSHUP_SMS")
        const response = await fetch(urlForUser, this.fetchUtil.get({ agent: this.fetchAgent }))
        this.metricUtils.endProfilingTimer(apiLatencyTimerContext)

        const gupshupResponse = await response.text()
        this.logger.info(`GupshupSMSHelper::sendSMS response: ${gupshupResponse}`)

        const responseArr = gupshupResponse.split("|").map((str: string) => str.trim())

        const sent = responseArr[0] === "success"

        if (sent) {
            notificationWrapper.notification.externalId = `${Constants.SMS_GUPSHUP_EXTERNALID_PREFIX}${responseArr[2]}`
            notificationWrapper.notification.sentAt = new Date()
            notificationWrapper.notification.status = "SENT"
            notificationWrapper.notification.billableUnits = BillingUtils.getSMSUnitsConsumed(body, country, "GUPSHUP")
            this.logger.debug(`GupshupSMSHelper::sendSMS SMS sent via GUP SHUP. ` +
                `notificationId: ${notificationWrapper.notification.notificationId} ` +
                `response: ${gupshupResponse}`)
        } else {
            let providerFailReason = `${responseArr[1]}_${responseArr[2]}`
            const errorMessage = `GupshupSMSHelper::sendSMS Failed to send ` +
                `notificationId: ${notificationWrapper.notification.notificationId} ` +
                `Fail reason: ${providerFailReason}. gupshup response: ${JSON.stringify(gupshupResponse)}`
            this.logger.error(errorMessage)
            throw new DataErrorV2("Gupshup failure", { meta: providerFailReason})
        }

        return sent
    }

    private getSmsmUrl(creative: SMSCreative, notificationWrapper: NotificationWrapper, userSMSInfo: UserSMSInfo,
                       body: string, isTransactional: boolean, sender: string) {
        const { phone, email, country } = userSMSInfo
        const templateUrl = Constants.getGupshupSendSMSTemplateUrl(country)

        let urlForUser

        switch (country) {
            case "DUBAI":
            case "USA-CANADA":
                throw new DataErrorV2(`International messages not supported on GupShup`, {})
            default:
                let accountDetails = this.getAccountDetials(creative.creativeId, isTransactional, phone, email)
                if (!accountDetails) {
                    throw new DataErrorV2("UNAUTHORIZED", {meta: "UNAUTHORIZED"})
                }
                urlForUser = mustache.render(templateUrl, {
                    "to": phone,
                    "message": body,
                    "gupshupAccountId": accountDetails.id,
                    "gupshupAccountPassword": accountDetails.password,
                    "notifId": notificationWrapper.notification.notificationId
                })

                if (!!creative.smsParams.templateId)
                    urlForUser += `&dltTemplateId=${creative.smsParams.templateId}`
                if (!!creative.smsParams.sender)
                    urlForUser += `&mask=${creative.smsParams.sender}`

                break
        }

        return urlForUser
    }
}

export default GupshupSMSHelper