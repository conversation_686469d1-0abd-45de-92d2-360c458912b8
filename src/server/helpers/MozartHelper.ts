import {BASE_TYPES, Configuration, FetchUtil, ILogger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {MOZART_CLIENT_TYPES, MozartService, ISubmitJobPayload} from "@curefit/mozart-client/dist"

import {inject, injectable} from "inversify"
import * as _ from "lodash"

import Constants from "../constants"
import TYPES from "../ioc/types"
import {MetricUtils} from "../utils/MetricUtils"


@injectable()
export class MozartHelper {
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(BASE_TYPES.FetchUtil) private fetchUtil: FetchUtil,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(BASE_TYPES.Configuration) private appConfig: Configuration,
        @inject(MOZART_CLIENT_TYPES.MozartService) private mozartService: MozartService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService
    ) {
    }

    public async scheduleTask(taskId: string, scheduleTaskAt: Date) {
        const jobConfigId =
            _.get(this.appConfig.getConfiguration(), "mozart.iris_notification_job_config_id")
        const atSchedule = new Date(scheduleTaskAt).toISOString()

        if (!jobConfigId) {
            const errorMessage = `MozartHelper::scheduleTask Missing job config id for ` +
                `iris_notification_job_config_id`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            throw new Error(errorMessage)
        }

        const jobRequest: ISubmitJobPayload = {
            jobConfigId: jobConfigId,
            refId: taskId,
            source: Constants.IRIS,
            atSchedule,
            payload: {
                irisTaskId: taskId
            }
        }
        await this.mozartService.submitJobAsync(jobRequest)
    }

    // TODO: Configure separate task ID for alpha env
    public async scheduleNotification(atSchedule: string, campaignId: string, creativeId: string,
                                      notificationIds: string[]) {
        const jobConfigId =
            _.get(this.appConfig.getConfiguration(), "mozart.iris_schedule_notification_job_config_id")

        if (!jobConfigId) {
            const errorMessage = `MozartHelper::scheduleNotificationViaQueue Missing job config id for ` +
                `iris_schedule_notification_job_config_id`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            throw new Error(errorMessage)
        }

        const jobRequest: ISubmitJobPayload = {
            jobConfigId,
            atSchedule,
            source: Constants.IRIS,
            payload: {
                creativeId,
                campaignId,
                notificationIds
            }
        }
        await this.mozartService.submitJobAsync(jobRequest)
        notificationIds.forEach(notificationId =>
            this.metricUtils.incrementNotificationScheduledCounter(notificationId))
    }

}
