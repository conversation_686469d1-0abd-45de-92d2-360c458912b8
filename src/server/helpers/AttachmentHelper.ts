import * as aws from "aws-sdk"
import {Attachment, Template, TemplateAttachments, UserTags} from "../../iris-common"
import {inject, injectable} from "inversify"
import {BASE_TYPES, Configuration, ILogger} from "@curefit/base"
import {IRIS_MODELS_TYPES, TemplateReadonlyDaoMongoImpl} from "../../iris-models"
import * as htmlpdf from "html-pdf"
import TYPES from "../ioc/types"
import {MetricUtils} from "../utils/MetricUtils"
import * as _ from "lodash"
import {DataError} from "@curefit/error-client"
import uuid = require("uuid")
import HandlebarsUtils from "../utils/HandlebarsUtils"
import * as path from "node:path"
import * as fs from "node:fs"
import { ILockAccess, RedlockAccess } from "@curefit/lock-utils"
import { MultiRedisAccess, REDIS_TYPES } from "@curefit/redis-utils"

const fetch = require("node-fetch")
const { Parser } = require("json2csv")
const os = require("os")

@injectable()
export class AttachmentHelper {

    private s3: aws.S3

    private readonly S3_BUCKET_NAME: string
    private readonly S3_PUBLIC_BUCKET_NAME: string
    private lockAccess: ILockAccess

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(IRIS_MODELS_TYPES.TemplateReadonlyDao) private templateReadonlyDao: TemplateReadonlyDaoMongoImpl,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(BASE_TYPES.Configuration) private appConfig: Configuration,
        @inject(REDIS_TYPES.MultiRedisAccess) private multiRedisAccess: MultiRedisAccess,

    ) {
        this.s3 = new aws.S3({
            region: "ap-south-1"
        })
        this.S3_BUCKET_NAME = _.get(this.appConfig.getConfiguration(), "s3.attachmentBucket")
        this.S3_PUBLIC_BUCKET_NAME = _.get(this.appConfig.getConfiguration(), "s3.attachmentBucketPublic")
        this.lockAccess = new RedlockAccess(this.multiRedisAccess, logger, "BUMBLE")
    }

    public async getAttachments(userContext: UserTags, usePublicS3Bucket: boolean): Promise<{ filename: any, content: any, url: string }[]> {
        const attachments: Attachment[] = userContext.attachments
        let attachmentContents = await Promise.all(attachments.map(attachment => {
            if (attachment.fileUrl) {
                return fetch(attachment.fileUrl)
                    .then((result: any) => {
                        return result.body
                    }).catch((ex: any) => {
                        throw new Error(ex)
                    })
            }
            return this.s3.getObject(
                {
                    Bucket: attachment.s3Info.bucket,
                    Key: attachment.s3Info.key
                })
                .promise()
                .then(result => {
                    return result.Body
                }).catch(ex => {
                    throw new Error(ex)
                })
        }))

        let mailAttachments: { filename: any, content: any, url: string }[] = []
        attachmentContents.forEach((attachmentContent, index) => {
            let fileName = attachments[index].customFileName
            if (!fileName) {
                fileName = attachments[index].s3Info.key
            }
            mailAttachments.push({
                filename: fileName,
                content: attachmentContent,
                url: attachments[index].fileUrl ? attachments[index].fileUrl : `https://${attachments[index].s3Info.bucket}.s3-ap-south-1.amazonaws.com/${encodeURIComponent(attachments[index].s3Info.key)}`
            })
        })

        await this.getTemplateAttachments(userContext, mailAttachments, usePublicS3Bucket)
        return mailAttachments
    }

    private async getTemplateAttachments(userContext: UserTags, mailAttachments: { filename: any, content: any, url: string }[], usePublicS3Bucket: boolean) {
        if (!userContext.templateAttachments) return
        await userContext.templateAttachments.reduce(async (promise: Promise<void>, attachment) => {
            await promise
            const template = await this.templateReadonlyDao.findOne({templateId: attachment.templateId})
            if (_.isEmpty(template)) {
                this.logger.error("Could not find template for " + attachment.templateId)
                throw new DataError("Could not find template for " + attachment.templateId)
            }
            switch (template.type) {
                case "HTML_TO_PDF":
                    await this.getHtmlToPdfTemplateAttachments(template, attachment, userContext, mailAttachments, usePublicS3Bucket, !attachment.doNotUploadToS3)
                    break
                case "CSV":
                    await this.getCsvTemplateAttachments(template, attachment, userContext, mailAttachments, usePublicS3Bucket, !attachment.doNotUploadToS3)
                    break
                default:
                    this.logger.error("Unknown template type found: " + template.type)
                    throw new DataError(`Unknown template type found: ${template.type}`)
                    break
            }
        }, Promise.resolve())
    }

    private async getHtmlToPdfTemplateAttachments(template: Template, attachment: TemplateAttachments, userContext: UserTags, mailAttachments: { filename: any, content: any, url: string }[], usePublicS3Bucket: boolean, uploadTemplateAttachmentsToS3Bucket: boolean) {
        let retryCount = 0
        const MAX_RETRIES = 3
        const executeWithRetry = async (): Promise<void> => {
            try {
                const html = HandlebarsUtils.compile(template.templateBody, attachment.tags)
                const attachmentContent = await this.getPDFcontentFromHtml(html, attachment.pdfProperties)
                let fileName = attachment.customFileName ? attachment.customFileName : template.defaultFileName
                if (!fileName.endsWith(".pdf")) fileName = `${fileName.trim()}.pdf`
                const key = `${uuid()}__${fileName}`
                mailAttachments.push({
                    filename: fileName,
                    content: attachmentContent,
                    url: uploadTemplateAttachmentsToS3Bucket ? `https://${usePublicS3Bucket ? this.S3_PUBLIC_BUCKET_NAME : this.S3_BUCKET_NAME}.s3-ap-south-1.amazonaws.com/${encodeURIComponent(key)}` : " "
                })
                if (uploadTemplateAttachmentsToS3Bucket) await this.uploadTos3(attachmentContent, userContext, fileName, key, usePublicS3Bucket)
            }
            catch (err) {
                retryCount++
                if (retryCount < MAX_RETRIES) {
                    this.logger.error("HTML to PDF template error: " + err)
                    return executeWithRetry()
                }
                else {
                    throw new DataError("Template fail for: " + template.templateId + " with error: " + err)
                }
            }
        }
        return executeWithRetry()
    }

    private async getCsvTemplateAttachments(template: Template, attachment: TemplateAttachments, userContext: UserTags, mailAttachments: { filename: any, content: any, url: string }[], usePublicS3Bucket: boolean, uploadTemplateAttachmentsToS3Bucket: boolean) {
        try {
            const fields = attachment.fields
            if (_.isEmpty(fields)) {
                throw new DataError("fields can't be empty in csv attachment payload")
            }
            const attachmentContent: Buffer = await this.getCsvContent(fields)
            let fileName = attachment.customFileName ? attachment.customFileName : template.defaultFileName
            if (!fileName.endsWith(".csv")) fileName = `${fileName.trim()}.csv`
            const key = `${uuid()}__${fileName}`
            mailAttachments.push({
                filename: fileName,
                content: attachmentContent,
                url: uploadTemplateAttachmentsToS3Bucket ? `https://${usePublicS3Bucket ? this.S3_PUBLIC_BUCKET_NAME : this.S3_BUCKET_NAME}.s3-ap-south-1.amazonaws.com/${encodeURIComponent(key)}` : " "
            })
            if (uploadTemplateAttachmentsToS3Bucket) await this.uploadTos3(attachmentContent, userContext, fileName, key, usePublicS3Bucket)
        }
        catch (err) {
            this.logger.error("CSV template error: " + err)
            throw new DataError("Template fail for: " + template.templateId + " with error: " + err)
        }
    }

    private async uploadTos3(attachmentContent: Buffer, userContext: UserTags, fileName: string, key: string, usePublicBucket: boolean) {
        try {
            await this.s3.upload({
                Bucket: usePublicBucket ? this.S3_PUBLIC_BUCKET_NAME : this.S3_BUCKET_NAME,
                Key: key,
                Body: attachmentContent,
                ContentType: fileName.endsWith("pdf") ? "application/pdf" : undefined
            }).promise()
            userContext.attachments = userContext.attachments ? userContext.attachments : []
            userContext.attachments.push({
                customFileName: fileName,
                s3Info: {
                    bucket: this.S3_BUCKET_NAME,
                    key: key
                }
            })
        } catch (e) {
            this.logger.error("error in uploading attachment: ", e)
            throw e
        }
    }

    private async getPDFcontentFromHtml(html: any, pdfProperties?: { [key: string]: string }): Promise<Buffer> {

        const host: string = os.hostname()
        const pdfConversionLock = await this.lockAccess.lockResource(`HTML_PDF_${host}`, 5000)

        const tmpFile = path.join("/tmp", `${uuid()}.pdf`)
        fs.writeFileSync(tmpFile, "")

        let pdfOptions = {
            border: {
                top: pdfProperties != null && pdfProperties["borderTop"] != null ? pdfProperties["borderTop"] : "20px",
                bottom: pdfProperties != null && pdfProperties["borderBottom"] != null ? pdfProperties["borderBottom"] : "20px"
            },
            height: pdfProperties != null && pdfProperties["height"] != null ? pdfProperties["height"] : undefined,
            width: pdfProperties != null && pdfProperties["width"] != null ? pdfProperties["width"] : undefined,
            timeout: 100000,
            renderDelay: 500,
            phantomPath: "/usr/bin/phantomjs",
            filename: tmpFile
        }

        try {
            const bufferData = new Promise<Buffer>((resolve, reject) => {
                htmlpdf.create(html, pdfOptions).toBuffer((err: any, buffer: any) => {
                    if (err) {
                        reject(err)
                    }
                    resolve(buffer)
                })
            })
            return Promise.resolve(bufferData)
        }
        finally {
            await this.lockAccess.unlockResource(pdfConversionLock)
            fs.unlinkSync(tmpFile)
        }
    }

    private async getCsvContent(body: any): Promise<Buffer> {
        const json2csvParser = new Parser()
        const csv = json2csvParser.parse(body)
        return csv
    }

    public async uploadToPublicS3AndGetURL(attachmentContent: aws.S3.Body, fileName: string) {
        try {
            const key = `${uuid()}__${fileName}`
            await this.s3.upload({
                Bucket: this.S3_PUBLIC_BUCKET_NAME,
                Key: key,
                Body: attachmentContent,
                ContentType: fileName.endsWith("pdf") ? "application/pdf" : "image/png"
            }).send()
            const url = `https://${this.S3_PUBLIC_BUCKET_NAME}.s3-ap-south-1.amazonaws.com/${encodeURIComponent(key)}`
            this.logger.info(`AttachmentHelper::uploadToPublicS3AndGetURL returning public url ${url}`)
             return url
        } catch (e) {
            this.logger.error(`error in uploading attachment: ${e}`)
            throw e
        }
    }

    public async getAttachmentUrls(userContext: UserTags): Promise<{ filename: any, url: any }[]> {
        const attachments: Attachment[] = userContext.attachments
        let attachmentUrls = await Promise.all(attachments.map(async attachment => {
            if (!!attachment.publicFileUrl) {
                this.logger.info(`AttachmentHelper::getAttachmentUrls returning public url ${attachment.publicFileUrl}`)
                return attachment.publicFileUrl
            }
            else if (attachment.fileUrl) {
                return fetch(attachment.fileUrl)
                    .then((result: any) => {
                        return this.uploadToPublicS3AndGetURL(result.body, attachment.customFileName)
                    }).catch((ex: any) => {
                        throw new Error(ex)
                    })
            }
            return this.s3.getObject(
                {
                    Bucket: attachment.s3Info.bucket,
                    Key: attachment.s3Info.key
                }
            ).promise()
                .then(result => {
                    return this.uploadToPublicS3AndGetURL(result.Body, attachment.customFileName)
                }).catch(ex => {
                    throw new Error(ex)
                })

        }))
        let mailAttachments: { filename: any, url: any }[] = []
        attachmentUrls.forEach((attachmentUrl, index) => {
            let fileName = attachments[index].customFileName
            mailAttachments.push({
                filename: fileName,
                url: attachmentUrl
            })
        })
        return mailAttachments
    }

}
