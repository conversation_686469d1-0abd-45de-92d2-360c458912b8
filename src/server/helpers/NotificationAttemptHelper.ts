import {inject, injectable} from "inversify"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {ServiceProvider} from "@curefit/iris-common"
import {INotificationAttemptDao, IRIS_MODELS_TYPES} from "../../iris-models"

@injectable()
class NotificationAttemptHelper {
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(IRIS_MODELS_TYPES.NotificationAttemptDao) private notificationAttemptDao: INotificationAttemptDao
    ) {}

    public async createNotificationAttempt(notificationId: string, serviceProvider: ServiceProvider, status: string, externalId?: string, response?: any) {
        await this.notificationAttemptDao.create({
            notificationId: notificationId,
            serviceProvider: serviceProvider,
            externalId: externalId,
            status: status,
            metadata: response
        })
    }
}

export default NotificationAttemptHelper
