import {Container, inject, injectable} from "inversify"

import {ISESHelper} from "./SESHelper"
import {BASE_TYPES, FetchUtil, ILogger} from "@curefit/base"
import {EmailCreative, NotificationWrapper, UserNotificationStatus} from "../../iris-common"
import TYPES from "../ioc/types"

export interface IEmailNotificationHelper {
    sendNotifications(creative: EmailCreative, notifications: NotificationWrapper[], isTransactional: boolean, dryRun?: boolean): Promise<UserNotificationStatus[]>
}

export function EmailNotificationHelperFactory(kernel: Container) {

    @injectable()
    class EmailNotificationHelper implements IEmailNotificationHelper {

        constructor( @inject(BASE_TYPES.ILogger) private logger: ILogger,
                     @inject(TYPES.SESHelper) private sesHelper: ISESHelper,
                     @inject(BASE_TYPES.FetchUtil) private fetchUtil: FetchUtil) { }

        public sendNotifications(creative: EmailCreative, notifications: NotificationWrapper[],
                                 isTransactional: boolean, dryRun?: boolean): Promise<UserNotificationStatus[]> {
            switch (creative.emailParams.serviceProvider) {
                case "SES":
                    return this.sesHelper.sendMailsViaSES(creative, notifications, isTransactional, dryRun)
                default:
                    this.logger.error("Unsupported Service Provider : " + creative.emailParams.serviceProvider)
            }

            return null
        }

    }

    return EmailNotificationHelper
}
