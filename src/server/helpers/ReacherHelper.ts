import {BASE_TYPES, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, UrlUtil} from "@curefit/base"
import {inject, injectable} from "inversify"
import Constants from "../constants"
import {EmailValidResponse} from "../../iris-common"
import {MetricUtils, ProfilerType} from "../utils/MetricUtils"
import TYPES from "../ioc/types"
const fetch = require("node-fetch")

export interface ReacherAPIResponse {
    input: string,
    is_reachable: string,
    misc: {
        is_disposable: boolean,
        is_role_account: boolean
    },
    mx?: {
        accepts_mail: boolean,
        records: string[]
    },
    smtp?: {
        can_connect_smtp: boolean,
        has_full_inbox: boolean,
        is_catch_all: boolean,
        is_deliverable: boolean,
        is_disabled: boolean
    },
    syntax?: {
        is_valid_syntax: boolean,
        domain: string,
        username: string
    }
}


export interface IReacherHelper {
    validateEmail(email: string): Promise<EmailValidResponse>
}

@injectable()
export class ReacherHelper implements IReacher<PERSON>elper {
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(BASE_TYPES.UrlUtil) private urlUtil: UrlUtil,
        @inject(BASE_TYPES.FetchUtil) private fetchUtil: FetchUtil,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
    ) {}

    public async validateEmail(email: string): Promise<EmailValidResponse> {
        this.logger.debug(`ReacherHelper::validateEmail using 3rd Party App for validating email`)

        const callUrl = Constants.REACHER_API_BASE_LINK
        const headers = {"authorization": Constants.REACHER_EMAIL_VALIDATION_API_KEY}
        const formDataMap: Map<string, string> = new Map<string, string>()
        formDataMap.set("to_email", email)
        const formData  = this.fetchUtil.getFormData(formDataMap)

        const apiLatencyTimerContext = this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "Reacher" +
            " Email Validation")
        const res = await fetch(callUrl, await this.fetchUtil.postForm(formData, headers))
        this.metricUtils.endProfilingTimer(apiLatencyTimerContext)

        const result = await this.fetchUtil.parseResponse<ReacherAPIResponse>(res)
        this.logger.info(`ReacherHelper::validateEmail email: ${email} response ${JSON.stringify(result)}`)

        return <EmailValidResponse> {
            email: email,
            isValidEmail: result.syntax.is_valid_syntax && result.mx.accepts_mail && result.smtp.is_deliverable ,
            deepEmailStatusCheck: true
        }
    }
}
