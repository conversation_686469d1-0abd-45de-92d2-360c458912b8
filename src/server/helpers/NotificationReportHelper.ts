import {inject, injectable} from "inversify"
import {
    IBasicNotificationDao,
    INotificationReportReadonlyDao,
    INotificationReportReadWriteDao,
    IRIS_MODELS_TYPES
} from "../../iris-models"
import {
    ClickToCallReport,
    CreativeType,
    NotificationReport,
    NotificationStatus,
    ServiceProvider,
    TaskReport
} from "../../iris-common"
import * as _ from "lodash"
import TYPES from "../ioc/types"
import {KnowlarityHelper} from "./KnowlarityHelper"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {SolutionsInfiniHelper} from "./SolutionsInfiniHelper"
import {DataError, DataErrorV2} from "@curefit/error-client"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import FonadaSMSHelper from "./SMS/FonadaSMSHelper"

@injectable()
class NotificationReportHelper {
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(IRIS_MODELS_TYPES.NotificationReportReadWriteDao) private notificationReportReadWriteDao: INotificationReportReadWriteDao,
        @inject(IRIS_MODELS_TYPES.NotificationReportReadonlyDao) private notificationReportReadonlyDao: INotificationReportReadonlyDao,
        @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
        @inject(TYPES.KnowlarityHelper) private knowlarityHelper: KnowlarityHelper,
        @inject(TYPES.SolutionsInfiniHelper) private solutionsInfiniHelper: SolutionsInfiniHelper,
        @inject(TYPES.FonadaSMSHelper) private fonadaSMSHelper: FonadaSMSHelper,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService
    ) {}

    public async createClickToCallReport(clickToCallProviderReport: any, serviceProvider: string, notificationId: string) {
        let mappedResponse: any
        if (serviceProvider === "KNOWLARITY") {
            mappedResponse = await this.knowlarityHelper.clickToCallReportMapper(clickToCallProviderReport)
        }
        else if (serviceProvider === "SOLUTIONS_INFINI") {
            mappedResponse = await this.solutionsInfiniHelper.clickToCallReportMapper(clickToCallProviderReport)
        }
        let { clickToCallReport, rawCallerStatus, rawReceiverStatus } = mappedResponse

        clickToCallReport = {
            ...clickToCallReport,
            creativeType: "CLICK_TO_CALL",
            notificationId: notificationId,
            serviceProvider: serviceProvider
        }

        clickToCallReport = await this.notificationReportReadWriteDao.create(clickToCallReport)
        return { clickToCallReport, rawCallerStatus, rawReceiverStatus }
    }

    public async createInboundCallReport(inboundCallProviderReport: any, serviceProvider: ServiceProvider, notificationId: string) {
        let mappedResponse: any
        if (serviceProvider === "SOLUTIONS_INFINI") {
            mappedResponse = await this.solutionsInfiniHelper.createInboundCallReport(inboundCallProviderReport)
        } else {
            return;
        }

        let { inboundCallReport, rawCallerStatus, rawReceiverStatus } = mappedResponse

        inboundCallReport = {
            ...inboundCallReport,
            creativeType: "CLICK_TO_CALL",
            notificationId: notificationId,
            serviceProvider: serviceProvider
        }

        inboundCallReport = await this.notificationReportReadWriteDao.create(inboundCallReport)
        return { inboundCallReport, rawCallerStatus, rawReceiverStatus}


    }
    public async getTaskReport(taskId: string): Promise<TaskReport> {
        const taskReportDbResponse = await this.notificationDao.getNotificationTaskReport(taskId)
        const taskReport: any = {}
        taskReport.taskId = taskId
        taskReport.taskStatus = _.map(taskReportDbResponse, singleResponse => {
            return {[singleResponse["status"]]: singleResponse["count"]}
        })
        return taskReport
    }

    public async getClickToCallReport(notificationId: any): Promise<ClickToCallReport> {
        const callReport: ClickToCallReport = await this.notificationReportReadWriteDao.findOne({ notificationId: notificationId })
        if (!callReport) {
            const errorMessage = `Call report for notification ${notificationId} not found`
            throw new DataError(errorMessage)
        }

        return {
            caller: callReport.caller,
            receiver: callReport.receiver,
            status: callReport.status,
            statusCaller: callReport.statusCaller,
            statusReceiver: callReport.statusReceiver,
            startTime: callReport.startTime,
            duration: callReport.duration,
            recordPath: callReport.recordPath,
            externalId: callReport.externalId
        }
    }

    public async getNotificationReportBulk(notificationIds: string[]): Promise<NotificationReport[]> {
        const notificationReports: NotificationReport[] = []
        const notificationReportsResponse = await this.notificationReportReadonlyDao.find({
            condition: {
                "notificationId": {$in: notificationIds}
            }
        })
        notificationReportsResponse.forEach( notifReportResponse => {
            notificationReports.push({
                notificationId: notifReportResponse.notificationId,
                clickToCallReport: {
                    caller: notifReportResponse.caller,
                    receiver: notifReportResponse.receiver,
                    status: notifReportResponse.status,
                    statusCaller: notifReportResponse.statusCaller,
                    statusReceiver: notifReportResponse.statusReceiver,
                    startTime: notifReportResponse.startTime,
                    duration: notifReportResponse.duration,
                    recordPath: notifReportResponse.recordPath
                }
            })
        })
        return notificationReports
    }

    // TODO : MOVE TO RESPECTIVE PROVIDER CLASSES
    public async findNotificationStatus(serviceProvider: ServiceProvider, notificationType: CreativeType,
                                        providerStatus: string, metadata?: string): Promise<NotificationStatus> {
        switch (serviceProvider) {
            case("SOLUTIONS_INFINI"):
                return this.solutionsInfiniHelper.notificationStatusMapper(notificationType, providerStatus, metadata)
            case ("GUPSHUP"):
                if (notificationType !== "SMS") {
                    this.logger.error(`NotificationReportHelper::findNotificationStatus invalid notification type ${notificationType}`)
                    this.rollbarService.sendError(new Error(`NotificationReportHelper::findNotificationStatus invalid notification type ${notificationType}`))
                    return "FAILED"
                }
                return providerStatus === "SUCCESS" ? "DELIVERED" : "FAILED"
            case ("FONADA"):
                return this.fonadaSMSHelper.notificationStatusMapper(providerStatus)
            default:
                this.logger.error("unidentified provider: " + serviceProvider)
                return "FAILED"
        }
    }
}

export default NotificationReportHelper
