import {inject, injectable} from "inversify"
import {BASE_TYPES, <PERSON>tch<PERSON><PERSON>, <PERSON>og<PERSON>, UrlUtil} from "@curefit/base"
import Constants from "../constants"
import TYPES from "../ioc/types"
import {MetricUtils, ProfilerType} from "../utils/MetricUtils"
import {PhoneNumberUtils} from "../utils/PhoneNumberUtils"
import {C2CServiceAccount, ClickToCallLegStatus, ClickToCallReport, ClickToCallStatus} from "../../iris-common"
import NotificationAttemptHelper from "./NotificationAttemptHelper"
import {ClickToCallProviderResponse} from "./ClickToCallNotificationHelper"

const fetch = require("node-fetch")

interface ClickToCallKnowlarityResponse {
    status: string,
    call_id?: string,
    call_status?: string,
    error?: string
}

@injectable()
export class KnowlarityHelper {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(BASE_TYPES.UrlUtil) private urlUtil: UrlUtil,
        @inject(BASE_TYPES.FetchUtil) private fetchUtil: FetchUtil,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(TYPES.NotificationAttemptHelper) private notificationAttemptHelper: NotificationAttemptHelper,
    ) {
    }

    public async makeClickToCall(callerNumber: string, receiverNumber: string, notificationId: string,
                                 country: string, isConfidential?: boolean, dryRun?: boolean, callerId?: string) {
        this.logger.info(" notificationId: " + notificationId + " in CLICK_TO_CALL isConfidential: " + isConfidential)
        const headers = {
            "auth_key": Constants.getKnowlarityClickToCallAppAuthorization(callerNumber, receiverNumber, country, isConfidential)
        }
        const callbackUrl = Constants.getKnowlarityClickToCallBackUrl(country) + "/" + notificationId
        const formDataMap: Map<string, string> = new Map<string, string>()
        formDataMap.set("agent_number", PhoneNumberUtils.addCountryCode(callerNumber, country))
        formDataMap.set("caller_number", PhoneNumberUtils.addCountryCode(receiverNumber, country))
        formDataMap.set("caller_id", callerId || Constants.getKnowlarityClickToCallCallerId(country, isConfidential))
        formDataMap.set("callback_url", callbackUrl)
        const formData  = this.fetchUtil.getFormData(formDataMap)
        const callUrl = Constants.getKnowlarityClickToCallApiUrl()
        let response: ClickToCallProviderResponse

        if (dryRun) {
            response = { status : "success", callId: Constants.KNOWLARITY + "_DRY_RUN" }
        } else {
            try {
                this.logger.info(`KnowlarityHelper::makeClickToCall notification ${notificationId} ` +
                    `headers: ${JSON.stringify(headers)} formDat: ${JSON.stringify(formData)}`)

                const apiLatencyTimerContext =
                    this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "Knowlarity C2C")
                const res = await fetch(callUrl, await this.fetchUtil.postForm(formData, headers))
                this.metricUtils.endProfilingTimer(apiLatencyTimerContext)

                const clickToCallKnowlarityResponse: ClickToCallKnowlarityResponse
                    = await this.fetchUtil.parseResponse<ClickToCallKnowlarityResponse>(res)
                this.logger.info(`KnowlarityHelper::makeClickToCall notification ${notificationId} ` +
                    `response: ${JSON.stringify(clickToCallKnowlarityResponse)}`)

                if (clickToCallKnowlarityResponse.status &&
                    (clickToCallKnowlarityResponse.status.toUpperCase() === "success".toUpperCase())) {
                    this.logger.info("successfully made click to call using knowlarity with call_id: "
                        + clickToCallKnowlarityResponse.call_id + ", call_status: " + clickToCallKnowlarityResponse.call_status)
                    response = {
                        status : "success",
                        callId : clickToCallKnowlarityResponse.call_id ?
                            (Constants.KNOWLARITY + "_" + clickToCallKnowlarityResponse.call_id) : "none"
                    }
                }
                else {
                    await this.notificationAttemptHelper.createNotificationAttempt(notificationId, Constants.KNOWLARITY,
                        clickToCallKnowlarityResponse.call_id, clickToCallKnowlarityResponse.call_status,
                        JSON.stringify({ call_status: clickToCallKnowlarityResponse.call_status, error: clickToCallKnowlarityResponse.error }))
                    response = { status : "failed", failReason : clickToCallKnowlarityResponse.error || "SEND_FAIL" }
                    this.logger.error("failed to make click to call using knowlarity with message: "
                        + clickToCallKnowlarityResponse.error)
                }
            } catch (error) {
                this.logger.error(error)
                await this.notificationAttemptHelper.createNotificationAttempt(notificationId, Constants.KNOWLARITY,
                    "FAILED", "none", JSON.stringify({ error }))
                response = {status : "failed"}
            }
        }
        return response
    }

    public async clickToCallReportMapper(clickToCallProviderReport: any) {
        let clickToCallReport: ClickToCallReport
        let startTime = new Date(clickToCallProviderReport.startTime)
        startTime.setHours(startTime.getHours() - 5)
        startTime.setMinutes(startTime.getMinutes() - 30)
        clickToCallReport = {
            caller: clickToCallProviderReport.callerNumber,
            receiver: clickToCallProviderReport.receiverNumber,
            status: this.clickToCallStatusMapper(clickToCallProviderReport.callerStatus, clickToCallProviderReport.receiverStatus),
            statusCaller: this.clickToCallLegStatusMapper(clickToCallProviderReport.callerStatus),
            statusReceiver: this.clickToCallLegStatusMapper(clickToCallProviderReport.receiverStatus),
            startTime: startTime,
            duration: this.getDurationOfCallInSeconds(clickToCallProviderReport.durationOfCall),
            recordPath: clickToCallProviderReport.recording_url_link,
            externalId: clickToCallProviderReport.call_id,
            metadata: JSON.stringify(clickToCallProviderReport)
        }
        return {
            clickToCallReport,
            rawCallerStatus: clickToCallProviderReport.callerStatus,
            rawReceiverStatus: clickToCallProviderReport.receiverStatus
        }
    }

    private clickToCallLegStatusMapper(legStatus: string): ClickToCallLegStatus {
        if (legStatus === "Connected") return "ANSWER"
        if (legStatus === "None") return "FAILED"
        if (legStatus === "Missed") return "MISSED"
        if (legStatus === "Not Connected") return "FAILED"
        return "FAILED"
    }

    private clickToCallStatusMapper(callerStatus: string, receiverStatus: string): ClickToCallStatus {
        if (callerStatus === "Connected") {
            if (receiverStatus === "Connected") return "CONNECTED"
            if (receiverStatus === "None") return "ATTEMPTED"
            if (receiverStatus === "Missed") return "RINGED"
            if (receiverStatus === "Not Connected") return "ATTEMPTED"
        }
        return "FAILED"
    }

    private getDurationOfCallInSeconds(durationOfCall: string): number {
        let durationOfCallInSeconds: number = 0
        const unitTimes = durationOfCall.split(":").reverse()
        unitTimes.forEach( (value, index) => {
            durationOfCallInSeconds = durationOfCallInSeconds + Number.parseInt(value) * this.getUnitTimeMultiplier(index, durationOfCall)
        })
        return durationOfCallInSeconds
    }

    private getUnitTimeMultiplier(unit: number, durationOfCall: string) {
        if (unit === 0) return 1
        if (unit === 1) return 60
        if (unit === 2) return 3600
        if (unit === 3) return 86400
        this.logger.debug("Time unit in durationOfCall(KNOWLARITY) incorrect: " + durationOfCall)
        return 0
    }
}