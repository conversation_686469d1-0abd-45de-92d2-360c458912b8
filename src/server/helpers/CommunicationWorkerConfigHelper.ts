import {inject, injectable} from "inversify"
import {BASE_TYPES, Configuration, ILogger} from "@curefit/base"
import * as _ from "lodash"
import {createClient, RedisClient} from "redis"
import Constants from "../constants"
import Bottleneck from "@maselious/bottleneck"

@injectable()
class CommunicationWorkerConfigHelper {
    private client: RedisClient
    private sms_limiter: Bottleneck
    private email_limiter: Bottleneck
    private obd_limiter: Bottleneck
    private ctc_limiter: Bottleneck
    private ian_limiter: Bottleneck
    private pn_limiter: Bottleneck
    private whatsapp_limiter: Bottleneck
    private connection: Bottleneck.RedisConnection
    constructor( @inject(BASE_TYPES.ILogger) private logger: ILogger,
                 @inject(BASE_TYPES.Configuration) private appConfig: Configuration,
    ) {
        const redisConfigs = _.get(appConfig.getConfiguration(), "redisConf.connections")
        const redisConfig = _.find(redisConfigs, {"entitySpace": "REDIS_CACHE"})

        this.client = createClient({
            port: redisConfig.hosts[0].port,
            host: redisConfig.hosts[0].host
        })

        this.connection = new Bottleneck.RedisConnection({
            client: this.client
        })

        this.initialize()
    }

    public initialize(): void {
        this.sms_limiter = this.initLimiter(Constants.SMS, Constants.SMS_WORKER_DELAY, Constants.SMS_PER_MIN_LIMIT)
        this.email_limiter = this.initLimiterPerSec(Constants.EMAIL, Constants.EMAIL_WORKER_DELAY, Constants.EMAIL_PER_SEC_LIMIT, Constants.EMAIL_THROTTLE_RESERVOIR_REFRESH_INTERVAL)
        this.ian_limiter = this.initLimiter(Constants.IAN, Constants.IAN_WORKER_DELAY, Constants.IAN_PER_MIN_LIMIT)
        this.ctc_limiter = this.initLimiter(Constants.CTC, Constants.CTC_WORKER_DELAY, Constants.CTC_PER_MIN_LIMIT)
        this.pn_limiter = this.initLimiter(Constants.PN, Constants.PN_WORKER_DELAY, Constants.PN_PER_MIN_LIMIT)
        this.whatsapp_limiter = this.initLimiterPerSec(Constants.WHATSAPP, Constants.WHATSAPP_WORKER_DELAY, Constants.WHATSAPP_PER_SEC_LIMIT, Constants.WHATSAPP_THROTTLE_RESERVOIR_REFRESH_INTERVAL)
        this.obd_limiter = this.initLimiter(Constants.OBD, Constants.OBD_WORKER_DELAY, Constants.OBD_PER_MIN_LIMIT)
    }

    private initLimiter(workerTag: string, minTime: number, reservoirSize: number): Bottleneck {
        return  new Bottleneck({
            id: workerTag,
            clearDatastore: true,
            connection: this.connection,
            minTime: minTime,
            reservoir: reservoirSize,
            reservoirRefreshAmount: reservoirSize,
            reservoirRefreshInterval: Constants.THROTTLE_RESERVOIR_REFRESH_INTERVAL
        })
    }

    /*
    Some external APIs need requests to be throttled at per second level. For those workers, we use this initializer
     */
    private initLimiterPerSec(workerTag: string, minTime: number, reservoirSize: number, reservoirRefreshInterval: number): Bottleneck {
        return  new Bottleneck({
            id: workerTag,
            clearDatastore: true,
            connection: this.connection,
            minTime: minTime,
            reservoir: reservoirSize,
            maxConcurrent: reservoirSize,        // strict rate limiting is needed for emails/whatsapp, can be removed for PNs if required
            reservoirRefreshAmount: reservoirSize,
            reservoirRefreshInterval: reservoirRefreshInterval
        })
    }

    async updateWorkerConfig(workerTag: string, reservoirSize: number, maxConcurrecy?: number, minTime?: number): Promise<boolean> {
        let options: any = {
            id: workerTag,
            reservoirRefreshInterval: Constants.THROTTLE_RESERVOIR_REFRESH_INTERVAL
        }
        if (minTime !== undefined) {
            options["minTime"] = minTime
        }
        if (maxConcurrecy !== undefined && maxConcurrecy !== null) {
            options["maxConcurrecy"] = maxConcurrecy
        }
        options["reservoir"] = reservoirSize
        options["reservoirRefreshAmount"] = reservoirSize
        switch (workerTag) {
            case Constants.SMS: {
                this.sms_limiter.updateSettings(options)
                break
            }
            case Constants.EMAIL: {
                this.email_limiter.updateSettings(options)
                break
            }
            case Constants.OBD: {
                this.obd_limiter.updateSettings(options)
                break
            }
            case Constants.CTC: {
                this.ctc_limiter.updateSettings(options)
                break
            }
            case Constants.IAN: {
                this.ian_limiter.updateSettings(options)
                break
            }
            case Constants.PN: {
                this.pn_limiter.updateSettings(options)
                break
            }
            case Constants.WHATSAPP: {
                this.whatsapp_limiter.updateSettings(options)
                break
            }
        }

        return true
    }

    getWorkerConfig(workerTag: string): Promise<any> {
        return new Promise<object>((resolve, reject) => {
            this.client.hgetall("b_" + workerTag + "_settings", (err: Error, object: any) => {
                if (err) {
                    this.logger.error("settings for the worker Tag" + workerTag + "do not exist in Redis.")
                    reject(err)
                } else {
                    resolve({reservoirSize: object["reservoirRefreshAmount"]})
                }
            })
        })
    }
}

export default CommunicationWorkerConfigHelper
