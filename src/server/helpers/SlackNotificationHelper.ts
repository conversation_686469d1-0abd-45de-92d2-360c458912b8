import { Container, inject, injectable } from "inversify"

import { ISESHelper } from "./SESHelper"
import { BASE_TYPES, FetchUtil, ILogger } from "@curefit/base"
import { EmailCreative, NotificationWrapper, UserNotificationStatus, SlackCreative } from "../../iris-common"
import TYPES from "../ioc/types"
import { CommonUtils } from "../utils/CommonUtils"
import * as _ from "lodash"
import HandlebarsUtils from "../utils/HandlebarsUtils"
import Constants from "../constants"
import NotificationLogService from "../service/NotificationLogService"
import { RollbarService, ERROR_COMMON_TYPES } from "@curefit/error-common"
import { MetricUtils, ProfilerType } from "../utils/MetricUtils"
import { ISlackAuthMappingReadOnlyDao, SlackAuthMapping, SlackPostMessagePayload } from "../models/Slack"
import FunctionalTagsUtils from "../utils/FunctionalTagsUtils"
import TagUtils from "../utils/TagUtils"

const { WebClient } = require("@slack/web-api")


export interface ISlackNotificationHelper {
    sendNotifications(creative: SlackCreative, notifications: NotificationWrapper[], isTransactional: boolean, dryRun?: boolean): Promise<UserNotificationStatus[]>
}

export function SlackNotificationHelperFactory(kernel: Container) {

    @injectable()
    class SlackNotificationHelper implements ISlackNotificationHelper {

        private slackWebApiClient: any
        constructor(@inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(BASE_TYPES.FetchUtil) private fetchUtil: FetchUtil,
            @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
            @inject(TYPES.NotificationLogService) private notificationLogService: NotificationLogService,
            @inject(TYPES.SlackAuthMappingReadOnlyDao) private slackAuthMappingReadOnlyDao: ISlackAuthMappingReadOnlyDao,

        ) {
            this.slackWebApiClient = new WebClient()
        }

        public async sendNotifications(creative: SlackCreative, notificationWrappers: NotificationWrapper[],
            isTransactional: boolean, dryRun?: boolean): Promise<UserNotificationStatus[]> {
            const renderLibrary = HandlebarsUtils
            const ret = await Promise.all(notificationWrappers.map(async notificationWrapper => {
                const name = notificationWrapper.userContext.tags[Constants.USER_NAME_TAG]

                const userTags = Object.assign({}, notificationWrapper.userContext.tags,
                    name ? { "name": name } : {})

                const userId = notificationWrapper.userContext.userId
                const sentStatus: any = {}

                let slackText, slackBlocks
                try {
                    slackText = TagUtils.render(creative.title, userTags, renderLibrary)
                    if (creative.slackParams.blocksJsonAsString) {
                        slackBlocks = TagUtils.render(creative.slackParams.blocksJsonAsString, userTags, renderLibrary)
                    }
                } catch (err) {
                    notificationWrapper.notification.failReason = "TEMPLATE_FAIL"
                    notificationWrapper.notification.status = "FAILED"
                    this.logger.error(`SESHelper::sendSlackNotifications Templating error; ` +
                        `notification: ${notificationWrapper.notification.notificationId} ` +
                        `Error: ${err.toString()}. Using render library: ${renderLibrary.typeName}`)
                    return
                }

                if (await this.notificationLogService.isDuplicate(
                    { slackText, slackBlocks }, userId, dryRun)) {
                    notificationWrapper.notification.failReason = "DUPLICATE"
                    notificationWrapper.notification.status = "FAILED"
                    notificationWrapper.notification.billableUnits = 0
                    return
                }

                if (dryRun) {
                    sentStatus["s_" + userId] = true
                } else {
                    try {
                        const authMapping = await this.getSlackAuthMapping(userId)
                        const apiLatencyTimerContext =
                            this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "SLACK")

                        const messagePayload: SlackPostMessagePayload = {
                            token: authMapping.botAccessToken,
                            channel: authMapping.channelId,
                            text: slackText,
                            blocks: JSON.parse(slackBlocks)
                        }
                        const response = await this.slackWebApiClient.chat.postMessage(messagePayload)
                        this.metricUtils.endProfilingTimer(apiLatencyTimerContext)
                        if (response && response.ok) {
                            sentStatus["s_" + userId] = true
                        } else {
                            sentStatus["s_" + userId] = false
                            sentStatus["rr_" + userId] = response.error
                        }
                    } catch (error) {
                        this.logger.error("Error while sending slack message", error)
                        sentStatus["s_" + userId] = false
                        sentStatus["rr_" + userId] = error
                    }
                }

                let sent = !_.isUndefined(sentStatus["s_" + notificationWrapper.userContext.userId]) 
                    ? sentStatus["s_" + notificationWrapper.userContext.userId] : false
                const uns: UserNotificationStatus = {
                    userId: notificationWrapper.userContext.userId,
                    creativeId: creative.creativeId,
                    notificationId: notificationWrapper.notification.notificationId,
                    sent: sent,
                    failedReason: !_.isUndefined(sentStatus["rr_" + notificationWrapper.userContext.userId]) ? sentStatus["rr_" + notificationWrapper.userContext.userId] : ""
                }
                if (sent) {
                    notificationWrapper.notification.sentAt = new Date()
                    this.logger.debug("Message sent through slack for notification Id : " + notificationWrapper.notification.notificationId)
                    notificationWrapper.notification.status = "SENT"
                    await this.notificationLogService.logNotification({ slackText, slackBlocks },
                        userId, "SLACK", isTransactional, dryRun)
                } else {
                    this.logger.error(`Slack message for ${notificationWrapper.notification.notificationId} not sent due to ${uns.failedReason}`)
                    this.rollbarService.sendError(new Error(`Slack message for ${notificationWrapper.notification.notificationId} not sent due to ${uns.failedReason}`))
                    notificationWrapper.notification.failReason = "SEND_FAIL"
                    notificationWrapper.notification.status = "FAILED"
                }
                return uns
            }))
            return Promise.resolve(ret)
        }

        private async getSlackAuthMapping(userId: string): Promise<SlackAuthMapping> {
            // TODO: Add redis caching to avoid hitting mongo everytime
            return this.slackAuthMappingReadOnlyDao.findOne({ userId })
        }
    }

    return SlackNotificationHelper
}
