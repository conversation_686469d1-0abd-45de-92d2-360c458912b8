import {Container, inject, injectable} from "inversify"
import * as _ from "lodash"
import * as mustache from "mustache"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {
    CommunicationCampaign,
    InAppNotificationCreative,
    InAppNotificationData,
    NotificationMeta,
    NotificationState,
    NotificationWrapper,
    PaginatedResults
} from "../../iris-common"
import {DataError, DataErrorV2} from "@curefit/error-client"
import {MustacheUtils} from "../utils/MustacheUtils"
import {TimeUtil} from "@curefit/util-common"
import {IBasicNotificatioMetaDao, IRIS_MODELS_TYPES} from "../../iris-models"
import {IMultiCrudKeyValue, REDIS_TYPES} from "@curefit/redis-utils"
import {CacheAccessImpl, ICacheAccess} from "@curefit/cache-utils"
import {IANNotificationKey} from "../models/IANNotificationKey"
import Constants from "../constants"
import {BillingUtils} from "../utils/BillingUtils"
import HandlebarsUtils from "../utils/HandlebarsUtils"
import TYPES from "../ioc/types"
import {MetricUtils} from "../utils/MetricUtils"
import FunctionalTagsUtils from "../utils/FunctionalTagsUtils"
import TagUtils from "../utils/TagUtils"


export interface IInAppNotificationHelper {
    persistNotifications(creative: InAppNotificationCreative, notificationWrappers: NotificationWrapper[], isTransactional: boolean, campaign: CommunicationCampaign, appId?: string, dryRun?: boolean): Promise<boolean>
    getActiveNotificationMetasForUser(userId: string, appId: string): Promise<NotificationMeta[]>
    updatePropsByNotificationId(notificationId: string, props: {[key: string]: any}): Promise<boolean>
    getNotificationMetasForUserPaginated(userId: string, appId: string, states: NotificationState[], pageSize: number,
                                         pageNumber: number): Promise<PaginatedResults<NotificationMeta>>
    updateNotificationMetas(notificationIds: string[], updateProps: {[key: string]: any}): Promise<boolean>
    bulkCacheDelete(noticationMetas: NotificationMeta[]): Promise<boolean>
    getNotificationMetaCountsForUser(userId: string, appId: string, expiryAfter?: string): Promise<any>
}


export function InAppNotificationHelperFactory(kernel: Container) {

    @injectable()
    class InAppNotificationHelper implements IInAppNotificationHelper {
        protected notificationMetaCache: ICacheAccess<IANNotificationKey, NotificationMeta[]>

        constructor(
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(IRIS_MODELS_TYPES.BasicNotificationMetaDao) private basicNotificatioMetaDao: IBasicNotificatioMetaDao,
            @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
            @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        ) {
            this.notificationMetaCache = new CacheAccessImpl<IANNotificationKey, NotificationMeta[]>
            (multiCrudKeyValueDao.getICrudKeyValue("REDIS_CACHE"))
        }

        public async persistNotifications(creative: InAppNotificationCreative, notificationWrappers: NotificationWrapper[],
                                          isTransactional: boolean, campaign: CommunicationCampaign,
                                          appId?: string, dryRun?: boolean): Promise<boolean> {
            let tagLocations = [creative.title, creative.inAppNotificationParams.actionTitle,
                creative.inAppNotificationParams.actionUrl, creative.inAppNotificationParams.expiry]

            const allTags = TagUtils.allTagsUsed(tagLocations)
            let notificationMetas: NotificationMeta[] = []
            notificationWrappers.forEach((notificationWrapper) => {
                try {
                    notificationMetas.push(
                        this.buildNotificationMeta(creative, campaign, notificationWrapper, allTags, appId, dryRun)
                    )
                    notificationWrapper.notification.sentAt = new Date()
                    notificationWrapper.notification.status = "SENT"
                    notificationWrapper.notification.billableUnits = dryRun ? 0 : BillingUtils.getIANUnitsConsumed()
                } catch (error) {
                    notificationWrapper.notification.status = "FAILED"
                    notificationWrapper.notification.failReason = _.get(error, "meta", "SEND_FAIL")
                }

            })
            await this.basicNotificatioMetaDao.bulkCreate(notificationMetas)
            return this.bulkCacheDelete(notificationMetas)
        }

        public async bulkCacheDelete(notificationMetas: NotificationMeta[]): Promise<boolean> {
            notificationMetas.map(notificationMeta => {
                this.notificationMetaCache.delete(IANNotificationKey.from(notificationMeta.userId, notificationMeta.appId))
            })
            return true
        }

        private buildNotificationMeta(creative: InAppNotificationCreative, campaign: CommunicationCampaign,
                                      notificationWrapper: NotificationWrapper, allTags: string[],
                                      appId?: string, dryRun?: boolean): NotificationMeta {

            if (!notificationWrapper.userContext || !notificationWrapper.userContext.userId) {
                this.logger.error(`InAppNotificationHelper::buildNotificationMeta Did not find user id for ` +
                    `${notificationWrapper.notification.notificationId}`)
                throw new DataErrorV2("User id mandatory", { meta: "USER_ID_MISSING" })
            }

            const { inAppNotificationParams } = creative
            const isHandlebars =
                _.get(inAppNotificationParams, "templateEngine", "mustache") === "handlebars"

            if (notificationWrapper.userContext.tags && notificationWrapper.userContext.tags.title &&
                notificationWrapper.userContext.tags.title.length > 44) {
                this.logger.error(`InAppNotificationHelper::buildNotificationMeta Title can be maximum of 44 characters. ` +
                    `Original Title Length is ${notificationWrapper.userContext.tags.title.length}`)
                throw new DataErrorV2("Title can be maximum of 44 characters.", { meta: "TITLE_TOO_LONG" })
            }

            let actionTitle, actionUrl, title, expiry, scheduledAt, image

            if (isHandlebars) {
                try {
                    actionTitle = TagUtils.render(inAppNotificationParams.actionTitle,
                        notificationWrapper.userContext.tags, HandlebarsUtils)
                    actionUrl = TagUtils.render(inAppNotificationParams.actionUrl,
                        notificationWrapper.userContext.tags, HandlebarsUtils)
                    title = TagUtils.render(creative.title, notificationWrapper.userContext.tags, HandlebarsUtils)
                    expiry = TagUtils.render(inAppNotificationParams.expiry,
                        notificationWrapper.userContext.tags, HandlebarsUtils)

                    if (inAppNotificationParams.scheduledAt) {
                        scheduledAt = TagUtils.render(inAppNotificationParams.scheduledAt,
                            notificationWrapper.userContext.tags, HandlebarsUtils)
                    }
                    image = TagUtils.render(inAppNotificationParams.image || "",
                        notificationWrapper.userContext.tags, HandlebarsUtils)

                } catch (error) {
                    throw new DataErrorV2("Error in templating. Using handlebars", { meta: "TEMPLATE_FAIL" })
                }
            } else {
                if (!MustacheUtils.contextHasAllTags(allTags, notificationWrapper.userContext.tags)) {
                    this.logger.error(`Missing data for tags for notification ` +
                        `${notificationWrapper.notification.notificationId} and user ${notificationWrapper.userContext.userId}`)
                    throw new DataErrorV2("Missing data for tags", { meta: "TEMPLATE_FAIL" })
                }

                try {
                    actionTitle = TagUtils.render(inAppNotificationParams.actionTitle, notificationWrapper.userContext.tags, mustache)

                    actionUrl = TagUtils.render(inAppNotificationParams.actionUrl, notificationWrapper.userContext.tags, mustache)

                    title = TagUtils.render(creative.title, notificationWrapper.userContext.tags, mustache)

                    expiry = TagUtils.render(inAppNotificationParams.expiry, notificationWrapper.userContext.tags, mustache)

                    scheduledAt = inAppNotificationParams.scheduledAt || ""
                    scheduledAt = TagUtils.render(scheduledAt, notificationWrapper.userContext.tags, mustache)

                    if (inAppNotificationParams.image) {
                        image = TagUtils.render(inAppNotificationParams.image, notificationWrapper.userContext.tags, mustache)
                    }

                } catch (error) {
                    throw new DataErrorV2("Error in templating. Using mustache", { meta: "TEMPLATE_FAIL" })
                }
            }

            const data: InAppNotificationData = {
                title,
                action: {
                    title: actionTitle,
                    url: actionUrl
                },
                image,
                textColor: inAppNotificationParams.textColor,
                backgroundColor: inAppNotificationParams.backgroundColor
            }

            const notificationMeta: NotificationMeta = {
                notificationId: notificationWrapper.notification.notificationId,
                // App id defaults to curefit. IAN app id works in upper case
                // Messy logic but legacy reasons
                appId: appId ? appId.toUpperCase() : Constants.APP_ID_CUREFIT.toUpperCase(),
                taskId: notificationWrapper.notification.taskId,
                userId: notificationWrapper.notification.userId,

                /*
                 * (P.S. Warning, hack ahead)
                 * Legacy code expects IST timezone, set as default by old version of TimeUtil.
                 * Updated version of TimeUtil does not set any timezone as default.
                 * Have tried keep it consistent with old code
                 *
                 * Old TimeUtil code:
                 * https://github.com/curefit/base-utils/blob/91b0bc6a47b841595c49834313b1572cdb5fab1b/src/TimeUtil.ts#L11
                 * Old usage:
                 * expiry: expiry ? TimeUtil.parseDateTime(expiry) :
                 * TimeUtil.parseDate(TimeUtil.addDays(TimeUtil.todaysDateWithTimezone(), 10))
                 */

                expiry: expiry ? TimeUtil.parseDateTime(expiry, TimeUtil.IST_TIMEZONE) :
                    TimeUtil.parseDate(
                        TimeUtil.addDays(TimeUtil.IST_TIMEZONE,
                            TimeUtil.todaysDateWithTimezone(TimeUtil.IST_TIMEZONE), 10),
                        TimeUtil.IST_TIMEZONE),
                scheduledAt: scheduledAt ? TimeUtil.parseDateTime(scheduledAt, TimeUtil.IST_TIMEZONE) : new Date(),
                type: creative.inAppNotificationParams.type,
                vertical: campaign.vertical,
                state: "RECEIVED",
                dataBlob: JSON.stringify(data)
            }

            if (dryRun) {
                const yesterday = new Date()
                yesterday.setDate(yesterday.getDate() - 1)
                notificationMeta.expiry = yesterday
            }

            this.logger.debug("Notification meta is", JSON.stringify(notificationMeta))
            return notificationMeta
        }

        public async getNotificationMetaCountsForUser(userId: string, appId: string,
                                                      expiryAfter?: string): Promise<any> {
            return this.basicNotificatioMetaDao.getNotificationsMetasCountsForUser(userId, appId, expiryAfter)
        }

        public async getNotificationMetasForUserPaginated(userId: string, appId: string,
                                                          states: NotificationState[], pageSize: number,
                                                          pageNumber: number): Promise<PaginatedResults<NotificationMeta>> {
            if (!userId || !appId) {
                throw new DataError("Missing userId or appId")
            }

            const limit = pageSize || Constants.NOTIFICATION_META_PAGE_SIZE
            const skip = pageNumber ? pageNumber * pageSize : 0
            return this.basicNotificatioMetaDao.getNotificationMetasForUser(userId, appId, states, limit, skip)
        }

        public async getActiveNotificationMetasForUser(userId: string, appId: string): Promise<NotificationMeta[]> {
            let notificationMetas = await this.notificationMetaCache.get(IANNotificationKey.from(userId, appId))
            if (!notificationMetas) {
                notificationMetas = await this.basicNotificatioMetaDao.getActiveNotificationMetasForUser(userId, appId)
                this.notificationMetaCache.upsert(IANNotificationKey.from(userId, appId), notificationMetas, Constants.IAN_REDIS_KEY_EXPIRY)
            }
            notificationMetas.filter((nm) => nm.expiry > new Date())
            return notificationMetas
        }

        public async updateNotificationMetas(notificationIds: string[],
                                             updateProps: {[key: string]: any}): Promise<boolean> {
            this.deleteCacheNotificationEntry(notificationIds)
            return this.basicNotificatioMetaDao.updateNotificationMetaBulk(notificationIds, updateProps)
        }

        public updatePropsByNotificationId(notificationId: string, props: {[key: string]: any}): Promise<boolean> {
            this.deleteCacheNotificationEntry([ notificationId ])
            return this.basicNotificatioMetaDao.updatePropsByNotificationId(notificationId, props)
        }

        private async deleteCacheNotificationEntry(notificationIds: string[]): Promise<boolean> {
            const notifications = await this.basicNotificatioMetaDao.getBulk(notificationIds)
            await Promise.all(notifications.map(notification => {
                this.notificationMetaCache.delete(IANNotificationKey.from(notification.userId, notification.appId))
            }))
            return true
        }
    }

    return InAppNotificationHelper
}
