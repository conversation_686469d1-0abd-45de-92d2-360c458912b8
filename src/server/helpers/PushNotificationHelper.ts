import {BASE_TYPES, ILogger} from "@curefit/base"
import {NotificationWrapper, P<PERSON>reative, UserNotificationStatus} from "../../iris-common"
import {IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {DataError, DataErrorV2, NotFoundErrorV2} from "@curefit/error-client"
import {DEVICE_MODELS_TYPES, IDeviceReadonlyDao} from "@curefit/device-models"
import { Action } from "@curefit/apps-common"
import {Device, DeviceInfo} from "@curefit/device-common"

import {Container, inject, injectable} from "inversify"
import * as mustache from "mustache"
import * as _ from "lodash"

import {MustacheUtils} from "../utils/MustacheUtils"
import {MetricUtils, ProfilerType} from "../utils/MetricUtils"
import TYPES from "../ioc/types"
import PushNotificationService, { NotificationPayload } from "../service/PushNotificationService"
import {BillingUtils} from "../utils/BillingUtils"
import Constants from "../constants"
import IdentityService from "../service/IdentityService"
import HandlebarsUtils from "../utils/HandlebarsUtils"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import NotificationLogService from "../service/NotificationLogService"
import PushNotificationTokenNotRegisteredPublisher from "../publishers/PushNotificationTokenNotRegisteredPublisher"
import {CommonUtils} from "../utils/CommonUtils"
import TagUtils from "../utils/TagUtils"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import { DeviceDetail } from "@curefit/user-common"

export interface IPushNotificationHelper {
    sendNotifications(creative: PNCreative, notifications: NotificationWrapper[], isTransactional: boolean,
                      appId: string, dryRun?: boolean): Promise<UserNotificationStatus[]>
}

export function PushNotificationHelperFactory(kernel: Container) {

    @injectable()
    class PushNotificationHelper implements IPushNotificationHelper {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,
            @inject(TYPES.PushNotificationService) private pushNotificationService: PushNotificationService,
            @inject(TYPES.IdentityService) private identityService: IdentityService,
            @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
            @inject(TYPES.NotificationLogService) private notificationLogService: NotificationLogService,
            @inject(TYPES.PushNotificationTokenNotRegisteredPublisher) private pushNotificationTokenNotRegisteredPublisher: PushNotificationTokenNotRegisteredPublisher,
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
        ) { }

        public async sendNotifications(creative: PNCreative, notificationWrappers: NotificationWrapper[],
                                       isTransactional: boolean, appId?: string,
                                       dryRun?: boolean): Promise<UserNotificationStatus[]> {
            let tagLocations = [creative.pushNotificationParams.action, creative.title, creative.body]
            if (creative.pushNotificationParams.image) {
                tagLocations.push(creative.pushNotificationParams.image)
            }

            const allTags = TagUtils.allTagsUsed(tagLocations)
            const resp: UserNotificationStatus[] = []

            const timerContext = this.metricUtils.startProfilingTimer(
                ProfilerType.PERF, "PushNotificationHelper::sendNotifications", notificationWrappers.length)

            await Promise.all(notificationWrappers.map(async notificationWrapper => {

                let sent = false
                let failReason
                try {
                    const response = await this.sendNotification(
                        creative, notificationWrapper, allTags,
                        appId || notificationWrapper.userContext.appId, dryRun, isTransactional)
                    sent = response.success
                    if (sent && response.messageId) {
                        notificationWrapper.notification.externalId =
                            Constants.FIREBASE_EXTERNALID_PREFIX + response.messageId
                        notificationWrapper.notification.sentAt = new Date()
                    }
                    failReason = response.failReason
                } catch (e) {
                    sent = false
                    failReason = _.get(e, "meta", "SEND_FAIL")
                    this.logger.debug(`Unable to send push notification for ` +
                        `${notificationWrapper.notification.notificationId} due to ${e.toString()}`)
                }

                if (sent) {
                    notificationWrapper.notification.status = "SENT"
                    notificationWrapper.notification.billableUnits = dryRun ? 0 : BillingUtils.getPNUnitsConsumed()
                } else {
                    notificationWrapper.notification.failReason = failReason
                    notificationWrapper.notification.status = "FAILED"
                }

                resp.push({
                    userId: notificationWrapper.userContext.userId,
                    creativeId: creative.creativeId,
                    notificationId: notificationWrapper.notification.notificationId,
                    sent,
                    failedReason: notificationWrapper.notification.failReason
                })

            }))

            this.metricUtils.endProfilingTimer(timerContext)
            return Promise.resolve(resp)
        }

        // TODO: Refactor this
        async getActiveDevice(userId: string, appId?: string, minAppVersion?: number,
                              maxAppVersion?: number, osName?: string): Promise<{ activeDevice: any, deviceId: string }> {

            if (
                appId.toLowerCase() === Constants.APP_ID_TEAMFIT ||
                appId.toLowerCase() === Constants.APP_ID_CULTAPP
            ) {
                let devices = await this.identityService.fetchDevices(appId, userId)
                this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                    `devices: ${JSON.stringify(devices)}`)

                // Filter out devices
                devices = devices.filter(device => {
                    // Min app version checks
                    if (minAppVersion && device.appVersion < minAppVersion ) { return false }

                    // Ignore if PN token not found
                    if (!device.pushNotificationToken) { return false }
                    return true
                })

                let activeDevice: DeviceInfo
                let deviceId: string

                if (devices.length !== 0) {
                    activeDevice = devices[devices.length - 1]
                    deviceId = _.get(activeDevice, "deviceId", null)
                }

        return { activeDevice, deviceId }
            } else if (
                appId.toLowerCase() === Constants.APP_ID_CUREFIT ||
                appId.toLowerCase() === Constants.APP_ID_LIVEFIT ||
                appId.toLowerCase() === Constants.APP_ID_SUGARFIT ||
                appId.toLowerCase() === Constants.APP_ID_ULTRAFIT ||
                appId.toLowerCase() === Constants.APP_ID_CULTWATCH
            ) {
                let devices: DeviceDetail[] = await this.userService.getLoggedInDevicesForUserRegisterDate(userId, appId.toLowerCase(), "50000000000000") // Using a very large value to get all devices

                let deviceIds = devices.map((device) => { return device.deviceId })
                let isUserLoggedIn: boolean = false

                this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                    `devices: ${JSON.stringify(deviceIds)}`)

                devices = devices.filter((device: DeviceDetail) => {
                    this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                        `Checking device ${JSON.stringify(device)}`)

                    // Remove device if state is set to `DeletedDueToLoginIssue`.
                    // CF API sets this flag to fix an old login flow issue
                    if (_.get(device, "state", "") === "DeletedDueToLoginIssue") {
                        this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                            `device ${device.deviceId} rejected due to ` +
                            `state = DeletedDueToLoginIssue`)
                        return false
                    }

                    if (device.uninstalled || !device.lastModifiedOn) {
                        this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                            `device ${device.deviceId} rejected due to ` +
                            `device.uninstalled || !device.lastModifiedOn`)
                        return false
                    }

                    // Push notifications go to only non browser devices
                    if (device.osName.toLowerCase() === "browser") {
                        this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                            `device ${device.deviceId} rejected due to ` +
                            `device.activeDevice.osName.toLowerCase() === "browser"`)
                        return false
                    }

                    // Creative specific Os check
                    if (!!osName && device.osName.toLowerCase() !== osName.toLowerCase()) {
                        this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                            `device ${device.deviceId} rejected due to ` +
                            `device.osName.toLowerCase() !== osName"`)
                        return false
                    }

                    // Filter out devices with no push notification token
                    if (!device.pushNotificationToken) {
                        this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                            `device ${device.deviceId} rejected due to ` +
                            `!device.activeDevice.pushNotificationToken`)
                        return false
                    }

                    // Filter out tokens which FCM has rejected earlier
                    // To ensure backward compatibility, we check for the value to equal NotRegistered
                    // Registered and undefined are both okay
                    if (device.pushNotificationTokenState === "NotRegistered") {
                        this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                            `device ${device.deviceId} rejected due to ` +
                            `device.activeDevice.pushNotificationTokenState === "NotRegistered"`)
                        return false
                    }

                    // Creative specific app version checks
                    if (minAppVersion && +device.appVersion < minAppVersion) {
                        this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                            `device ${device.deviceId} rejected due to ` +
                            `minAppVersion && device.activeDevice.appVersion < minAppVersion`)
                        return false
                    }
                    if (maxAppVersion && +device.appVersion > maxAppVersion) {
                        this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                            `device ${device.deviceId} rejected due to ` +
                            `maxAppVersion && device.activeDevice.appVersion > maxAppVersion`)
                        return false
                    }

                    if (appId && device.tenant !== appId) {
                        this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                            `device ${device.deviceId} rejected due to ` +
                            `appId && device.activeDevice.tenant !== appId. ${appId} ${device.tenant}`)
                        return false
                    }

                    if (device.isLoggedIn) {
                        this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                            `found logged in device with id: ${device.deviceId}`)
                        isUserLoggedIn = true
                    }

                    return true
                })

                deviceIds = devices.map((device) => { return device.deviceId })

                this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                    `filtered devices: ${JSON.stringify(deviceIds)}`)

                let activeDevice: DeviceDetail
                let deviceId: string

                if (devices.length === 0) {
                    this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                        `No devices found`)
                    return { activeDevice, deviceId }
                }

                // if user is logged in on any device, remove all logged out devices
                if (isUserLoggedIn) {
                    devices = devices.filter( (device: DeviceDetail) => {
                        if (!device.isLoggedIn) {
                            this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                                `device ${device.deviceId} rejected due to isLoggedIn check`)
                            return false
                        }
                        return true
                    })
                }

                let latestDate: number = devices[0].lastModifiedOn
                this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                    `Initial latest date: ${latestDate}`)

                devices.forEach(device => {
                    this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                        `Latest data: ${latestDate} updateDate: ${device.lastModifiedOn}`)
                    if (latestDate <= device.lastModifiedOn) {
                        this.logger.debug(`PushNotificationHelper::getActiveDevice userId: ${userId} ` +
                            `Updating latest device to ${device.deviceId}`)
                        latestDate = device.lastModifiedOn
                        activeDevice = device
                        deviceId = device.deviceId
                    }
                })
                return { activeDevice, deviceId }
            } else {
                const errorMessage = `PushNotificationHelper::getActiveDevice unknown app id ${appId}`
                this.logger.error(errorMessage)
                this.rollbarService.sendError(new Error(errorMessage))
                return { activeDevice: null, deviceId: null }
            }
        }

        private async sendNotification(creative: PNCreative, notificationWrapper: NotificationWrapper,
                                       allTags: string[], appId?: string, dryRun?: boolean,
                                       isTransactional?: boolean):
                                       Promise<{ success: boolean, messageId?: string, failReason?: string}> {
            if (!notificationWrapper.userContext || !notificationWrapper.userContext.userId) {
                this.logger.error(`Did not find user id for ${notificationWrapper.notification.notificationId}`)
                throw new DataError("user id mandatory")
            }


            // Defaults to mustache
            const pushNotificationParams = creative.pushNotificationParams
            const isHandlebars =
                _.get(pushNotificationParams, "templateEngine", "mustache") === "handlebars"

            const timerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF, "getActiveDevice")
            const { activeDevice, deviceId } = await this.getActiveDevice(notificationWrapper.userContext.userId, appId,
                creative.pushNotificationParams.minAppVersion, creative.pushNotificationParams.maxAppVersion, creative.pushNotificationParams.osName)
            this.metricUtils.endProfilingTimer(timerContext)

            if (!activeDevice) {
                this.logger.debug(`User ${notificationWrapper.userContext.userId} for ` +
                    `${notificationWrapper.notification.notificationId} does not have compatible active device `)
                throw new NotFoundErrorV2("User does not have compatible app installed",
                    { meta: "INCOMPATIBLE_DEVICE"})
            }
            notificationWrapper.userContext.deviceId = deviceId
            notificationWrapper.notification.deviceInfo = {
                deviceId,
                advertiserId: activeDevice.advertiserId,
                appId: activeDevice.appId,
                appVersion: activeDevice.appVersion ? activeDevice.appVersion.toString() : undefined,
                deviceModel: activeDevice.deviceModel,
                osName: activeDevice.osName,
                osVersion: activeDevice.osVersion
            }

            // Mustache validation needs to be done prior to rendering
            if (!isHandlebars && !MustacheUtils.contextHasAllTags(allTags, notificationWrapper.userContext.tags)) {
                this.logger.error(`Missing data for tags for notification: ` +
                    `${notificationWrapper.notification.notificationId} ` +
                    `tags: ${JSON.stringify(notificationWrapper.userContext.tags)}`)
                throw new DataErrorV2("Error in templating. Using mustache", { meta: "TEMPLATE_FAIL"})
            }

            let action, title, body, image, subTitle, smallIcon, groupKey, overwriteKey, meta, icon
            const actions: Action[] = []
            const renderLibrary = isHandlebars ? HandlebarsUtils : MustacheUtils
            try {
                action = TagUtils.render(pushNotificationParams.action, notificationWrapper.userContext.tags, renderLibrary)
                title = TagUtils.render(creative.title, notificationWrapper.userContext.tags, renderLibrary)
                body = TagUtils.render(creative.body, notificationWrapper.userContext.tags, renderLibrary)
                icon = TagUtils.render(pushNotificationParams.icon, notificationWrapper.userContext.tags, renderLibrary)
                if (pushNotificationParams.image) {
                    image = TagUtils.render(pushNotificationParams.image, notificationWrapper.userContext.tags, renderLibrary)
                }
                if (pushNotificationParams.subTitle) {
                    subTitle = TagUtils.render(pushNotificationParams.subTitle,
                        notificationWrapper.userContext.tags, renderLibrary)
                }
                if (pushNotificationParams.smallIcon) {
                    smallIcon = TagUtils.render(pushNotificationParams.smallIcon,
                        notificationWrapper.userContext.tags, renderLibrary)
                }
                if (pushNotificationParams.groupKey) {
                    groupKey = TagUtils.render(pushNotificationParams.groupKey,
                        notificationWrapper.userContext.tags, renderLibrary)
                }
                if (pushNotificationParams.overwriteKey) {
                    overwriteKey = TagUtils.render(pushNotificationParams.overwriteKey,
                        notificationWrapper.userContext.tags, renderLibrary)
                }
                if (pushNotificationParams.actions) {
                    for (const action of pushNotificationParams.actions) {
                        actions.push({
                            title: TagUtils.render(action.title, notificationWrapper.userContext.tags, renderLibrary),
                            url: TagUtils.render(action.url, notificationWrapper.userContext.tags, renderLibrary),
                            actionType: action.actionType,
                            meta: action.meta
                        })
                    }
                }
                if (pushNotificationParams.chronometerProperties?.overrideTimestampValue) {
                    creative.pushNotificationParams.chronometerProperties.chronometerTimestamp = Number(TagUtils.render(pushNotificationParams.chronometerProperties.overrideTimestampValue, notificationWrapper.userContext.tags, renderLibrary))
                }
            } catch (error) {
                this.logger.error(`PushNotificationHelper::sendNotification Templating error; ` +
                    `notification: ${notificationWrapper.notification.notificationId} ` +
                    `Error: ${error.toString()}. Using render library: ${renderLibrary.typeName} ` +
                    `tags: ${JSON.stringify(notificationWrapper.userContext.tags)}`)
                throw new DataErrorV2("Error in templating", { meta: "TEMPLATE_FAIL"})
            }

            if (CommonUtils.isEnvironmentStage()) {
                title = "[STAGE]" + title
            }

            if (!!notificationWrapper.userContext.metaData) {
                meta = notificationWrapper.userContext.metaData
            }

            const userId = notificationWrapper.userContext.userId
            const analyticData = {
                notificationId: notificationWrapper.notification.notificationId,
                creativeId: notificationWrapper.notification.creativeId,
                campaignId: notificationWrapper.notification.campaignId,
                notificationType: creative.pushNotificationParams.notificationType ?
                    creative.pushNotificationParams.notificationType : "DEFAULT",
                minAppVersion: creative.pushNotificationParams.minAppVersion ?
                    "" + creative.pushNotificationParams.minAppVersion : undefined,
            }

            const chronometerProperties = {
                showChronometer: !!creative.pushNotificationParams.chronometerProperties ? creative.pushNotificationParams.chronometerProperties.showChronometer : false,
                chronometerDirection: !!creative.pushNotificationParams.chronometerProperties ? creative.pushNotificationParams.chronometerProperties.chronometerDirection : "down",
                chronometerTimestamp: !!creative.pushNotificationParams.chronometerProperties ? creative.pushNotificationParams.chronometerProperties.chronometerTimestamp : undefined
            }

            if (chronometerProperties.chronometerTimestamp === -1) {
                chronometerProperties.chronometerTimestamp = (new Date()).getTime()
            }

            let timeoutAfter = creative.pushNotificationParams.timeoutAfter || Constants.PUSH_NOTIFICATION_AUTO_COLLAPSE_TIME_IN_MILLISECONDS

            // If the notification is a down counter, we want to expire it before it reaches 00:00,
            // otherwise it starts to show negative values
            if (timeoutAfter === Constants.PUSH_NOTIFICATION_AUTO_COLLAPSE_TIME_IN_MILLISECONDS  &&
                chronometerProperties?.showChronometer &&
                !!chronometerProperties.chronometerTimestamp &&
                chronometerProperties.chronometerDirection === "down") {
                timeoutAfter = chronometerProperties.chronometerTimestamp - Date.now() - 1000
            }



            const notificationPayload: NotificationPayload = {
                title,
                subTitle,
                body,
                smallIcon,
                actions,
                groupKey,
                sound: creative.pushNotificationParams.sound ? creative.pushNotificationParams.sound : "default",
                android_channel_id: creative.pushNotificationParams.androidChannelId,
                icon: icon,
                image: pushNotificationParams.image ? image : undefined,
                data: { action, analyticData},
                payload: { action, analyticData, meta},
                sortKey: creative.pushNotificationParams.sortKey,
                // People are stupid, expect stupid things.
                // Do all validations, do not allow empty strings
                overwriteKey: overwriteKey && overwriteKey.length > 0 ? overwriteKey : undefined,
                category: creative.pushNotificationParams.category,
                silentNotification: !!creative.pushNotificationParams.silentNotification,
                chronometerProperties: chronometerProperties,
                timeoutAfter: timeoutAfter,
                ongoing: creative.pushNotificationParams.ongoing,
            }

            // Duplicate notification and rate limit checks
            if (await this.notificationLogService.isDuplicate({ title, body }, userId, dryRun)) {
                throw new DataErrorV2("Duplicate notification", { meta: "DUPLICATE"})
            } else if (await this.notificationLogService.throttleNotification(
                userId, "PUSH_NOTIFICATION", isTransactional, dryRun)) {
                throw new DataErrorV2("User rate limit", { meta: "THROTTLED"})
            }

            // If expiry exists, derive TTL from expiry.
            // Else derive from createdAt
            let timeElapsed = 0
            if (notificationWrapper.notification.createdAt) {
                timeElapsed = (Date.now() - notificationWrapper.notification.createdAt.getTime()) / 1000
            } else {
                this.logger.info(`PushNotificationHelper::sendNotification createdAt missing for ` +
                    `${notificationWrapper.notification.notificationId}. ` +
                    `notification: ${JSON.stringify(notificationWrapper.notification)}`)
            }

            let timeToLiveInSeconds =
                notificationWrapper.userContext.expiry ?
                    (new Date(notificationWrapper.userContext.expiry).getTime() - Date.now()) / 1000 :
                (creative.pushNotificationParams.timeToLiveInSeconds ||
                    Constants.PUSH_NOTIFICATION_DEFAULT_TIME_TO_LIVE_IN_SECONDS) - timeElapsed

            timeToLiveInSeconds = Math.round(timeToLiveInSeconds)
            timeToLiveInSeconds = dryRun ? Constants.PUSH_NOTIFICATION_MAX_TIME_TO_LIVE_IN_SECONDS :
                Math.min(timeToLiveInSeconds, Constants.PUSH_NOTIFICATION_MAX_TIME_TO_LIVE_IN_SECONDS)

            if (timeToLiveInSeconds <= 0) {
                throw new DataErrorV2("Expired", { meta: "EXPIRED"})
            }

            const res = await this.pushNotificationService.sendNotification(userId, notificationPayload, activeDevice,
                notificationWrapper.notification.notificationId, isTransactional, timeToLiveInSeconds, appId, dryRun)

            if (!res.success &&
                (
                    // Not registered token error via legacy api
                    res.failReason === `${Constants.FCM_FAIL_REASON_PREFIX}` + `_` +
                                       `${Constants.FCM_NOT_REGISTERED_ERROR}` ||
                    // Not registered token error via HTTP v1 api
                    res.failReason === `${Constants.FCM_ADMIN_FAIL_REASON_PREFIX}` + `_` +
                                       `${Constants.FCM_ADMIN_NOT_REGISTERED_ERROR}`
                )
            ) {
                this.logger.info(`Publishing message not registered`)
                this.pushNotificationTokenNotRegisteredPublisher.publish(appId, deviceId, userId,
                    activeDevice.pushNotificationToken, new Date())
            }

            // Log notification for duplicate checks and throttling
            res.success && (await this.notificationLogService.logNotification(
                { title, body }, userId, "PUSH_NOTIFICATION", isTransactional, dryRun))
            return res
        }

    }

    return PushNotificationHelper
}
