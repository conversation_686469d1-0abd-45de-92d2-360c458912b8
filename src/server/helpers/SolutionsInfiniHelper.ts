import {inject, injectable} from "inversify"
import {BASE_TYPES, FetchUtil, ILogger, UrlUtil} from "@curefit/base"
import Constants from "../constants"
import TYPES from "../ioc/types"
import {MetricUtils, ProfilerType} from "../utils/MetricUtils"
import {
    C2CServiceAccount,
    ClickToCallCreative,
    ClickToCallReport,
    CreativeType, InboundCallReport,
    NotificationStatus
} from "../../iris-common"
import * as _ from "lodash"
import * as mustache from "mustache"
import NotificationAttemptHelper from "./NotificationAttemptHelper"
import {ClickToCallProviderResponse} from "./ClickToCallNotificationHelper"
import {IBasicNotificationDao, IRIS_MODELS_TYPES} from "../../iris-models"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {CommonUtils} from "../utils/CommonUtils"

const fetch = require("node-fetch")

interface SolInfiniStatusResponse {
    status?: string,
    message?: string,
    data?: any
}

interface ClickToCallSolInfiniResponse {
    status: string, // "200"
    message: string,
    data?: {
        id?: string
    }
}

@injectable()
export class SolutionsInfiniHelper {

    private readonly clickToCallLogUrl: string = ""
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(BASE_TYPES.UrlUtil) private urlUtil: UrlUtil,
        @inject(BASE_TYPES.FetchUtil) private fetchUtil: FetchUtil,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.NotificationAttemptHelper) private notificationAttemptHelper: NotificationAttemptHelper,
        @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
    ) {
        this.clickToCallLogUrl = CommonUtils.isEnvironmentProductionOrAlpha() ?
            Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.LOG_URL.PRODUCTION :
            Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.LOG_URL.STAGE
    }

    public async makeClickToCall(callerNumber: string, receiverNumber: string, notificationId: string, country: string, isConfidential?: boolean, dryRun?: boolean, serviceAccount?: C2CServiceAccount, callerId?: string) {
        let response: ClickToCallProviderResponse
        if ( serviceAccount === Constants.C2C_SERVICE_ACCOUNT.SUGARFIT && isConfidential) {
            const errorMessage = "No api_key in Sugarfit for Confidential calls"
            const error = new Error(errorMessage)
            this.logger.error(errorMessage)
            this.rollbarService.sendError(error)
            response = {status : "failed", failReason: errorMessage}
            return response
        }
        const callbackUrl = encodeURIComponent(mustache.render(Constants.getSolInfiniClickToCallBackUrl(isConfidential), {"id": notificationId}))
        const apiKey = Constants.getSolInfiniClickToCallApiKey(callerNumber, receiverNumber, isConfidential, null, null, serviceAccount)
        let callUrl = this.urlUtil.constructUrl(Constants.getSolInfiniClickToCallApiUrl(), "", [
            { key: "method", value: "dial.click2call" },
            { key: "output", value: "json" },
            { key: "return", value: "1" },
            { key: "caller", value:  callerNumber},
            { key: "receiver", value:  receiverNumber},
            { key: "api_key", value: apiKey },
            { key: "callback", value: callbackUrl },
            { key: "caller_id", value: callerId }
        ])

        this.logger.info("SolutionsInfiniHelper::makeClickToCall call url: " + callUrl)
        if (dryRun) {
            response = { status: "success", callId: Constants.SOLUTIONS_INFINI + "_DRY_RUN"}
        } else {
            try {
                const apiLatencyTimerContext =
                    this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "Sol Infini C2C")
                const res = await fetch(callUrl, this.fetchUtil.get())
                this.metricUtils.endProfilingTimer(apiLatencyTimerContext)

                const clickToCallSolInfiniResponse: ClickToCallSolInfiniResponse =
                    await this.fetchUtil.parseResponse<ClickToCallSolInfiniResponse>(res)
                this.logger.info("notificationId : "  + notificationId + " , SOLUTIONS_INFINI CLICK_TO_CALL response: "
                    + JSON.stringify(clickToCallSolInfiniResponse))
                const sent: boolean = (clickToCallSolInfiniResponse.status === "200" && clickToCallSolInfiniResponse.message === "OK")
                if (sent) {
                    response = {
                        status: "success",
                        callId: (clickToCallSolInfiniResponse.data && clickToCallSolInfiniResponse.data.id) ? (Constants.SOLUTIONS_INFINI + "_" + clickToCallSolInfiniResponse.data.id) : "none"
                    }
                } else {
                    await this.notificationAttemptHelper.createNotificationAttempt(notificationId,
                        Constants.SOLUTIONS_INFINI, clickToCallSolInfiniResponse.status,
                        clickToCallSolInfiniResponse.data ? clickToCallSolInfiniResponse.data.id : null,
                        JSON.stringify({status: clickToCallSolInfiniResponse.status, message: clickToCallSolInfiniResponse.message}))
                    response = {status : "failed", failReason: clickToCallSolInfiniResponse.message || "SEND_FAIL"}
                    this.logger.info("failed to make click to call using solutions_infini with message: "
                        + clickToCallSolInfiniResponse.message)
                }
            } catch (error) {
                this.logger.error(error)
                await this.notificationAttemptHelper.createNotificationAttempt(notificationId, Constants.SOLUTIONS_INFINI,
                    "FAILED", "none", JSON.stringify({ error }))
                response = { status : "failed" }
            }
        }
        return response
    }

    public async getClickToCallLog(id: string, creative: ClickToCallCreative) {
        const headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        let serviceAccount: C2CServiceAccount = !!creative.clickToCallParams && !!creative.clickToCallParams.serviceAccount ? creative.clickToCallParams.serviceAccount : Constants.C2C_SERVICE_ACCOUNT.CUREFIT
        const callUrl = this.urlUtil.constructUrl(this.clickToCallLogUrl, "", [
            { key: "method", value: "dial.c2cstatus" },
            { key: "api_key", value: Constants.getSolInfiniClickToCallApiKey(null, null, Constants.isConfidential(creative), null, true, serviceAccount) },
            { key: "id", value: id },
            { key: "format", value: "JSON" }
        ])

        return fetch(callUrl, this.fetchUtil.get( headers)).then((response: any) => {
            return this.fetchUtil.parseResponse<SolInfiniStatusResponse>(response)
        }).then((r: any) => {
            return r
        }).catch((err: any) => {
            this.logger.error(err)
            return {
                message: "FAIL"
            }
        })
    }

    public async isClickToCallSuccess(call_id: string, creative: ClickToCallCreative): Promise<boolean> {
        const response: SolInfiniStatusResponse = await this.getClickToCallLog(call_id, creative)
        if (response.status === "200" && response.message === "OK" && this.isCallSuccess(response.data)) return true
        return false
    }

    private isCallSuccess(data: any): boolean {
        if (data && data[0]) {
            if (data[0].status === "ANSWER" || data[0].status === "BUSY" || data[0].status === "CANCEL" || data[0].status === "NOANSWER") return true
        }
        return false
    }

    public async clickToCallReportMapper(clickToCallProviderReport: any) {
        let clickToCallReport: ClickToCallReport
        clickToCallReport = {
            caller: clickToCallProviderReport.caller,
            receiver: clickToCallProviderReport.receiver,
            status: this.clickToCallStatusMapper(clickToCallProviderReport.status, clickToCallProviderReport.status1, clickToCallProviderReport.status2),
            statusCaller: this.clickToCallLegStatusMapper(clickToCallProviderReport.status1),
            statusReceiver: this.clickToCallLegStatusMapper(clickToCallProviderReport.status2),
            startTime: new Date(clickToCallProviderReport.starttime * 1000),
            duration: clickToCallProviderReport.duration,
            recordPath: clickToCallProviderReport.recordpath,
            externalId: clickToCallProviderReport.callerid,
            metadata: JSON.stringify(clickToCallProviderReport)
        }
        return {
            clickToCallReport,
            rawCallerStatus: clickToCallProviderReport.status1,
            rawReceiverStatus: clickToCallProviderReport.status2
        }
    }

    public async createInboundCallReport(inboundCallProviderReport: any) {

        const dialStatus = inboundCallProviderReport.dialstatus
        const isCallConnected = !_.isEmpty(inboundCallProviderReport.recordpath)
        const doesAgentHangUp = !_.isEmpty(inboundCallProviderReport.hangupfirst)
        const callerStatus = this.inboundCallCallerStatusMapper(dialStatus, isCallConnected)
        const agentStatus = this.inboundCallAgentStatusMapper(dialStatus, doesAgentHangUp)

        const inboundCallReport: InboundCallReport = {
            caller: inboundCallProviderReport.caller,
            duration: inboundCallProviderReport.duration,
            receiver: inboundCallProviderReport.receiver,
            recordPath: inboundCallProviderReport.recordpath,
            startTime: inboundCallProviderReport.starttime,
            status: this.inboundCallStatusMapper(callerStatus, agentStatus),
            statusCaller: callerStatus,
            statusReceiver: agentStatus,
            virtualNumber: inboundCallProviderReport.virtualnumber,
            metadata: JSON.stringify(inboundCallProviderReport)
        }

        return {
            inboundCallReport,
            rawCallerStatus: callerStatus,
            rawReceiverStatus: agentStatus
        }
    }

    private inboundCallStatusMapper(callerStatus: string, receiverStatus: string) {
        if (callerStatus === "CONNECTED") {
            if (receiverStatus === "ANSWER") return "CONNECTED"
            if (receiverStatus === "BUSY") return "RINGED"
            if (receiverStatus === "DISCONNECTED") return "ATTEMPTED"
            if (receiverStatus === "FAILED") return "FAILED"
            if (receiverStatus === "MISSED") return "RINGED"
        }
        if (callerStatus === "DISCONNECTED_BEFORE_RINGING" || callerStatus === "DISCONNECTED_AFTER_RINGING") {
            return `USER_${callerStatus}`
        }
        return "FAILED"
    }

    private inboundCallAgentStatusMapper(dialStatus: string, doesAgentHangUp: boolean) {
        if (dialStatus === "ANSWER") return "ANSWER"
        if (dialStatus === "BUSY") return "BUSY"
        if (dialStatus === "NOANSWER") return "MISSED"
        if (dialStatus === "CANCEL" && doesAgentHangUp) return "DISCONNECTED"
        if (dialStatus === "FAILED") return "FAILED"
        return "FAILED"
    }

    private inboundCallCallerStatusMapper(dialStatus: string, isCallConnected: boolean) {
        if (_.isEmpty(dialStatus)) return "DISCONNECTED_BEFORE_RINGING"
        if (dialStatus === "FAILED") return "FAILED"
        if (dialStatus === "CANCEL" && !isCallConnected) return "DISCONNECTED_AFTER_RINGING"
        return "CONNECTED"
    }

    public async notificationStatusMapper(notificationType: CreativeType, providerStatus: string, metadata?: string): Promise<NotificationStatus> {
        switch (notificationType) {
            case "SMS":
                return this.smsStatusMapper(providerStatus)
            case "OBD":
                return this.obdStatusMapper(providerStatus)
            default:
                this.logger.error("unidentified notificationType: " + notificationType + " for serviceProvider Solution Infini")
                return "FAILED"
        }
    }

    private clickToCallLegStatusMapper(legStatus: string) {
        if (legStatus === "ANSWER") return "ANSWER"
        if (legStatus === "BUSY") return "BUSY"
        if (legStatus === "CANCEL") return "DISCONNECTED"
        if (legStatus === "FAILED") return "FAILED"
        if (legStatus === "CONGESTION") return "FAILED"
        if (legStatus === "NOANSWER") return "MISSED"
        return "FAILED"
    }

    private clickToCallStatusMapper(status: string, callerStatus: string, receiverStatus: string) {
        if (!_.isUndefined(status) && status.startsWith("FAILED")) return "FAILED"
        if (callerStatus === "ANSWER") {
            if (receiverStatus === "ANSWER") return "CONNECTED"
            if (receiverStatus === "BUSY") return "RINGED"
            if (receiverStatus === "CANCEL") return "ATTEMPTED"
            if (receiverStatus === "FAILED") return "FAILED"
            if (receiverStatus === "CONGESTION") return "FAILED"
            if (receiverStatus === "NOANSWER") return "RINGED"
        }
        if (callerStatus === "BUSY" || callerStatus === "NOANSWER" || callerStatus === "CANCEL") return "ATTEMPTED"
        if (callerStatus === "FAILED" || callerStatus === "CONGESTION") return "FAILED"

        return "FAILED"
    }

    private smsStatusMapper(providerStatus: string): NotificationStatus {
        if (providerStatus === "DELIVRD") return "DELIVERED"
        const failedStatusList = Constants.SOLUTIONS_INFINI_SMS_CONFIG.STATUS_LIST.FAILED
        if (failedStatusList.includes(providerStatus)) return "FAILED"
        const redFlagStatusList = Constants.SOLUTIONS_INFINI_SMS_CONFIG.STATUS_LIST.RED_FLAG
        if (redFlagStatusList.includes(providerStatus)) {
            this.rollbarService.sendError(new Error(`red flag provider status: ${providerStatus} , for SMS send via Solution Infini. Please take appropriate action!`))
            this.logger.error("red flag provider status: " + providerStatus + " , for SMS send via Solution Infini. Please take appropriate action!")
            return "FAILED"
        }
        else {
            this.rollbarService.sendError(new Error(`unidentified provider status: ${providerStatus} , for SMS send via Solution Infini`))
            this.logger.error("unidentified provider status: " + providerStatus + " for SMS send via Solution Infini")
            return "FAILED"
        }
    }

    private async obdStatusMapper(providerStatus: string): Promise<NotificationStatus> {
        if (providerStatus === "SENT") return "SENT"
        if (providerStatus === "FAILED") return "FAILED"
        if (providerStatus === "COMPLETED") return "DELIVERED"
        if (providerStatus === "BUSY" || providerStatus === "NOANSWER" || providerStatus === "CONGESTION" || providerStatus === "TIMEOUT") return "FAILED"
        this.rollbarService.sendError(new Error(`unidentified provider status: ${providerStatus} , for OBD send via Solution Infini`))
        this.logger.error("unidentified provider status: " + providerStatus + " for OBD send via Solution Infini")
        return "FAILED"
    }
}