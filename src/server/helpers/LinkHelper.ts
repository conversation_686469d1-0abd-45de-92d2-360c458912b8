import {DeeplinkServices, Dynamiclink, LinkPayload} from "../../iris-common"
import {CommonUtils} from "../utils/CommonUtils"
import Constants from "../constants"

class LinkHelper {
    static createFirebaseLink = (link: LinkPayload) => {
        const applink = LinkHelper.getApplink(link)
        if (link.weblink) {
            const url = new URL(LinkHelper.appendHttps(link.weblink))
            url.searchParams.append("link", applink)
            return url.href
        }
        return `https://cure.fit?link=${applink}`
    }

    static createBranchLink = (link: LinkPayload) => {
        return LinkHelper.getApplink(link)
    }

    static removeQueryParams = (link: string) => {
        if (!link) return ""
        const url = new URL(link)
        url.search = ""
        return url.href
    }

    static appendHttps = (url: string) => {
        const prefix = "https://"
        if (url.substr(0, prefix.length) !== prefix) {
            return `${prefix}${url}`
        }
        return url
    }

    static getApplink = (link: LinkPayload) => {
        const appUrl = new URL(link.applink)
        /* attaching the query params */

        link.campaign_audience && appUrl.searchParams.set("campaign_audience", link.campaign_audience)
        link.campaign_name && appUrl.searchParams.set("utm_campaign", link.campaign_name)
        link.media_source && appUrl.searchParams.set("utm_source", link.media_source)
        link.campaign_vertical && appUrl.searchParams.set("campaign_vertical", link.campaign_vertical)
        link.medium && appUrl.searchParams.set("utm_medium", link.medium)

      if (link.linkType === DeeplinkServices.FIREBASE) {
            const analyticsInfo = ((<Dynamiclink>link).analyticsInfo || {}).googlePlayAnalytics
            analyticsInfo.utmCampaign && appUrl.searchParams.set("utm_campaign", analyticsInfo.utmCampaign)
            analyticsInfo.utmSource && appUrl.searchParams.set("utm_source", analyticsInfo.utmSource)
            analyticsInfo.utmMedium && appUrl.searchParams.set("utm_medium", analyticsInfo.utmMedium)
        }

        return appUrl.href
    }

    static getWeblink = (link: LinkPayload) => {
        const webLink = new URL(link.weblink)

        link.campaign_audience && webLink.searchParams.set("campaign_audience", link.campaign_audience)
        link.campaign_name && webLink.searchParams.set("utm_campaign", link.campaign_name)
        link.media_source && webLink.searchParams.set("utm_source", link.media_source)
        link.campaign_vertical && webLink.searchParams.set("campaign_vertical", link.campaign_vertical)
        link.medium && webLink.searchParams.set("utm_medium", link.medium)
        return webLink.href
    }

    static getUniversalLink = (url: string) => {
        /*
         * eg: firebase url https://dl.cure.fit/3MXp will be
         * converted to https://cure.fit/3MXp
         */

        const linkDomain = Constants.UNIVERSAL_LINK_DOMAIN
        const proxyDomain = CommonUtils.isEnvironmentProductionOrAlpha() ?
            Constants.UNIVERSAL_LINK_STATIC_DOMAIN_PRODUCTION : Constants.UNIVERSAL_LINK_STATIC_DOMAIN_STAGE

        const split = url.split(linkDomain)
        return split.length === 2 ? `${proxyDomain}${split[1]}` : ""
    }

    static isFirebaseLinksSupported = (appVersion: number): boolean => {
        return appVersion >= 8.15
    }
}

export default LinkHelper