import {DataError, DataErrorV2} from "@curefit/error-client"
import {IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {BASE_TYPES, ILogger} from "@curefit/base"

import {inject, injectable} from "inversify"

import Constants from "../constants"
import * as moment from "moment"
import * as _ from "lodash"
import {v4 as uuid} from "uuid"

import {BumbleService, DimensionValue} from "../../bumble/services/BumbleService"
import {IInAppNotificationHelper} from "./InAppNotificationHelper"
import {IOBDNotificationHelper} from "./OBDNotificationHelper"
import {MozartHelper} from "./MozartHelper"
import {MetricUtils, ProfilerType} from "../utils/MetricUtils"
import {IClickToCallNotificationHelper} from "./ClickToCallNotificationHelper"
import {IWhatsappNotificationHelper} from "./whatsapp/WhatsappNotificationHelper"
import {PhoneNumberUtils} from "../utils/PhoneNumberUtils"
import SegmentationService from "../service/SegmentationService"
import {CommonUtils} from "../utils/CommonUtils"
import NotificationStatusUpdatePublisher from "../publishers/NotificationStatusUpdatePublisher"
import CampaignRequestCacheService from "../service/CampaignRequestCacheService"
import UnsubscriptionService from "../service/UnsubscriptionService"
import SMSNotificationHelper from "./SMS/SMSNotificationHelper"
import UserContextHelper from "./UserContextHelper"
import TYPES from "./../ioc/types"
import {
    BaseCreative,
    CancelCampaignNotificationsRequest,
    CancelCampaignNotificationsResponse,
    ClickToCallCreative,
    CommunicationCampaign,
    EmailCreative,
    ExternalCommunicationCampaign,
    GetLastSentMessageRequest,
    GetLastSentMessageRespose,
    InAppNotificationCreative,
    IRIS_QUEUE_NAMES,
    Notification,
    NotificationSegmentTask,
    NotificationSegmentTaskJobTypeEnum,
    NotificationSegmentTaskStatusEnum,
    NotificationWrapper,
    OBDCreative,
    PNCreative,
    QueueConstants,
    SendCampaignNotificationsRequest,
    SendCampaignNotificationsResponse,
    SendCampaignNotificationsToSegmentRequest,
    SendNotificationsRequest,
    SlackCreative,
    SMSCreative,
    UserTags,
    WhatsappCreative
} from "@curefit/iris-common"

import {
    IBasicNotificatioMetaDao,
    IBasicNotificationDao,
    ICommunicationCampaignReadonlyDao,
    ICreativeReadonlyDao,
    INotificationSegmentTaskReadWriteDao,
    IRIS_MODELS_TYPES
} from "../../iris-models"
import {IPushNotificationHelper} from "./PushNotificationHelper"
import {IEmailNotificationHelper} from "./EmailNotificationHelper"
import ScaleService from "../service/ScaleService"
import NotificationWrapperCacheService from "../service/NotificationWrapperCacheService"
import FeatureUtils from "../utils/FeatureUtils"
import {ISlackNotificationHelper} from "./SlackNotificationHelper"
import {AthenaService} from "../service/AthenaService"
import {CampaignManagerService} from "../service/CampaignManagerService"
import {CommValidationsService} from "../service/CommValidationsService"

@injectable()
class CampaignHelper {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(TYPES.FeatureUtils) private featureUtils: FeatureUtils,
        @inject(TYPES.ScaleService) private scaleService: ScaleService,
        @inject(TYPES.BumbleService) private bumbleService: BumbleService,
        @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,
        @inject(TYPES.UserContextHelper) private userContextHelper: UserContextHelper,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.SegmentationService) private segmentationService: SegmentationService,
        @inject(TYPES.UnsubscriptionService) private unsubscriptionService: UnsubscriptionService,
        @inject(TYPES.MozartHelper) private mozartHelper: MozartHelper,
        @inject(IRIS_MODELS_TYPES.CreativeReadonlyDao) private creativeDao: ICreativeReadonlyDao,
        @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
        @inject(TYPES.PushNotificationHelper) private pushNotificationHelper: IPushNotificationHelper,
        @inject(TYPES.EmailNotificationHelper) private emailNotificationHelper: IEmailNotificationHelper,
        @inject(TYPES.InAppNotificationHelper) private inAppNotificationHelper: IInAppNotificationHelper,
        @inject(TYPES.SMSNotificationHelper) private smsNotificationHelper: SMSNotificationHelper,
        @inject(TYPES.OBDNotificationHelper) private obdNotificationHelper: IOBDNotificationHelper,
        @inject(TYPES.WhatsappNotificationHelper) private whatsappHelper: IWhatsappNotificationHelper,
        @inject(TYPES.SlackNotificationHelper) private slackNotificationHelper: ISlackNotificationHelper,
        @inject(TYPES.NotificationWrapperCacheService) private notificationWrapperCacheService: NotificationWrapperCacheService,
        @inject(TYPES.CampaignRequestCacheService) private campaignRequestCacheService: CampaignRequestCacheService,
        @inject(IRIS_MODELS_TYPES.BasicNotificationMetaDao) private basicNotificatioMetaDao: IBasicNotificatioMetaDao,
        @inject(TYPES.ClickToCallNotificationHelper) private clickToCallNotificationHelper: IClickToCallNotificationHelper,
        @inject(IRIS_MODELS_TYPES.CommunicationCampaignReadonlyDao) private communicationCampaignDao: ICommunicationCampaignReadonlyDao,
        @inject(TYPES.NotificationStatusUpdatePublisher) private notificationStatusUpdatePublisher: NotificationStatusUpdatePublisher,
        @inject(IRIS_MODELS_TYPES.NotificationSegmentTaskReadWriteDao) private notificationSegmentTaskReadWriteDao: INotificationSegmentTaskReadWriteDao,
        @inject(TYPES.AthenaService) private athenaService: AthenaService,
        @inject(TYPES.CampaignManagerService) private campaignManagerService: CampaignManagerService,
        @inject(TYPES.CommValidationsService) private commValidationsService: CommValidationsService,
    ) { }

    async cancelCampaignMessages(req: CancelCampaignNotificationsRequest): Promise<CancelCampaignNotificationsResponse> {
        const result = await this.basicNotificatioMetaDao.updatePropsByTaskId(req.taskId, { state: "CANCELLED" })
        const notificationMetas = await this.basicNotificatioMetaDao.getNotificationMetasByTaskId(req.taskId)
        this.inAppNotificationHelper.bulkCacheDelete(notificationMetas)
        return { success: result }
    }

    /**
     * Get the last sent message time for the given set of users for the given campaign and creative
     */
    public async getLastSentCampaignMessages(getNotificationsTime: GetLastSentMessageRequest): Promise<GetLastSentMessageRespose> {
        const resp: GetLastSentMessageRespose = {}

        const notificationPromises: Promise<Notification>[] = []
        getNotificationsTime.userIds.forEach(u => {
            notificationPromises.push(this.notificationDao.getLatestForUser(getNotificationsTime.campaignId, getNotificationsTime.creativeId, u))
        })

        const notifications: Notification[] = await Promise.all(notificationPromises)

        notifications.forEach(n => {
            resp[n.userId] = n.sentAt
        })
        return resp
    }

    public async sendCampaignMessagesToQueue(req: SendCampaignNotificationsRequest): Promise<boolean> {
        const before = Date.now()
        try {
            const res = this.queueService.sendMessageAsync(QueueConstants.getQueueName("IRIS_CAMPAIGN"), req)
            const now = new Date().getTime()
            this.logger.info(`took ${(now - before) / 1000} secs to send message to SQS for campaignId: ${req.campaignId}`)
            this.metricUtils.reportQueuePushTime("IRIS_CAMPAIGN", now - before)
            return res
        } catch (error) {
            const errorMessage = `CampaignHelper::sendCampaignMessagesToQueue Error while sending to queue: ${error.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            return false
        }
    }

    private async assignEmailUnsubscriptionUrl(notificationWrappers: NotificationWrapper[], appId: string): Promise<any> {
        for (const notificationWrapper of notificationWrappers) {
            const emailId = notificationWrapper.userContext.tags[Constants.USER_EMAIL_TAG_OLD] ||
                notificationWrapper.userContext.tags[Constants.USER_EMAIL_TAG] || notificationWrapper.userContext.emailId
            let unsubscribeUrl = this.unsubscriptionService.generateEmailUnsubscriptionUrl(emailId, appId)

            // Add notif id for debugging
            unsubscribeUrl = unsubscribeUrl ?
                `${unsubscribeUrl}&id=${notificationWrapper.notification.notificationId}&emailId=${encodeURIComponent(emailId)}` : unsubscribeUrl

            notificationWrapper.userContext.tags[Constants.USER_EMAIL_UNSUBSCRIPTION_URL_TAG] = unsubscribeUrl

            this.logger.info(`CampaignHelper::generateUnsubscriptionUrl generated email unsubscription url ` +
                `${notificationWrapper.userContext.tags[Constants.USER_EMAIL_UNSUBSCRIPTION_URL_TAG]} for notification ` +
                `${notificationWrapper.notification.notificationId}`)
        }
    }

    private async trySendingNotifications(notifications: NotificationWrapper[], campaign: CommunicationCampaign,
                                          creative: BaseCreative, appId: string, forceSend?: boolean,
                                          isBulk?: boolean, chunk_progress?: number) {

        this.logger.debug(`CampaignHelper::trySendingNotifications Processing notifications for ` +
            `${campaign.campaignId} : ${creative.creativeId}`)

        // check if creative is blacklisted
        const isBlacklistedCreative: Boolean = await this.commValidationsService.cachedIsBlacklistedCreative(creative.creativeId)
        if (isBlacklistedCreative) {
            const message = `CampaignHelper::trySendingNotifications - ` +
                `Notification(s) were blocked because the creative with ID ${creative.creativeId} is currently blacklisted.`

            this.logger.info(message)

            for (let notificationWrapper of notifications) {
                notificationWrapper.notification.status = "FAILED"
                notificationWrapper.notification.failReason = "CREATIVE_BLACKLISTED"
            }

            return
        }

        const isTransactional = (campaign.type === "TRANSACTION")
        if (creative.type === "EMAIL") {
            await this.assignEmailUnsubscriptionUrl(notifications, appId)
        }

        // For testing. Dry run does not actually send the notification
        const dryRun = campaign.campaignId.startsWith(Constants.DRY_RUN_CAMPAIGN_PREFIX)
        // For whatsapp testing , sends test response in case of whatsapp campaigns
        const testRun = campaign.campaignId === Constants.CREATIVE_TESTING_CAMPAIGN

        if (isTransactional) {
            const unexpiredNotifications: NotificationWrapper[] = []
            for (let notificationWrapper of notifications) {
                this.logger.debug(`CampaignHelper::trySendingNotifications Processing transactional notification ` +
                    `${notificationWrapper.notification.notificationId}`)

                if (CommonUtils.hasNotificationExpired(notificationWrapper.userContext)) {
                    this.logger.info(`CampaignHelper::trySendingNotifications transactional notification ` +
                        `${notificationWrapper.notification.notificationId} has expired`)
                    notificationWrapper.notification.status = "FAILED"
                    notificationWrapper.notification.failReason = "EXPIRED"
                } else {
                    let isUnsubscribed = await this.isUserUnsubscribedToComm(notificationWrapper.notification.userId, campaign, creative, dryRun, appId)
                    this.logger.debug(`CampaignHelper::trySendingNotifications userId: ${notificationWrapper.notification.userId}`
                    + `isUnsubscribed: ${isUnsubscribed}`)

                    if (isUnsubscribed) {
                        this.logger.info(`CampaignHelper::trySendingNotifications userId: ${notificationWrapper.notification.userId} has unsubscribed`)
                        notificationWrapper.notification.status = "FAILED"
                        notificationWrapper.notification.failReason = "UNSUBSCRIBED"
                    } else {
                        this.logger.debug(`CampaignHelper::trySendingNotifications Sending transactional notification ` +
                            `${notificationWrapper.notification.notificationId}`)
                        unexpiredNotifications.push(notificationWrapper)
                    }
                }
            }
            
            switch (creative.type) {
                case "PUSH_NOTIFICATION":
                    await this.pushNotificationHelper.sendNotifications(
                        <PNCreative>creative, unexpiredNotifications, isTransactional, appId, dryRun)
                    break
                case "EMAIL":
                    await this.emailNotificationHelper.sendNotifications(
                        <EmailCreative>creative, unexpiredNotifications, isTransactional, dryRun)
                    break
                case "SMS":
                    await this.smsNotificationHelper.sendNotifications(
                        <SMSCreative>creative, unexpiredNotifications, isTransactional, dryRun)
                    break
                case "OBD":
                    await this.obdNotificationHelper.sendNotifications(
                        <OBDCreative>creative, unexpiredNotifications, isTransactional, dryRun)
                    break
                case "IN_APP_NOTIFICATION":
                    await this.inAppNotificationHelper.persistNotifications(
                        <InAppNotificationCreative>creative, unexpiredNotifications, isTransactional, campaign, appId, dryRun)
                    break
                case "CLICK_TO_CALL":
                    await this.clickToCallNotificationHelper.sendNotifications(
                        <ClickToCallCreative>creative, unexpiredNotifications, isTransactional, dryRun)
                    break
                case "WHATSAPP":
                    await this.whatsappHelper.sendNotifications(
                        <WhatsappCreative>creative, unexpiredNotifications, isTransactional, dryRun, testRun)
                    break
                case "SLACK":
                    await this.slackNotificationHelper.sendNotifications(
                        <SlackCreative>creative, unexpiredNotifications, isTransactional, dryRun)
                    break
            }

            notifications.forEach(notificationWrapper => {
                this.metricUtils.profileNotification(notificationWrapper.notification, campaign, creative)
            })

            if (!dryRun) {
                this.logger.debug(`CampaignHelper::trySendingNotifications publishing status updates for notificationWrapper: ${JSON.stringify(notifications)}`)
                await this.notificationStatusUpdatePublisher.publish(
                    notifications.map((notificationWrapper) => {
                        return {
                            notification: notificationWrapper.notification,
                            status: notificationWrapper.notification.status,
                            // sent at will be available only for sent, for all other states use current time
                            timestamp: (notificationWrapper.notification.sentAt || new Date()).getTime()
                        }
                    })
                )
            }

        } else {

            const body: any = {
                creative, campaign,
                appId, dryRun, testRun,
                isBulk: isBulk || false,
                taskId: notifications[0].notification.taskId,
                chunk_progress: chunk_progress || 0,
                forceSend: forceSend || false
            }

            /**
             * Check whether we should send only notificaiton ids or complete messages viq queue
             * If only notificationIds, cache the notificaiton first for quick read at worker
             * We send only notificaitonIds in order to reduce message size
             */
            if (this.featureUtils.passOnlyNotificationIdsToWorkers()) {
                await Promise.all(notifications.map(notificationWrapper =>
                    this.notificationWrapperCacheService.cacheNotification(notificationWrapper)
                ))
                body.notificationIds = notifications.map(notification => notification.notification.notificationId)
            } else {
                body.notifications = notifications
            }

            const before = Date.now()
            let queueName: IRIS_QUEUE_NAMES
            switch (creative.type) {
                case "PUSH_NOTIFICATION":
                    queueName = Constants.PN_PR_JOB_QUEUE
                    break
                case "EMAIL":
                    queueName = Constants.EMAIL_PR_JOB_QUEUE
                    break
                case "SMS":
                    queueName = Constants.SMS_PR_JOB_QUEUE
                    break
                case "OBD":
                    queueName = Constants.OBD_PR_JOB_QUEUE
                    break
                case "IN_APP_NOTIFICATION":
                    queueName = Constants.IAN_PR_JOB_QUEUE
                    break
                case "CLICK_TO_CALL":
                    queueName = Constants.CTC_PR_JOB_QUEUE
                    break
                case "WHATSAPP":
                    queueName = Constants.WHATSAPP_PR_JOB_QUEUE
                    break
                default: {
                    const errorMessage = `CampaignHelper::trySendingNotifications Attempting to send via unknown` +
                        `chanel: ${creative.type}`
                    this.logger.error(errorMessage)
                    this.rollbarService.sendError(new Error(errorMessage))
                }
            }
            this.queueService.sendMessageAsync(QueueConstants.getQueueName(queueName), body)
            this.metricUtils.reportQueuePushTime(queueName, new Date().getTime() - before)
        }
    }

    private async isUserUnsubscribedToComm(userId: string, campaign: CommunicationCampaign, creative: BaseCreative,
                                           dryRun: boolean, appId: string): Promise<boolean> {
        let dimensions: DimensionValue[] = [
            {
                dimensionName: Constants.BUMBLE_DIMENSION_CHANNEL,
                dimensionValue: creative.type
            }
        ]
        if (campaign.vertical) {
            dimensions.push({
                dimensionName: Constants.BUMBLE_DIMENSION_VERTICAL,
                dimensionValue: campaign.vertical
            })
        }
        if (creative.objective) {
            dimensions.push({
                dimensionName: Constants.BUMBLE_DIMENSION_OBJECTIVE,
                dimensionValue: creative.objective
            })
        }

        let isUserUnsubscribed = false
        try {
            let bumbleNamespace = appId || Constants.APP_ID_CUREFIT
            let { unsubscribed } = await this.bumbleService.fetchUnsubscribedStatusAndToken(bumbleNamespace,
                userId, dimensions, dryRun, true, appId)
            isUserUnsubscribed = unsubscribed
        } catch (err) {
            const errorString = `CampaignHelper::isUserUnsubscribedToComm Failed to fetch unsubscribed flag for ` +
                `userId: ${userId}. Error: ${err.toString()}`
            this.logger.error(errorString)
            this.rollbarService.sendError(new Error(errorString))
        }
        return isUserUnsubscribed
    }

    private async loadCreatives(creativeIds: string[], creativeFreshnessThreshold?: number): Promise<BaseCreative[]> {
        const timerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF, "creativeDao.find")
        const creatives = await this.creativeDao.find(
            { condition: { creativeId: { $in: creativeIds }, registrationStatus: "ACTIVE" } })
        this.metricUtils.endProfilingTimer(timerContext)

        if (_.isEmpty(creatives)) {
            this.logger.info("Could not find creatives for " + JSON.stringify(creativeIds))
            throw new DataError("Could not find creatives for " + JSON.stringify(creativeIds))
        }

        if (creativeFreshnessThreshold && creativeFreshnessThreshold > 0) {
            const today = moment()
            for (const creative of creatives) {
                if (!(<any>creative)["updatedDate"] ||
                    today.unix() - moment((<any>creative)["updatedDate"]).unix() > creativeFreshnessThreshold) {
                    this.logger.info(`Given freshness limit ${creativeFreshnessThreshold} and ` +
                        `freshness of creative ${creative.creativeId} is ${(<any>creative)["updatedDate"]}. Not processing`)
                    throw new DataError(`Creative ${creative.creativeId} does not pass freshness check`)
                }
            }
        }
        return creatives
    }

    public async loadCampaign(campaignId: string): Promise<any> {
        let timerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF, "communicationCampaignDao.findOne")
        const campaign = await this.communicationCampaignDao.findOne({ campaignId })
        this.metricUtils.endProfilingTimer(timerContext)

        if (_.isEmpty(campaign)) {
            this.logger.info("Could not find campaign for " + campaignId)
            throw new DataError("Could not find campaign for " + campaignId)
        }
        return campaign
    }

    private async copyGlobalContexts(sendNotifications: SendCampaignNotificationsRequest): Promise<any> {
        for (let userContext of sendNotifications.userContexts) {
            userContext.tags = Object.assign({}, sendNotifications.globalTags, userContext.tags)
            if (!userContext.attachments) {
                userContext.attachments = []
            }
            if (sendNotifications.globalAttachments) {
                userContext.attachments = userContext.attachments.concat(sendNotifications.globalAttachments)
            }

            // App id should ONLY be taken from the request; and should default to curefit
            userContext.appId = sendNotifications.appId || Constants.APP_ID_CUREFIT

            // Copy global expiry if not present at an individual level
            userContext.expiry = userContext.expiry ? userContext.expiry :
                sendNotifications.expiry ? sendNotifications.expiry : undefined

            // Copy global scheduled time if not present at an individual level
            userContext.atSchedule = userContext.atSchedule ? userContext.atSchedule :
                sendNotifications.atSchedule ? sendNotifications.atSchedule : undefined
        }
    }

    public async updateNotifications(notificationWrappers: NotificationWrapper[]): Promise<any> {
        for (const notificationWrapperChunk of _.chunk(notificationWrappers, 5)) {
            const updateNotificationTimerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF,
                "UpdateProps::SERVER", notificationWrapperChunk.length)

            await Promise.all(notificationWrapperChunk.map((notificationWrapper) => {

                //Info log to be removed after debugging no sentAt in callback issue
                const threshold = Math.random()
                if (threshold < 0.01) {
                    this.logger.info(`Updating following fields for notificationId ${notificationWrapper.notification.notificationId} :
                sentAt: ${notificationWrapper.notification.sentAt}, externalId: ${notificationWrapper.notification.externalId}`)
                }

                this.notificationDao.updateProps(notificationWrapper.notification.notificationId, {
                    sentAt: notificationWrapper.notification.sentAt,
                    failReason: notificationWrapper.notification.failReason,
                    status: notificationWrapper.notification.status,
                    externalId: notificationWrapper.notification.externalId,
                    billableUnits: notificationWrapper.notification.billableUnits,
                    userTags: this.userContextHelper.encodeUserContext(notificationWrapper.userContext),
                    deviceInfo: notificationWrapper.notification.deviceInfo ?
                        notificationWrapper.notification.deviceInfo : undefined
                })
            }))

            this.metricUtils.endProfilingTimer(updateNotificationTimerContext)
        }
    }

    public async createNotifications(notificationWrappers: NotificationWrapper[]) {
        for (const chunk of _.chunk(notificationWrappers, 25)) {
            const timerContext = this.metricUtils.startProfilingTimer(
                ProfilerType.PERF, "bulkCreate::SERVER", notificationWrappers.length)

            await this.notificationDao.bulkCreate(chunk.map(notificationWrapper => {
                return notificationWrapper.notification
            }))

            this.metricUtils.endProfilingTimer(timerContext)
        }
    }

    // Segregates out notification to be scheduled, and to be sent
    public segregateNotifications(notificationWrappers: NotificationWrapper[]):
        { notificationsToSend: NotificationWrapper[], notificationsToSchedule: NotificationWrapper[]} {

        const [ notificationsToSend, notificationsToSchedule ] = _.partition(notificationWrappers,
            (notificationWrapper) => {
            return notificationWrapper.userContext.atSchedule === undefined
        })

        return { notificationsToSend, notificationsToSchedule }
    }

    public async scheduleNotifications(notificationsToSchedule: NotificationWrapper[],
                                       campaignId: string, creativeId: string) {
        for (const notificationWrapper of notificationsToSchedule) {
            await this.mozartHelper.scheduleNotification(
                new Date(notificationWrapper.userContext.atSchedule).toISOString(),
                campaignId, creativeId,
                [ notificationWrapper.notification.notificationId ])
        }
    }

    public async sendCampaignMessagesThroughOneCreative(sendNotificationsRequest: SendCampaignNotificationsRequest,
                                                        previousCreativeId?: string) {
        if (!sendNotificationsRequest.creativeIds || sendNotificationsRequest.creativeIds.length === 0) {
            throw new DataErrorV2("No creatives provided", {})
        }

        sendNotificationsRequest.taskId = sendNotificationsRequest.taskId || uuid()
        sendNotificationsRequest.appId = sendNotificationsRequest.appId || Constants.APP_ID_CUREFIT

        // Log send campaign request in cache.
        // Will be used later in case send for notification fails via current creative
        // Existing object means previous creative failed; Do not overwrite!!
        let newRequest: boolean = false
        if (!(await this.campaignRequestCacheService.getSingleCreativeCampaignRequest(sendNotificationsRequest.taskId))) {
            sendNotificationsRequest.creativeIds = _.uniq(sendNotificationsRequest.creativeIds)
            await this.campaignRequestCacheService.logSingleCreativeCampaignRequest(
                sendNotificationsRequest.taskId, sendNotificationsRequest)
            newRequest = true
        }

        // Load the creative
        let creativeIdIndex = 0
        if (previousCreativeId) {
            const previousCreativeIdIndex = sendNotificationsRequest.creativeIds.indexOf(previousCreativeId)
            if (previousCreativeIdIndex === -1 ||
                previousCreativeIdIndex === sendNotificationsRequest.creativeIds.length - 1) {
                const errorMessage = `CampaignHelper::sendCampaignMessagesThroughOneCreative Creative issue. ` +
                    `previousCreativeId: ${previousCreativeId}` +
                    `sendNotificationsRequest: ${JSON.stringify(sendNotificationsRequest)}`
                this.logger.error(errorMessage)
                throw new DataErrorV2(errorMessage, {})
            }
            creativeIdIndex = previousCreativeIdIndex + 1
        }

        const creativeId = sendNotificationsRequest.creativeIds[creativeIdIndex]
        const creativeIds = [ creativeId ]

        return await this.sendCampaignNotificationsForCreativeIds(sendNotificationsRequest, creativeIds, newRequest)
    }

public async sendCampaignNotificationsWithFallback(sendNotificationsRequest: SendCampaignNotificationsRequest, enableFallback?: boolean, previousCreativeId?: string, isBulk?: boolean, chunk_progress?: number) {
        if (!sendNotificationsRequest.creativeIds || sendNotificationsRequest.creativeIds.length === 0) {
            throw new DataErrorV2("No creatives provided", {})
        }

        sendNotificationsRequest.taskId = sendNotificationsRequest.taskId || uuid()
        sendNotificationsRequest.appId = sendNotificationsRequest.appId || Constants.APP_ID_CUREFIT

        // Log send campaign request in cache.
        // Will be used later in case send for notification fails via current creative
        // Existing object means previous creative failed; Do not overwrite!!
        let newRequest: boolean = !enableFallback || !previousCreativeId
        if (enableFallback && !(await this.campaignRequestCacheService.getCampaignRequest(sendNotificationsRequest.taskId))) {
            sendNotificationsRequest.creativeIds = _.uniq(sendNotificationsRequest.creativeIds)
            await this.campaignRequestCacheService.logCampaignRequest(
                sendNotificationsRequest.taskId, sendNotificationsRequest)
        }

        const creativeIds: string[] = enableFallback && previousCreativeId
            ? [ sendNotificationsRequest.fallbackCreativeIds[previousCreativeId] ]
            : sendNotificationsRequest.creativeIds

        this.logger.debug(`CampaignHelper::sendCampaignNotificationsWithFallback initiating sendCampaignNotificationsForCreativeIds,taskId:${sendNotificationsRequest.taskId} newRequest:${newRequest}, previousCreativeId:${previousCreativeId}, creativeIds:${creativeIds}`)

        return await this.sendCampaignNotificationsForCreativeIds(sendNotificationsRequest, creativeIds, newRequest)
    }

public async sendCampaignMessages(sendNotifications: SendCampaignNotificationsRequest, isBulk?: boolean,
                                      chunk_progress?: number): Promise<SendCampaignNotificationsResponse> {
        return await this.sendCampaignNotificationsWithFallback(sendNotifications, true, null, isBulk, chunk_progress)
    }

    private async sendCampaignNotificationsForCreativeIds(sendNotificationsRequest: SendCampaignNotificationsRequest, creativeIds: string[], newRequest: boolean) {
        // Load campaign
        const campaign = await this.loadCampaign(sendNotificationsRequest.campaignId)
        const isTransactional: boolean = campaign.type === "TRANSACTION"

        // For testing. Dry run does not actually send the notification
        const dryRun = campaign.campaignId.startsWith(Constants.DRY_RUN_CAMPAIGN_PREFIX)

        const creatives = await this.loadCreatives(creativeIds, sendNotificationsRequest.creativeFreshnessThreshold)

        await this.enrichUserContexts(sendNotificationsRequest, creatives)
        const notificationWrappers: NotificationWrapper[] = []
        for (const creative of creatives) {
            const wrappersForCreative = await this.createWrapperAndScheduleOrSendNotification(sendNotificationsRequest, creative, dryRun, newRequest, isTransactional, campaign)
            notificationWrappers.push(...wrappersForCreative)
            this.logger.debug(`CampaignHelper::sendCampaignNotificationsForCreativeIds created ${wrappersForCreative.length} notificationWrappers,
             totalWrappers: ${notificationWrappers.length} for creativeId: ${creative.creativeId}`)
        }

        this.logger.debug(`CampaignHelper::sendCampaignNotificationsForCreativeIds send ${notificationWrappers.length} notificationWrappers for 
        creatives: ${creativeIds}`)

        const sendCampaignNotificationsResponse: SendCampaignNotificationsResponse = {}
        notificationWrappers.forEach(notificationWrapper => {
            const userId = notificationWrapper.notification.userId
            sendCampaignNotificationsResponse[userId] = sendCampaignNotificationsResponse[userId] || []
            sendCampaignNotificationsResponse[userId].push({
                creativeId: creatives[0].creativeId,
                notificationId: notificationWrapper.notification.notificationId,
                status: notificationWrapper.notification.status,
                sent: !!notificationWrapper.notification.sentAt,
                failReason: notificationWrapper.notification.failReason ?
                    notificationWrapper.notification.failReason : null,
                scheduled: notificationWrapper.notification.status === "SCHEDULED",
                whatsappTestRunMessage: notificationWrapper.notification.whatsappTestRunMessage
            })
        })

        this.logger.debug(`CampaignHelper::sendCampaignNotificationsForCreativeIds updated response for ${_.keys(sendCampaignNotificationsResponse).length} userContexts notificationWrappers for 
        creatives: ${creativeIds}`)

        return sendCampaignNotificationsResponse
    }

    public async createNotificationForInboundCampaign(externalCampaign: ExternalCommunicationCampaign, caller: string, isTransactional: boolean): Promise<string> {
        const createNotificationsRequest = this.clickToCallNotificationHelper
            .getNotificationCreationRequestForInboundCall(externalCampaign, caller)
        const notificationWrapper = this.createNotificationWrapper(createNotificationsRequest.userContexts[0],
            createNotificationsRequest, createNotificationsRequest.creativeIds[0], false, true, isTransactional)
        await this.createNotifications([notificationWrapper])
        return notificationWrapper.notification.notificationId
    }

    private async createWrapperAndScheduleOrSendNotification(sendNotificationsRequest: SendCampaignNotificationsRequest, creative: BaseCreative, dryRun: boolean,
                                                             newRequest: boolean, isTransactional: boolean, campaign: CommunicationCampaign,  isBulk?: boolean,
                                                             chunk_progress?: number) {
        let notificationWrappers: NotificationWrapper[] = sendNotificationsRequest.userContexts
            .map(userContext => this.createNotificationWrapper(userContext, sendNotificationsRequest, creative.creativeId, dryRun, newRequest, isTransactional))
        //Create notifications
        await this.createNotifications(notificationWrappers)

        // Filter out notification to be scheduled, and to be sent
        const {notificationsToSend, notificationsToSchedule} = this.segregateNotifications(notificationWrappers)

        // Schedule notifications to be sent later. We don't need to update them further
        await this.scheduleNotifications(notificationsToSchedule, sendNotificationsRequest.campaignId, creative.creativeId)

        // Send notifications to be sent now. We need to update only transactional notifications.
        // Promotional will get updated by workers, Scheduled will get updated later on
        for (const notificationWrapperChunk of _.chunk(notificationsToSend, 5)) {
            await this.trySendingNotifications(notificationWrapperChunk, campaign, creative,
                sendNotificationsRequest.appId, sendNotificationsRequest.forceSend, isBulk, chunk_progress)
            if (isTransactional) {
                await this.updateNotifications(notificationWrapperChunk)
            }
        }

        this.logger.debug(`CampaignHelper::createWrapperAndScheduleOrSendNotification notifications scheduled:${notificationsToSchedule.length}
         and sent:${notificationsToSend.length}, totalWrappers:${notificationWrappers.length}`)
        return notificationWrappers
    }


    private async enrichUserContexts(sendNotificationsRequest: SendCampaignNotificationsRequest, creatives: BaseCreative[]) {
        // Copy global contexts to user level
        await this.copyGlobalContexts(sendNotificationsRequest)

        sendNotificationsRequest.userContexts = await this.userContextHelper.enrichUserTags(
            sendNotificationsRequest.userContexts, creatives, sendNotificationsRequest.appId)

        sendNotificationsRequest.userContexts.forEach(userContext => {
            if (userContext.phone) {
                const phoneStyle = PhoneNumberUtils.findPhoneStyle(userContext.phone)
                if (phoneStyle === "Invalid") this.logger.error(`Invalid phoneStyle: ${userContext.phone} ` +
                    `for creatives: ${_.map(creatives, creative => creative.creativeId)}`)
            }
        })
    }

    private createNotificationWrapper(userContext: UserTags, sendNotificationsRequest: SendCampaignNotificationsRequest, creativeId: string, dryRun: boolean, newRequest: boolean, isTransactional: boolean): NotificationWrapper {
        // Copy meta tags if present
        userContext.metaTags = {
            ...sendNotificationsRequest.globalMetaTags,
            ..._.get(sendNotificationsRequest.creativeSpecificMetaTags, creativeId, {})
        }
        let userTagsString: string = this.userContextHelper.encodeUserContext(userContext)

        // TODO: Legacy code; handle these failures elegantly
        if (userTagsString.length > Constants.USERTAG_DB_ENTRY_SIZE_LIMIT) {
            this.logger.error("usertag db entry size=" + userTagsString.length + " exceeds size limit=" +
                Constants.USERTAG_DB_ENTRY_SIZE_LIMIT + " in characters")
            userTagsString = null
        }

        const createdAt = new Date()
        if (dryRun) {
            // Done to make data clean up for load tests easy.
            // DB is partitioned on month, simply drop old partitions
            createdAt.setMonth(createdAt.getMonth() - 6)
        }

        // If not a new request, delete schedule time.
        // This will only happen when notification via previous creative failed
        if (!newRequest) {
            userContext.atSchedule = undefined
        }

        return {
            notification: {
                createdAt,
                status: userContext.atSchedule ? "SCHEDULED" :
                    isTransactional ? "CREATED" : "QUEUED",
                notificationId: `${CommonUtils.isEnvironmentStage() ? "stage-" : ""}${uuid()}`,
                creativeId,
                campaignId: sendNotificationsRequest.campaignId,
                userId: userContext.userId || userContext.emailId || userContext.phone,
                userIdType: userContext.userId ? Constants.USER_ID_TYPES.CUREFIT_USER_ID :
                    (userContext.emailId ? Constants.USER_ID_TYPES.EMAIL_ID : Constants.USER_ID_TYPES.PHONE_NUMBER),
                taskId: sendNotificationsRequest.taskId,
                scheduledAt: userContext.atSchedule,
                userTags: userTagsString,
                appId: sendNotificationsRequest.appId
            },
            userContext
        }
    }


    public async processScheduledNotifications(sendNotifications: SendNotificationsRequest,
                                               callbackReceivedAt?: Date): Promise<SendCampaignNotificationsResponse> {
        const campaign = await this.communicationCampaignDao.findOne({ campaignId: sendNotifications.campaignId })
        const creative = await this.creativeDao.findOne({ creativeId: sendNotifications.creativeId, registrationStatus: "ACTIVE" })
        const notifications = await this.notificationDao.getNotificationsById(sendNotifications.notificationIds)

        if (_.isEmpty(notifications)) {
            this.logger.info("Could not find notifications for " + sendNotifications.notificationIds)
            this.rollbarService.sendError(new Error("Could not find notifications for " + sendNotifications.notificationIds))
        }

        if (!notifications[0].scheduledAt) {
            const errorMessage = `CampaignHelper::processScheduledNotifications no scheduledAt for scheduled notification`
                + `${notifications[0].notificationId}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
        } else {
            this.metricUtils.reportCallbackLatency("ATS",
                callbackReceivedAt.getTime() - new Date(notifications[0].scheduledAt).getTime())
        }

        const notificationsToSend = notifications.filter(notification => { return _.isNil(notification.sentAt) })
        if (notificationsToSend.length !== notifications.length) {
            const errorMessage = `CampaignHelper::processScheduledNotifications retrying sent notifications!!! ` +
                `notifications: ${JSON.stringify(notifications.map(notification => notification.notificationId))} ` +
                `notificationsToSend: ${JSON.stringify(notificationsToSend.map(notification => notification.notificationId))} `
            this.logger.error(errorMessage)
        }
        await this.sendExistingNotifications(notificationsToSend, campaign, creative)

        //update response object
        const resp: SendCampaignNotificationsResponse = {}
        notificationsToSend.map(notification => {
            const userId = notification.userId
            resp[userId] = resp[userId] || []
            resp[userId].push({
                creativeId: sendNotifications.creativeId,
                notificationId: notification.notificationId,
                sent: !!notification.sentAt,
                status: notification.status
            })
        })
        return resp
    }

    public async sendExistingNotifications(notifications: Notification[], campaign: CommunicationCampaign,
                                           creative: BaseCreative) {
        const decodedUserContexts =
            notifications.map(notification => this.userContextHelper.decodeUserContext(notification.userTags))
        const uniqueAppIds = _.uniq(decodedUserContexts.map(userContext => userContext.appId))

        // Generate notification wrappers
        const notificationWrappers: NotificationWrapper[] = []
        _.zip(notifications, decodedUserContexts).map(wrapper => {
            notificationWrappers.push({
                notification: wrapper[0],
                userContext: wrapper[1]
            })
        })


        // We need to re enrich user context; values injected from user cache and rashi might have changed!!
        // We group by appId since enrich method takes appId
        for (const appId of uniqueAppIds) {
            // Filter out wrappers for this particular appId
            const filteredNotificationWrappers = _.filter(notificationWrappers, (notificationWrapper) => {
                return notificationWrapper.userContext.appId === appId
            })

            // Re enrich user contexts for filtered notifications
            const reEnrichedUserContext = await this.userContextHelper.enrichUserTags(
                filteredNotificationWrappers.map(notificationWrapper => notificationWrapper.userContext),
                [ creative ], appId)

            // Assign re enriched user contexts to notification wrappers
            _.zip(filteredNotificationWrappers, reEnrichedUserContext).forEach(wrapper => {
                wrapper[0].userContext = wrapper[1]
            })
        }

        const isTransactional: boolean = campaign.type === "TRANSACTION"

        // Transactional notifications will be processed by server itself. Updated below
        // Other updates of promotional notifications will be updated by workers
        if (!isTransactional) {
            for (const chunk of _.chunk(notificationWrappers, 5)) {
                const timerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF,
                    "sendExistingNotifications::updateProps::SERVER", chunk.length)

                await Promise.all(chunk.map((nw) => {
                    this.notificationDao.updateProps(nw.notification.notificationId, {
                        status: "QUEUED",
                        userTags: this.userContextHelper.encodeUserContext(nw.userContext)
                    })
                }))

                this.metricUtils.endProfilingTimer(timerContext)
            }
        }

        // We group by appId again, since trySendingNotifications takes appId
        for (const appId of uniqueAppIds) {
            // Filter out wrappers for this particular appId
            const filteredNotificationWrappers = _.filter(notificationWrappers, (notificationWrapper) => {
                return notificationWrapper.userContext.appId === appId
            })

            // Send notifications
            await this.trySendingNotifications(filteredNotificationWrappers, campaign, creative, appId)
        }


        // Other updates of non transactional notifications will be updated by workers
        if (isTransactional) {
            //update status of notifications

            for (const notificationWrapperChunk of _.chunk(notificationWrappers, 5)) {
                const timerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF,
                    "sendExistingNotifications::updateProps::SERVER", notificationWrapperChunk.length)

                await Promise.all(notificationWrapperChunk.map(notificationWrapper => {
                    const { notification, userContext } = notificationWrapper
                    return this.notificationDao.updateProps(notification.notificationId, {
                        sentAt: notification.sentAt,
                        bumbleTokenId: notification.bumbleTokenId,
                        failReason: notification.failReason,
                        status: notification.status,
                        externalId: notification.externalId,
                        billableUnits: notification.billableUnits,
                        userTags: this.userContextHelper.encodeUserContext(userContext),
                        deviceInfo: notification.deviceInfo ? notification.deviceInfo : undefined
                    })
                }))

                this.metricUtils.endProfilingTimer(timerContext)
            }
        }
    }

    // Send to segment and helpers
    // This flow is enabled on alpha, but only the initial part executes in alpha
    // Events from downstream services are published on production, hence iris production will end up processing it
    // TODO: Problem solve to enable on alpha as well
    public async sendCampaignMessagesToSegment(req: SendCampaignNotificationsToSegmentRequest): Promise<NotificationSegmentTask> {
        const { segmentId, userId, campaignId, segmentExecutionType, creativeIds, globalTags, globalAttachments,
            forceSend, expiry, globalMetaTags, creativeSpecificMetaTags, fallbackCreativeIds } = req
        this.logger.debug(`CampaignHelper::sendCampaignMessagesToSegment received Request:${JSON.stringify(req)}`)

        if (!segmentId || !userId || !campaignId || !creativeIds || creativeIds.length === 0) {
            throw new DataError("Incorrect request, missing data")
        }
        const appId = req.appId || Constants.APP_ID_CUREFIT

        // Create task
        let notificationSegmentTask: NotificationSegmentTask = await this.notificationSegmentTaskReadWriteDao.create({
            taskId: uuid(),
            userId,
            segmentId,
            ...(segmentExecutionType != null && {segmentExecutionType}),
            creativeIds,
            campaignId,
            globalTags,
            globalAttachments,
            forceSend,
            expiry,
            appId,
            globalMetaTags, creativeSpecificMetaTags,
            jobs: [],
            status: NotificationSegmentTaskStatusEnum.CREATED,
            batchesProcessed: 0,
            fallbackCreativeIds
        })

        this.logger.debug(`CampaignHelper::sendCampaignMessagesToSegment created notificationSegmentTask:${JSON.stringify(notificationSegmentTask)}`)

        // Create segment download job
        try {

            if (segmentExecutionType != null && (segmentExecutionType == "ONE_TIME_REFRESH" || segmentExecutionType == "REAL_TIME_REFRESH")){
                // calls a job on athena which then calls a job a batch mozart job which is consumed by iris_segment_batches
                const {athenaTaskId} = await this.segmentationService.downloadTransientSegment(notificationSegmentTask.taskId, segmentId, userId, campaignId, appId);

                if (!!athenaTaskId) {
                    notificationSegmentTask = await this.notificationSegmentTaskReadWriteDao.findOneAndUpdatePartial(
                            {taskId: notificationSegmentTask.taskId},
                            { $set: { status: NotificationSegmentTaskStatusEnum.PROCESSING},

                                $push: { jobs: { jobId: athenaTaskId, type: NotificationSegmentTaskJobTypeEnum.TRANSIENT_SEGMENT_DOWNLOAD_ATHENA }}})
                } else {
                    throw new Error(`Transient segment download, athena task formation failed for campaignId: ${campaignId} , segmentId: ${segmentId} `)
                }
            }
            else {
                // triggers a streaming request on segmentation which then calls iris_segment_batches with the userIds ; null is returned here if segment has 0 users
                const {userSegmentStreamTaskId, totalBatches}   = await this.segmentationService.downloadSegment(segmentId, notificationSegmentTask.taskId, userId, appId)

                if (!!userSegmentStreamTaskId) {
                    notificationSegmentTask = await this.notificationSegmentTaskReadWriteDao.findOneAndUpdatePartial(
                        { taskId: notificationSegmentTask.taskId },
                        { $set: { status: NotificationSegmentTaskStatusEnum.SENDING, totalBatches: totalBatches},
                            $push: { jobs: { jobId: userSegmentStreamTaskId, type: NotificationSegmentTaskJobTypeEnum.BATCH_PROCESS_QUEUE }}})
                } else {
                    this.logger.info(`Found zero active users while downing segment ${segmentId} with taskid ${notificationSegmentTask.taskId}`)

                    notificationSegmentTask = await this.notificationSegmentTaskReadWriteDao.findOneAndUpdatePartial(
                        { taskId: notificationSegmentTask.taskId },
                        { $set: { status: NotificationSegmentTaskStatusEnum.COMPLETED}})
                }
            }
        } catch (error) {
            const errorMessage = `CampaignHelper::sendCampaignMessagesToSegment Download Segment call to segmentation failed` +
                `taskId: ${notificationSegmentTask.taskId} Error: ${error.toString()}`
            this.logger.error(errorMessage)
            await this.rollbarService.sendError(new Error(errorMessage))
            notificationSegmentTask = await this.notificationSegmentTaskReadWriteDao.findOneAndUpdatePartial(
                { taskId: notificationSegmentTask.taskId },
                { $set: { status: NotificationSegmentTaskStatusEnum.FAILED }})
            // callback to Campaign Manager to update campaign Status
            let errMsg: string = "Download segment call to segmentation Failed"
            await this.campaignManagerService.campaignStatusUpdateOnSegmentDownloadFailure(errMsg, notificationSegmentTask)
        }

        this.logger.debug(`CampaignHelper::sendCampaignMessagesToSegment updated notificationSegmentTask:${JSON.stringify(notificationSegmentTask)}`)

        notificationSegmentTask = await this.notificationSegmentTaskReadWriteDao.findOne(
            { taskId: notificationSegmentTask.taskId })

        return notificationSegmentTask
    }

    public async getNotificationSegmentTask(taskId: string): Promise<NotificationSegmentTask> {
        return this.notificationSegmentTaskReadWriteDao.findOne({ taskId })
    }

    public async updateNotificationSegmentTaskStatus(taskId: string, status: NotificationSegmentTaskStatusEnum) {
        return this.notificationSegmentTaskReadWriteDao.findOneAndUpdatePartial(
            { taskId },
            { $set: { status }})
    }

    public async killNotificationSegmentTask(taskId: string): Promise<NotificationSegmentTask> {
        const notificationSegmentTask = await this.notificationSegmentTaskReadWriteDao.findOne({ taskId })
        if (!notificationSegmentTask) {
            throw new DataError(`Task with id ${taskId} does not exist`)
        }

        if (notificationSegmentTask.status === NotificationSegmentTaskStatusEnum.COMPLETED ||
            notificationSegmentTask.status === NotificationSegmentTaskStatusEnum.FAILED) {
            throw new DataError(`Task status ${notificationSegmentTask.status}. Cannot kill`)
        }

        return this.notificationSegmentTaskReadWriteDao.findOneAndUpdatePartial(
            { taskId }, { $set: { status: NotificationSegmentTaskStatusEnum.KILLED }})
    }

    public async resendNotificationFromAthena(notificationId: string): Promise<SendCampaignNotificationsResponse> {
        const query: string =  `SELECT * FROM "${Constants.ATHENA_DB}"."notification" WHERE notificationid ='${notificationId}' LIMIT 1`
        this.logger.debug(`CampaignHelper::queryAthena Querying Athena with ${query}`)
        let queryResponse: any
        try {
            queryResponse = await this.athenaService.executeQuery(query)
        } catch (e) {
            this.logger.error(e)
            const msg: string = "CampaignHelper::queryAthena Unable to find notification by id " + notificationId + `because ${e} `
            throw new Error(msg)
        }
        if (!queryResponse.data[0]) {
            throw new Error(`CampaignHelper::queryAthena Got empty response for notification with Id: ${notificationId}`)
        }
        let sendCampaignNotificationsRequest: SendCampaignNotificationsRequest = {
            campaignId: queryResponse.data[0].campaignid,
            creativeIds: [queryResponse.data[0].creativeid],
            userContexts: [this.userContextHelper.decodeUserContext(queryResponse.data[0].usertags)]
        }
        return this.sendCampaignMessages(sendCampaignNotificationsRequest)
    }

    // marks the completion of athena query and creation of the mozart job in athena Service
    public async transientSegmentDownloadTrinoSuccess(notificationSegmentTaskId: string, mozartJobId: number): Promise<NotificationSegmentTask> {
        try {
            return await this.notificationSegmentTaskReadWriteDao.findOneAndUpdatePartial(
                {
                    taskId: notificationSegmentTaskId
                },
                {
                    $set: {status: NotificationSegmentTaskStatusEnum.SENDING},
                    $push: {jobs: {jobId: mozartJobId, type: NotificationSegmentTaskJobTypeEnum.BATCH_PROCESS_QUEUE}}
                }
            )
        }
        catch (err) {
            let errorMsg: string = `SegmentationHelper::transientSegmentDownloadAthenaSuccess failed to update mozart job id : ${mozartJobId} to notification segment status for notificationSegmentTask with taskId: ${notificationSegmentTaskId}`
            this.logger.error(errorMsg)
            await this.rollbarService.sendError(new Error(errorMsg))
            throw err
        }
    }

    public async transientSegmentDownloadTrinoFail(notificationSegmentTaskId: string): Promise<NotificationSegmentTask> {
        try {
            const notificationSegmentTask: NotificationSegmentTask = await this.notificationSegmentTaskReadWriteDao.findOneAndUpdatePartial(
                {
                    taskId: notificationSegmentTaskId
                }, 
                { 
                     $set: { status: NotificationSegmentTaskStatusEnum.FAILED }
                })
            let failureMsg: string = `Transient Segment Download Trino Query Processing failed for notificationSegmentTask : ${notificationSegmentTask}`
            await this.campaignManagerService.campaignStatusUpdateOnSegmentDownloadFailure(failureMsg, notificationSegmentTask)
            return notificationSegmentTask
        }
        catch (err) {
            let errorMsg: string =  `SegmentationHelper::transientSegmentDownloadTrinoFail failed to update failure status to notificationSegmentTask with taskId: ${notificationSegmentTaskId}`
            this.logger.error(errorMsg)
            await this.rollbarService.sendError(new Error(errorMsg));
            throw err
        }

    }

}

export default CampaignHelper