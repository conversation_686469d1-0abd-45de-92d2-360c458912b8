import {Container, inject, injectable} from "inversify"

import {BASE_TYPES, ILogger} from "@curefit/base"
import {NotificationWrapper, OBDCreative, UserNotificationStatus} from "../../iris-common"
import {MustacheUtils} from "../utils/MustacheUtils"
import TYPES from "../ioc/types"
import {IKaleyraOBDHelper} from "./KaleyraOBDHelper"
import FunctionalTagsUtils from "../utils/FunctionalTagsUtils"
import TagUtils from "../utils/TagUtils"

export interface IOBDNotificationHelper {
    sendNotifications(creative: OBDCreative, notifications: NotificationWrapper[], isTransactional: boolean,
                      dryRun?: boolean): Promise<UserNotificationStatus[]>
}


export function OBDNotificationHelperFactory(kernel: Container) {

    @injectable()
    class OBDNotificationHelper implements IOBDNotificationHelper {

        constructor( @inject(BASE_TYPES.ILogger) private logger: ILogger,
                     @inject(TYPES.KaleyraOBDHelper) private kaleyraHelper: IKaleyraOBDHelper) { }

        public sendNotifications(creative: OBDCreative, notifications: NotificationWrapper[],
                                 isTransactional: boolean, dryRun?: boolean): Promise<UserNotificationStatus[]> {
            const allTags = TagUtils.allTagsUsed([creative.body])
            switch (creative.obdParams.serviceProvider) {
                case "KALEYRA":
                    return this.kaleyraHelper.sendNotifViaKaleyra(creative, notifications, allTags, isTransactional, dryRun)
                default:
                    this.logger.error("Unsupported Service Provider : " + creative.obdParams.serviceProvider)
            }

            return null
        }

    }

    return OBDNotificationHelper
}
