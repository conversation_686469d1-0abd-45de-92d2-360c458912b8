import {AppName} from "@curefit/base-utils"

export interface NotificationTo {
  Email?: string[]
}

export interface NotificationPlatformSpecificAttributes {
  deep_link?: string
  sound_file?: string
  category?: string
  badge_count?: number
  key?: string
}

export interface NotificationPlatformSpecific {
  windows?: NotificationPlatformSpecificAttributes
  ios?: NotificationPlatformSpecificAttributes
  android?: NotificationPlatformSpecificAttributes
}

export interface NotificationContent {
  title: string
  body: string
  platform_specific?: NotificationPlatformSpecific
}

export interface NotificationParams {
  to: NotificationTo
  tag_group: string
  respect_frequency_caps: boolean
  content: NotificationContent
}

export type NotificationType = "PUSH"

export const QUEUE_NAME = "NOTIFICATION_PROCESSOR"
export const APP_NAMES = ["CULTGEAR"]
export const NOTIFICATION_TYPES = ["PUSH"]

export interface INotificationService {
  sendNotification(notificationType: NotificationType, notificationParams: NotificationParams, appName: AppName): Promise<boolean>
  sendPush(notificationParams: NotificationParams, appName: AppName): Promise<boolean>
}
