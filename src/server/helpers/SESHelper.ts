import {BASE_TYPES, ILogger} from "@curefit/base"
import {EmailCreative, NotificationWrapper, UserNotificationStatus} from "../../iris-common"
import {IBasicNotificationDao, IRIS_MODELS_TYPES} from "../../iris-models"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

import {Container, inject, injectable} from "inversify"
import * as mustache from "mustache"
import * as _ from "lodash"
import * as  nodeMailer from "nodemailer"
import * as aws from "aws-sdk"

import Constants from "../constants"
import {AttachmentHelper} from "./AttachmentHelper"
import TYPES from "../ioc/types"
import {MetricUtils, ProfilerType} from "../utils/MetricUtils"
import {BillingUtils} from "../utils/BillingUtils"
import NotificationLogService from "../service/NotificationLogService"
import {CommonUtils} from "../utils/CommonUtils"
import HandlebarsUtils from "../utils/HandlebarsUtils"
import {MustacheUtils} from "../utils/MustacheUtils"
import {DataErrorV2} from "@curefit/error-client"
import FunctionalTagsUtils from "../utils/FunctionalTagsUtils"
import TagUtils from "../utils/TagUtils"

export interface ISESHelper {
    sendMailsViaSES(creative: EmailCreative, notifications: NotificationWrapper[],
                    isTransactional: boolean, dryRun?: boolean): Promise<UserNotificationStatus[]>
}

export function SESHelperFactory(kernel: Container) {

    @injectable()
    class SESHelper implements ISESHelper {
        private transporter: nodeMailer.Transporter

        constructor(
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
            @inject(TYPES.AttachmentHelper) private attachmentHelper: AttachmentHelper,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
            @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
            @inject(TYPES.NotificationLogService) private notificationLogService: NotificationLogService
        ) {

            /*
             We are using different regions for txn and promo emails
             */
            const region = process.env.DEPLOYMENT_TYPE === "server" ? "us-east-1" : "ap-south-1"
            this.transporter = nodeMailer.createTransport(  {
                SES: new aws.SES({
                    region: region
                })
            })
        }

        public async sendMailsViaSES(creative: EmailCreative, notificationWrappers: NotificationWrapper[],
                                     isTransactional: boolean, dryRun?: boolean): Promise<UserNotificationStatus[]> {
            const emailParams = creative.emailParams
            let subject = _.isEmpty(creative.title) ? null : creative.title
            if (CommonUtils.isEnvironmentStage()) {
                subject = "[STAGE]" + subject
            }
            const emailBody = creative.body
            const isHandlebars = _.get(emailParams, "templateEngine", "mustache") === "handlebars"

            const allTags = TagUtils.allTagsUsed([creative.title, creative.body])
            const renderLibrary = isHandlebars ? HandlebarsUtils : MustacheUtils

            const ret = await Promise.all(notificationWrappers.map(async notificationWrapper => {

                /*
                Adding this check here once again due to multiple user complaints on emails not stopping even after
                 unsubscription.
                 */
                if (!isTransactional) {
                    this.logger.info(`SESHelper::sendMailViaSES userId ${notificationWrapper.userContext.userId} for
                notificationId ${notificationWrapper.notification.notificationId} has unsubscribed flags as 
                ${notificationWrapper.userContext.tags[Constants.USER_SUBSCRIBED]} and ${notificationWrapper.userContext.tags[Constants.USER_SUBSCRIBED_OLD]}`)
                    if (notificationWrapper.userContext.tags[Constants.USER_SUBSCRIBED] === "false" ||
                        notificationWrapper.userContext.tags[Constants.USER_SUBSCRIBED_OLD] === "false") {
                        notificationWrapper.notification.failReason = "UNSUBSCRIBED"
                        notificationWrapper.notification.status = "FAILED"
                        this.logger.error(`SESHelper::sendMailsViaSES Unsubscribed failure for notification ` +
                            `${notificationWrapper.notification.notificationId}`)
                        return
                    }
                }
                const name = notificationWrapper.userContext.tags[Constants.USER_NAME_TAG_OLD] ||
                    notificationWrapper.userContext.tags[Constants.USER_NAME_TAG]

                const userTags = Object.assign({}, notificationWrapper.userContext.tags,
                    name ? { "name": name } : {})

                const emailId = notificationWrapper.userContext.tags[Constants.USER_EMAIL_TAG_OLD] ||
                    notificationWrapper.userContext.tags[Constants.USER_EMAIL_TAG] ||
                    notificationWrapper.userContext.emailId

                let emailCC: string[] = []
                if (notificationWrapper.userContext.emailCC && notificationWrapper.userContext.emailCC.length > 0) {
                    emailCC = notificationWrapper.userContext.emailCC
                }

                let emailBCC: string[] = []
                if (notificationWrapper.userContext.emailBcc && notificationWrapper.userContext.emailBcc.length > 0) {
                    emailBCC = notificationWrapper.userContext.emailBcc
                }

                if (!subject) {
                    notificationWrapper.notification.failReason = "MISSING_SUBJECT"
                    notificationWrapper.notification.status = "FAILED"
                    this.logger.error(`SESHelper::sendMailsViaSES Missing subject failure for notification ` +
                        `${notificationWrapper.notification.notificationId}`)
                    return
                }

                if (!emailId || !this.emailValidationCheck(emailId)) {
                    notificationWrapper.notification.failReason = "EMAIL_NOT_FOUND"
                    notificationWrapper.notification.status = "FAILED"
                    this.logger.error(`SESHelper::sendMailsViaSES Email id not found for notification ` +
                        `${notificationWrapper.notification.notificationId}`)
                    return
                }

                const fromEmail = TagUtils.render(emailParams.from, userTags, mustache)
                const userId =  notificationWrapper.userContext.userId
                const sentStatus: any = {}
                const fromName = TagUtils.render(emailParams.fromName, userTags, mustache)

                // Mustache validation needs to be done prior to rendering
                if (!isHandlebars && !MustacheUtils.contextHasAllTags(allTags, notificationWrapper.userContext.tags)) {
                    this.logger.error(`Missing data for tags for notification: ` +
                        `${notificationWrapper.notification.notificationId}`)
                    notificationWrapper.notification.failReason = "TEMPLATE_FAIL"
                    notificationWrapper.notification.status = "FAILED"
                    return
                }

                let title, body
                try {
                    title = TagUtils.render(subject, userTags, renderLibrary)
                    body = TagUtils.render(emailBody, userTags, renderLibrary)
                } catch (err) {
                    notificationWrapper.notification.failReason = "TEMPLATE_FAIL"
                    notificationWrapper.notification.status = "FAILED"
                    this.logger.error(`SESHelper::sendMailsViaSES Templating error; ` +
                        `notification: ${notificationWrapper.notification.notificationId} ` +
                        `Error: ${err.toString()}. Using render library: ${renderLibrary.typeName}`)
                    return
                }

                let mailAttachments: {filename: any, content: any, url: string}[]
                try {
                    mailAttachments = await this.attachmentHelper.getAttachments(notificationWrapper.userContext, false)
                } catch (ex) {
                    notificationWrapper.notification.failReason = "ATTACHMENT_FAIL"
                    notificationWrapper.notification.status = "FAILED"
                    this.logger.error("Could not send email to " + emailId + ", reason : ATTACHMENT_FAIL " + ex.toString())
                    this.rollbarService.sendError(new Error(`Attachment fail for ${notificationWrapper.notification.notificationId}`))
                    return
                }

                let mailOptions: any = {
                    from: fromName + "<" + fromEmail + ">",
                    to: emailId,
                    subject: title,
                    html: body,
                    cc: emailCC,
                    bcc: emailBCC,
                    attachments: mailAttachments,
                    ses: {
                        ConfigurationSetName: Constants.getConfigurationSet()
                    }
                }

                let SESId: string = ""
                if (!Constants.avoidDuplicateCheckCreative(creative.creativeId) &&
                    !Constants.isOtpRouteCreative(creative.creativeId) && await this.notificationLogService.isDuplicate(
                    { html: mailOptions.html, title }, emailId, dryRun)) {
                    notificationWrapper.notification.failReason = "DUPLICATE"
                    notificationWrapper.notification.status = "FAILED"
                    notificationWrapper.notification.billableUnits = 0
                    return
                }

                if (dryRun) {
                    sentStatus["s_" + userId] = true
                    SESId = Constants.EMAIL_EXTERNALID_PREFIX + "DRY_RUN"
                } else {
                    try {
                        const apiLatencyTimerContext =
                            this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "SES")
                        const response = await this.transporter.sendMail(mailOptions)
                        this.metricUtils.endProfilingTimer(apiLatencyTimerContext)
                        if (response && response.messageId) {
                            sentStatus["s_" + userId] = true
                            if (response.response) {
                                SESId = Constants.EMAIL_EXTERNALID_PREFIX + response.response
                            }
                        }
                    } catch (error) {
                        this.logger.error("Error while sending email", error)
                        sentStatus["s_" + userId] = false
                        sentStatus["rr_" + userId] = error
                    }
                }

                let sent = !_.isUndefined(sentStatus["s_" + notificationWrapper.userContext.userId]) ? sentStatus["s_" + notificationWrapper.userContext.userId] : false
                const uns: UserNotificationStatus = {
                    "userId": notificationWrapper.userContext.userId,
                    "creativeId": creative.creativeId,
                    "notificationId": notificationWrapper.notification.notificationId,
                    "sent": sent,
                    "failedReason": !_.isUndefined(sentStatus["rr_" + notificationWrapper.userContext.userId]) ? sentStatus["rr_" + notificationWrapper.userContext.userId] : ""
                }
                if (sent) {
                    notificationWrapper.notification.sentAt = new Date()
                    this.logger.debug("Email sent through SES for notification Id : " + notificationWrapper.notification.notificationId + " external id : " + SESId)
                    notificationWrapper.notification.externalId = SESId
                    notificationWrapper.notification.status = "SENT"
                    notificationWrapper.notification.billableUnits = dryRun ? 0 : BillingUtils.getEmailUnitsConsumed()
                    await this.notificationLogService.logNotification({ html: mailOptions.html, title },
                        emailId, "EMAIL", isTransactional, dryRun)
                } else {
                    this.logger.error(`Email for ${notificationWrapper.notification.notificationId} not sent due to ${uns.failedReason}`)
                    this.rollbarService.sendError(new Error(`Email for ${notificationWrapper.notification.notificationId} not sent due to ${uns.failedReason}`))
                    notificationWrapper.notification.failReason = "SEND_FAIL"
                    notificationWrapper.notification.status = "FAILED"
                }
                return uns
            }))
            return Promise.resolve(ret)
        }

        /*
        A lot of services send comma seperated emails in email field. We ned to trigger an email if even one
         email is valid.

         */
        private emailValidationCheck(emailIds: string): boolean {
            const emails: string[] = emailIds.split(",")

            const emailValidationRegexp = new RegExp(Constants.EMAIL_VALIDATION_REGEX)

            for (let email of emails) {
                if (emailValidationRegexp.test(email))
                    return true
            }

            return false
        }

    }

    return SESHelper
}

