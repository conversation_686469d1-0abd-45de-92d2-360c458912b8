import {BASE_TYPES, ILogger} from "@curefit/base"
import {User, WhatsappEnum} from "@curefit/user-common"
import {UserAttributesResponse} from "@curefit/rashi-client/src/rest/IUserAttributeClient"

import {inject, injectable} from "inversify"
import * as _ from "lodash"
const snappy = require("snappy")

import TYPES from "../ioc/types"
import Constants from "../constants"
import FeatureUtils from "../utils/FeatureUtils"
import CreativeService from "../service/CreativeService"
import {BaseCreative, PatternForAttributes, UserTags} from "../../iris-common"
import UserProfileAttributeUtils from "../utils/UserProfileAttributeUtils"
import {AWSUtils} from "../utils/AWSUtils"
import {MetricUtils, ProfilerType} from "../utils/MetricUtils"

import {FunctionalTagsService} from "../service/FunctionalTagsService"
import { USER_CLIENT_TYPES, IUserService } from "@curefit/user-client"
@injectable()
class UserContextHelper {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(TYPES.FeatureUtils) private featureUtils: FeatureUtils,
        @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
        @inject(TYPES.UserProfileAttributeUtils) private userProfileAttributeUtils: UserProfileAttributeUtils,
        @inject(TYPES.AWSUtils) private awsUtils: AWSUtils,
        @inject(TYPES.FunctionalTagsService) private functionalTagsService: FunctionalTagsService,

    ) {
    }

    public checkTagLengths(creativeId: string, userTags: any, creativeTags: Set<string>) {
        for (const key in userTags) {
            if (!userTags.hasOwnProperty(key)) {
                continue
            }

            if (_.isObject(userTags[key])) {
                this.checkTagLengths(creativeId, userTags[key], creativeTags)
            } else if (key in creativeTags && ("" + (userTags[key] || "")).length > Constants.SMS_TEMPLATE_VALUE_MAX_LENGTH) {
                const errorMessage = `UserContextHelper::checkTagLengths template variable more than 30 ` +
                    `characters. Creative: ${creativeId} userTag: ${key} ` +
                    `value: ${userTags[key]}`
                this.logger.error(errorMessage)
                throw Error(errorMessage)

            }
        }
    }

    public encodeUserContext(userContext: UserTags): string {
        /*
            Our current MySQL server uses utf8 charset, which only partially implements proper UTF-8 encoding.
            It can only store utf8 symbols that can be represented using 3 bytes.
            We should move the db charset to utf8mb4 as a proper solve.
            As a workaround, we convert to base64 before writing.
            https://mathiasbynens.be/notes/mysql-utf8mb4#utf8-to-utf8mb4

            1. Stringify userContext object
            2. Compress using snappy (to save space)
            3. Convert to base64 ( to ensure that the db TEXT column accepts utf-8 objects)
         */

        if (this.featureUtils.encodeUserTagString()) {
            const userContextString = JSON.stringify(userContext)
            const compressedUserContextString = snappy.compressSync(userContextString)
            return Buffer.from(compressedUserContextString).toString("base64")
        } else {
            return JSON.stringify(userContext)
        }
    }

    public decodeUserContext(userContextString: string, generateAttachmentSignedUrls: boolean = false): UserTags {
        /*
            If the decoded base64 string is a valid snappy compressed string,
            decompress and evaluate
            Else, evaluate directly as a string.

            Does not check for `encodeUserTagString` feature flag, since it can decode both
         */

        let userContextBuffer = Buffer.from(userContextString, "base64")
        if (snappy.isValidCompressedSync(userContextBuffer)) {
            userContextBuffer = snappy.uncompressSync(userContextBuffer, { asBuffer: true })
            return JSON.parse(userContextBuffer.toString("utf-8")) as UserTags
        } else {
            return JSON.parse(userContextString) as UserTags
        }
    }

    public async generateSignedUrlForAttachments(userContext: UserTags): Promise<UserTags> {
        for (let attachment of userContext.attachments) {
            if (attachment.s3Info)
                attachment.signedUrl = await this.awsUtils.getSignedUrlForGetObject(attachment.s3Info.bucket, attachment.s3Info.key)
        }
        return userContext
    }


    private clearUserTags(userContext: UserTags) {
        userContext.tags[Constants.USER_EMAIL_TAG] = userContext.tags[Constants.USER_EMAIL_TAG_OLD] = null
        userContext.tags[Constants.USER_NAME_TAG] = userContext.tags[Constants.USER_NAME_TAG_OLD] = null
        userContext.tags[Constants.USER_PHONE_TAG] = userContext.tags[Constants.USER_PHONE_TAG_OLD] = null
        userContext.tags[Constants.USER_SUBSCRIBED] = userContext.tags[Constants.USER_SUBSCRIBED_OLD] = null
    }

    public async enrichUserTags(userContexts: UserTags[], creatives: BaseCreative[],
                                appId: string): Promise<UserTags[]> {

        // Internal apps, do not user user cache / rashi
        if (Constants.INTERNAL_APP_IDS.indexOf(appId) > -1) { return userContexts }

        // This fetches some basic user tags from rashi or user cache
        // For instance, email, phone number, etc.
        if (await this.featureUtils.useRashiForUserTags()) {
            this.logger.debug(`CampaignHelper::enrichUserTags Using Rashi for user tags`)
            let timerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF, "useRashiForUserTags")
            userContexts = await this.enrichUserTagsFromRashi(userContexts, appId)
            this.metricUtils.endProfilingTimer(timerContext)
        } else {
            this.logger.debug(`CampaignHelper::enrichUserTags Using UserCache for user tags`)
            let timerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF, "enrichUserTagsFromUserCache")
            userContexts = await this.enrichUserTagsFromUserCache(userContexts)
            this.metricUtils.endProfilingTimer(timerContext)
        }
        // This populates rashi tags embedded in the creative

        let timerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF, "enrichUserProfileAttributes")
        let enrichedTags = await this.enrichAttributesHelper(userContexts, creatives, appId,
            {prefix: Constants.USER_PROFILE_ATTRIBUTE_TAG_PREFIX,
                suffix: Constants.USER_PROFILE_ATTRIBUTE_TAG_SUFFIX, regex: Constants.USER_PROFILE_ATTRIBUTE_TAG_REG_EXP},
            false)
        this.metricUtils.endProfilingTimer(timerContext)

        //Populate Global Attributes from Rashi in the creative
        try {
            enrichedTags = await this.enrichAttributesHelper(userContexts, creatives, appId,
                {prefix: Constants.GLOBAL_ATTRIBUTE_TAG_PREFIX,
                    suffix: Constants.GLOBAL_ATTRIBUTE_TAG_SUFFIX, regex: Constants.GLOBAL_ATTRIBUTE_TAG_REG_EXP},
                true)
        } catch (e) {
            this.logger.error(`UserContextHelper::enrichUserTags unable to enrich Global Attributes because ${e}`)
        }

        //Populate Functional tags
        try {
            enrichedTags = await this.enrichAttributesHelper(userContexts, creatives, appId,
                {prefix: Constants.PERSONALIZED_ATTRIBUTE_TAG_PREFIX,
                    suffix: Constants.PERSONALIZED_ATTRIBUTE_TAG_SUFFIX, regex: Constants.PERSONALIZED_ATTRIBUTE_TAG_REG_EXP},
                true, true)
        } catch (e) {
            this.logger.error(`UserContextHelper::enrichUserTags unable to enrich Personalized Attributes because ${e}`)
        }

        return enrichedTags
    }

    public async enrichUserTagsFromUserCache(userContexts: UserTags[]): Promise<UserTags[]> {
        const userIds = userContexts.filter(uc => uc.userId != null).map(uc => uc.userId)
        let sliceLength = 10
        let start = 0
        let end = sliceLength
        let userList: {[userId: string]: User} = {}

        do {
            const usersList = await this.userService.getUsersCached(userIds.slice(start, end))
            Object.assign(userList, this.getUserMap(usersList))
            start = end
            end += sliceLength
        } while (start < userIds.length)
        const usersMap = _.keyBy(userList, u => u.id)

        for (const userContext of userContexts) {
            if (!userContext.userId) { continue }

            const user = usersMap[userContext.userId]
            this.clearUserTags(userContext)

            userContext.tags[Constants.USER_ID_TAG] = userContext.userId
            if (user) {
                userContext.tags[Constants.USER_EMAIL_TAG] = userContext.tags[Constants.USER_EMAIL_TAG_OLD] = user.email
                userContext.tags[Constants.USER_NAME_TAG] = userContext.tags[Constants.USER_NAME_TAG_OLD] =
                    (user.firstName ? user.firstName : "") + " " + (user.lastName ? user.lastName : "")
                userContext.tags[Constants.USER_PHONE_TAG] = userContext.tags[Constants.USER_PHONE_TAG_OLD] =
                    user.countryCallingCode ? user.countryCallingCode + "" + user.phone : user.phone
                userContext.tags[Constants.USER_SUBSCRIBED] = userContext.tags[Constants.USER_SUBSCRIBED_OLD] =
                    user.isSubscribedForCommunications === false ? "false" : "true"
            } else {
                this.logger.error(`UserContextHelper::enrichUserTagsFromUserCache Could not fetch user ` +
                    `for userId ${userContext.userId}`)
            }
        }

        return userContexts
    }

    public async enrichUserTagsFromRashi(userContexts: UserTags[], appId: string): Promise<UserTags[]> {
        const userIds = userContexts.filter(uc => uc.userId != null).map(uc => uc.userId)
        const userAttributes: { [userId: string]: Map<string, any>} = {}

        // Fetch for all users
        for (const userIdChunk of _.chunk(userIds, 10)) {
            let promises: Promise<UserAttributesResponse>[] = []
            for (const userId of userIdChunk) {
                promises.push(this.userProfileAttributeUtils.getCachedUserAttributes(userId, [
                    Constants.USER_PROFILE_ATTRIBUTE_KEY_FULL_NAME,
                    Constants.USER_PROFILE_ATTRIBUTE_KEY_EMAIL,
                    Constants.USER_PROFILE_ATTRIBUTE_KEY_PHONE,
                    Constants.USER_PROFILE_ATTRIBUTE_KEY_IS_SUBSCRIBED,
                    Constants.PREFERRED_CENTER,
                    Constants.PLAY_PREFERRED_CENTER,
                    Constants.PLAY_PREFERRED_WORKOUT,
                    Constants.CITY_ID], appId))
            }
            const responses = await Promise.all(promises)
            for (const response of responses) {
                userAttributes[response.userId] = response.attributes
            }
        }

        // Assign for each notification
        for (const userContext of userContexts) {
            if (!userContext.userId) { continue }

            const user = userAttributes[userContext.userId]
            if (!!user) {
                this.clearUserTags(userContext)
                userContext.tags[Constants.USER_ID_TAG] = userContext.userId
                userContext.tags[Constants.USER_EMAIL_TAG] = userContext.tags[Constants.USER_EMAIL_TAG_OLD] =
                    user.get(Constants.USER_PROFILE_ATTRIBUTE_KEY_EMAIL) || null
                userContext.tags[Constants.USER_NAME_TAG] = userContext.tags[Constants.USER_NAME_TAG_OLD] =
                    user.get(Constants.USER_PROFILE_ATTRIBUTE_KEY_FULL_NAME) || null
                userContext.tags[Constants.USER_PHONE_TAG] = userContext.tags[Constants.USER_PHONE_TAG_OLD] =
                    !!user.get(Constants.USER_PROFILE_ATTRIBUTE_KEY_PHONE) ?
                        user.get(Constants.USER_PROFILE_ATTRIBUTE_KEY_PHONE).toString().replace("-", "") : null
                userContext.tags[Constants.USER_SUBSCRIBED] = userContext.tags[Constants.USER_SUBSCRIBED_OLD] =
                    user.get(Constants.USER_PROFILE_ATTRIBUTE_KEY_IS_SUBSCRIBED) == null ? null :
                        user.get(Constants.USER_PROFILE_ATTRIBUTE_KEY_IS_SUBSCRIBED).toString()
                userContext.tags[Constants.PREFERRED_CENTER] =
                    user.get(Constants.PREFERRED_CENTER) == null ? null :
                        user.get(Constants.PREFERRED_CENTER).toString()
                userContext.tags[Constants.PLAY_PREFERRED_CENTER] =
                    user.get(Constants.PLAY_PREFERRED_CENTER) == null ? null :
                        user.get(Constants.PLAY_PREFERRED_CENTER).toString()
                userContext.tags[Constants.PLAY_PREFERRED_WORKOUT] =
                    user.get(Constants.PLAY_PREFERRED_WORKOUT) == null ? null :
                        user.get(Constants.PLAY_PREFERRED_WORKOUT).toString()
                userContext.tags[Constants.CITY_ID] =
                    user.get(Constants.CITY_ID) == null ? null :
                        user.get(Constants.CITY_ID).toString()
            } else {
                this.logger.error(`UserContextHelper::enrichUserTagsFromRashi Could not fetch user ` +
                    `for userId ${userContext.userId}`)
            }
        }

        return userContexts
    }

    // User attribute sample tag : { "tag" : "prefixATTRIBUTEsuffix" }
    public async enrichAttributesHelper(userContexts: UserTags[], creatives: BaseCreative[], appId: string, pattern: PatternForAttributes, isGlobalAtt: boolean, isPersonalAtt = false): Promise<UserTags[]> {

        const attributeTagsInCreative = []

        for (const creative of creatives) {
            const parsableText = CreativeService.getAllParsableText(creative).join(" ")
            let match
            const regexp = new RegExp(pattern.regex, "g")
            while (match = regexp.exec(parsableText)) {
                attributeTagsInCreative.push(match[0].slice(
                    pattern.prefix.length,
                    - pattern.suffix.length))
            }
        }

        for (const userContext of userContexts) {
            // Extract userAttributes to be populated.
            const values = _.values(userContext.tags)
            let attributeTags = values
                .filter(value => {
                    return String(value).startsWith(pattern.prefix) &&
                        String(value).endsWith(pattern.suffix)})
                .map(value => {
                    return value.slice(pattern.prefix.length,
                        - pattern.suffix.length)})

            attributeTags = [...attributeTags, ...attributeTagsInCreative]
            let attributes: Map<string, any> = new Map<string, any>()
            this.logger.debug(`UserContextHelper::enrichAttributesHelper attributeTags ${attributeTags}`)

            // Fetch/Evaluate the Attributes
            if (isPersonalAtt) {
                if (!_.isEmpty(attributeTags)) {
                    attributes = await this.functionalTagsService.evaluatePersonalisedAttributes(attributeTags, userContext)
                }
            }
            else if (isGlobalAtt) {
                if (!_.isEmpty(attributeTags)) {
                    // attributes = await this.userProfileAttributeUtils.getCachedGlobalAttributes(appId, attributeTags)
                    attributes = await this.userProfileAttributeUtils.getInterpretedCachedGlobalAttributes(appId, attributeTags)
                    attributes.forEach((value: any, key: string) => {
                        this.logger.info(`Global Attributes key: ${key} value ${value}`)
                        userContext.tags[key] = value
                    })
                }
            } else {
                if (!_.isEmpty(attributeTags) && userContext.userId) {
                    attributes = (await this.userProfileAttributeUtils.getCachedUserAttributes(
                        userContext.userId, attributeTags, appId)).attributes
                }
            }
            this.logger.debug(`UserContextHelper::enrichAttributesHelper attributes fetched for userId ${userContext.userId} phone ${userContext.phone} and appId ${appId} is ${attributes} ${JSON.stringify(attributes)}`)

            // Populate
            for (const tag of _.keys(userContext.tags)) {
                const tagValue = userContext.tags[tag]

                // Replace user profile attribute tag values
                if (String(tagValue).startsWith(pattern.prefix) && String(tagValue).endsWith(pattern.suffix)) {
                    const userProfileAttributeValue = attributes.get(
                        tagValue.slice(
                            pattern.prefix.length,
                            - pattern.suffix.length))

                    if (userProfileAttributeValue) {
                        userContext.tags[tag] = userProfileAttributeValue
                    } else {
                        /*
                         * Deliberately done, despite the perils.
                         * We want the template to fail in case the attribute is not present.
                         * The only way to ensure that is by removing the key from the object.
                         * Setting as null or undefined ** does not ** remove the key from the object.
                         */
                        delete userContext.tags[tag]
                    }
                }

                // Add profile attributes in creative to context if found
                for (const tag of attributeTagsInCreative) {
                    const value = attributes.get(tag)
                    if (value) {
                        userContext.tags[
                        pattern.prefix +
                        tag +
                        pattern.suffix] = value
                        this.logger.debug(`UserContextHelper::enrichAttributesHelper adding value in userContext 
                        ${pattern.prefix + tag + pattern.suffix} value: ${value}`)
                    }
                }
            }
        }

        return userContexts
    }

    public getUserMap(users: User[]): { [userId: string]: User } {
        return users.reduce<{ [userId: string]: User }>((accumulator, user) => {
            accumulator[user.id] = user
            return accumulator
        }, {})
    }

}

export default UserContextHelper