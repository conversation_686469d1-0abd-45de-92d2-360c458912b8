import {Container, inject, injectable} from "inversify"
import {BASE_TYPES, <PERSON>tch<PERSON>til, FetchUtilV2, <PERSON><PERSON><PERSON>, UrlUtil} from "@curefit/base"
import * as _ from "lodash"
import {Country, NotificationWrapper, OBDCreative, UserNotificationStatus} from "../../iris-common"
import Constants from "../constants"
import * as mustache from "mustache"
import * as  nodeMailer from "nodemailer"
import {PhoneNumberUtils} from "../utils/PhoneNumberUtils"
import {MustacheUtils} from "../utils/MustacheUtils"
import {MetricUtils, ProfilerType} from "../utils/MetricUtils"
import TYPES from "../ioc/types"
import NotificationLogService from "../service/NotificationLogService"
import TagUtils from "../utils/TagUtils"

const fetch = require("node-fetch")

export interface IKaleyraOBDHelper {
    sendNotifViaKaleyra(creative: OBDCreative, notifications: NotificationWrapper[], allTags: string[],
                        isTransactional: boolean, dryRun?: boolean): Promise<UserNotificationStatus[]>
}

interface KaleyraResponse {
    status: string, // "200"
    return: {
        group_id: number,
        data: [{
            id: string,
            mobile: string,
            status: string // SENT
        }]
    },
    refund: number,
    skipped: number,
    dnd: number,
    credits: number,
    total: number,
    message: string
}


export function KaleyraHelperFactory(kernel: Container) {

    @injectable()
    class KaleyraOBDHelper implements IKaleyraOBDHelper {
        private transporter: nodeMailer.Transporter

        constructor(
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(BASE_TYPES.UrlUtil) private urlUtil: UrlUtil,
            @inject(BASE_TYPES.FetchUtilV2) private fetchUtilV2: FetchUtilV2,
            @inject(BASE_TYPES.FetchUtil) private fetchUtil: FetchUtil,
            @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
            @inject(TYPES.NotificationLogService) private notificationLogService: NotificationLogService
        ) {
        }


        public async sendNotifViaKaleyra(creative: OBDCreative, notifications: NotificationWrapper[], allTags: string[],
                                         isTransactional: boolean, dryRun?: boolean): Promise<UserNotificationStatus[]> {
            // Load required values for the request
            await Promise.all(notifications.map(async n => {
                const phone = n.userContext.tags[Constants.USER_PHONE_TAG_OLD] ||
                    n.userContext.tags[Constants.USER_PHONE_TAG] || n.userContext.phone
                const name = n.userContext.tags[Constants.USER_NAME_TAG_OLD] || n.userContext.tags[Constants.USER_NAME_TAG]
                if (!phone) {
                    n.notification.failReason = "PHONE_NOT_FOUND"
                    n.notification.status = "FAILED"
                    this.logger.error("Could not send obd to userId: " + n.userContext.userId + ", email:" +
                        n.userContext.emailId + ", reason : PHONE_NOT_FOUND Phone number not found")
                    return
                }
                const userTags = Object.assign({}, n.userContext.tags, name ? {"name": name} : {})
                if (!MustacheUtils.contextHasAllTags(allTags, userTags)) {
                    n.notification.failReason = "TEMPLATE_FAIL"
                    n.notification.status = "FAILED"
                    this.logger.error("Could not send OBD to " + phone + ", reason : TEMPLATE_FAIL")
                    return
                }
                const body =
                    encodeURIComponent(TagUtils.render(creative.body, userTags, mustache))
                const useCaseId =
                    encodeURIComponent(TagUtils.render(creative.obdParams.useCaseId, userTags, mustache))
                if (dryRun) {
                    n.notification.sentAt = new Date()
                    n.notification.status = "SENT"
                    n.notification.externalId = Constants.SOLUTIONS_INFINI + "_" + "DRY_RUN"
                }
                this.logger.info(`Creative :: ${JSON.stringify(creative)}`)
                if (_.has(creative, "obdParams.serviceProviderInfo.kaleyra.useClickToCallAPI")
                    && creative.obdParams.serviceProviderInfo.kaleyra.useClickToCallAPI) {
                    await this.sendOBDNotificationV2(creative, useCaseId, body, phone, dryRun, n, isTransactional)
                } else {
                    await this.sendOBDNotificationV1(creative, useCaseId, body, phone, dryRun, n, isTransactional)
                }
            }))

            const ret: UserNotificationStatus[] = notifications.map(n => {

                return {
                    "userId": n.userContext.userId,
                    "creativeId": creative.creativeId,
                    "notificationId": n.notification.notificationId,
                    "sent": !!n.notification.sentAt,
                    "failedReason": n.notification.failReason
                }
            })

            return Promise.resolve(ret)
        }

        private getURLForOBDV1(country: Country, phone: string, apiKey: string, useCaseId: string, body: string, callbackUrl: string) {
            let templateUrl = Constants.getKaleyraOBDTemplateUrlV1(country)
            mustache.parse(templateUrl)
            const urlForUser = this.urlUtil.constructUrl(templateUrl, "", [
                {key: "method", value: "plugin.text2speech"},
                {key: "caller", value: phone},
                {key: "api_key", value: apiKey},
                {key: "name", value: useCaseId},
                {key: "format", value: "json"},
                {key: "message", value: body},
                {key: "task", value: "save"},
                {key: "callback", value: callbackUrl}
            ])
            return urlForUser
        }

        private async sendOBDNotificationV1(creative: OBDCreative, useCaseId: string, body: string, phone: string, dryRun: boolean, n: NotificationWrapper, isTransactional: boolean) {
            const country = PhoneNumberUtils.getCountry(phone)
            const apiKey = Constants.getKaleyraOBDApiKey(country, phone, creative.obdParams.sender)
            const callbackUrl = encodeURIComponent(mustache.render(Constants.getKaleyraOBDCallbackUrl(),
                {"id": n.notification.notificationId}))
            const urlForUser = this.getURLForOBDV1(country, phone, apiKey, useCaseId, body, callbackUrl)
            if (!Constants.isOtpRouteCreative(creative.creativeId) && await this.notificationLogService.isDuplicate({
                useCaseId,
                body
            }, phone, dryRun)) {
                n.notification.failReason = "DUPLICATE"
                n.notification.status = "FAILED"
                this.logger.info(`KaleyraOBDHelper::sendNotifViaKaleyra Duplicate notification ` +
                    `${n.notification.notificationId}. Not sending.`)
                return
            }

            try {
                const apiLatencyTimerContext =
                    this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "Kaleyra OBD")
                const response = await fetch(urlForUser, this.fetchUtil.post({}))
                this.metricUtils.endProfilingTimer(apiLatencyTimerContext)

                const kaleyraResponse: KaleyraResponse =
                    await this.fetchUtil.parseResponse<KaleyraResponse>(response)
                this.logger.info(`Kaleyra response: ${JSON.stringify(kaleyraResponse)}`)
                const sent: boolean =
                    (kaleyraResponse.status === "200" &&
                        (kaleyraResponse.message === "OK" ||
                            (kaleyraResponse.return.data && kaleyraResponse.return.data[0].status === "SENT")))
                if (sent) {
                    n.notification.sentAt = new Date()
                    n.notification.status = "SENT"
                    if (kaleyraResponse.return &&
                        kaleyraResponse.return.data &&
                        kaleyraResponse.return.data[0].id) {
                        n.notification.externalId = Constants.SOLUTIONS_INFINI + "_" + kaleyraResponse.return.data[0].id
                    }

                    // Log notification for duplicate checks and throttling
                    await this.notificationLogService.logNotification(
                        {useCaseId, body}, phone, "OBD", isTransactional, dryRun)

                } else {
                    n.notification.status = "FAILED"
                    n.notification.failReason = kaleyraResponse.status ?
                        Constants.SOLUTIONS_INFINI + "_" + kaleyraResponse.status : "SEND_FAIL"
                    let providerFailReason
                    if (kaleyraResponse.return && !_.isEmpty(kaleyraResponse.return.data)) {
                        if (kaleyraResponse.return.data[0].status === "DNDNUMB") n.notification.failReason = "DND"
                        providerFailReason = Constants.SOLUTIONS_INFINI + kaleyraResponse.return.data[0].status
                        n.notification.failReason = providerFailReason
                    }

                    this.logger.error("Could not send OBD to " + phone + ", reason : " + JSON.stringify(kaleyraResponse))
                }
            } catch (error) {
                this.logger.error("Error while sending OBD to " + phone, error)
                n.notification.failReason = "SEND_FAIL"
                n.notification.status = "FAILED"

            }
        }



        private getURLForOBDV2(country: Country, phone: string, apiKey: string, useCaseId: string) {
            let templateUrl = Constants.getKaleyraOBDTemplateUrlV2(country)
            mustache.parse(templateUrl)
            const urlForUser = this.urlUtil.constructUrl(templateUrl, "", [
                {key: "method", value: "dial.click2call"},
                {key: "caller", value: phone},
                {key: "api_key", value: apiKey},
                {key: "receiver", value: "ivr:".concat(useCaseId)}
            ])
            return urlForUser
        }

        private async sendOBDNotificationV2(creative: OBDCreative, useCaseId: string, body: string, phone: string, dryRun: boolean, n: NotificationWrapper, isTransactional: boolean) {
            const country = PhoneNumberUtils.getCountry(phone)
            const apiKey = Constants.getKaleyraOBDApiKey(country, phone, creative.obdParams.sender)
            const urlForUser = this.getURLForOBDV2(country, phone, apiKey, useCaseId)
            try {
                const apiLatencyTimerContext =
                    this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "Kaleyra OBD V2")
                const response = await fetch(urlForUser, this.fetchUtilV2.post({timeout : 5000}))
                this.metricUtils.endProfilingTimer(apiLatencyTimerContext)
                this.logger.info(`Kaleyra response: ${JSON.stringify(response)}`)
                // kaleyra response is of size 0, for both success and failure, with status code 200.
                n.notification.sentAt = new Date()
                n.notification.status = "SENT"
                // Log notification for duplicate checks and throttling
                await this.notificationLogService.logNotification({useCaseId, body}, phone, "OBD", isTransactional, dryRun)
            } catch (error) {
                this.logger.error("Error while sending OBD to " + phone, error)
                n.notification.failReason = "SEND_FAIL"
                n.notification.status = "FAILED"
            }
        }
    }

    return KaleyraOBDHelper
}
