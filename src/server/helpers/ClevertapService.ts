import BaseClevertapService from "./BaseClevertapService"
import {AppName, ClevertapCredentials, Constants} from "@curefit/base-utils"
import {INotificationService, NotificationParams, NotificationType} from "./INotificationService"
import {injectable} from "inversify"

interface ClevertapNotificationParams extends NotificationParams {}

@injectable()
class ClevertapService extends BaseClevertapService implements INotificationService {
    public sendNotification(notificationType: NotificationType, notificationParams: NotificationParams, appName: AppName): Promise<boolean> {
        switch (notificationType) {
            case "PUSH":
                return this.sendPush(notificationParams, appName)
            default:
                Promise.reject("Unknown notification type")
        }
    }

    public async sendPush(notificationParams: ClevertapNotificationParams, appName: AppName): Promise<boolean> {
        await this.makePostRequest("/1/send/push.json", notificationParams, this.headers(appName))
        return true
    }

    private headers(appName: AppName): ClevertapCredentials {
        return Constants.getClevertapCredentials(appName)
    }
}

export default ClevertapService
