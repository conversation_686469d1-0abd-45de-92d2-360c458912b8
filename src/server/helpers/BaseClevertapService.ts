import {BASE_TYPES, FetchUtil, ILogger} from "@curefit/base"
import {Constants} from "@curefit/base-utils"
import {inject, injectable} from "inversify"

const fetch = require("node-fetch")

@injectable()
class BaseClevertapService {

  constructor(
    @inject(BASE_TYPES.FetchUtil) private fetchUtil: FetchUtil,
    @inject(BASE_TYPES.ILogger) private logger: ILogger
  ) { }

  public async makePostRequest<T>(path: string, postData: any, headers?: any): Promise<T> {
    this.logger.info(`Clevertap request: path: ${path}, postData: ${JSON.stringify(postData)}, headers: ${JSON.stringify(headers)}`)
    const response = await fetch(Constants.getClevertapUrl(path), this.fetchUtil.post(postData, headers))
    const parsedResponse = await this.fetchUtil.parseResponse<T>(response)
    this.logger.info(`Clevertap parsedResponse: ${JSON.stringify(parsedResponse)}`)
    return parsedResponse
  }
}

export default BaseClevertapService
