import {BASE_TYPES, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, UrlUtil} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

import {Container, inject, injectable} from "inversify"

import TYPES from "../ioc/types"
import Constants from "../constants"
import {MetricUtils} from "../utils/MetricUtils"
import {PhoneNumberUtils} from "../utils/PhoneNumberUtils"
import {KnowlarityHelper} from "./KnowlarityHelper"
import LoadBalancerService from "../service/LoadBalancerService"
import {<PERSON>Helper} from "./MozartHelper"
import NotificationAttemptHelper from "./NotificationAttemptHelper"
import {SolutionsInfiniHelper} from "./SolutionsInfiniHelper"
import {BillingUtils} from "../utils/BillingUtils"
import {
    C2CServiceAccount,
    ClickToCallCreative, ExternalCommunicationCampaign,
    NotificationWrapper,
    SendCampaignNotificationsRequest,
    ServiceProvider,
    UserNotificationStatus
} from "../../iris-common"
import NotificationReportHelper from "./NotificationReportHelper"
import {IBasicNotificationDao, INotificationReportReadonlyDao, IRIS_MODELS_TYPES} from "../../iris-models"
import NotificationStatusUpdatePublisher from "../publishers/NotificationStatusUpdatePublisher"
import UserInteractionPublisher from "../publishers/UserInteractionPublisher"
import TagUtils from "../utils/TagUtils"
import {ClickToCallReportModel} from "../../iris-models/src/models/notifications/NotificationReportModels"
import _ = require("lodash")

export interface IClickToCallNotificationHelper {
    sendNotifications(creative: ClickToCallCreative,  notifications: NotificationWrapper[],
                      isTransactional: boolean, dryRun?: boolean): Promise<UserNotificationStatus[]>
    handleReport(serviceProvider: ServiceProvider, notificationId: string, clickToCallProviderReport: any): Promise<any>
    handleInboundCallReport(serviceProvider: ServiceProvider, notificationId: string, inboundCallProviderReport: any): Promise<void>
    getNotificationCreationRequestForInboundCall(externalCampaign: ExternalCommunicationCampaign, caller: string): SendCampaignNotificationsRequest
}

export interface ClickToCallProviderResponse {
    status: string,
    callId?: string,
    failReason?: string
}

export function ClickToCallNotificationHelperFactory(kernel: Container) {

    @injectable()
    class ClickToCallNotificationHelper implements IClickToCallNotificationHelper {

        constructor(
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(BASE_TYPES.UrlUtil) private urlUtil: UrlUtil,
            @inject(BASE_TYPES.FetchUtil) private fetchUtil: FetchUtil,
            @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
            @inject(TYPES.KnowlarityHelper) private knowlarityHelper: KnowlarityHelper,
            @inject(TYPES.UserInteractionPublisher) private userInteractionPublisher: UserInteractionPublisher,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
            @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
            @inject(TYPES.SolutionsInfiniHelper) private solutionsInfiniHelper: SolutionsInfiniHelper,
            @inject(TYPES.LoadBalancerService) private loadBalancerService: LoadBalancerService,
            @inject(TYPES.MozartHelper) private mozartHelper: MozartHelper,
            @inject(TYPES.NotificationReportHelper) private notificationReportHelper: NotificationReportHelper,
            @inject(TYPES.NotificationAttemptHelper) private notificationAttemptHelper: NotificationAttemptHelper,
            @inject(TYPES.NotificationStatusUpdatePublisher) private notificationStatusUpdatePublisher: NotificationStatusUpdatePublisher,
            @inject(IRIS_MODELS_TYPES.NotificationReportReadonlyDao) private notificationReportReadonlyDao: INotificationReportReadonlyDao,
        ) { }

        public async sendNotifications(creative: ClickToCallCreative,  notifications: NotificationWrapper[],
                                       isTransactional: boolean, dryRun?: boolean): Promise<UserNotificationStatus[]> {

            await Promise.all(notifications.map(async currentNotification => {
                const allTags = TagUtils.allTagsUsed([ creative.body || "" ])

                const receiverNumber =  currentNotification.userContext.phone || currentNotification.userContext.tags[Constants.USER_PHONE_TAG_OLD] ||
                    currentNotification.userContext.tags[Constants.USER_PHONE_TAG]

                if (!receiverNumber) {
                    currentNotification.notification.failReason = "PHONE_NOT_FOUND"
                    currentNotification.notification.status = "FAILED"
                    this.logger.error("receiver number is not present for userId: " + currentNotification.userContext.userId)
                    return
                }
                const country = PhoneNumberUtils.getCountry(receiverNumber)
                const isConfidential = Constants.isConfidential(creative)
                let serviceAccount: C2CServiceAccount = !!creative.clickToCallParams && !!creative.clickToCallParams.serviceAccount ? creative.clickToCallParams.serviceAccount : Constants.C2C_SERVICE_ACCOUNT.CUREFIT
                let nextServiceProvider = await this.getProviderFromLB(currentNotification.notification.notificationId, Constants.getValidServiceProviders(country, isConfidential, serviceAccount), creative.clickToCallParams ? creative.clickToCallParams.serviceProvider : null)
                if (!nextServiceProvider) { this.logger.error("No valid service providers for taskId: " + currentNotification.notification.taskId) }
                while (nextServiceProvider) {
                    try {
                        await this.clickToCallImpl(nextServiceProvider, creative, currentNotification, allTags, dryRun)
                        nextServiceProvider = null
                    }
                    catch (e) {
                        this.logger.error(e)
                        if (isTransactional) {
                            nextServiceProvider = await this.getProviderFromLB(currentNotification.notification.notificationId, Constants.getValidServiceProviders(country, isConfidential, serviceAccount))
                        }
                        else {
                            //DO NOT TRY AGAIN IF CAMPAIGN IS NOT TRANSACTIONAL
                            nextServiceProvider = null
                        }
                    }
                }
            }))

            const ret: UserNotificationStatus[] = notifications.map(n => {
                return {
                    "userId": n.userContext.userId,
                    "creativeId": creative.creativeId,
                    "notificationId": n.notification.notificationId,
                    "sent": !!n.notification.sentAt,
                    "failedReason": n.notification.failReason
                }
            })

            return Promise.resolve(ret)
        }

        private async clickToCallImpl(serviceProvider: string,
            creative: ClickToCallCreative, currentNotification: NotificationWrapper, allTags: string[], dryRun?: boolean) {
            const isConfidential = Constants.isConfidential(creative)
            const callerNumber = currentNotification.userContext.tags["agentNumber"]
            const callerId = currentNotification.userContext.tags["bridgeNumber"]?.slice(-10)
            if (!callerNumber) {
                this.logger.error("No agentNumber found to attempt click to call")
                throw new Error("Call failed")
            }

            const receiverNumber = currentNotification.userContext.phone ||
                currentNotification.userContext.tags[Constants.USER_PHONE_TAG_OLD] ||
                currentNotification.userContext.tags[Constants.USER_PHONE_TAG]

            if (!receiverNumber) {
                currentNotification.notification.failReason = "PHONE_NOT_FOUND"
                currentNotification.notification.status = "FAILED"
                this.logger.error("Could not make call to userId: " + currentNotification.userContext.userId +
                    ", email:" + currentNotification.userContext.emailId + ", reason : PHONE_NOT_FOUND Phone number not found")
                return
            }
            const country = PhoneNumberUtils.getCountry(receiverNumber)
            //check if service account is there else use default
            !!creative.clickToCallParams ? this.logger.info(`Service Account Received: ${creative.clickToCallParams.serviceAccount}`) : this.logger.error(`Service Account Received: No clickToCallParams found`)
            let serviceAccount: C2CServiceAccount = !!creative.clickToCallParams && !!creative.clickToCallParams.serviceAccount ? creative.clickToCallParams.serviceAccount : Constants.C2C_SERVICE_ACCOUNT.CUREFIT
            this.logger.info(`serviceAccount set to: ${serviceAccount}`)
            let response: ClickToCallProviderResponse
            switch (serviceProvider) {
                case Constants.SOLUTIONS_INFINI:
                    response = await this.solutionsInfiniHelper.makeClickToCall(callerNumber, receiverNumber,
                        currentNotification.notification.notificationId, country, isConfidential, dryRun, serviceAccount, callerId)
                    break
                case Constants.KNOWLARITY:
                    response = await this.knowlarityHelper.makeClickToCall(callerNumber, receiverNumber,
                        currentNotification.notification.notificationId, country, isConfidential, dryRun, callerId)
                    break
                default:
                    this.logger.error("Unsupported Service Provider : " + creative.clickToCallParams.serviceProvider)
                    return
            }
            if (response && response.status === "success") {
                currentNotification.notification.status = "SENT"
                currentNotification.notification.sentAt = new Date()
                currentNotification.notification.externalId = response.callId
                //Overwriting fail reason from previous unsuccessful try(if any)
                currentNotification.notification.failReason = (response && response.failReason) ? response.failReason : null
                //should do async, right?
                await this.loadBalancerService.updateProviderCounter(serviceProvider, country, true)
                // await this.mozartHelper.scheduleRetry(currentNotification.notification.notificationId, new Date(Date.now() + Constants.RETRY.RETRY_CHECK_TIME_IN_MS), Constants.KNOWLARITY_CLICK_TO_CALL_CONFIG.NAME, response.callId)
            } else {
                currentNotification.notification.status = "FAILED"
                currentNotification.notification.failReason = (response && response.failReason) ? response.failReason : "SEND_FAIL"
                this.logger.error("Could not connect call using knowlarity from: " +
                    currentNotification.userContext.tags["agentNumber"] + " to: " +
                    currentNotification.userContext.phone + ", reason : " + JSON.stringify(response))
                //should do async, right?
                await this.loadBalancerService.updateProviderCounter(serviceProvider, country, false)
                //Give more meaningful exception
                throw new Error("Call failed")
            }
        }

        private async getProviderFromLB(notificationId: string, listOfProviders: string[], priorityProvider?: string): Promise<string> {
            let nextServiceProvider: string = null
            await this.loadBalancerService.getNextProvider(notificationId, listOfProviders, priorityProvider)
                .then((r: string) => {
                    nextServiceProvider = r
                })
            if (nextServiceProvider) await this.loadBalancerService.createProviderEntry(notificationId, nextServiceProvider)
            return nextServiceProvider
        }

        public async handleReport(serviceProvider: ServiceProvider, notificationId: string,
                                  clickToCallProviderReport: any): Promise<any> {

            let existingNotification: ClickToCallReportModel = await this.notificationReportReadonlyDao.findOne({notificationId: notificationId})
            if (!_.isEmpty(existingNotification)) {
                this.logger.info(`ClickToCallNotificationhelper::handleReport report with id ${notificationId} already exists. Ignoring.`)
                return
            }
            const { clickToCallReport: report, rawCallerStatus, rawReceiverStatus } =
                await this.notificationReportHelper.createClickToCallReport(
                    clickToCallProviderReport, serviceProvider, notificationId)

            let {
                failReason,
                notification
            } = await this.updateNotificationPostReport(report, serviceProvider, rawCallerStatus, rawReceiverStatus,
                clickToCallProviderReport, notificationId)

            this.metricUtils.incrementCallbackFailReasonCounter("CLICK_TO_CALL", notification.campaignId,
                notification.creativeId, failReason ? failReason : "none", serviceProvider)

            await this.userInteractionPublisher.publishPhoneInteraction(notification, report.startTime, report.status,
                report)
        }

        public async handleInboundCallReport(serviceProvider: ServiceProvider, notificationId: string,
                                             inboundCallProviderReport: any): Promise<void> {

            let existingNotification: ClickToCallReportModel = await this.notificationReportReadonlyDao.findOne({notificationId: notificationId})
            if (!_.isEmpty(existingNotification)) {
                this.logger.info(`ClickToCallNotificationhelper::handleInboundCallReport report with id ${notificationId} already exists. Ignoring.`)
                return
            }

            const { inboundCallReport: report, rawCallerStatus, rawReceiverStatus } =
                await this.notificationReportHelper.createInboundCallReport(
                    inboundCallProviderReport, serviceProvider, notificationId)

            // async update
            this.updateNotificationPostReport(report, serviceProvider, rawCallerStatus, rawReceiverStatus,
                inboundCallProviderReport, notificationId)
        }

        private async updateNotificationPostReport(report: any, serviceProvider: ServiceProvider, rawCallerStatus: any,
                                                   rawReceiverStatus: any, serviceProviderReport: any,
                                                   notificationId: string) {
            const receivedAt = new Date()
            const status = report.status === "FAILED" ? "FAILED" : "DELIVERED"

            let failReason = null
            if (status === "FAILED") {
                if (report.statusCaller === "FAILED") {
                    failReason = `${serviceProvider}_CALLER_${rawCallerStatus}`
                } else if (report.statusReceiver === "FAILED") {
                    failReason = `${serviceProvider}_RECEIVER_${rawReceiverStatus}`
                } else {
                    failReason = `${serviceProvider}_UNKNOWN`
                }
                this.logger.info(`ClickToCallNotificationHelper::updateNotificationPostReport mapping ` +
                    `clickToCallProviderReport: ${JSON.stringify(serviceProviderReport)} to ${failReason}`)
            }

            const billableUnits = BillingUtils.getCTCUnitsConsumed(serviceProviderReport.billsec)

            const notification = await this.notificationDao.get(notificationId)
            if (!notification) {
                const errorMessage = `ClickToCallNotificationHelper::updateNotificationPostReport Could not find notification ` +
                    `${notificationId}. report: ${JSON.stringify(serviceProviderReport)}`
                const error = new Error(errorMessage)
                this.rollbarService.sendError(error)
                throw error
            }

            if (!!notification.sentAt) {
                this.metricUtils.reportExternalAPILatency(`${serviceProvider}_CALL_TRIGGER`,
                    (new Date(serviceProviderReport.starttime * 1000)).getTime()
                    - notification.sentAt.getTime())
            }

            await this.notificationDao.updateProps(notificationId,
                {receivedAt, status, failReason, billableUnits})

            this.logger.info(`Call entered the publish gate  ${status}, ${receivedAt.getTime()}, ${report})`)
            await this.notificationStatusUpdatePublisher.publish([{
                notification, status, timestamp: receivedAt.getTime(),
                metaData: {
                    ...report
                }
            }])
            return {failReason, notification}
        }

        public getNotificationCreationRequestForInboundCall(externalCampaign: ExternalCommunicationCampaign, caller: string): SendCampaignNotificationsRequest {
            switch (externalCampaign) {
                case "INBOUND_CENTER_INQUIRY":
                    return  {
                        campaignId: Constants.CUREFIT_INBOUND_CALLING_CAMPAIGN_ID,
                        creativeIds: [Constants.CLICK_TO_CALL_IN_CENTER_INQUIRY_CREATIVE_ID],
                        userContexts: [{phone: caller, tags: {}}]
                    }
                default:
                    return null;
            }
        }

    }

    return ClickToCallNotificationHelper
}
