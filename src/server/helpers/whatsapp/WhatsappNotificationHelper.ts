import {Container, inject, injectable} from "inversify"
import * as _ from "lodash"
import {
    NotificationStatus,
    NotificationWrapper, QueueConstants,
    ServiceProvider,
    UserNotificationStatus,
    WhatsappCreative
} from "../../../iris-common"
import {BASE_TYPES, FetchUtil, ILogger} from "@curefit/base"
import TYPES from "../../ioc/types"
import {MetricUtils, ProfilerType} from "../../utils/MetricUtils"
import {MustacheUtils} from "../../utils/MustacheUtils"
import Constants from "../../constants"
import {WhatsappEnum} from "@curefit/user-common"
import {CommonUtils} from "../../utils/CommonUtils"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import WhatsappUtils, {UserWhatsappInfo} from "./WhatsappUtils"
import ValueFirstWhatsappHelper from "./ValueFirstWhatsappHelper"
import <PERSON>leyraWhatsappHelper from "./KaleyraWhatsappHelper"
import {IBasicNotificationDao, IRIS_MODELS_TYPES} from "../../../iris-models"
import NotificationStatusUpdatePublisher from "../../publishers/NotificationStatusUpdatePublisher"
import TagUtils from "../../utils/TagUtils"
import NotificationLogService from "../../service/NotificationLogService"
import SprinklrWhatsappHelper from "./SprinklrWhatsappHelper"
import {
    IUserClient,
    RASHI_CLIENT_TYPES,
    UserIdResponse
} from "@curefit/rashi-client"
import UserProfileAttributeUtils from "../../utils/UserProfileAttributeUtils"
import {SendCampaignNotificationsRequest, UserTags} from "@curefit/iris-common"
import {IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {DataErrorV2} from "@curefit/error-client"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import UserContextHelper from "../UserContextHelper"
import {CommValidationsService} from "../../service/CommValidationsService"

const fetch = require("node-fetch")
export interface WhatsappConfig {
    accounts: {
        username: string
        password: string
        phone: string
        serviceProvider: ServiceProvider
        isDefault?: boolean
    }[]
}
export interface IWhatsappNotificationHelper {
    sendNotifications(creative: WhatsappCreative, notifications: NotificationWrapper[], isTransactional: boolean, dryRun?: boolean, testRun?: boolean): Promise<UserNotificationStatus[]>
    findWhatsappNotificationStatus(provider: ServiceProvider, statusError: string, reasonCode?: string): NotificationStatus
    findWhatsappNotificationFailureReason(provider: ServiceProvider, statusError: string, reasonCode: string): string
    updateNotificationStatus(externalId: string, status: NotificationStatus, serviceProvider: ServiceProvider, deliveryDate: Date, callbackReceivedAt: Date, failReason?: string): Promise<boolean>
    handleWhatsappCallback(externalId: string, serviceProvider: ServiceProvider, eventTime: Date, statusCode: string, errorMsg: string, callbackReceivedAt: Date): any
    handleWhatsappUnsubscription(message: string, phone: string): Promise<any>
}


export function WhatsappNotificationHelperFactory(kernel: Container) {
    @injectable()
    class WhatsappNotificationHelper implements IWhatsappNotificationHelper {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(BASE_TYPES.FetchUtil) private fetchUtil: FetchUtil,
            @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
            @inject(TYPES.WhatsappServiceConfiguration) private wpConfig: WhatsappConfig,
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
            @inject(TYPES.UserContextHelper) private userContextHelper: UserContextHelper,
            @inject(TYPES.ValueFirstWhatsappHelper) private valueFirstWhatsappHelper: ValueFirstWhatsappHelper,
            @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
            @inject(TYPES.NotificationStatusUpdatePublisher) private notificationStatusUpdatePublisher: NotificationStatusUpdatePublisher,
            @inject(TYPES.KaleyraWhatsappHelper) private kaleyraWhatsappHelper: KaleyraWhatsappHelper,
            @inject(TYPES.NotificationLogService) private notificationLogService: NotificationLogService,
            @inject(TYPES.SprinklrWhatsappHelper) private sprinklrWhatsappHelper: SprinklrWhatsappHelper,
            @inject(RASHI_CLIENT_TYPES.UserClient) private userClient: IUserClient,
            @inject(TYPES.UserProfileAttributeUtils) private userProfileAttributeUtils: UserProfileAttributeUtils,
            @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,
            @inject(TYPES.CommValidationsService) private commValidationsService: CommValidationsService,
        ) { }

        private async sendNotification(creative: WhatsappCreative, notificationWrapper: NotificationWrapper, isTransactional: boolean, dryRun?: boolean, testRun?: boolean) {

            const { phone, email, name, userId } = WhatsappUtils.fetchUserInformation(notificationWrapper)

            let user
            if (_.isEmpty(userId)) {
                user = await this.userService.getDefaultUser(userId)
            } else {
                user = await this.userService.getUser(userId)
            }

            // deprecated unsubscription flow
            if (user.isSubscribedForWhatsapp && user.isSubscribedForWhatsapp.toString() !== WhatsappEnum.APPROVED.toString()) {
                notificationWrapper.notification.failReason = "NOT_SUBSCRIBED_FOR_WHATSAPP"
                notificationWrapper.notification.status = "FAILED"
                this.logger.error(`WhatsappNotificationHelper::sendNotification could not send whatsapp to
                 userId: ${userId} email ${email} reason: ${notificationWrapper.notification.failReason}`)
                return
            }

            // check if user is unsubscribed from whatsapp
            const isUnsubscribedFromWhatsapp = await this.commValidationsService.isUnsubscribedFromCommunications(Constants.WHATSAPP_CHANNEL, userId, isTransactional)
           if (isUnsubscribedFromWhatsapp) {
                notificationWrapper.notification.failReason = "NOT_SUBSCRIBED_FOR_WHATSAPP"
                notificationWrapper.notification.status = "FAILED"
                this.logger.error(`WhatsappNotificationHelper::sendNotification could not send whatsapp to userId: ${userId} email: ${email} reason : ${notificationWrapper.notification.failReason}`)
                return
           }

            if (!phone) {
                notificationWrapper.notification.failReason = "PHONE_NOT_FOUND"
                notificationWrapper.notification.status = "FAILED"
                this.logger.error(`WhatsappNotificationHelper::sendNotification could not send whatsapp to 
                userId: ${userId} email: ${email} reason : PHONE_NOT_FOUND Phone number not found`)
                return
            }

            const isValidEnvironment: boolean = CommonUtils.isEnvironmentProductionOrAlpha() || Constants.checkIsWhiteListed(phone, email)
            if (!isValidEnvironment) {
                notificationWrapper.notification.failReason = "UNAUTHORIZED"
                notificationWrapper.notification.status = "FAILED"
                this.logger.error("Could not send whatsapp to " + phone + ", reason : UNAUTHORIZED, Api-Key not valid")
                return
            }

            if (!Constants.avoidDuplicateCheckCreative(creative.creativeId) && await this.notificationLogService.isDuplicate(
                {creativeId: creative.creativeId }, phone.slice(-10), dryRun)) {
                notificationWrapper.notification.failReason = "DUPLICATE"
                notificationWrapper.notification.status = "FAILED"
                notificationWrapper.notification.billableUnits = 0
                return
            } else if (await this.notificationLogService.throttleNotification(
                userId, "WHATSAPP", isTransactional, dryRun)) {
                return
            }

            const userTags = Object.assign({}, notificationWrapper.userContext.tags, name ? {"name": name} : {})
            const allTags = TagUtils.allTagsUsed([creative.body])
            let useBackupTemplate: boolean = false
            this.logger.debug(`WhatsappNotificationHelper::sendNotification ${notificationWrapper.notification.notificationId} allTags ${allTags} and userTags ${JSON.stringify(userTags)}`)
            if (!MustacheUtils.contextHasAllTags(allTags, userTags)) {
                if (!creative.whatsappParams.backupTemplateId) {
                    notificationWrapper.notification.failReason = "TEMPLATE_FAIL"
                    notificationWrapper.notification.status = "FAILED"
                    this.logger.error("Could not send whatsapp using mustache to " + phone + ", reason : TEMPLATE_FAIL")
                    return
                }
                useBackupTemplate = true
                this.logger.info("Using backup template for" + phone + "and creativeId" + creative.creativeId)
            }

            if (!_.isEmpty(notificationWrapper.userContext.attachments) && (creative.whatsappParams.serviceProvider === Constants.VALUE_FIRST || notificationWrapper.userContext.attachments.length > 1)) {
                notificationWrapper.notification.failReason = "ATTACHMENT_FAIL"
                notificationWrapper.notification.status = "FAILED"
                this.logger.error("Attachments in WhatsApp are supported only for Kaleyra. Max attachments allowed" +
                    " is 1.")
                return
            }

            if (dryRun) {
                notificationWrapper.notification.sentAt = new Date()
                notificationWrapper.notification.status = "SENT"
                notificationWrapper.notification.externalId = Constants.VALUE_FIRST + "_" + "DRY_RUN"
                notificationWrapper.notification.billableUnits = 0
                return
            }
            
            let success: boolean = false
            try {
                switch (creative.whatsappParams.serviceProvider) {
                    case Constants.VALUE_FIRST:
                        success = await this.valueFirstWhatsappHelper.sendMessage(creative, notificationWrapper, phone, userTags, useBackupTemplate, testRun)
                        break
                    case Constants.KALEYRA:
                        success = await this.kaleyraWhatsappHelper.sendMessage(creative, notificationWrapper, phone, userTags, useBackupTemplate, testRun)
                        break
                    case Constants.SPRINKLR:
                        success = await this.sprinklrWhatsappHelper.sendMessage(creative, notificationWrapper, phone, userTags, useBackupTemplate, testRun)
                        break
                    default:
                        const errorMsg = `Unsupported Service Provider: ${creative.whatsappParams.serviceProvider}`
                        this.logger.error(errorMsg)
                        this.rollbarService.sendError(new Error(errorMsg))
                }
            } catch (error) {
                notificationWrapper.notification.failReason = "SEND_FAIL"
                notificationWrapper.notification.status = "FAILED"
                const errorMessage = `WhatsappNotificationHelper::sendNotification notificationId: ` +
                    `${notificationWrapper.notification.notificationId} Failed due to: ${error.toString()}. `
                this.logger.error(errorMessage)
                this.rollbarService.sendError(new Error(errorMessage))
            }
            if (success) { // for throttling and duplicate notifications
                await this.notificationLogService.logNotification(
                    { creativeId: creative.creativeId  }, phone.slice(-10),
                    "WHATSAPP", isTransactional, dryRun)
            }

        }

        public async sendNotifications(creative: WhatsappCreative, notifications: NotificationWrapper[],
                                       isTransactional: boolean, dryRun?: boolean, testRun?: boolean): Promise<UserNotificationStatus[]> {

            const userNotificationStatuses: UserNotificationStatus[] = await Promise.all(notifications.map(async n => {
                await this.sendNotification(creative, n, isTransactional, dryRun, testRun)
                return {
                    "userId": n.userContext.userId,
                    "creativeId": creative.creativeId,
                    "notificationId": n.notification.notificationId,
                    "externalId": n.notification.externalId,
                    "sent": !!n.notification.sentAt,
                    "failedReason": n.notification.failReason
                }
            }))

            return userNotificationStatuses
        }

        public findWhatsappNotificationStatus(provider: ServiceProvider, statusError: string, reasonCode?: string): NotificationStatus {
            switch (provider) {
                case Constants.VALUE_FIRST:
                    return this.valueFirstWhatsappHelper.decodeDeliveryStatus(statusError, reasonCode)
                case Constants.KALEYRA:
                    return this.kaleyraWhatsappHelper.decodeDeliveryStatus(statusError)
                case Constants.SPRINKLR:
                    return this.sprinklrWhatsappHelper.decodeDeliveryStatus(statusError)
                default:
                    this.logger.error("unidentified provider: " + provider)
                    return "FAILED"
            }
        }

        public findWhatsappNotificationFailureReason(provider: ServiceProvider, statusError: string, reasonCode: string): string {
            switch (provider) {
                case Constants.VALUE_FIRST:
                    return this.valueFirstWhatsappHelper.decodeDeliveryFailureReason(statusError, reasonCode)
                case Constants.KALEYRA:
                    return this.kaleyraWhatsappHelper.decodeDeliveryFailureReason(statusError, reasonCode)
                case Constants.SPRINKLR:
                    return this.sprinklrWhatsappHelper.decodeDeliveryFailureReason(statusError, reasonCode)
                default:
                    this.logger.error("unidentified provider: " + provider)
                    return "DELIVER_FAIL"
            }
        }

        public async updateNotificationStatus(externalId: string, status: NotificationStatus, serviceProvider: ServiceProvider,
                                              deliveryDate: Date, callbackReceivedAt: Date, failReason?: string): Promise<boolean> {
            let updateProps
            switch (status) {
                case "READ": {
                    updateProps = { openedAt: deliveryDate, status, failReason: null }
                    break
                }
                case "DELIVERED": {
                    updateProps = { receivedAt: deliveryDate, status, failReason: null }
                    break
                }
                case "FAILED": {
                    updateProps = { status: status, failReason }
                    break
                }
            }

            const notification = await this.notificationDao.getNotificationsByExternalId(externalId)
            if (!notification) {
                // Only prod callback url is registered with value first.
                // Stage communication callbacks will land up here as well.
                this.logger.warn(`WhatsappNotificationHelper::updateNotificationStatus Received event for unknown` +
                    `external id ${externalId} status ${status}`)
            } else {
                await this.notificationDao.updatePropsByExternalId(externalId, updateProps)
                await this.notificationStatusUpdatePublisher.publish([{ notification,
                    status: updateProps.status, timestamp: deliveryDate.getTime() }])
                this.metricUtils.reportCallbackLatency(`WHATSAPP_${serviceProvider}`,
                    callbackReceivedAt.getTime() - deliveryDate.getTime())

                if (status === "DELIVERED" || status === "FAILED") {
                    this.metricUtils.incrementCallbackFailReasonCounter("WHATSAPP",
                        notification.campaignId, notification.creativeId,
                        failReason ? failReason : "none", serviceProvider)
                }
            }
            return true
        }

        public async handleWhatsappCallback(externalId: string, serviceProvider: ServiceProvider, eventTime: Date, statusCode: string, errorMsg: string, callbackReceivedAt: Date): Promise<any> {
            this.logger.info(`WhatsappNotificationHelper::handleWhatsappCallback externalId ${externalId} serviceProvider ${serviceProvider} eventTime ${eventTime} statusCode ${statusCode} errorMsg ${errorMsg} callbackReceivedAt ${callbackReceivedAt}`)

            if(!statusCode) {
                const errorMessage = `WhatsappNotificationHelper::handleWhatsappCallback Unable to translate status; found empty statusCode in callback` +
                `${statusCode}.`
                throw Error(errorMessage)
            }

            const status: NotificationStatus = await this.findWhatsappNotificationStatus(serviceProvider, statusCode)

            if (status === "UNKNOWN") {
               this.logger.error(`WhatsappNotificationHelper::handleWhatsappCallback ignoring callback with statusCode: ${statusCode}`)
               return Promise.resolve({ message: "OK" })
            }

            const failReason = status === "FAILED" ? this.findWhatsappNotificationFailureReason(
                serviceProvider, errorMsg, errorMsg) : null
    

            try {
                await this.updateNotificationStatus(externalId, status, serviceProvider, eventTime, callbackReceivedAt, failReason)
            } catch (e) {
                throw Error(`WhatsappNotificationHelper::handleWhatsappCallback Unable to process whatsapp delivery ` +
                    `event for callback Error: ${e.toString()}`)
            }
            return Promise.resolve({ message: "OK" })

        }

        public async handleWhatsappUnsubscription(message: string, phone: string): Promise<any> {
            this.logger.info(`WhatsappNotificationHelper:handleWhatsappReplies received request for message: ${message} and phone: ${phone}`)

            if (message.toLowerCase() === "start" || message.toLowerCase() === "stop") {
                const stopFlag: boolean = message.toLowerCase() === "stop"

                //fetch userId from phone number
                const userIdResponse: UserIdResponse = await this.userClient.getUserIdFromEmailOrPhone(phone)
                if (userIdResponse.userId === +phone) {
                    this.logger.info(`WhatsappNotificationHelper::handleWhatsappReplies unable to find userId for phone ${phone}`)
                    return
                }

                //fetch userCommunicationLimits from rashi
                const userLimits = await this.userProfileAttributeUtils.getCachedUserAttribute(
                    userIdResponse.userId.toString(), UserProfileAttributeUtils.USER_PROFILE_ATTRIBUTE_KEY_COMMUNICATION_LIMITS) || {}
                const dailyLimits = _.get(userLimits, "daily", [])
                this.logger.info(`WhatsappNotificationHelper::handleWhatsappReplies fetched userLimits ${JSON.stringify(userLimits)}`)

                //update limits
                const {wasConfigUpdated, updatedDailyLimits} = this.updateWhatsappLimits(dailyLimits, stopFlag)
                if (!wasConfigUpdated) {
                    updatedDailyLimits.push({description: [ {
                            "dimensionname" : "channel",
                            "dimensionvalue" : "WHATSAPP"
                        }], unsubscribed: stopFlag})
                }

                const updatedUserLimits = _.set(userLimits, "daily", updatedDailyLimits)

                await this.sendAcknowledgementMessageAndUpdateWhatsappLimit(userIdResponse, updatedUserLimits, stopFlag)
            } else {
                this.logger.debug(`WhatsappNotificationHelper::handleWhatsappReplies ignoring message ${message} from ${phone}`)
                return
            }
        }

        private updateWhatsappLimits(dailyLimits: any, stopFlag: boolean): any {
            const updatedDailyLimits = []
            let wasConfigUpdated = false
            for (let limitExpressions of dailyLimits) {

                const {description, limit} = limitExpressions

                for (const desc of description) {
                    const {dimensionname, dimensionvalue} = desc
                    if (dimensionname.toLowerCase() === "channel" && dimensionvalue.toUpperCase() === Constants.WHATSAPP) {
                        const unsubscribed = stopFlag ? true : false
                        limitExpressions = {description, limit, unsubscribed }
                        wasConfigUpdated = true
                        break
                    }
                }

                updatedDailyLimits.push(limitExpressions)
            }
            return {wasConfigUpdated, updatedDailyLimits}
        }

        private async sendAcknowledgementMessageAndUpdateWhatsappLimit(userIdResponse: UserIdResponse, updatedUserLimits: any[], stopFlag: boolean) {
            const sendCampaignRequest: SendCampaignNotificationsRequest = {
                campaignId: Constants.WHATSAPP_PREFERENCE_UPDATE_CAMPAIGN_ID,
                creativeIds: stopFlag ? [Constants.WHATSAPP_STOP_ACK_CREATIVE_ID] : [Constants.WHATSAPP_START_ACK_CREATIVE_ID],
                userContexts: [{userId: userIdResponse.userId.toString(), tags: {}}]
            }

            if (stopFlag) {
                this.queueService.sendMessage(QueueConstants.getQueueName("IRIS_CAMPAIGN"), sendCampaignRequest)
                this.logger.info(`WhatsappNotificationHelper::handleWhatsappReplies successfully send acknowledgement to user ${userIdResponse.userId} ${JSON.stringify(sendCampaignRequest)}`)
            }

            try {
                await this.userProfileAttributeUtils.setUserAttributes(
                    userIdResponse.userId.toString(),
                    UserProfileAttributeUtils.USER_PROFILE_ATTRIBUTE_KEY_COMMUNICATION_LIMITS,
                    updatedUserLimits, "GLOBAL", "", "STRING")
                this.logger.info(`WhatsappNotificationHelper::handleWhatsappReplies successfully unsubscribed user ${userIdResponse.userId} from whatsapp with limits ${JSON.stringify(updatedUserLimits)}`)
            } catch (e) {
                this.rollbarService.sendError(new Error(`WhatsappNotificationHelper::handleWhatsappReplies Failed User Attribute Update for user ${userIdResponse.userId} for stopFlag ${stopFlag}`))
                return
            }

            if (!stopFlag) {
                this.queueService.sendMessage(QueueConstants.getQueueName("IRIS_CAMPAIGN"), sendCampaignRequest)
                this.logger.info(`WhatsappNotificationHelper::handleWhatsappReplies successfully send acknowledgement to user ${userIdResponse.userId} ${JSON.stringify(sendCampaignRequest)}`)
            }
        }

    }
    return WhatsappNotificationHelper
}
