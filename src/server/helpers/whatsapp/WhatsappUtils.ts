import {NotificationWrapper} from "../../../iris-common"
import Constants from "../../constants"



export interface UserWhatsappInfo {
    phone?: string
    name?: string
    email?: string
    userId?: string
}


export default class WhatsappUtils {

    public static fetchUserInformation(notification: NotificationWrapper): UserWhatsappInfo {
        const phone = notification.userContext.tags[Constants.USER_PHONE_TAG_OLD] ||
            notification.userContext.tags[Constants.USER_PHONE_TAG] || notification.userContext.phone
        const name = notification.userContext.tags[Constants.USER_NAME_TAG_OLD] || notification.userContext.tags[Constants.USER_NAME_TAG]
        const email = notification.userContext.tags[Constants.USER_EMAIL_TAG_OLD] ||
            notification.userContext.tags[Constants.USER_EMAIL_TAG]
        const userId = notification.userContext.userId

        return {phone, name, email, userId}
    }
}