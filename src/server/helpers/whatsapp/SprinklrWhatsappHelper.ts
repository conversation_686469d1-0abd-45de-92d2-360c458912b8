import {inject, injectable} from "inversify"
import TYPES from "../../ioc/types"
import {MetricUtils, ProfilerType} from "../../utils/MetricUtils"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {WhatsappConfig} from "./WhatsappNotificationHelper"
import {BASE_TYPES, FetchUtilV2, ILogger} from "@curefit/base"
import Constants from "../../constants"
import {BillingUtils} from "../../utils/BillingUtils"
import {AttachmentHelper} from "../AttachmentHelper"
import { NotificationWrapper, NotificationStatus, WhatsappCreative } from "@curefit/iris-common"
import TagUtils from "../../utils/TagUtils"
import * as mustache from "mustache"

const fetch = require("node-fetch")

export interface SprinklrWhatsappResponse {
    data?: string,
    errors?: any[]
}

export interface SprinklrRequest {
    assetId: string,
    snId: string,
    accountId: string,
    deflectionType: string,
    createCase: boolean,
    messageCustomProperties?: {[key: string]: string}
}
@injectable()
class SprinklrWhatsappHelper {
    
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(BASE_TYPES.FetchUtilV2) private fetchUtilV2: FetchUtilV2,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.WhatsappServiceConfiguration) private wpConfig: WhatsappConfig,
        @inject(TYPES.AttachmentHelper) private attachmentHelper: AttachmentHelper,
    ) {}

    public async sendMessage(creative: WhatsappCreative, notificationWrapper: NotificationWrapper, phone: string,
                             userTags: { [tagName: string]: string }, useBackupTemplate: boolean, testRun?: boolean) {

        const toNumber = "91" + phone.slice(-10)

        const fromNumber = !!creative.whatsappParams.fromNumber ? "91" + creative.whatsappParams.fromNumber.slice(-10) :
            Constants.SPRINKLR_WHATSAPP_CONF.defaultNumber

        let attachments: {filename: any, url: any}[]
        try {
            this.logger.info(`SprinklrWhatsappHelper::sendMessage userContexts ${JSON.stringify(notificationWrapper.userContext)}`)
            attachments = await this.attachmentHelper.getAttachmentUrls(notificationWrapper.userContext)
            this.logger.info(`SprinklrWhatsappHelper::sendMessage attachments ${JSON.stringify(attachments)}`)
        } catch (ex) {
            notificationWrapper.notification.failReason = "ATTACHMENT_FAIL"
            notificationWrapper.notification.status = "FAILED"
            this.logger.error("SprinklrWhatsappHelper::sendMessage Could not send WhatsApp to " + toNumber + ", reason" +
                " :" + " ATTACHMENT_FAIL " + ex.toString())
            this.rollbarService.sendError(new Error(`SprinklrWhatsappHelper::sendMessage Attachment fail for ${notificationWrapper.notification.notificationId} due to ${ex.toString()}`))
            return
        }

        if(testRun) {
            this.logger.info(`SprinklrWhatsappHelper::sendMessage whatsapp test run execution called`
                + `notif id: ${notificationWrapper.notification.notificationId}`)
            notificationWrapper.notification.sentAt = new Date()
            notificationWrapper.notification.status = "SENT"
            notificationWrapper.notification.externalId = `${Constants.SPRINKLR}_TEST_RUN`
            notificationWrapper.notification.billableUnits = 0
            notificationWrapper.notification.whatsappTestRunMessage = useBackupTemplate ?  creative.whatsappParams.backupBody : TagUtils.render(creative.body, userTags, mustache)
            return true
        }
        
        /* Presently taking only one attachment for WhatsApp messages */
        const attachment = attachments[0]

        const headers = {
            "Key": Constants.SPRINKLR_WHATSAPP_CONF.apiKey,
            "Authorization": Constants.SPRINKLR_WHATSAPP_CONF.authKey,
            "Content-Type": "application/json",
        }
        let messageCustomProperties : {[key: string]: string} = {}
        if (!useBackupTemplate) {
            messageCustomProperties = this.getTemplateParameters(creative, userTags)
        }

        if (!!attachment)
            messageCustomProperties[Constants.SPRINKLR_WHATSAPP_CONF.attachmentVariable] = attachment.url


        const sprinklrRequest: SprinklrRequest = {
            assetId: useBackupTemplate ? creative.whatsappParams.backupTemplateId : creative.whatsappParams.templateId,
            snId: toNumber,
            accountId: Constants.getSprinklrWhatsappAccountId(+fromNumber),
            deflectionType: "WHATSAPP_BUSINESS",
            createCase: false,
            messageCustomProperties: messageCustomProperties
        }

        this.logger.info(`SprinklrWhatsappHelper::sendMessage url ${Constants.SPRINKLR_WHATSAPP_CONF.baseUrl} headers: ${JSON.stringify(headers)} form data ${JSON.stringify(sprinklrRequest)}`)
    

        const apiLatencyTimerContext =
            this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "Sprinklr_Whatsapp")
        const response = await fetch(
            Constants.SPRINKLR_WHATSAPP_CONF.baseUrl,
            this.fetchUtilV2.post({ headers: headers, body: sprinklrRequest as any})
        )
        this.metricUtils.endProfilingTimer(apiLatencyTimerContext)

        this.logger.info(`SprinklrWhatsappHelper::sendMessage response: ${JSON.stringify(response)}`)
        const responseData: SprinklrWhatsappResponse = await this.fetchUtilV2.parseResponse<SprinklrWhatsappResponse>(response)
        this.logger.info(`SprinklrWhatsappHelper::sendMessage responseData: ${JSON.stringify(responseData)}`)

        const sent: boolean = responseData.errors.length === 0
        if (sent) {
            notificationWrapper.notification.sentAt = new Date()
            notificationWrapper.notification.status = "SENT"
            notificationWrapper.notification.externalId = `${Constants.SPRINKLR}_${JSON.parse(responseData.data)[0]}`
            notificationWrapper.notification.billableUnits = BillingUtils.getWhatsappUnitsConsumed()
            this.logger.debug(`SprinklrWhatsappHelper::sendMessage whatsapp message sent via Sprinklr`
                + `notif id: ${notificationWrapper.notification.notificationId} response: ${JSON.stringify(responseData)}`)
        } else {
            notificationWrapper.notification.status = "FAILED"
            notificationWrapper.notification.failReason = responseData.errors[0].message
            this.logger.error(`SprinklrWhatsappHelper::sendMessage Could not send whatsapp to ${phone} reason: ${JSON.stringify(responseData)}`)
        }
        return sent
    }

    private getTemplateParameters(creative: WhatsappCreative, userTags: { [tagName: string]: string }): { [key: string]: string } {

        this.logger.info(`SprinklrWhatsappHelper::getTemplateParameters, creative : ${JSON.stringify(creative)}`)
        //parameter key names in the order they exist in the registered template
        let creativeParamKeys: string[] = creative.body.match(/\$\$\$[a-zA-Z0-9(){},_]*\$\$\$|{{[a-zA-Z0-9_-]*}}/g) || []
        let messageCustomProperties: {[key: string]: string} = {}

        this.logger.info(`SprinklrWhatsappHelper::getTemplateParameters, Creative parameter keys : ${JSON.stringify(creativeParamKeys)} UserTags: ${JSON.stringify(userTags)}`)
        for (let index = 0; index < creativeParamKeys.length; index++) {
            if (Constants.SPRINKLR_WHATSAPP_CONF.variableOrder.length <= index) {
                const errMsg = `SprinklrWhatsappHelper::getTemplateParameters Too many variable params in the` +
                    ` request, please update variable config ${index}`
                throw new Error(errMsg)
            }
            const isGlobalRegexParam = creativeParamKeys[index].startsWith("$$$") && creativeParamKeys[index].endsWith("$$$")
            this.logger.info(`SprinklrWhatsappHelper::getTemplateParameters messageCustomProperties`)
            if (isGlobalRegexParam) {
                messageCustomProperties[Constants.SPRINKLR_WHATSAPP_CONF.variableOrder[index]] = userTags[creativeParamKeys[index]]
            } else {
                messageCustomProperties[Constants.SPRINKLR_WHATSAPP_CONF.variableOrder[index]] = userTags[creativeParamKeys[index].slice(2, -2)]
            }
        }

        this.logger.info(`SprinklrWhatsappHelper::getTemplateParameters messageCustomProperties ${JSON.stringify(messageCustomProperties)} `)
        return messageCustomProperties
    }

    public decodeDeliveryStatus(status: string): NotificationStatus {
        switch (status) {
            case "message.published":
                return "SENT"
            case "message.delivered":
                return "DELIVERED"
            case "message.read":
                return "READ"
            case "message.failed":
                return "FAILED"
            default:
                return "UNKNOWN"
        }
    }

    public decodeDeliveryFailureReason(statusError: string, reasonCode: string): string {
        if(reasonCode) {
            return "SPRINKLR_" + reasonCode.toUpperCase().replace(" ", "_")
        }
        else {
            return "SPRINKLR_UNKNOWN_ERROR"
        }
    }
}

export default SprinklrWhatsappHelper