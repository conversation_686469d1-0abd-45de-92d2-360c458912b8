import {CommonUtils} from "../../utils/CommonUtils"

class WhatsappConstants {
    static errorCodes: {[key: number]: string} = {
        52992: "Username / Password incorrect",
        57089: "Contract expired",
        57090: "User Credit expired",
        57091: "User disabled",
        65280: "Service is temporarily unavailable",
        65535: "The specified message does not conform to DTD",
        0: "SMS submitted success NO Error (not returned in ValueFirst",
        28673: "Destination number not numeric",
        28674: "Destination number empty",
        28675: "Sender address empty",
        28676: "Template Mismatch",
        28677: "UDH is invalid / SPAM message",
        28678: "Coding is invalid",
        28679: "SMS text is empty",
        28680: "Invalid sender ID",
        28681: "Invalid message, Duplicate message, Submit failed",
        28682: "Invalid Receiver ID (will validate Indian mobile numbers only.)",
        28683: "Invalid Date time for message Schedule (If the date specified in message post for schedule delivery is less than current date or more than expiry date or more than 1 year)",
        28694: "If occurred any error , related to TEMPLATEINFO parameter, which include like invalid template id is provided, variables count mismatch than the template Text variables count, template text not found for the given template id.",
        28695: "Related to WhatsApp template matching and mapping",
        8448: "Message delivered successfully",
        8449: "Message failed",
        8450: "Message ID is invalid",
        13568: "Command Completed Successfully",
        13569: "Cannot update/delete schedule since it has already been processed",
        13570: "Cannot update schedule since the new date-time parameter is incorrect.",
        13571: "Invalid SMS ID/GUID",
        13572: "Invalid Status type for schedule search query. The status strings can be “PROCESSED”, “PENDING” and “ERROR”.",
        13573: "Invalid date time parameter for schedule search query",
        13574: "Invalid GUID for GUID search query",
        13575: "Invalid command action",
    }
    static getWPMessageId(username: string, password: string, tempInfo: string, notificationId: string, from: string, to: string ) {

        /*
            I'm truly sorry you had to come across this file; best of luck.
            I've saved the Value First documentation, in case it helps (you won't find it anywhere else on the internet)
            https://s3.console.aws.amazon.com/s3/object/cf-iris?region=ap-south-1&prefix=UserGuide_ValueFirst_Channel_v1.0.4.pdf
            Callback URLS are configured in a weird way; check page 30 for more information

            Hope you don't have to spend as much time as I did.
         */

        const deliveryCallbackRootUrl = CommonUtils.isEnvironmentProductionOrAlpha() ?
            "https://iris-api.curefit.co" : "https://iris-api.stage.curefit.co"
        const deliveryCallbackUrl = "/notifications/whatsappDelivery?" +
            "guid=%5&" +
            "statusError=%4&" +
            "reasonCode=%2&" +
            "deliveryDate=%3&" +
            "messageStatus=%16"

        return {
            "@VER" : "1.2",
            "USER": {
                "@USERNAME": username,
                "@PASSWORD": password,
                "@UNIXTIMESTAMP": ""
            },
            "DLR": {
                "@URL": `${deliveryCallbackRootUrl}${deliveryCallbackUrl}`
            },
            "SMS": [
                {
                    "@UDH": "0",
                    "@CODING": "1",
                    "@TEMPLATEINFO": tempInfo,
                    "@PROPERTY": "0",
                    "@ID": notificationId,
                    "ADDRESS": [
                        {
                            "@FROM": from,
                            "@TO": to,
                            "@SEQ": notificationId,
                            "@TAG": "whatsapp-curefit"
                        }
                    ]
                }
            ]
        }

    }
}

export default WhatsappConstants
