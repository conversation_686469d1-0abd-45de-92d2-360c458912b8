import Constants from "../../constants"
import {BASE_TYPES, FetchUtil, ILogger} from "@curefit/base"
import WhatsappConstants from "./WhatsappConfig"
import {MetricUtils, ProfilerType} from "../../utils/MetricUtils"
import * as _ from "lodash"
import {BillingUtils} from "../../utils/BillingUtils"
import {inject, injectable} from "inversify"
import TYPES from "../../ioc/types"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {WhatsappConfig} from "./WhatsappNotificationHelper"
import TagUtils from "../../utils/TagUtils"
import * as mustache from "mustache"
import { NotificationFailureReason, NotificationStatus, NotificationWrapper, WhatsappCreative, WhatsappCreativeParams } from "@curefit/iris-common"

const fetch = require("node-fetch")
@injectable()
class ValueFirstWhatsappHelper {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(BASE_TYPES.FetchUtil) private fetchUtil: FetchUtil,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.WhatsappServiceConfiguration) private wpConfig: WhatsappConfig,
    ) {}

    public async sendMessage(creative: WhatsappCreative, notificationWrapper: NotificationWrapper, phone: string,
                             userTags: { [tagName: string]: string }, useBackupTemplate: boolean, testRun? : boolean) {
        
        if(useBackupTemplate) {
            this.logger.error("ValueFirstWhatsappHelper:: Backup template not configured for creativeId" + creative.creativeId)
            notificationWrapper.notification.failReason = "TEMPLATE_FAIL"
            notificationWrapper.notification.status = "FAILED"
            return
        }

        if(testRun) {
            this.logger.info(`ValueFirstWhatsappHelper::sendMessage whatsapp test run execution called`
                + `notif id: ${notificationWrapper.notification.notificationId}`)
            notificationWrapper.notification.sentAt = new Date()
            notificationWrapper.notification.status = "SENT"
            notificationWrapper.notification.externalId = `${Constants.VALUE_FIRST}_TEST_RUN`
            notificationWrapper.notification.billableUnits = 0
            notificationWrapper.notification.whatsappTestRunMessage = notificationWrapper.notification.whatsappTestRunMessage =  TagUtils.render(creative.body, userTags, mustache)
            return true
        }

        const WPMessageId = this.getWhatsappMessageId(creative, notificationWrapper, phone, userTags)

        const apiLatencyTimerContext =
            this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "ValueFirst")
        const response = await fetch(Constants.VALUE_FIRST_CONFIG.API_URL, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(WPMessageId),
        })
        this.metricUtils.endProfilingTimer(apiLatencyTimerContext)

        const data = await this.fetchUtil.parseResponse<any>(response)
        this.logger.debug("WhatsappNotificationHelper::sendViaValueFirst response data: "
            + JSON.stringify(data))

        let sent: boolean = true
        const errorObject = _.get(data, "MESSAGEACK.GUID.ERROR")
        if (errorObject) {
            sent = false
            this.logger.debug("WhatsappNotificationHelper::sendViaValueFirst errorObject: "
                + errorObject.toString())
        }

        if (sent) {
            notificationWrapper.notification.sentAt = new Date()
            notificationWrapper.notification.status = "SENT"
            notificationWrapper.notification.externalId = Constants.VALUE_FIRST + "_" + data.MESSAGEACK.GUID.GUID
            notificationWrapper.notification.billableUnits = BillingUtils.getWhatsappUnitsConsumed()
            this.logger.debug(`ValueFirstWhatsappHelper::sendMessage whatsapp message sent via ValueFirst`
            + `notif id: ${notificationWrapper.notification.notificationId} response: ${JSON.stringify(data)}`)
        } else {
            notificationWrapper.notification.status = "FAILED"
            const errCode: number = data.MESSAGEACK.GUID.ERROR.CODE
            notificationWrapper.notification.failReason = WhatsappConstants.errorCodes[errCode]
            this.logger.error("WhatsappNotificationHelper::sendViaValueFirst Could not send whatsapp to "
                + phone + ", reason : " + JSON.stringify(data) + " JSON body was: " + JSON.stringify(WPMessageId))
        }
        return sent
    }

    private getWhatsappMessageId(creative: WhatsappCreative, notificationWrapper: NotificationWrapper, phone: string, userTags: any) {
        const whatsappParams = creative.whatsappParams
        let tempInfo: string = ""
        let extracted_params: string[] = creative.body.match(/{{[a-zA-Z0-9]*}}/g)
        tempInfo = tempInfo + whatsappParams.templateId
        for (let param in extracted_params) {
            tempInfo = tempInfo + "~" + userTags[extracted_params[param].slice(2, -2)]
        }
        this.logger.debug("Using mustache template for notificationId: " + notificationWrapper.notification.notificationId + " & creativeId:" + notificationWrapper.notification.creativeId)

        const wpConfigForCreative = this.getValueFirstConfig(whatsappParams)
        const WPMessageId = WhatsappConstants.getWPMessageId(
            wpConfigForCreative.username,
            wpConfigForCreative.password,
            tempInfo,
            notificationWrapper.notification.notificationId,
        wpConfigForCreative.phone,
            phone
        )
        this.logger.debug(`messageId: ${JSON.stringify(WPMessageId)}`)
        return WPMessageId
    }

    private getValueFirstConfig(creativeParams: WhatsappCreativeParams) {
        const defaultConfig = this.wpConfig.accounts.find(wpa => !!wpa.isDefault && wpa.serviceProvider === Constants.VALUE_FIRST)

        if (!_.isUndefined(creativeParams.fromNumber) && !creativeParams.fromNumber.includes(defaultConfig.phone) ) {
            // Find a matching config..  if not found use that
            const creativeAccount = this.wpConfig.accounts.find(wpa =>
                wpa.serviceProvider === Constants.VALUE_FIRST && creativeParams.fromNumber.includes(wpa.phone) )
            if (creativeAccount) {
                this.logger.debug(`WhatsappNotificationHelper::getValueFirstConfig Using account ` +
                    `${JSON.stringify(creativeAccount)}`)
                return creativeAccount
            }
            this.logger.info(`Unable to find account for number ${creativeParams.fromNumber}`)
        }

        this.logger.info(`WhatsappNotificationHelper::getValueFirstConfig Using default ` +
            `account ${JSON.stringify(defaultConfig)}`)
        return defaultConfig
    }

    public decodeDeliveryStatus(statusError: string, reasonCode: string): NotificationStatus {
        switch (statusError) {
            case "8448":
                switch (reasonCode) {
                    case "000":
                        return "READ"
                    case "173":
                        return "DELIVERED"
                }
            case "8449":
                return "FAILED"
            case "8450":
                return "FAILED"
        }
    }

    public decodeDeliveryFailureReason(statusError: string, reasonCode: string): NotificationFailureReason {
        switch (reasonCode) {
            case "401":
                return "NOT_REGISTERED"
        }
        switch (statusError) {
            case "8450":
                return "MESSAGE_ID_INVALID"
        }
        return "UNKNOWN"
    }
}

export default ValueFirstWhatsappHelper
