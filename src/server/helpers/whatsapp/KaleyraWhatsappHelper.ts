import {inject, injectable} from "inversify"
import TYPES from "../../ioc/types"
import {MetricUtils, ProfilerType} from "../../utils/MetricUtils"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {WhatsappConfig} from "./WhatsappNotificationHelper"
import { BASE_TYPES, FetchUtilV2, ILogger } from "@curefit/base"
import {
    NotificationStatus,
    NotificationWrapper,
    WhatsappCreative
} from "@curefit/iris-common"
import Constants from "../../constants"
import * as mustache from "mustache"
import {BillingUtils} from "../../utils/BillingUtils"
import {AttachmentHelper} from "../AttachmentHelper"
import TagUtils from "../../utils/TagUtils"
import { MustacheUtils } from "../../utils/MustacheUtils"
import { Agent } from "http"

const https = require("https")
const fetch = require("node-fetch")

export interface KaleyraWhatsappResponse {
    id?: string
    data?: {message_id: string}[]
    error?: {error?: string}
}

@injectable()
class KaleyraWhatsappHelper {

    private WHATSAPP_URL_TEMPLATE: string = "https://api.kaleyra.io/v1/{{{SID}}}/messages?channel=whatsapp&to={{{to}}}" +
        "&from={{{from}}}&type={{{type}}}&template_name={{{templateName}}}&params={{{params}}}&lang_code=en&callback_url={{{deliveryUrl}}}" +
        "&media_url={{{mediaUrl}}}&caption={{{caption}}}"

    private DELIVERY_CALLBACK_ENDPOINT: string = "/notifications/whatsappDelivery/kaleyra"

    private fetchAgent: Agent

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(BASE_TYPES.FetchUtilV2) private fetchUtil: FetchUtilV2,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.WhatsappServiceConfiguration) private wpConfig: WhatsappConfig,
        @inject(TYPES.AttachmentHelper) private attachmentHelper: AttachmentHelper,
    ) {
        this.fetchAgent = new https.Agent({
            keepAliveMsecs: 50,
            keepAlive: true,
            maxSockets: 50,
            maxFreeSockets: 2,
            timeout: 300000
        })
    }

    private getWhatsappUrlTemplate (creative: WhatsappCreative): string {
        if (creative.whatsappParams?.website_url_dynamic) {
            return this.WHATSAPP_URL_TEMPLATE + "&param_url={{paramUrl}}"
        }
        return this.WHATSAPP_URL_TEMPLATE
    }

    public async sendMessage(creative: WhatsappCreative, notificationWrapper: NotificationWrapper, phone: string,
                             userTags: { [tagName: string]: string }, useBackupTemplate : boolean, testRun?: boolean) {

        const templateParams = this.getTemplateParameters(creative, userTags)

        const formattedPhoneNumber = "91" + phone.slice(-10)

        const from = creative.whatsappParams.fromNumber || Constants.KALEYRA_WHATSAPP_CONF.defaultNumber

        const deliveryUrl = this.getDeliveryUrl()

        let attachments: {filename: any, url: any}[]
        try {
            this.logger.debug(`KaleyraWhatsappHelper::sendMessage userContexts ${JSON.stringify(notificationWrapper.userContext)}`)
            attachments = await this.attachmentHelper.getAttachmentUrls(notificationWrapper.userContext)
            this.logger.debug(`KaleyraWhatsappHelper::sendMessage attachments ${JSON.stringify(attachments)}`)
        } catch (ex) {
            notificationWrapper.notification.failReason = "ATTACHMENT_FAIL"
            notificationWrapper.notification.status = "FAILED"
            this.logger.error("KaleyraWhatsappHelper::sendMessage Could not send WhatsApp to " + formattedPhoneNumber + ", reason" +
                " :" + " ATTACHMENT_FAIL " + ex.toString())
            this.rollbarService.sendError(new Error(`KaleyraWhatsappHelper::sendMessage Attachment fail for ${notificationWrapper.notification.notificationId} due to ${ex.toString()}`))
            return
        }

        /* Presently taking only one attachment for WhatsApp messages */
        const attachment = attachments[0]

        const templateType = (!!creative.whatsappParams.kaleyraHeaderType && creative.whatsappParams.kaleyraHeaderType === Constants.TEXT_IMAGE_DOCUMENT_VIDEO) ?
            "mediatemplate" : "template"
        if (creative.whatsappParams?.website_url_dynamic) {
            const allTags = TagUtils.allTagsUsed([creative.whatsappParams?.website_url_dynamic || ""])
            if (!MustacheUtils.contextHasAllTags(allTags, userTags)) {
                if (creative.whatsappParams?.backup_website_url_dynamic == null) {
                    this.logger.info(`KaleyraWhatsappHelper::sendMessage The mapped tags for whatsapp dynamic url allTags : ${allTags} userTags: ${JSON.stringify(userTags)}`)
                    notificationWrapper.notification.failReason = "TEMPLATE_FAIL"
                    notificationWrapper.notification.status = "FAILED"
                    this.logger.error("Could not send whatsapp using mustache for dynamic URL to " + phone + ", reason : TEMPLATE_FAIL")
                    return
                }
                useBackupTemplate = true
            }
        }

        if(testRun) {
            this.logger.info(`KaleyraWhatsappHelper::sendMessage whatsapp test run execution called`
                + `notif id: ${notificationWrapper.notification.notificationId}`)
            notificationWrapper.notification.sentAt = new Date()
            notificationWrapper.notification.status = "SENT"
            notificationWrapper.notification.externalId = `${Constants.KALEYRA}_TEST_RUN`
            notificationWrapper.notification.billableUnits = 0
            notificationWrapper.notification.whatsappTestRunMessage = useBackupTemplate ?  creative.whatsappParams.backupBody : TagUtils.render(creative.body, userTags, mustache)
            return true
        }

        const encodedUrl = mustache.render(this.getWhatsappUrlTemplate(creative), {
            SID: encodeURIComponent(Constants.KALEYRA_WHATSAPP_CONF.SID),
            to: encodeURIComponent(formattedPhoneNumber),
            from: encodeURIComponent(from),
            templateName:  useBackupTemplate ? encodeURIComponent(creative.whatsappParams.backupTemplateId) : encodeURIComponent(creative.whatsappParams.templateId),
            params: encodeURIComponent(templateParams),
            deliveryUrl: encodeURIComponent(deliveryUrl),
            type: encodeURIComponent(templateType),
            mediaUrl: encodeURIComponent(!!attachment && creative.whatsappParams?.kaleyraHeaderType === "TEXT_IMAGE_DOCUMENT_VIDEO" ? attachment.url : ""),
            caption: !!attachment ? attachment.filename : "",
            paramUrl: creative.whatsappParams?.website_url_dynamic && !useBackupTemplate ? 
            encodeURIComponent(TagUtils.render(creative.whatsappParams.website_url_dynamic, userTags, mustache))
            : ""
        })

        this.logger.debug(`KaleyraWhatsappHelper::sendMessage for notification ${notificationWrapper.notification.notificationId} encodedUrl: ${encodedUrl}`)

        const apiLatencyTimerContext =
            this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "Kaleyra_Whatsapp")
            
        const response = await fetch(encodedUrl, this.fetchUtil.post({headers: {"api-key": Constants.KALEYRA_WHATSAPP_CONF.apiKey}, agent: this.fetchAgent}))
        this.metricUtils.endProfilingTimer(apiLatencyTimerContext)

        const responseData: KaleyraWhatsappResponse = await this.fetchUtil.parseResponse<KaleyraWhatsappResponse>(response)
        this.logger.debug(`KaleyraWhatsappHelper::sendMessage response data: ${JSON.stringify(responseData)}`)
        const sent: boolean = !!(responseData.data && responseData.data[0] && responseData.data[0].message_id)
        if (sent) {
            notificationWrapper.notification.sentAt = new Date()
            notificationWrapper.notification.status = "SENT"
            notificationWrapper.notification.externalId = `${Constants.KALEYRA}_${responseData.data[0].message_id}`
            notificationWrapper.notification.billableUnits = BillingUtils.getWhatsappUnitsConsumed()
            this.logger.debug(`KaleyraWhatsappHelper::sendMessage whatsapp message sent via kaleyra`
                + `notif id: ${notificationWrapper.notification.notificationId} response: ${JSON.stringify(responseData)}`)
        } else {
            notificationWrapper.notification.status = "FAILED"
            notificationWrapper.notification.failReason = responseData.error.error
            this.logger.error(`KaleyraWhatsappHelper::sendMessage Could not send whatsapp to ${phone} reason: ${JSON.stringify(responseData)}`)
        }
        return sent
    }

    private getDeliveryUrl(): string {
        return Constants.getIrisPublicBaseUrl() + this.DELIVERY_CALLBACK_ENDPOINT
    }

    private getTemplateParameters(creative: WhatsappCreative, userTags: { [tagName: string]: string }): string {

        //parameter key names in the order they exist in the registered template
        let creativeParamKeys: string[] = creative.body.match(/{{[a-zA-Z0-9_-]*}}/g)

        let paramValues: string = ""
        for (let index in creativeParamKeys) {
            //slice removes the surrounding curly braces
            paramValues = `${paramValues},"${userTags[creativeParamKeys[index].slice(2, -2)]}"`
        }
        //this removes the initial comma
        paramValues = paramValues.slice(1)
        return paramValues
    }

    public decodeDeliveryStatus(status: string): NotificationStatus {
        switch (status) {
            case "sent":
                return "SENT"
            case "delivered":
                return "DELIVERED"
            case "read":
                return "READ"
            default:
                return "FAILED"
        }
    }

    public decodeDeliveryFailureReason(statusError: string, reasonCode: string): string {
        switch (reasonCode) {
            // Message failed to send because there are restrictions on how many messages can be sent from this phone number. This may be because too many previous messages were blocked or flagged as spam.
            case "E13032":
                return "KALEYRA_EXCEEDED_DAILY_LIMIT"

            // 'To' number is invalid or the Whatsapp account does not exist.
            case "E13009":
                return "KALEYRA_NUMBER_INVALID"

            // Error received from Facebook dockers due to high requests. Please throttle message requests to ~20 request/sec or contact <NAME_EMAIL>.
            case "E13082":
                return "KALEYRA_THROTTLED"

            // Internal Error
            case "E13048":
                return "KALEYRA_INTERNAL_ERROR"

            // Attachment Fail
            case "E13035":
                return "KALEYRA_ATTACHMENT_FAIL"
            case "E13041":
                return "KALEYRA_RESOURCE_NOT_FOUND"

            // Message failed to send because it was pending for too long
            case "E13026":
                return "KALEYRA_EXPIRED"


            /*
            CLOUD API ERROR CODES
             */

            //The application has reached its API call rate limit.
            case "4":
                return "KALEYRA_THROTTLED_4"

            //The WhatsApp Business Account has reached its application rate limit.
            case "80007":
                return "KALEYRA_THROTTLED_80007"

            //Cloud API message throughput has been reached.
            case "130429":
                return "KALEYRA_THROTTLED_130429"

            //Message failed to send because there are restrictions on how many messages can be sent from this phone number.
            case "131048":
                return "KALEYRA_EXCEEDED_DAILY_LIMIT_131048"

            // Invalid request or server error
            case "1":
                return "KALEYRA_INVALID_REQUEST"

            //Temporary due to downtime or due to being overloaded.
            case "2":
                return "KALEYRA_DOWNTIME_2"

            //The server is temporarily unavailable.
            case "133004":
                return "KALEYRA_DOWNTIME_133004"

            //The request included one or more unsupported or misspelled parameters.
            case "100":
                return "KALEYRA_INVALID_PARAMS"

            //The message failed to send due to an unknown error.
            case "131000":
                return "KALEYRA_INTERNAL_ERROR_131000"

            //The request is missing a required parameter.
            case "131008":
                return "KALEYRA_MISSING_PARAMETER"

            //Message failed to send because there were one or more errors related to your payment method.
            case "131042":
                return "KALEYRA_PAYMENT_ISSUES"

            //The media type sent by the WhatsApp user is not supported(Upload).
            case "131052":
                return "KALEYRA_ATTACHMENT_FAIL_131052"
            //The media type sent to the WhatsApp user is not supported(Download).
            case "131053":
                return "KALEYRA_ATTACHMENT_FAIL_131053"

            //The number of variable parameter values included in the request did not match the number of variable parameters defined in the template.
            case "132000":
                return "KALEYRA_PARAM_MISMATCH_132000"

            //Template parameters are invalid or missing.
            case "132015":
                return "KALEYRA_PARAM_MISMATCH_132015"

            //The template does not exist in the specified language or the template has not been approved.
            case "132001":
                return "KALEYRA_INVALID_TEMPLATE"


            //Phone number is not registered on the Whatsapp Business Platform
            case "133010":
                return "KALEYRA_INVALID_FROM_NUMBER"



            default:
                return "KALEYRA_" + reasonCode?.toUpperCase()

        }
    }
}

export default KaleyraWhatsappHelper