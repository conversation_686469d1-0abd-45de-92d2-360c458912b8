import {BASE_TYPES, ILogger} from "@curefit/base"
import {AppName} from "@curefit/base-utils"
import {IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"

import {inject, injectable} from "inversify"

import TYPES from "../ioc/types"
import {NotificationParams, NotificationType, QUEUE_NAME} from "./INotificationService"
import {
    EmailValidResponse,
    Notification,
    PaginatedResults,
    QueueConstants,
    UserNotificationFilterRequest
} from "../../iris-common"
import {IBasicNotificationDao, IBasicNotificationReadOnlyDao, IRIS_MODELS_TYPES} from "../../iris-models"
import RetryService from "../service/RetryService"
import {UserUtils} from "../utils/UserUtils"
import Constants from "../constants"
import {IReacherHelper} from "./ReacherHelper"
import {snsTopic} from "../enums"
import {EventData, EVENTS_TYPES, EventsService} from "@curefit/events-util"
import {AWSUtils} from "../utils/AWSUtils"

@injectable()
class NotificationHelper {
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(TYPES.UserUtils) private userUtils: UserUtils,
        @inject(TYPES.RetryService) private retryService: RetryService,
        @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,
        @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
        @inject(IRIS_MODELS_TYPES.BasicNotificationReadOnlyDao) private notificationReadonlyDao: IBasicNotificationReadOnlyDao,
        @inject(TYPES.ReacherHelper) private reacherHelper: IReacherHelper,
        @inject(EVENTS_TYPES.EventsService) private eventService: EventsService,
    ) {}

    public async sendNotification(notificationType: NotificationType, appName: AppName, notificationParams: NotificationParams) {
        const attributes = new Map<string, string>()

        attributes.set("appName", appName)
        attributes.set("notificationType", notificationType)

        this.queueService.sendMessageAsync(QueueConstants.getQueueName(QUEUE_NAME), notificationParams, attributes)
    }

    public async notificationSendRetryCheck(notificationId: string, serviceProvider: string, providerExternalId: string) {
        const notification: Notification = await this.notificationDao.get(notificationId)
        if (!notification) this.logger.error("Notification not found for notificationId: " + notificationId)
        await this.retryService.retryOneExistingNotification(notification, serviceProvider, providerExternalId)
    }

    public async getUserNotificationsPaginated(filterRequest: UserNotificationFilterRequest): Promise<PaginatedResults<Notification>> {
        const userIds = filterRequest.userIds
        const userIdentities = await this.userUtils.getUserIdentities(userIds)
        const request: UserNotificationFilterRequest = {
            userIds: userIdentities,
            startDate: filterRequest.startDate,
            endDate: filterRequest.endDate,
            creativeType: filterRequest.creativeType,
            campaignType: filterRequest.campaignType,
            creativeIds: filterRequest.creativeIds,
            campaignIds: filterRequest.campaignIds,
            vertical: filterRequest.vertical,
            limit: filterRequest.limit ? filterRequest.limit : 20,
            skip: filterRequest.skip ? filterRequest.skip : 0
        }
        return this.notificationReadonlyDao.getUserNotifications(request)
    }

    public async validateEmail(email: string, skipLocalCheck = false, checkLastXDays = Constants.DEFAULT_EMAIL_VALIDATION_LAST_X_DAYS): Promise<EmailValidResponse> {
        const regexp = new RegExp(Constants.EMAIL_VALIDATION_REGEX)
        let response

        if (regexp.test(email)) {
            response = (!skipLocalCheck && await this.notificationDao.validateEmail(email, checkLastXDays)) ?
                {email: email, isValidEmail: true, deepEmailStatusCheck: false} :
                await this.reacherHelper.validateEmail(email)
        } else {
            this.logger.debug(`NotificationController::validateEmail Email ${email} failed RegEx`)
            response = {email: email, isValidEmail: false, deepEmailStatusCheck: false}
        }

        //Publishing to SNS ONLY when email is not valid
        if (!response.isValidEmail) {
            const source: Map<string, any> = new Map<string, any>([["source", "iris-validate-email-api"]])
            const eventData: EventData<any> = new EventData<any>(Constants.IRIS + "_" + Constants.SNS_EMAIL_VALIDATION_REPORTS,
                null, Date.now(), {email: response.email}, source)
            try {
                await this.eventService.publishMessage(
                    AWSUtils.getAwsSNSTopicName(snsTopic.INVALID_EMAILS, false), eventData)
            } catch (err) {
                this.logger.error(`NotificationHelper::validateEmail Failed to publish for event data ` +
                    `${JSON.stringify(eventData)} with error ${err.toString()} on SNSTopic${AWSUtils.getAwsSNSTopicName(snsTopic.INVALID_EMAILS, false)}`)

            }
        }
        return response
    }
}

export default NotificationHelper
