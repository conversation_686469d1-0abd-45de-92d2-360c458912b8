import {BASE_TYPES, ILogger} from "@curefit/base"

import {Container, inject, injectable} from "inversify"
import {controller, httpGet, httpPost} from "inversify-express-utils"

import TYPES from "../ioc/types"
import UserProfileAttributeUtils from "../utils/UserProfileAttributeUtils"
import MozartService from "../service/MozartService"
import {IBasicNotificationDao, ICreativeReadWriteDao, IRIS_MODELS_TYPES} from "../../iris-models"
import NotificationLogService from "../service/NotificationLogService"
import UnsubscriptionService from "../service/UnsubscriptionService"
import UserContextHelper from "../helpers/UserContextHelper"
import HandlebarsUtils from "../utils/HandlebarsUtils"
import {MustacheUtils} from "../utils/MustacheUtils"
import {SMSCreative} from "../../iris-common"
import PowersetGenerator from "../../bumble/utils/PowersetGenerator"
import ScaleService from "../service/ScaleService"
import {IMultiCrudKeyValue, REDIS_TYPES} from "@curefit/redis-utils"
import {CacheAccessImpl, ICacheAccess} from "@curefit/cache-utils"
import FunctionalTagsUtils from "../utils/FunctionalTagsUtils"
import TagUtils from "../utils/TagUtils"

export function DebugControllerFactory(kernel: Container) {

    @controller("/debug")
    class DebugController {


        constructor(
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
            @inject(TYPES.ScaleService) private scaleService: ScaleService,
            @inject(TYPES.MozartService) private mozartService: MozartService,
            @inject(TYPES.UserContextHelper) private userContextHelper: UserContextHelper,
            @inject(IRIS_MODELS_TYPES.CreativeReadWriteDao) private creativeDao: ICreativeReadWriteDao,
            @inject(TYPES.NotificationLogService) private notificationLogService: NotificationLogService,
            @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
            @inject(TYPES.UserProfileAttributeUtils) private userProfileAttributeUtils: UserProfileAttributeUtils,
            @inject(TYPES.UnsubscriptionService) private unsubscriptionService: UnsubscriptionService
        ) {
        }

        @httpGet("/user/:id/cachedAttributes")
        public async getCachedUserAttributes(request: any): Promise<any> {
            this.logger.info(`DebugController::getCachedUserAttributes Fetching attributes for ${request.params.id}`)
            const cachedAttributes =
                (await this.userProfileAttributeUtils.getCachedUserAttributes(request.params.id)).attributes
            cachedAttributes.forEach((value: any, key: string) => {
                this.logger.info(`DebugController::getCachedUserAttributes ${value} ${key}`)
            })
            return cachedAttributes
        }



        @httpGet("/cachedGlobalAttributes")
        public async getCachedGlobalAttributes(request: any): Promise<any> {
                this.logger.info(`DebugController::getCachedUserAttributes Fetching global attributes from redis cache`)
            const cachedGlobalAttributes =
                await this.userProfileAttributeUtils.getInterpretedCachedGlobalAttributes("curefit")
            this.logger.info(`Cached global attributes: ${cachedGlobalAttributes}`)
            cachedGlobalAttributes.forEach((value: any, key: string) => {
                this.logger.info(`DebugController::getCachedGlobalAttributes ${value} ${key}`)
            })
            return cachedGlobalAttributes
        }

        @httpGet("/job/:id")
        public async getJobById(request: any): Promise<any> {
            return this.mozartService.getJob(request.params.id)
        }

        @httpGet("/markExpired/:notificationId")
        public async markExpired(request: any): Promise<any> {
            const notificationId = request.params.notificationId
            const notification = await this.notificationDao.get(notificationId)
            const userTags = this.userContextHelper.decodeUserContext(notification.userTags)

            const yesterday = new Date()
            yesterday.setDate(yesterday.getDate() - 1)
            userTags.expiry = yesterday

            notification.userTags = this.userContextHelper.encodeUserContext(userTags)
            await this.notificationDao.update(notification)
        }

        @httpGet("/evictPromotionalNotificationLogKey/:userId/:creativeType")
        public async evictPromotionalNotificationLogKey(request: any): Promise<any> {
            const { userId, creativeType } = request.params
            const existing = this.notificationLogService.getPromotionalNotificationKey(userId, creativeType)
            await this.notificationLogService.evictPromotionalNotificationLogKey(userId, creativeType)
            return { existing }
        }

        @httpPost("/updateSMSCreativeSenderIds")
        public async updateSMSCreativeSenderId(request: any): Promise<any> {
            const { updateTuples } = request.body

            for (const updateTuple of updateTuples) {
                const { creativeId, senderId } = updateTuple
                this.logger.info(`DebugController::updateSMSCreativeSenderId updating ${creativeId} with ${senderId}`)
                await this.creativeDao.findOneAndUpdatePartial(
                    { creativeId },
                    { $set: { "smsParams.sender" : senderId }})
            }
        }

        @httpPost("/generateAllTemplates")
        public async generateAllTemplates(request: any): Promise<any> {
            const { creativeId } = request.body

            const creative = (await this.creativeDao.findOne({ creativeId }) as SMSCreative)
            const isHandlebars = creative.smsParams.templateEngine === "handlebars"

            let creativeText = creative.body

            // Temporarily rename else
            creativeText = creativeText.replace(
                new RegExp(/{{else}}/, "g"), "{{#else}}")
            // Replace variables with {#var#}
            creativeText = creativeText.replace(
                new RegExp(/{{{[.0-9a-zA-Z]+}}}/, "g"), "{#var#}") // {{{var}}}
            creativeText = creativeText.replace(
                new RegExp(/{{[.0-9a-zA-Z]+}}/, "g"), "{#var#}") // {{var}}

            // Fix back else
            creativeText = creativeText.replace(
                new RegExp(/{{#else}}/, "g"), "{{else}}")

            const renderLibrary = isHandlebars ? HandlebarsUtils : MustacheUtils
            const mustacheIfTagRegExp = new RegExp(/{{#[0-9a-zA-Z]+}}/, "g") // {{#if var}}
            const handlebarsIfTagRegExp = new RegExp(/{{#if[ 0-9a-zA-Z]+}}/, "g") // {{#var}}
            const ifTagRegExp = isHandlebars ? handlebarsIfTagRegExp : mustacheIfTagRegExp
            const ifTagPrefixLength = isHandlebars ? 6 : 3 // {{#if var}} vs {{#var}}

            // Extract unique if tags
            let ifTagMatch
            const ifTags = []
            while (ifTagMatch = ifTagRegExp.exec(creativeText)) {
                const tag = ifTagMatch[0].slice(ifTagPrefixLength, ifTagMatch[0].length - 2)
                if (ifTags.indexOf(tag) === -1) {
                    ifTags.push(tag)
                }
            }

            // Create tag dict with all tags set as false
            let subsets: string[][] = PowersetGenerator.generatePowerSets(ifTags)

            const defaultTags: any = {}
            for (const tag of ifTags) {
                defaultTags[tag] = false
            }

            const templates = []
            for (const subset of subsets) {
                const renderTags = { ...defaultTags }
                for (const item of subset) {
                    renderTags[item] = true
                }
                templates.push(TagUtils.render(creativeText, renderTags, renderLibrary))
            }

            return { templates }
        }

        @httpGet("/getScalingHistoryKeys")
        public async getScalingHistoryKeys(request: any): Promise<any> {
            return this.scaleService.getScalingHistoryKeys()
        }

        @httpPost("/removeScalingHistoryKeys")
        public async removeScalingHistoryKeys(request: any): Promise<any> {
            const { channel } = request.body
            return this.scaleService.removeScalingHistoryKeys(channel)
        }
    }

    return DebugController
}