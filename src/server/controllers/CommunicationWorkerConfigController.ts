import {controller, httpGet, httpPost} from "inversify-express-utils"
import {Container, inject, injectable} from "inversify"
import {BASE_TYPES, ILogger} from "@curefit/base"
import CommunicationWorkerConfigHelper from "../helpers/CommunicationWorkerConfigHelper"
import TYPES from "../ioc/types"
import {DataError} from "@curefit/error-client"
import Constants from "../constants"


export function CommunicationWorkerConfigControllerFactory(kernel: Container) {

    @controller("/communicationWorkers")
    class CommunicationWorkerConfigController {

        constructor(
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(TYPES.CommunicationWorkerConfigHelper) private cwHelper: CommunicationWorkerConfigHelper) {
        }

        @httpPost("/config")
        public async updateConfig(request: any): Promise<any> {
            if (!request.body.workerTag) {
                throw new DataError("Body should contain parameter workerTag")
            }
            if (!request.body.reservoirSize || request.body.reservoirSize < 0 ||
                request.body.reservoirSize > Constants.getGlobalRateLimits(request.body.workerTag)) {
                throw new DataError("Body should contain parameter reservoirSize and its value should be 0 < x < "
                    + Constants.getGlobalRateLimits(request.body.workerTag))
            }
            await this.cwHelper.updateWorkerConfig(request.body.workerTag, request.body.reservoirSize,
                request.body.maxConcurrency, request.body.minTime)
            return {"status": "OK"}
        }

        @httpGet("/config/:workerTag")
        public async getConfig(request: any): Promise<any> {
            if (!request.params.workerTag) {
                throw new DataError("Request should contain parameter workerTag")
            }
            return await this.cwHelper.getWorkerConfig(request.params.workerTag)
        }

        @httpGet("/initWorkerConfig")
        public async initWorkerRedisConfig(request: any): Promise<any> {
            this.cwHelper.initialize()
            return {"status": "OK"}
        }
    }
    return CommunicationWorkerConfigController
}