import TYPES from "../ioc/types"
import {Container, inject} from "inversify"
import {controller, httpGet, httpPost, httpPut} from "inversify-express-utils"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {FunctionalTagsService} from "../service/FunctionalTagsService"
import {FunctionTag} from "../../iris-common/src/functionalTags"

export function FunctionalTagsControllerFactory(kernel: Container) {

    @controller("/functionalTags")
    class FunctionalTagsController {

        constructor(
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(TYPES.FunctionalTagsService) private functionalTagsService: FunctionalTagsService) {
        }

        @httpGet("/allFunctions")
        public async getAllFunctions(request: any): Promise<Record<string, FunctionTag>> {
            this.logger.info("getting all functions")
            return this.functionalTagsService.getAllFunctions()
        }
    }
}