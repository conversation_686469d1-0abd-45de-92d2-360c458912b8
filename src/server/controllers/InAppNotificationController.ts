import {controller, httpGet, httpPost, httpPut} from "inversify-express-utils"
import {Container, inject, injectable} from "inversify"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {GenericResponse, NotificationMeta, PaginatedResults} from "../../iris-common"
import TYPES from "../ioc/types"
import {IInAppNotificationHelper} from "../helpers/InAppNotificationHelper"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import Constants from "../constants"
import {DataError} from "@curefit/error-client"

export function InAppNotificationControllerFactory(kernel: Container) {

    @controller("/inAppNotification")
    class InAppNotificationController {

        constructor(
            @inject(TYPES.InAppNotificationHelper) private inAppNotificationHelper: IInAppNotificationHelper,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
            @inject(BASE_TYPES.ILogger) private logger: ILogger) {
        }

        @httpGet("/active/:userId/:appId")
        public async getActiveNotificationMetasForUser(request: any): Promise<NotificationMeta[]> {
            const userId: string = request.params.userId
            const appId: string = request.params.appId
            const notificationMetaList: NotificationMeta[] = await this.inAppNotificationHelper.getActiveNotificationMetasForUser(userId, appId)
            return notificationMetaList
        }

        @httpGet("/")
        public async getNotificationMetasForUserPaginated(request: any): Promise<PaginatedResults<NotificationMeta>> {
            let { userId, appId, states, pageSize, pageNumber } = request.query
            states = states ? states.split(",") : []
            return this.inAppNotificationHelper.getNotificationMetasForUserPaginated(userId, appId, states,
                pageSize, pageNumber)
        }

        @httpGet("/counts")
        public async getNotificationMetaCountsForUser(request: any): Promise<any> {
            const { userId, appId, expiryAfter } = request.query
            return this.inAppNotificationHelper.getNotificationMetaCountsForUser(userId, appId, expiryAfter)
        }

        @httpPost("/update")
        public async updateNotificationMetaStateBulk(request: any): Promise<GenericResponse> {
            const { notificationIds, updateProps } = request.body
            if ((notificationIds || []).length > Constants.NOTIFICATION_META_BULK_UPDATE_SIZE_LIMIT) {
                throw new DataError(`Request size too large. Limit: `
                    + `${Constants.NOTIFICATION_META_BULK_UPDATE_SIZE_LIMIT} notifications`)
            }
            try {
                await this.inAppNotificationHelper.updateNotificationMetas(notificationIds, updateProps)
            } catch (err) {
                const errorMessage = `InAppNotificationController::updateNotificationMetaStateBulk update state failed`
                    + `${err} notificationIds: ${notificationIds} updateProps: ${updateProps}`
                this.logger.error(errorMessage)
                this.rollbarService.sendError(new Error(errorMessage))
                return { "status": "FAILURE" }
            }
            return { "status": "SUCCESS" }
        }

        @httpPut("/:id")
        public async updatePropsByNotificationId(request: any): Promise<GenericResponse> {
            const notificationId: string = request.params.id
            await this.inAppNotificationHelper.updatePropsByNotificationId(notificationId, request.body)
            return {"status": "SUCCESS"}
        }

    }

    return InAppNotificationController
}

