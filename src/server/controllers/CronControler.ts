import {BASE_TYPES, ILogger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

import {Container, inject, injectable} from "inversify"
import {controller, httpGet, httpPost} from "inversify-express-utils"
import * as _ from "lodash"

import {INotificationSegmentTaskReadWriteDao, IRIS_MODELS_TYPES} from "../../iris-models"
import {NotificationSegmentTaskStatusEnum} from "../../iris-common"
import TYPES from "../ioc/types"
import HealthCheckService, {HealthCheckTypeEnum} from "../service/HealthCheckService"
import {MetricUtils} from "../utils/MetricUtils"
import FailureAlertService from "../service/FailureAlertService"
import ScaleService from "../service/ScaleService"
import Constants from "../constants"


export function CronControllerFactory(kernel: Container) {

    @controller("/cron")
    class CronController {

        constructor(
            @inject(BASE_TYPES.ILogger) protected logger: ILogger,
            @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
            @inject(TYPES.ScaleService) private scaleService: ScaleService,
            @inject(TYPES.HealthCheckService) private healthCheckService: HealthCheckService,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
            @inject(TYPES.FailureAlertService) private failureAlertService: FailureAlertService,
            @inject(IRIS_MODELS_TYPES.NotificationSegmentTaskReadWriteDao) private notificationSegmentTaskReadWriteDao: INotificationSegmentTaskReadWriteDao
        ) {
        }

        @httpGet("/checkPendingSegmentTasks")
        public async checkStuckSegmentTasks(request: any): Promise<any> {
            const pendingTasks = await this.notificationSegmentTaskReadWriteDao.find(
                { condition: { status: {$in: [
                    NotificationSegmentTaskStatusEnum.PROCESSING,
                    NotificationSegmentTaskStatusEnum.CREATED,
                    NotificationSegmentTaskStatusEnum.SENDING]}
                }})

            pendingTasks.forEach(pendingTask => {
                const createdAt = new Date(_.get(pendingTask, "createdDate"))
                const secondsElapsed = (Date.now() - createdAt.getTime()) / 1000
                if (secondsElapsed > 3600 &&
                    (pendingTask.batchesProcessed / pendingTask.totalBatches) <= Constants.PENDING_SEGMENT_TASK_ALERT_THRESHOLD_RATIO) {
                    const errorMessage = `CronController::checkPendingSegmentTasks taskId: ${pendingTask.taskId}  ` +
                        `pending for more than one hour`
                    this.logger.error(errorMessage)
                    this.rollbarService.sendError(new Error(errorMessage))
                }
            })

            await this.healthCheckService.pingHealthCheck(HealthCheckTypeEnum.PENDING_SEGMENT_TASKS)
            return null
        }

        @httpPost("/assessCampaignHealth")
        public async assessCampaignHealth(request: any): Promise<any> {
            const { timeRange } = request.body
            this.failureAlertService.assessCampaignHealth(timeRange)
                .catch((err) => {
                    const errorMessage = `CronController::assessCampaignHealth Failed with error: ${JSON.stringify(err)}`
                    this.logger.error(errorMessage)
                    this.rollbarService.sendError(new Error(errorMessage))
                })

            await this.healthCheckService.pingHealthCheck(HealthCheckTypeEnum.ASSESS_CAMPAIGN_HEALTH)
            return null
        }

        @httpGet("/scaleIn")
        public async scaleIn(request: any): Promise<any> {

            this.scaleService.scaleIn()
                .catch((err) => {
                    const errorMessage = `CronController::scaleIn Failed with error: ${JSON.stringify(err)}`
                    this.logger.error(errorMessage)
                    this.rollbarService.sendError(new Error(errorMessage))
                })

            await this.healthCheckService.pingHealthCheck(HealthCheckTypeEnum.SCALE_IN_SERVICE)
            return null
        }
    }

    return CronController
}