import {BASE_TYPES, <PERSON><PERSON><PERSON>, C<PERSON>Util} from "@curefit/base"
import {NotFoundError} from "@curefit/error-client"
import {ICreativeReadWriteDao, IRIS_MODELS_TYPES} from "../../iris-models"
import {BaseCreative} from "../../iris-common"

import {controller, httpGet, httpPost, httpPut} from "inversify-express-utils"
import {Container, inject} from "inversify"
import {ObjectID} from "mongodb"


import TYPES from "../ioc/types"
import {AWSUtils} from "../utils/AWSUtils"
import CreativeService from "../service/CreativeService"
import {Creative, CreativeResponse} from "@curefit/campaign-manager-common"
import {
    CAMPAIGN_MANAGER_CLIENT_TYPES,
    ICreativeService as CampaignMangerCreativeService
} from "@curefit/campaign-manager-client"
import {CommonUtils} from "../utils/CommonUtils"
import {CommValidationsService} from "../service/CommValidationsService"
import {AlertService} from "../service/AlertService"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"


export function CreativeControllerFactory(kernel: Container) {

    @controller("/creatives")
    class CreativeController {

        constructor(
            @inject(IRIS_MODELS_TYPES.CreativeReadWriteDao) private creativeDao: ICreativeReadWriteDao,
            @inject(TYPES.AWSUtils) private awsUtils: AWSUtils,
            @inject(TYPES.CreativeService) private creativeService: CreativeService,
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(CAMPAIGN_MANAGER_CLIENT_TYPES.CreativeService) private campaignMangerCreativeService: CampaignMangerCreativeService,
            @inject(TYPES.CommValidationsService) private commValidationsService: CommValidationsService ,
            @inject(TYPES.AlertService)private alertService: AlertService,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
            @inject(BASE_TYPES.ClsUtil) protected clsUtil: CLSUtil) {
        }

        @httpGet("/allNonActive")
        public async getAllNonActiveCreatives(request: any): Promise<BaseCreative[]> {
            this.logger.info("getting all non active creatives")
            const creatives: BaseCreative[] = await this.creativeDao.find({condition: {registrationStatus: {$ne: "ACTIVE"}}})
            this.logger.info("Fetched Creatives : " + creatives.length)
            return creatives
        }

        @httpGet("/all")
        public async getAllCreatives(request: any): Promise<BaseCreative[]> {
            this.logger.info("getting all creatives")
            const creatives: BaseCreative[] = await this.creativeDao.retrieve()
            this.logger.info("Fetched Creatives : " + creatives.length)
            return creatives
        }

        @httpGet("/:id")
        public async getCreative(request: any): Promise<BaseCreative> {
            this.logger.info("Get creative by id " + request.params.id)
            const creative: BaseCreative = await this.creativeDao.findOne({_id: new ObjectID(request.params.id)})
            this.logger.info("Sending creative by id " + request.params.id)
            if (!creative) {
                const msg: string = "Unable to find creative by id " + request.params.id
                this.logger.info(msg)
                throw new NotFoundError(msg)
            }
            return creative
        }

        @httpPost("/")
        public async addCreative(request: any): Promise<BaseCreative> {
            const creative: BaseCreative = request.body
            return this.creativeService.addCreative(creative)
        }

        @httpPost("/activateCreative/:id")
        public async markCreativeActive(request: any): Promise<boolean> {
            const {templateId} = request.query
            return this.creativeService.updateRegistrationStatus(request.params.id, "ACTIVE", templateId)
        }

        @httpPost("/submitCreative/:id")
        public async markCreativeSubmitted(request: any): Promise<boolean> {
            return this.creativeService.updateRegistrationStatus(request.params.id, "PENDING")
        }

        @httpPost("/rejectCreative/:id")
        public async markCreativeRejected(request: any): Promise<boolean> {
            return this.creativeService.updateRegistrationStatus(request.params.id, "REJECTED")
        }

        @httpPut("/:id")
        public async updateCreative(request: any): Promise<BaseCreative> {
            const creative: BaseCreative = request.body
            if (creative.creativeId.startsWith("SMS") && !CommonUtils.isEnvironmentAlpha()) {
                throw new Error("SMS creatives cannot be updated")
            }

            await this.creativeDao.findOneAndUpdate({_id: new ObjectID(request.params.id)}, creative)
            this.logger.info("Updated creative with id " + request.params.id)
            return creative
        }

        @httpPost("/backfill")
        public async backfillAllCreativesToAssetManager(request: any) {
            let allIrisCreatives: BaseCreative[] = await this.getAllCreatives({})
            this.logger.info(`Received ${allIrisCreatives.length} creatives from IRIS`)
            let cmCreativeResponses: CreativeResponse[] = []
            for (let creative of allIrisCreatives) {
                if (!!creative.creativeId && !creative.creativeId.includes("CEREBRUM")) {
                    this.logger.error(`CreativeController::backfill Adding creative with creativeId ${creative.creativeId}`)
                    creative.author = creative.author || "SYSTEM_IRIS_BACKFILL"
                    try {
                        let creativeToBeBackfilled: Creative = await this.creativeService.irisCreativeToCampaignManagerCreative(creative)
                        let cmCreativeResponse: CreativeResponse = await this.clsUtil.getAsyncLocalStorage().run(this.clsUtil.getAsyncLocalStorage().getStore(), () => {
                            this.clsUtil.getNamespace().set(CLSUtil.USER_ID_FIELD, creative.author)
                            return this.campaignMangerCreativeService.createCreative(creativeToBeBackfilled, true)
                        })
                        cmCreativeResponses.push(cmCreativeResponse)
                        this.logger.debug(`CreativeController::backfill Added creativeId ${creative.creativeId} as ${JSON.stringify(cmCreativeResponse)}`)
                    } catch (e) {
                        this.logger.error(`CreativeController::backfill Creative with Id: ${creative.creativeId} FAILED due to ${e}`)
                    }

                }
            }
            return {totalReceived: allIrisCreatives.length, totalTransferred : cmCreativeResponses.length}
        }

        @httpPost("/backfillbyid")
        public async backfillCreativesToAssetManagerbyId(request: any) {
            const creative: BaseCreative = await this.creativeDao.findOne({creativeId: request.body.id})
            creative.author = creative.author || "SYSTEM_IRIS_BACKFILL"
            this.logger.debug(`IRIS Creative: ${JSON.stringify(creative)}`)

            let creativeToBeBackfilled: Creative = await this.creativeService.irisCreativeToCampaignManagerCreative(creative)
            this.logger.debug(`Translated Creative ${JSON.stringify(creativeToBeBackfilled)}`)

            return this.clsUtil.getAsyncLocalStorage().run(this.clsUtil.getAsyncLocalStorage().getStore(), () => {
                this.clsUtil.getNamespace().set(CLSUtil.USER_ID_FIELD, creative.author)
                return this.campaignMangerCreativeService.createCreative(creativeToBeBackfilled, true)
            })

        }

        /* for mozart job , if already blacklisted doesn't proceed */
        @httpPost("/addToBlacklist")
        public async addItemsToBlacklist(request: any) {
            const creativeId = request.body.creativeId
            if (await this.commValidationsService.isBlacklistedCreative(creativeId)) {
                this.logger.info(`CreativeController::addItemsToBlacklist creativeId ${creativeId} is already blacklisted`)
                return
            }
            const result = await this.commValidationsService.addCreativeToBlacklist(request.body.creativeId)
            if (result.blacklisted) {
                await this.alertService.alertOwnerCreativeAddedToBlacklist(result.creativeId)
            }
            else {
                let errorMsg = `CreativeController::addItemsToBlacklist error adding creative to blacklist with id: ${result.creativeId}`
                this.logger.error(errorMsg)
                await this.rollbarService.sendError(new Error(errorMsg))
                throw new Error(errorMsg)
            }
        }

        // devtool api to extend expiry
        @httpPost("/blacklist")
        public async blacklistCreative(request: any) {
           const result = await this.commValidationsService.addCreativeToBlacklist(request.body.creativeId)
           if (result.blacklisted) {
               await this.alertService.alertOwnerCreativeAddedToBlacklist(result.creativeId)
           }
           else {
               let errorMsg = `CreativeController::blacklistCreative error adding creative to blacklist with id: ${result.creativeId}`
               this.logger.error(errorMsg)
           }
            return result
        }

        @httpPost("/bulkBlacklist")
        public async blacklistCreativesBulk(request: any) {
            let creativeIdList: string[] = request.body.creativeIdList ? request.body.creativeIdList : []
            const results = await this.commValidationsService.bulkAddCreativesToBlacklist(creativeIdList)
            // for all true results notify
            let failureList: string[] = []
            for (const result of results) {
                if (result.blacklisted) {
                    await this.alertService.alertOwnerCreativeAddedToBlacklist(result.creativeId)
                }
                else {
                    failureList.push(result.creativeId)
                }
            }

            if (failureList.length > 0) {
                let errorMsg = `CreativeController::blacklistCreativesBulk error adding following creatives to blacklist: ${JSON.stringify(failureList)}`
                this.logger.error(errorMsg)
                await this.rollbarService.sendError(new Error(errorMsg))
            }
            return results
        }

        @httpPost("/removeFromBlacklist")
        public async removeFromBlacklist(request: any) {
            let creativeIdList: string[] = request.body.creativeIdList ? request.body.creativeIdList : []
            return this.commValidationsService.expireCreativeBlacklist(creativeIdList)
        }

        @httpGet("/checkBlacklisted/:creativeId")
        public async checkBlacklisted(request: any) {
            let creativeId: string = request.params.creativeId
            return this.commValidationsService.isBlacklistedCreative(creativeId)
        }

        @httpGet("/blacklisted/all")
        public async allBlacklisted(request: any) {
            return this.commValidationsService.findAllBlacklisted("CREATIVE")
        }

        @httpPost("/identifyAndMarkBlacklisted")
        public async identifyAndMarkBlacklisted(request: any) {
            return await this.commValidationsService.identifyAndMarkCreativeBlacklist()
        }

    }
    return CreativeController
}
