import {controller, httpGet, httpPost, httpPut} from "inversify-express-utils"
import {Container, inject, injectable} from "inversify"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {ICommunicationCampaignReadWriteDao, IRIS_MODELS_TYPES} from "../../iris-models"
import {NotFoundError} from "@curefit/error-client"
import {CommunicationCampaign, PaginatedResults} from "../../iris-common"


export function CommunicationCampaignControllerFactory(kernel: Container) {

    @controller("/communicationcampaigns")
    class CommunicationCampaignController {

        constructor(
            @inject(IRIS_MODELS_TYPES.CommunicationCampaignReadWriteDao)
            private communicationcampaignDao: ICommunicationCampaignReadWriteDao,
            @inject(BASE_TYPES.ILogger) private logger: ILogger) {
        }

        @httpGet("/all")
        public async getAllCommunicationCampaigns(request: any): Promise<CommunicationCampaign[]> {
            const communicationcampaigns: CommunicationCampaign[] = await this.communicationcampaignDao.retrieve()
            return communicationcampaigns
        }

        @httpGet("/paginated")
        public async getPaginatedCommunicationCampaigns(request: any): Promise<PaginatedResults<CommunicationCampaign>> {
            const limitParam = request.query.limit
            const limit = limitParam ? parseInt(limitParam, 10) : 10
            if (isNaN(limit)) {
                // Handle the error, e.g., return a 400 Bad Request
                throw new Error("Invalid limit parameter")
            }
            const validatedLimit = Math.min(limit, 100) // Cap the limit at 100
            const skipParam = request.query.skip
            const skip = skipParam ? parseInt(skipParam, 10) : 0
            if (isNaN(skip)) {
                // Handle the error, e.g., return a 400 Bad Request
                throw new Error("Invalid skip parameter")
            }

            // Build condition for filtering
            const condition: any = {}

            // Add filters if provided
            if (request.query.type) {
                condition.type = request.query.type
            }

            if (request.query.campaignId) {
                condition.campaignId = { $regex: request.query.campaignId, $options: 'i' } // Case-insensitive search
            }

            if (request.query.name) {
                condition.name = { $regex: request.query.name, $options: 'i' } // Case-insensitive search
            }

            // Get total count for this filter
            const countQuery = {
                condition: condition
            }
            const totalCount = await this.communicationcampaignDao.count(countQuery)

            // Get paginated results
            const findQuery = {
                condition: condition,
                count: validatedLimit,
                start: skip
            }

            this.logger.info(`Fetching paginated communication campaigns with limit: ${validatedLimit}, skip: ${skip}, filters: ${JSON.stringify(condition)}`)
            const campaigns = await this.communicationcampaignDao.find(findQuery)

            return {
                results: campaigns,
                metadata: {
                    limit: validatedLimit,
                    skip: skip,
                    totalCount: totalCount
                }
            }
        }

        @httpGet("/:id")
        public async getCommunicationCampaign(request: any): Promise<CommunicationCampaign> {
            const communicationcampaignId: string = request.params.id
            const communicationcampaign: CommunicationCampaign =
                await this.communicationcampaignDao.findOne({ campaignId: communicationcampaignId })
            if (!communicationcampaign) {
                const msg: string = "Unable to find communicationcampaign by id " + communicationcampaignId
                this.logger.info(msg)
                throw new NotFoundError(msg)
            }
            return communicationcampaign
        }

        @httpPost("/")
        public async addCommunicationCampaign(request: any): Promise<CommunicationCampaign> {
            const communicationcampaign: CommunicationCampaign = request.body
            const created: CommunicationCampaign = await this.communicationcampaignDao.create(communicationcampaign)
            return created
        }

        @httpPut("/:id")
        public async updateCommunicationCampaign(request: any): Promise<CommunicationCampaign> {
            const communicationcampaign: CommunicationCampaign = request.body
            const communicationcampaignId: string = request.params.id

            await this.communicationcampaignDao.findOneAndUpdate({ campaignId: communicationcampaignId }, communicationcampaign)
            return communicationcampaign
        }

    }
    return CommunicationCampaignController
}