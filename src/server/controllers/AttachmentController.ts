import TYPES from "../ioc/types"
import {Container, inject} from "inversify"
import {controller, httpGet, httpPost, httpPut} from "inversify-express-utils"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {TemplateAttachments, UserTags} from "../../iris-common"
import {AttachmentHelper} from "../helpers/AttachmentHelper"

export function AttachmentControllerFactory(kernel: Container) {

    @controller("/attachment")
    class AttachmentController {

        constructor(
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(TYPES.AttachmentHelper) private attachmentHelper: AttachmentHelper) {
        }

        @httpPost("/urlFromTemplate")
        public async getAttachmentURLFromTemplate(request: any) {
            const templateAttachments: TemplateAttachments[] = [request.body]
            const userTags: UserTags = {
                templateAttachments: templateAttachments,
                tags : templateAttachments[0].tags,
                attachments: []
            }
            const attachments: { filename: any, content: any, url: string }[] = await this.attachmentHelper.getAttachments(userTags, true)
            return {url: attachments[0].url}
        }
    }
}