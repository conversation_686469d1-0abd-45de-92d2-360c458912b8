import {controller, httpGet, httpPost} from "inversify-express-utils"
import {Container, inject, injectable} from "inversify"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {Tenant} from "@curefit/user-common"

import TYPES from "../ioc/types"
import {IFirebaseAnalyticsService} from "../service/FirebaseAnalyticsService"
import {
    FirebasePurchaseEvent,
    FirebaseConversionEvent,
    FirebaseCustomEvent,
    FirebaseNotificationEvent
} from "../../iris-common"

export function FirebaseAnalyticsControllerFactory(kernel: Container) {

    @controller("/firebase-analytics")
    class FirebaseAnalyticsController {

        constructor(
            @inject(TYPES.FirebaseAnalyticsService) private firebaseAnalyticsService: IFirebaseAnalyticsService,
            @inject(BASE_TYPES.ILogger) private logger: ILogger
        ) {
        }

        @httpPost("/sendPurchaseEvent")
        public async sendPurchaseEvent(request: any): Promise<{ success: boolean, response?: any }> {
            this.logger.info(`FirebaseAnalyticsController::sendPurchaseEvent received request ${JSON.stringify(request.body)}`)
            
            const { userId, purchaseEventData, tenant = Tenant.CUREFIT_APP } = request.body
            
            if (!userId) {
                throw new Error("userId is required")
            }
            
            if (!purchaseEventData) {
                throw new Error("purchaseEventData is required")
            }

            const purchaseEvent: FirebasePurchaseEvent = {
                transaction_id: purchaseEventData.transaction_id,
                value: purchaseEventData.value,
                currency: purchaseEventData.currency || "INR",
                items: purchaseEventData.items || [],
                coupon: purchaseEventData.coupon,
                shipping: purchaseEventData.shipping,
                tax: purchaseEventData.tax
            }

            return this.firebaseAnalyticsService.sendPurchaseEvent(userId, purchaseEvent, tenant)
        }

        @httpPost("/sendPurchaseSuccessEvent")
        public async sendPurchaseSuccessEvent(request: any): Promise<{ success: boolean, response?: any }> {
            this.logger.info(`FirebaseAnalyticsController::sendPurchaseSuccessEvent received request ${JSON.stringify(request.body)}`)
            
            const { userId, transactionId, value, currency = "INR", itemName, tenant = Tenant.CUREFIT_APP } = request.body
            
            if (!userId) {
                throw new Error("userId is required")
            }
            
            if (!transactionId) {
                throw new Error("transactionId is required")
            }
            
            if (!value) {
                throw new Error("value is required")
            }

            return this.firebaseAnalyticsService.sendPurchaseSuccessEvent(userId, transactionId, value, currency, itemName, tenant)
        }

        @httpPost("/sendConversionEvent")
        public async sendConversionEvent(request: any): Promise<{ success: boolean, response?: any }> {
            this.logger.info(`FirebaseAnalyticsController::sendConversionEvent received request ${JSON.stringify(request.body)}`)
            
            const { userId, conversionEventData, tenant = Tenant.CUREFIT_APP } = request.body
            
            if (!userId) {
                throw new Error("userId is required")
            }
            
            if (!conversionEventData) {
                throw new Error("conversionEventData is required")
            }

            const conversionEvent: FirebaseConversionEvent = {
                event_name: conversionEventData.event_name,
                user_id: userId,
                timestamp_micros: conversionEventData.timestamp_micros || Date.now() * 1000,
                user_properties: conversionEventData.user_properties,
                event_params: conversionEventData.event_params
            }

            return this.firebaseAnalyticsService.sendConversionEvent(userId, conversionEvent, tenant)
        }

        @httpPost("/sendCustomEvent")
        public async sendCustomEvent(request: any): Promise<{ success: boolean, response?: any }> {
            this.logger.info(`FirebaseAnalyticsController::sendCustomEvent received request ${JSON.stringify(request.body)}`)
            
            const { userId, customEventData, tenant = Tenant.CUREFIT_APP } = request.body
            
            if (!userId) {
                throw new Error("userId is required")
            }
            
            if (!customEventData) {
                throw new Error("customEventData is required")
            }

            const customEvent: FirebaseCustomEvent = {
                event_name: customEventData.event_name,
                parameters: customEventData.parameters || {},
                user_id: userId,
                session_id: customEventData.session_id,
                engagement_time_msec: customEventData.engagement_time_msec
            }

            return this.firebaseAnalyticsService.sendCustomEvent(userId, customEvent, tenant)
        }

        @httpPost("/sendNotificationEvent")
        public async sendNotificationEvent(request: any): Promise<{ success: boolean, response?: any }> {
            this.logger.info(`FirebaseAnalyticsController::sendNotificationEvent received request ${JSON.stringify(request.body)}`)
            
            const { userId, notificationEventData, tenant = Tenant.CUREFIT_APP } = request.body
            
            if (!userId) {
                throw new Error("userId is required")
            }
            
            if (!notificationEventData) {
                throw new Error("notificationEventData is required")
            }

            const notificationEvent: FirebaseNotificationEvent = {
                notification_id: notificationEventData.notification_id,
                campaign_id: notificationEventData.campaign_id,
                creative_id: notificationEventData.creative_id,
                event_type: notificationEventData.event_type,
                timestamp: notificationEventData.timestamp || Date.now(),
                user_id: userId,
                app_id: notificationEventData.app_id
            }

            return this.firebaseAnalyticsService.sendNotificationEvent(userId, notificationEvent, tenant)
        }

        @httpPost("/getAnalytics")
        public async getAnalytics(request: any): Promise<any> {
            this.logger.info(`FirebaseAnalyticsController::getAnalytics received request ${JSON.stringify(request.body)}`)
            
            const { tenant = Tenant.CUREFIT_APP, start_date, end_date, event_names = [] } = request.body
            
            if (!start_date || !end_date) {
                throw new Error("start_date and end_date are required")
            }

            return this.firebaseAnalyticsService.getAnalytics(tenant, start_date, end_date, event_names)
        }

        @httpGet("/health")
        public async healthCheck(request: any): Promise<{ status: string, timestamp: number }> {
            return {
                status: "Firebase Analytics Controller is healthy",
                timestamp: Date.now()
            }
        }
    }

    return FirebaseAnalyticsController
}

export default FirebaseAnalyticsControllerFactory
