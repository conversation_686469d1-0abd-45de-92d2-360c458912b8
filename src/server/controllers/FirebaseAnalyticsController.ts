import {BASE_TYPES, ILogger} from "@curefit/base"

import {Container, inject, injectable} from "inversify"
import {controller, httpGet, httpPost} from "inversify-express-utils"

import TYPES from "../ioc/types"
import {IFirebaseService} from "../service/FirebaseService"
import {PurchaseEvent} from "../../iris-common"

export function FirebaseAnalyticsControllerFactory(kernel: Container) {
    // Note: kernel parameter is required for consistency with other controllers
    // and the inversify-express-utils framework pattern

    @controller("/firebase-analytics")
    class FirebaseAnalyticsController {

        constructor(
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(TYPES.FirebaseService) private firebaseService: IFirebaseService
        ) {
        }

        @httpPost("/sendPurchaseEvent")
        public async sendPurchaseEvent(request: any): Promise<{ success: boolean, response?: any, error?: string }> {
            this.logger.info(`rahul_logs_this: FirebaseAnalyticsController::sendPurchaseEvent - Starting with request: ${JSON.stringify(request.body)}`)

            try {
                // Support both GymFit format (nested) and direct format (flat)
                let userId, price, productId, productName, packType

                if (request.body.purchaseEventData) {
                    // GymFit format: nested purchaseEventData
                    this.logger.info(`rahul_logs_this: FirebaseAnalyticsController::sendPurchaseEvent - Using GymFit nested format`)
                    userId = request.body.userId
                    const eventData = request.body.purchaseEventData
                    price = eventData.price
                    productId = eventData.productId
                    productName = eventData.productName
                    packType = eventData.packType
                } else {
                    // Direct format: flat structure
                    this.logger.info(`rahul_logs_this: FirebaseAnalyticsController::sendPurchaseEvent - Using direct flat format`)
                    userId = request.body.userId
                    price = request.body.price
                    productId = request.body.productId
                    productName = request.body.productName
                    packType = request.body.packType
                }

                // Validate required fields
                if (!userId) {
                    const error = "userId is required"
                    this.logger.error(`rahul_logs_this: FirebaseAnalyticsController::sendPurchaseEvent - ERROR: ${error}`)
                    return { success: false, error }
                }

                if (!price) {
                    const error = "price is required"
                    this.logger.error(`rahul_logs_this: FirebaseAnalyticsController::sendPurchaseEvent - ERROR: ${error}`)
                    return { success: false, error }
                }

                if (!productId) {
                    const error = "productId is required"
                    this.logger.error(`rahul_logs_this: FirebaseAnalyticsController::sendPurchaseEvent - ERROR: ${error}`)
                    return { success: false, error }
                }

                if (!productName) {
                    const error = "productName is required"
                    this.logger.error(`rahul_logs_this: FirebaseAnalyticsController::sendPurchaseEvent - ERROR: ${error}`)
                    return { success: false, error }
                }

                this.logger.info(`rahul_logs_this: FirebaseAnalyticsController::sendPurchaseEvent - Extracted data: userId=${userId}, price=${price}, productId=${productId}, productName=${productName}, packType=${packType}`)

                // Create purchase event data
                const purchaseEventData: PurchaseEvent = {
                    price: price.toString(),
                    productId,
                    productName,
                    packType
                }

                this.logger.info(`rahul_logs_this: FirebaseAnalyticsController::sendPurchaseEvent - Calling FirebaseService for userId: ${userId}`)
                
                // Call Firebase service
                const response = await this.firebaseService.sendPurchaseEvent(userId, purchaseEventData)
                
                this.logger.info(`rahul_logs_this: FirebaseAnalyticsController::sendPurchaseEvent - Successfully completed for userId: ${userId}`)
                
                return {
                    success: true,
                    response: response
                }
                
            } catch (error) {
                const errorMessage = `Failed to send Firebase Analytics purchase event: ${error.toString()}`
                this.logger.error(`rahul_logs_this: FirebaseAnalyticsController::sendPurchaseEvent - ERROR: ${errorMessage}`)
                
                return {
                    success: false,
                    error: errorMessage
                }
            }
        }

        @httpGet("/health")
        public async healthCheck(request: any): Promise<{ status: string, timestamp: number, service: string }> {
            this.logger.info(`rahul_logs_this: FirebaseAnalyticsController::healthCheck - Health check requested`)
            
            return {
                status: "Firebase Analytics Controller is healthy",
                timestamp: Date.now(),
                service: "firebase-analytics"
            }
        }

        @httpPost("/test")
        public async testPurchaseEvent(request: any): Promise<{ success: boolean, testData?: any, response?: any, error?: string }> {
            this.logger.info(`rahul_logs_this: FirebaseAnalyticsController::testPurchaseEvent - Test endpoint called`)
            
            try {
                // Test data
                const testData = {
                    userId: "test_user_12345",
                    price: "999.99",
                    productId: "TEST_MONTHLY_GYM_PLAN",
                    productName: "Test Monthly Gym Plan",
                    packType: "monthly"
                }

                this.logger.info(`rahul_logs_this: FirebaseAnalyticsController::testPurchaseEvent - Using test data: ${JSON.stringify(testData)}`)

                const purchaseEventData: PurchaseEvent = {
                    price: testData.price,
                    productId: testData.productId,
                    productName: testData.productName
                }

                // Call Firebase service
                const response = await this.firebaseService.sendPurchaseEvent(testData.userId, purchaseEventData)
                
                this.logger.info(`rahul_logs_this: FirebaseAnalyticsController::testPurchaseEvent - Test completed successfully`)
                
                return {
                    success: true,
                    testData: testData,
                    response: response
                }
                
            } catch (error) {
                const errorMessage = `Test failed: ${error.toString()}`
                this.logger.error(`rahul_logs_this: FirebaseAnalyticsController::testPurchaseEvent - ERROR: ${errorMessage}`)
                
                return {
                    success: false,
                    error: errorMessage
                }
            }
        }
    }

    return FirebaseAnalyticsController
}

export default FirebaseAnalyticsControllerFactory
