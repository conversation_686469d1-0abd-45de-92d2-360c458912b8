import {BASE_TYPES, ILogger} from "@curefit/base"
import {AuthError, DataError} from "@curefit/error-client"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

import {controller, httpGet, httpPost} from "inversify-express-utils"
import {Container, inject, injectable} from "inversify"
import express = require("express")
import * as _ from "lodash"

import TYPES from "./../ioc/types"
import {
    CancelCampaignNotificationsRequest,
    CancelCampaignNotificationsResponse,
    GetLastSentMessageRequest,
    GetLastSentMessageRespose,
    NotificationSegmentTask,
    SendCampaignNotificationsRequest,
    SendCampaignNotificationsResponse,
    SendCampaignNotificationsToSegmentRequest,
    SendNotificationsRequest
} from "../../iris-common"
import {MetricUtils} from "../utils/MetricUtils"
import NotificationEventProcessingService from "../service/NotificationEventProcessingService"
import {ApiKeyMappingCache} from "../auth/ApiKeyMappingCache"
import CampaignHelper from "../helpers/CampaignHelper"
import NotificationQueueConsumer from "../consumers/NotificationQueueConsumer"
import Constants from "../constants"
import {SendCampaignWithCustomTags} from "../../iris-common/src/campaign"


export function UserCommunicationCampaignControllerFactory(kernel: Container) {

    @controller("/campaigns")
    class UserCommunicationCampaignController {

        constructor(
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
            @inject(TYPES.CampaignHelper) private campaignHelper: CampaignHelper,
            @inject(TYPES.NotificationEventProcessingService) private notificationEventProcessingService: NotificationEventProcessingService,
            @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
            @inject(TYPES.ApiKeyMappingCache) private apiKeyMappingCache: ApiKeyMappingCache) {
        }

        @httpPost("/lastSent")
        public async getLastSentCampaignMessages(request: any): Promise<GetLastSentMessageRespose> {
            this.metricUtils.incrementApiCallCounter("getLastSentCampaignMessages", "/lastSent")
            const getNotificationsTime: GetLastSentMessageRequest = request.body
            return this.campaignHelper.getLastSentCampaignMessages(getNotificationsTime)
        }

        @httpPost("/send")
        public async sendCampaignMessages(request: any): Promise<SendCampaignNotificationsResponse> {
            this.metricUtils.incrementApiCallCounter("sendCampaignMessages", "/send")
            const sendNotifications: SendCampaignNotificationsRequest = request.body
            //fetch out upto 10 custom tags here, creativeId, workEmailx
            this.logger.debug(`headers in /send api: ${request.headers}`)
            sendNotifications.appId = request.headers[Constants.TENANT_HEADER_KEY] || sendNotifications.appId
            if (!sendNotifications.userContexts) {
                throw new DataError("User contexts missing")
            }
            if (sendNotifications.userContexts.length > 1000) {
                this.rollbarService.sendError(new Error("Notification request received for " +
                    sendNotifications.userContexts.length + "users"))
            }
            return this.campaignHelper.sendCampaignMessages(sendNotifications)
        }

        @httpPost("/sendCampaignWithCustomTags")
        public async sendCampaignMessagesWithCustomTags(request: any): Promise<SendCampaignNotificationsResponse> {
            this.metricUtils.incrementApiCallCounter("sendCampaignWithCustomTags", "/sendCampaignWithCustomTags")
            const sendNotificationsWithCustomTags: SendCampaignWithCustomTags = request.body
            this.logger.info(`request received in body api ${JSON.stringify(request.body)}`)
            sendNotificationsWithCustomTags.appId = request.headers[Constants.TENANT_HEADER_KEY] || sendNotificationsWithCustomTags.appId
            let sendNotifications: SendCampaignNotificationsRequest = {
                campaignId: sendNotificationsWithCustomTags.campaignId,
                creativeIds: [sendNotificationsWithCustomTags.creativeId],
                userContexts: [{
                    emailId: sendNotificationsWithCustomTags.emailId,
                    tags: {
                        "customTag1": sendNotificationsWithCustomTags.customTag1,
                        "customTag2": sendNotificationsWithCustomTags.customTag2,
                        "customTag3": sendNotificationsWithCustomTags.customTag3,
                        "customTag4": sendNotificationsWithCustomTags.customTag4,
                        "customTag5": sendNotificationsWithCustomTags.customTag5,
                        "customTag6": sendNotificationsWithCustomTags.customTag6,
                        "customTag7": sendNotificationsWithCustomTags.customTag7,
                        "customTag8": sendNotificationsWithCustomTags.customTag8,
                        "customTag9": sendNotificationsWithCustomTags.customTag9,
                        "customTag10": sendNotificationsWithCustomTags.customTag10
                    }
                }]
            }
            if (!sendNotifications.userContexts) {
                throw new DataError("User contexts missing")
            }
            if (sendNotifications.userContexts.length > 1000) {
                this.rollbarService.sendError(new Error("Notification request received for " +
                    sendNotifications.userContexts.length + "users"))
            }
            return this.campaignHelper.sendCampaignMessages(sendNotifications)
        }


        @httpPost("/ext/send")
        public async sendCampaignMessagesExt(request: any): Promise<SendCampaignNotificationsResponse> {
            this.metricUtils.incrementApiCallCounter("sendCampaignMessagesExt", "/ext/send")
            const isAuthorized = await this.apiKeyMappingCache.isAuthorized(request)
            if (!isAuthorized) {
                throw new AuthError("User unauthenticated")
            }
            return this.sendCampaignMessages(request)
        }

        @httpPost("/cancel")
        public async cancelCampaignMessages(request: any): Promise<CancelCampaignNotificationsResponse> {
            this.metricUtils.incrementApiCallCounter("cancelCampaignMessages", "/cancel")
            const payload: CancelCampaignNotificationsRequest = request.body
            return this.campaignHelper.cancelCampaignMessages(payload)
        }

        @httpPost("/cancelNotificationTask")
        async cancelNotificationTask(request: any, response: express.Response): Promise<any> {
            if (!request.body.taskId) {
                throw new DataError("Body should contain parameter taskId")
            }
            const taskId = request.body.taskId
            const result: boolean = await this.notificationEventProcessingService.cancelTask(taskId)
            if (result)
                return response.status(200).send({ message: "OK" })
            else
                return response.status(500).send()
        }

        @httpPost("/sendOne")
        public async sendCampaignMessagesThroughOneCreative(request: any): Promise<SendCampaignNotificationsResponse> {
            this.metricUtils.incrementApiCallCounter(
                "sendCampaignMessagesThroughOneCreative", "/sendOne")
            const sendNotifications: SendCampaignNotificationsRequest = request.body
            if (!sendNotifications.userContexts) {
                throw new DataError("User contexts missing")
            }
            this.logger.debug(`headers in /sendOne api: ${request.headers}`)
            sendNotifications.appId = request.headers[Constants.TENANT_HEADER_KEY] || sendNotifications.appId
            return this.campaignHelper.sendCampaignMessagesThroughOneCreative(sendNotifications)
        }

        @httpPost("/sendAsync")
        public async sendCampaignMessagesAsync(request: any, response: express.Response) {
            this.metricUtils.incrementApiCallCounter("sendCampaignMessagesAsync", "/sendAsync")
            const sendNotifications: SendCampaignNotificationsRequest = request.body
            if (!sendNotifications.userContexts) {
                throw new DataError("User contexts missing")
            }

            this.logger.debug(`headers in /sendAsync api: ${request.headers}`)
            sendNotifications.appId = request.headers[Constants.TENANT_HEADER_KEY] || sendNotifications.appId

            // Make a copy
            const sendCampaignNotificationsRequest: SendCampaignNotificationsRequest
                = JSON.parse(JSON.stringify(sendNotifications))

            // Chunk in batches of max size the queue can handle
            for (const userContextChunk of
                _.chunk(sendNotifications.userContexts, NotificationQueueConsumer.MAX_USER_CONTEXT_SIZE)) {
                sendCampaignNotificationsRequest.userContexts = userContextChunk

                try {
                    await this.campaignHelper.sendCampaignMessagesToQueue(sendNotifications)
                } catch (err) {
                    const errorMessage = `UserCommunicationCampaignController::sendCampaignMessagesAsync Error while ` +
                        `pushing to queue. Error: ${err.toString()}`
                    this.logger.error(errorMessage)
                    this.rollbarService.sendError(new Error(errorMessage))
                }
            }
        }

        @httpPost("/sendToSegment")
        public async sendToSegment(request: any): Promise<any> {
            this.metricUtils.incrementApiCallCounter("sendToSegment", "/sendToSegment")
            const notificationsRequest: SendCampaignNotificationsToSegmentRequest = request.body

            this.logger.debug(`headers in /sendToSegment api: ${request.headers}`)
            notificationsRequest.appId = request.headers[Constants.TENANT_HEADER_KEY] || notificationsRequest.appId
            return this.campaignHelper.sendCampaignMessagesToSegment(notificationsRequest)
        }

        @httpGet("/getNotificationSegmentTask/:taskId")
        public async getNotificationSegmentTask(request: any): Promise<any> {
            this.metricUtils.incrementApiCallCounter("getNotificationSegmentTask",
                "/getNotificationSegmentTask/:taskId")
            const { taskId } = request.params
            return this.campaignHelper.getNotificationSegmentTask(taskId)
        }

        @httpPost("/updateNotificationSegmentTaskStatus/:taskId")
        public async updateNotificationSegmentTaskStatus(request: any): Promise<any> {
            const { taskId } = request.params
            const { status } = request.body
            return this.campaignHelper.updateNotificationSegmentTaskStatus(taskId, status)
        }

        @httpGet("/killNotificationSegmentTask/:taskId")
        public async killNotificationSegmentTask(request: any): Promise<any> {
            this.metricUtils.incrementApiCallCounter("killNotificationSegmentTask",
                "/killNotificationSegmentTask/:taskId")
            const { taskId } = request.params
            return this.campaignHelper.killNotificationSegmentTask(taskId)
        }

        @httpPost("/sendScheduledNotifications")
        public async sendScheduledNotifications(request: any): Promise<SendCampaignNotificationsResponse> {
            const callbackReceivedAt = new Date()
            this.metricUtils.incrementApiCallCounter("sendScheduledNotifications",
                "/sendScheduledNotifications")
            const sendNotifications: SendNotificationsRequest = request.body

            this.logger.debug(`headers in /sendScheduledNotifications api: ${request.headers}`)
            sendNotifications.appId = request.headers[Constants.TENANT_HEADER_KEY] || sendNotifications.appId
            return this.campaignHelper.processScheduledNotifications(sendNotifications, callbackReceivedAt)
        }

        @httpPost("/startScheduledTask")
        public async startScheduledTask(request: any): Promise<any> {
            this.metricUtils.incrementApiCallCounter("startScheduledTask", "/startScheduledTask")
            const taskId: string = request.body.irisTaskId
            await this.notificationEventProcessingService.pickScheduledTask(taskId)
            return Promise.resolve({ message: "OK" })
        }

        @httpPost("/transientDownloadTrinoSuccess")
        public async transientSegmentDownloadTrinoSuccess(req: any): Promise<any>  {
            const {notificationSegmentTaskId, mozartJobId} = req.body
            return await this.campaignHelper.transientSegmentDownloadTrinoSuccess(notificationSegmentTaskId, mozartJobId)
        }

        @httpPost("/transientDownloadTrinoFailure")
        public async transientSegmentDownloadTrinoFail(req: any): Promise<any> {
            const {notificationSegmentTaskId} = req.body
            return await this.campaignHelper.transientSegmentDownloadTrinoFail(notificationSegmentTaskId)
        }
    }

    return UserCommunicationCampaignController
}
