import { BASE_TYPES, ILogger } from "@curefit/base"
import { Container, inject } from "inversify"
import { controller, httpGet } from "inversify-express-utils"
import FirebaseAdminService from "../service/FirebaseAdminService"
import TYPES from "../ioc/types"

export function FirebaseReportsControllerFactory(kernel: Container) {

    @controller("/firebaseReports")
    class FirebaseReportsController {

        constructor(
            @inject(TYPES.FirebaseAdminService) private firebaseAdminService: FirebaseAdminService,
            @inject(BASE_TYPES.ILogger) private logger: ILogger) {
        }

        @httpGet("/")
        public async getFirebaseDeliveryDataReports(): Promise<Boolean[]>  {
            // get call to firebase to generate reports
            const reportData = await this.firebaseAdminService.getDeliveryDataReportsFromFirebase();
            // save report data to DB 
            return this.firebaseAdminService.createWeeklyReport(reportData.androidDeliveryData)
        }
    }
    return FirebaseReportsController
}