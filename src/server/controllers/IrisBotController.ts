import {Auth<PERSON>rror, DataError} from "@curefit/error-client"
import {BASE_TYPES, FetchUtil, ILogger} from "@curefit/base"

import * as _ from "lodash"
import {Container, inject, injectable} from "inversify"
import {controller, httpPost} from "inversify-express-utils"

import Constants from "../constants"
import {IBasicNotificationDao, IRIS_MODELS_TYPES} from "../../iris-models"
import TYPES from "../ioc/types"
import {MetricUtils} from "../utils/MetricUtils"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import UserContextHelper from "../helpers/UserContextHelper"
import {CommonUtils} from "../utils/CommonUtils"

export function IrisBotControllerFactory(kernel: Container) {

    @controller("/bot")
    class IrisBotController {

        private readonly prometheusEndpoint: string
        private readonly prometheusMetricKeyPrefix: string

        constructor(
            @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
            @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
            @inject(TYPES.UserContextHelper) private userContextHelper: UserContextHelper
        ) {
            this.prometheusEndpoint = CommonUtils.isEnvironmentProductionOrAlpha() ?
                "https://monitoring.curefit.co/prometheus/api/v1/query?query=" :
                "https://monitoring.stage.curefit.co/prometheus/api/v1/query?query="
            this.prometheusMetricKeyPrefix = process.env.ENVIRONMENT
        }

        private authenticateRequest(token: string) {
            if (token !== process.env.IRIS_BOT_AUTH_TOKEN) {
                const errorMessage = `IrisBotController::authenticateRequest Received unauthorized request to iris `
                    + `bot. Token: ${token}`
                this.logger.error(errorMessage)
                this.rollbarService.sendError(new Error(errorMessage))
                throw new AuthError("Not Authorised")
            }
        }

        @httpPost("/status")
        public async getNotificationStatus(request: any): Promise<any> {
            const token = _.get(request, "body.token", null)
            this.authenticateRequest(token)

            // Monitor throughput on this endpoint since it exposes sensitive data
            this.metricUtils.incrementApiCallCounter("bot.getNotificationStatus", "/bot/status")

            const notificationId = _.get(request, "body.text", null)
            const notification = await this.notificationDao.get(notificationId)

            if (!notification) {
                return {
                    "text": `Notification with id ${notificationId} not found`
                }
            }

            const notificationSummary = [
                `Notification Id: \t ${notificationId}`,
                `Campaign: \t ${notification.campaignId}`,
                `Creative: \t ${notification.creativeId}`,
                `User Id: \t *******`,
                `User Id Type: \t ${notification.userIdType}`,
                `Status: \t ${notification.status}`,
                `External id: \t ${notification.externalId}`
            ]

            if (notification.failReason) { notificationSummary.push(`failReason: \t ${notification.failReason}`) }

            const notificationTimeline = []
            if (notification.createdAt) { notificationTimeline.push(`createdAt: \t ${notification.createdAt}`) }
            if (notification.sentAt) { notificationTimeline.push(`sentAt: \t ${notification.sentAt}`) }
            if (notification.receivedAt) { notificationTimeline.push(`receivedAt: \t ${notification.receivedAt}`) }
            if (notification.openedAt) { notificationTimeline.push(`openedAt: \t ${notification.openedAt}`) }

            let userContextTags = this.userContextHelper.decodeUserContext(notification.userTags).tags
            let metaTags = this.userContextHelper.decodeUserContext(notification.userTags).metaTags

            // Remove user information
            if (userContextTags) {
                delete userContextTags[Constants.USER_EMAIL_TAG]
                delete userContextTags[Constants.USER_EMAIL_TAG_OLD]
                delete userContextTags[Constants.USER_NAME_TAG]
                delete userContextTags[Constants.USER_NAME_TAG_OLD]
                delete userContextTags[Constants.USER_PHONE_TAG]
                delete userContextTags[Constants.USER_PHONE_TAG_OLD]
                delete userContextTags[Constants.USER_SUBSCRIBED]
                delete userContextTags[Constants.USER_SUBSCRIBED_OLD]
                delete userContextTags[Constants.USER_ID_TAG]
                delete userContextTags[Constants.USER_EMAIL_UNSUBSCRIPTION_URL_TAG]
            } else {
                userContextTags = {}
            }

            const templateVariables = [`userTags: \t ${JSON.stringify(userContextTags, null, 8)}`]
            metaTags = [`metaTags: \t ${JSON.stringify(metaTags, null, 8)}`]

            const response = {
                "blocks": [
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": notificationSummary.join("\n")
                        }
                    },
                    {
                        "type": "divider"
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": notificationTimeline.join("\n")
                        }
                    },
                    {
                        "type": "divider"
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": templateVariables.join("\n")
                        }
                    },
                    {
                        "type": "divider"
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": metaTags.join("\n")
                        }
                    },
                    {
                        "type": "divider"
                    }
                ]
            }
            return response

        }

        @httpPost("/stat")
        public async getStats(request: any): Promise<any> {
            const token = _.get(request, "body.token", null)
            this.authenticateRequest(token)

            const paramString = _.get(request, "body.text", "")
            const params = paramString.trim().split(/[ ,]+/)
            if (params.length < 1 || params[0].length === 0) {
                return {
                    "blocks": [
                        {
                            "type": "divider"
                        },
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text":
                                    "Invalid usage. Try `/stat campaignId creativeId timeRange[5m, 2h, 1d, ...]`" +
                                    "\nE.g.: `/stat CUREFIT_OTP SMS_OTP_SERVICE 1h`"
                            }
                        },
                        {
                            "type": "divider"
                        }
                    ]
                }
            }

            const campaignId = params[0]
            const creativeId = params[1]
            const timeRange = params.length > 2 ? params[2] : "1h"

            return this.createStatResponse(creativeId, campaignId, timeRange)

        }

        @httpPost("/campaign")
        public async getCampaignStats(request: any): Promise<any> {
            const token = _.get(request, "body.token", null)
            this.authenticateRequest(token)

            const paramString = _.get(request, "body.text", "")
            const params = paramString.trim().split(/[ ,]+/)
            if (params.length < 1 || params[0].length === 0) {
                return {
                    "blocks": [
                    {
                        "type": "divider"
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text":
                                "Invalid usage. Try `/campaign campaignId timeRange[5m, 2h, 1d, ...]`" +
                                "\nE.g.: `/campaign CUREFIT_OTP 1h`"
                        }
                    },
                    {
                        "type": "divider"
                    }
                ]
            }
        }

            const campaignId = params[0]
            const timeRange = params.length > 1 ? params[1] : "1h"
            return this.createStatResponse(null, campaignId, timeRange)
        }

        @httpPost("/creative")
        public async getCreativeStats(request: any): Promise<any> {
            const token = _.get(request, "body.token", null)
            this.authenticateRequest(token)

            const paramString = _.get(request, "body.text", "")
            const params = paramString.trim().split(/[ ,]+/)
            if (params.length < 1 || params[0].length === 0) {
                return {
                    "blocks": [
                        {
                            "type": "divider"
                        },
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text":
                                    "Invalid usage. Try `/creative creativeId timeRange[5m, 2h, 1d, ...]`" +
                                    "\nE.g.: `/campaign SMS_OTP_SERVICE 1h`"
                            }
                        },
                        {
                            "type": "divider"
                        }
                    ]
                }
            }

            const creativeId = params[0]
            const timeRange = params.length > 1 ? params[1] : "1h"
            return this.createStatResponse(creativeId, null, timeRange)
        }


        private async createStatResponse(creativeId: string, campaignId: string, timeRange: string): Promise<any> {

            const { sentCount, failCount, failureMetrics } =
                await this.metricUtils.getFailureMetrics(creativeId, campaignId, timeRange)
            const { deliveredCount, readCount } =
                await this.metricUtils.getStatusMetrics(creativeId, campaignId, timeRange)

            const primaryResponse = []
            if (campaignId) { primaryResponse.push(`Campaign: \t ${campaignId}`) }
            if (creativeId) { primaryResponse.push(`Creative: \t ${creativeId}`) }
            if (timeRange) { primaryResponse.push(`Time range: \t ${timeRange}`) }


            const statusResponse = [
                `*Summary*`,
                ``,
                `Sent: \t *${sentCount}*`,
                `Failed: \t *${failCount}*`,
                `Delivered: \t *${deliveredCount}*`,
                `Read: \t *${readCount}*`,
            ]

            const failureResponse = [ `*Failure reasons*`, ``]

            for (const metric of failureMetrics) {
                if (metric.label === "none") {
                    continue
                }
                failureResponse.push(`${metric.label}: \t *${metric.value}*`)
            }

            return {
                "blocks": [
                    {
                        "type": "divider"
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": primaryResponse.join("\n")
                        }
                    },
                    {
                        "type": "divider"
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": statusResponse.join("\n")
                        }
                    },
                    {
                        "type": "divider"
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": failureResponse.join("\n")
                        }
                    },
                    {
                        "type": "divider"
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": `P.S. These are *approximate* counts for gauging trends. ` +
                                `For accurate counts, use Metabase.`
                        }
                    },
                    {
                        "type": "divider"
                    },
                ]
            }
        }

    }

    return IrisBotController
}