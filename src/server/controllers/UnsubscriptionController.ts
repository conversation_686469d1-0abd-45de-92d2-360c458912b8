import {Container, inject, injectable} from "inversify"
import {controller, httpGet} from "inversify-express-utils"
import {BASE_TYPES, ILogger} from "@curefit/base"
import TYPES from "../ioc/types"
import UnsubscriptionService from "../service/UnsubscriptionService"

export function UnsubscriptionControllerFactory(kernel: Container) {

    @controller("/unsubscribe")
    class UnsubscriptionController {

        constructor(
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(TYPES.UnsubscriptionService) private unsubscriptionService: UnsubscriptionService
        ) {
        }

        @httpGet("/:creativeType/decodeToken/:token")
        public async decodeToken(request: any): Promise<any> {
            const { token, creativeType } = request.params
            return this.unsubscriptionService.decodeUnsubscriptionToken(creativeType, token)
        }

    }

    return UnsubscriptionController
}