import {DataError, DataErrorV3, NotFoundError} from "@curefit/error-client"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

import {controller, httpGet, httpPost, httpPut} from "inversify-express-utils"
import {Container, inject, injectable} from "inversify"
import * as _ from "lodash"
import express = require("express")

import Constants from "../constants"
import {MetricUtils} from "../utils/MetricUtils"
import {CommonUtils} from "../utils/CommonUtils"
import {BillingUtils} from "../utils/BillingUtils"
import {
    ClickToCallReport,
    EmailValidResponse, ExternalCommunicationCampaign,
    Notification,
    NotificationReport,
    NotificationStatus,
    PaginatedResults,
    SendCampaignNotificationsRequest,
    UserNotificationFilterRequest,
    UserTags
} from "../../iris-common"
import TYPES from "../ioc/types"
import NotificationEventProcessingService from "../service/NotificationEventProcessingService"
import NotificationHelper from "../helpers/NotificationHelper"
import {APP_NAMES, NOTIFICATION_TYPES} from "../helpers/INotificationService"
import NotificationReportHelper from "../helpers/NotificationReportHelper"
import {IWhatsappNotificationHelper} from "../helpers/whatsapp/WhatsappNotificationHelper"
import NotificationStatusUpdatePublisher from "../publishers/NotificationStatusUpdatePublisher"
import {IClickToCallNotificationHelper} from "../helpers/ClickToCallNotificationHelper"
import UserInteractionPublisher from "../publishers/UserInteractionPublisher"
import SMSNotificationHelper from "../helpers/SMS/SMSNotificationHelper"
import UserContextHelper from "../helpers/UserContextHelper"
import {IBasicNotificationDao, IRIS_MODELS_TYPES} from "../../iris-models"
import CampaignHelper from "../helpers/CampaignHelper"
import {SendCampaignNotificationsResponse} from "../../iris-client/src/ICampaignService"
import {DatalakeMetricService} from "../service/DatalakeMetricService"
import {TaskManagementService} from "../service/TaskManagementService"
import FonadaSMSHelper from "../helpers/SMS/FonadaSMSHelper"
import {LeadCategorisationService} from "../service/LeadCategorisationService"

export function NotificationsControllerFactory(kernel: Container) {

    @controller("/notifications")
    class NotificationsController {

        constructor(
            @inject(TYPES.CommonUtils) private commonUtils: CommonUtils,
            @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
            @inject(TYPES.UserContextHelper) private userContextHelper: UserContextHelper,
            @inject(TYPES.NotificationHelper) private notificationHelper: NotificationHelper,
            @inject(TYPES.UserInteractionPublisher) private userInteractionPublisher: UserInteractionPublisher,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
            @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
            @inject(TYPES.NotificationReportHelper) private notificationReportHelper: NotificationReportHelper,
            @inject(TYPES.WhatsappNotificationHelper) private whatsappNotificationHelper: IWhatsappNotificationHelper,
            @inject(TYPES.SMSNotificationHelper) private smsNotificationHelper: SMSNotificationHelper,
            @inject(TYPES.FonadaSMSHelper) private fonadaSMSHelper: FonadaSMSHelper,
            @inject(TYPES.ClickToCallNotificationHelper) private clickToCallNotificationHelper: IClickToCallNotificationHelper,
            @inject(TYPES.NotificationStatusUpdatePublisher) private notificationStatusUpdatePublisher: NotificationStatusUpdatePublisher,
            @inject(TYPES.NotificationEventProcessingService) private notificationEventProcessingService: NotificationEventProcessingService,
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(TYPES.CampaignHelper) private campaignHelper: CampaignHelper,
            @inject(TYPES.DatalakeMetricService) private customMetricService: DatalakeMetricService,
            @inject(TYPES.TaskManagementService) private taskManagementService: TaskManagementService,
            @inject(TYPES.LeadCategorisationService) private leadCategorisationService: LeadCategorisationService,
        ) { }

        @httpGet("/smsDelivery/gs")
        public async updateGupshupSmsDelivery(request: any): Promise<any> {
            this.logger.debug(`NotificationsController::updateGupshupSmsDelivery query: ${JSON.stringify(request.query)}`)

            const callbackReceivedAt = new Date()
            const externalId: string = request.query.externalId
            const deliveredAt: Date = new Date(Number(request.query.deliveredTS))
            const callbackStatus = request.query.status
            const errorCause = request.query.cause
            //removing everything before first "-"
            const notificationId: string = externalId.substring(externalId.indexOf("-") + 1)
            this.logger.debug(`NotificationsController::updateGupshupSmsDelivery notificationId: ${notificationId}`)
            /*
            We publish the callbacks to a delayed queue in order to avoid receiving callbacks before IRIS updates
             the entire batch in its DB.
             */
            await this.smsNotificationHelper.publishSMSCallback("GUPSHUP", callbackStatus,
                notificationId, deliveredAt, callbackReceivedAt, errorCause)
            return Promise.resolve({ message: "OK" })
        }

        @httpGet("/smsDelivery/fonada")
        public async updateFonadaSmsDelivery(request: any, response: any): Promise<any> {
            this.logger.info(`NotificationsController::updateFonadaSmsDelivery query: ${JSON.stringify(request.query)}`)
            try {
                const { txid, deliverystatus, deliverydt, errCode } = request.query

                const validateCallback = this.fonadaSMSHelper.validateFonadaCallbackRequest(txid, deliverystatus, deliverydt, errCode)

                if(!validateCallback.status) {
                    let errorMsg = validateCallback.message
                    this.logger.error(errorMsg)
                    this.rollbarService.sendError(new Error(errorMsg))
                    return response.status(400).json({
                        message: errorMsg
                    })
                }
                
                const callbackReceivedAt : Date = new Date()
                const externalId : string = Constants.SMS_FONADA_EXTERNALID_PREFIX + txid
                const notification = await this.notificationDao.getNotificationsByExternalId(externalId)
                if(!notification) {
                    let errorMsg : string = `No notification sent record exist against externalId: ${externalId}`
                    throw new Error(errorMsg)
                }
                const deliveredAt : Date = deliverydt ? new Date(decodeURIComponent(deliverydt).replace(/^(\d{2})-(\d{2})-(\d{4})/, '$2-$1-$3') + ' GMT+0530') : new Date()
                const callbackStatus : string = deliverystatus
                const errorCause : string = errCode ? errCode.toString() : "UNKNOWN"
                const notificationId : string = notification.notificationId
                this.logger.debug(`NotificationsController::updateFonadaSMSDelivery notificationId: ${notificationId}`)
                await this.smsNotificationHelper.publishSMSCallback("FONADA", callbackStatus,
                    notificationId, deliveredAt, callbackReceivedAt, errorCause)
            }
            catch(e) {
                let errorMessage = `NotificationsController::updateFonadaSMSDelivery Error processing callback from Fonada; err: ${e.toString()}`
                this.logger.error(errorMessage)
                this.rollbarService.sendError(new Error(errorMessage))
            }
            return Promise.resolve({ message: "OK" })
        }

        @httpGet("/ext/smsDelivery")
        public async updateSmsDeliveryExt(request: any): Promise<any> {
            return this.updateSmsDelivery(request)
        }

        @httpPut("/smsDelivery")
        public async updateSmsDelivery(request: any): Promise<any> {
            const callbackReceivedAt = new Date()
            const notificationId: string = request.query.custom
            const deliveredAt: Date = new Date(request.query.delivat * 1000) // delivat is in seconds
            const callbackStatus = request.query.status

            /*
            We publish the callbacks to a delayed queue in order to avoid receiving callbacks before IRIS updates
             the entire batch in its DB.
             */
            this.logger.info(`NotificationsController::updateSmsDelivery query: ${JSON.stringify(request.query)}`)
            await this.smsNotificationHelper.publishSMSCallback("SOLUTIONS_INFINI", callbackStatus,
                notificationId, deliveredAt, callbackReceivedAt)
            return Promise.resolve({ message: "OK" })
        }

        @httpGet("/soipDelivery")
        public async updateSoipDelivery(request: any): Promise<any> {
            const callbackReceivedAt = new Date()
            const externalId: string = request.query.kid
            const status: string = request.query.status
            const failReason: string = request.query.status_trace
            const failReasonExplanation: string = request.query.description

            this.logger.info(`NotificationsController::updateSOIPDelivery received callback for ${externalId} ${status} ${failReason} ${failReasonExplanation} at ${callbackReceivedAt.toString()}`)

            await this.smsNotificationHelper.processSOIPCallback(externalId, status, failReason, callbackReceivedAt)

        }

        @httpGet("/whatsappDelivery")
        public async updateWhatsappDelivery(request: any): Promise<any> {
            const callbackReceivedAt = new Date()

            const guid: string = request.query.guid


            const deliveryDateIST: Date = new Date(request.query.deliveryDate)
            const deliveryDate = new Date(deliveryDateIST.getTime() - 330 * 60 * 1000)
            const statusError: string = request.query.statusError
            const reasonCode: string = request.query.reasonCode

            const status: NotificationStatus =
                await this.whatsappNotificationHelper.findWhatsappNotificationStatus(
                    Constants.VALUE_FIRST, statusError, reasonCode)
            const failReason = status === "FAILED" ?
                this.whatsappNotificationHelper.findWhatsappNotificationFailureReason(
                    Constants.VALUE_FIRST, statusError, reasonCode)
                : null

            if (!status) {
                const errorMessage = `NotificationsController::updateWhatsappDelivery Unable to translate status: ` +
                    `${statusError}. Full message: ${JSON.stringify(request.query)}`
                const error = new Error(errorMessage)
                this.rollbarService.sendError(error)
                throw error
            }

            const externalId: string = Constants.VALUE_FIRST + "_" + guid
            try {
                await this.whatsappNotificationHelper.updateNotificationStatus(externalId, status, Constants.VALUE_FIRST,
                    deliveryDate, callbackReceivedAt, failReason)
            } catch (e) {
                this.logger.error(`NotificationsController::updateWhatsappDelivery Unable to process whatsapp delivery ` +
                    `event for callback ${request.query}. Error: ${e.toString()}`)
            }
            return Promise.resolve({ message: "OK" })
        }

        @httpPost("/whatsappDelivery/sprinklr")
        public async updateWhatsappDeliveryForSprinklr(request: any): Promise<any> {
            try {
                this.logger.info(`NotificationsController::updateWhatsappDeliveryForSprinklr`, {params: request.params, body: request.body})
                
                if(_.isEmpty(request.body)) {
                    this.logger.error("NotificationsController::updateWhatsappDeliveryForSprinklr ignoring callback received empty request body")
                    return Promise.resolve({})
                }

                const callbackReceivedAt = new Date()
                const statusCode = request.body.type
                const messageId = request.body.payload.messageId

                if(!messageId) {
                    this.logger.error("NotificationsController::updateWhatsappDeliveryForSprinklr ignoring callback Message Id missing in request")
                    return Promise.resolve({})
                }
                const externalId = Constants.SPRINKLR + "_" + messageId.split("_").pop()
                const eventTime = new Date(+request.body.eventTime)
                const errorMsg = request.body.payload.error

                this.whatsappNotificationHelper.handleWhatsappCallback(externalId, Constants.SPRINKLR, eventTime, statusCode, errorMsg, callbackReceivedAt)
            } catch (e) {
                this.logger.error(`NotificationsController::updateWhatsappDeliveryForSprinklr`, {e})
                const error = new Error(`NotificationsController::updateWhatsappDeliveryForSprinklr Unable to handle callback due to ${e}`)
                this.rollbarService.sendError(error)
            }
            return Promise.resolve({})
        }

        @httpPost("/updateWhatsappPreferencesForUser/sprinklr")
        public async updateWhatsappPreferencesForUser(request: any): Promise<any> {
            this.logger.info(`NotificationsController::updateWhatsappPreferencesForUser received request ${JSON.stringify(request.body)}`)

            const phone = request.body?.payload?.senderProfile?.channelId
            const message = request.body?.payload?.content?.text

            // This number is the number we send messages from. We want to ignore messages sent from this number.
            if (!phone || phone === "918792514523" || phone === "919945510078" || _.isNaN(+phone)) {
               this.logger.info(`NotificationsController::updateWhatsappPreferencesForUser Invalid phone ${phone}, ignoring`)
               return Promise.resolve({})
            }
            if (!message) {
                this.logger.info(`NotificationsController::updateWhatsappPreferencesForUser Invalid message ${message}, ignoring`)
                return Promise.resolve({})
            }

            return this.whatsappNotificationHelper.handleWhatsappUnsubscription(message, phone.slice(-10))

        }

        @httpGet("/whatsappDelivery/kaleyra")
        public async updateWhatsappDeliveryForKaleyra(request: any): Promise<any> {
            this.logger.debug(`NotificationsController::updateWhatsappDeliveryForKaleyra query: ${JSON.stringify(request.query)}`)

            const callbackReceivedAt = new Date()
            const id: string = request.query.id
            const deliveryDate: Date = new Date(Number(request.query.timestamp) * 1000)
            const reqStatus: string = request.query.status
            const reasonCode: string = request.query.error_code

            const status: NotificationStatus =
                await this.whatsappNotificationHelper.findWhatsappNotificationStatus(
                    Constants.KALEYRA, reqStatus, reasonCode)
            const failReason = status === "FAILED" ?
                this.whatsappNotificationHelper.findWhatsappNotificationFailureReason(
                    Constants.KALEYRA, reqStatus, reasonCode)
                : null

            if (!status) {
                const errorMessage = `NotificationsController::updateWhatsappDeliveryForKaleyra Unable to translate status: ` +
                    `${reqStatus}. Full message: ${JSON.stringify(request.query)}`
                const error = new Error(errorMessage)
                this.rollbarService.sendError(error)
                throw error
            }

            const externalId: string = Constants.KALEYRA + "_" + id
            /*
            whatsapp has a callback for sent and delivered both, but they might not be received in the same order
            so only updating on status [DELIVERED, READ, FAILED]
             */
            if (status !== "SENT") {
                try {
                    await this.whatsappNotificationHelper.updateNotificationStatus(externalId, status, Constants.KALEYRA,
                        deliveryDate, callbackReceivedAt, failReason)
                } catch (e) {
                    this.logger.error(`NotificationsController::updateWhatsappDeliveryForKaleyra Unable to process whatsapp delivery ` +
                        `event for callback ${request.query}. Error: ${e.toString()}`)
                }
            }
            return Promise.resolve({ message: "OK" })
        }


        @httpGet("/obdCallback")
        public async updateOBDDelivery(request: any): Promise<any> {
            const receivedAt: Date = new Date()

            this.logger.info(`NotificationsController::updateOBDDelivery request query: ${JSON.stringify(request.query)}`)
            const notificationId: string = request.query.id
            const status = await this.notificationReportHelper.findNotificationStatus(
                Constants.SOLUTIONS_INFINI, "OBD", request.query.status)

            const notification = await this.notificationDao.get(notificationId)
            if (!notification) {
                const errorMessage = `NotificationsController::updateOBDDelivery Could not find notification ` +
                    `${notificationId}. query: ${request.query}`
                const error = new Error(errorMessage)
                this.rollbarService.sendError(error)
                throw error
            }

            const failReason = status === "FAILED" ? `${Constants.SOLUTIONS_INFINI}_${request.query.status}` : null
            await this.notificationDao.updateProps(notificationId,
                { receivedAt, status, failReason, billableUnits: BillingUtils.getOBDUnitsConsumed() })
            await this.notificationStatusUpdatePublisher.publish([{ notification, status,
                timestamp: receivedAt.getTime() }])

            this.metricUtils.incrementCallbackFailReasonCounter("OBD", notification.campaignId,
                notification.creativeId, failReason ? failReason : "none", Constants.SOLUTIONS_INFINI)
            await this.userInteractionPublisher.publishPhoneInteraction(notification, receivedAt, status)
            return Promise.resolve({ message: "OK" })
        }

        @httpGet("/clickToCallReport")
        public async updateClickToCallDelivery(request: any): Promise<any> {
            this.logger.info(`NotificationController::updateClickToCallDelivery received status ${JSON.stringify(request.query)}`)
            const notificationId: string = request.query.notificationid
            const clickToCallProviderReport = {
                caller: request.query.caller,
                receiver: request.query.receiver,
                starttime: request.query.starttime,
                endtime: request.query.endtime,
                duration: request.query.duration,
                billsec: request.query.billsec,
                status: request.query.status,
                status1: request.query.status1,
                status2: request.query.status2,
                recordpath: request.query.recordpath,
                callerid: request.query.callerid,
                serviceProvider: Constants.SOLUTIONS_INFINI
            }
            await this.clickToCallNotificationHelper.handleReport(
                "SOLUTIONS_INFINI", notificationId, clickToCallProviderReport)
            return Promise.resolve({ message: "OK" })
        }

        @httpGet("/inboundCallReport")
        public async createInboundCallReport(request: any){
            this.logger.info(`NotificationController::createInboundClickToCallReport received status ${JSON.stringify(request.query)}`)
            const inboundCallProviderReport = {
                caller: request.query.caller,
                receiver: request.query.receiver,
                starttime: request.query.starttime,
                endtime: request.query.endtime,
                duration: request.query.duration,
                billsec: request.query.billsec,
                dialstatus: request.query.dialstatus,
                hangupfirst: request.query.hangupfirst,
                recordpath: request.query.recordpath,
                virtualnumber: request.query.virtualnumber,
                inboundCall: request.query.inboundCall,
                callId: request.query.callId,
                serviceProvider: Constants.SOLUTIONS_INFINI
            }
            if (_.isEmpty(request.query.receiver)) {
                this.logger.info(`NotificationController::createInboundClickToCallReport for caller - ${request.query.caller} on agent found, ignoring call report`);
                this.customMetricService.publishInboundCallingNoCMEvent(inboundCallProviderReport)
                await this.taskManagementService.pushIVRLeadToTelesales(inboundCallProviderReport.caller, inboundCallProviderReport.virtualnumber);
                return Promise.resolve({message: "ACCEPTED"})
            }

            //Push all 'CM at center' found leads to PMS whether its connected or not
            const externalCampaignId = request.query.campaignId as ExternalCommunicationCampaign

            const notificationId = await this.campaignHelper.createNotificationForInboundCampaign(externalCampaignId, request.query.caller, true)

            try {
                await this.taskManagementService.pushIVRLeadToPMS(inboundCallProviderReport.caller, inboundCallProviderReport.virtualnumber, inboundCallProviderReport.receiver, notificationId)
            } catch (err) {
                this.logger.error("Failed to push IVR lead to PMS", err)
            }

            await this.clickToCallNotificationHelper.handleInboundCallReport("SOLUTIONS_INFINI", notificationId, inboundCallProviderReport)

            await this.leadCategorisationService.categoriseLeadType({...inboundCallProviderReport, notificationId, externalCampaignId})

            return Promise.resolve({message: "ACCEPTED"})
        }

        @httpPost("/fetchRecentlySentCampaigns")
        public async fetchRecentlySentCampaigns(request: any): Promise<string[]> {
            const campaignIds: string[] = request.body.campaignIds
            const startDate: string = request.body.startDate
            const endDate: string = request.body.endDate
            this.logger.info(`NotificationController:fetchRecentlySentCampaigns received fetchRecentlySentCampaigns for ${campaignIds}, ${startDate} ${endDate}`)
            const result = await this.notificationDao.fetchRecentlySentCampaigns(campaignIds, startDate, endDate)
            this.logger.info(`NotificationController:fetchRecentlySentCampaigns result ${result} `)
            return result
        }

        @httpPut("/task/:id")
        public async processNotificationTask(request: any): Promise<boolean> {
            const notificationTaskId = request.params.id
            try {
                await this.notificationEventProcessingService.processTask(notificationTaskId)
            } catch (err) {
                this.logger.error("Unable to process notification task from controller because ", err.toString())
                throw err
            }
            return Promise.resolve(true)
        }

        /*
        For resending old(>30 days) notifications
         */
        @httpPost("/retrigger")
        public async retriggerOld(request: any): Promise<SendCampaignNotificationsResponse> {
            return this.campaignHelper.resendNotificationFromAthena(request.body.notificationId)
        }

        @httpGet("/:id")
        public async getNotification(request: any): Promise<Notification> {
            const notificationId: string = request.params.id
            const notification: Notification = await this.notificationDao.get(notificationId)
            if (!notification) {
                const msg: string = "Unable to find notification by id " + notificationId
                this.logger.info(msg)
                throw new NotFoundError(msg)
            }
            let userContext: UserTags = this.userContextHelper.decodeUserContext(notification.userTags)
            userContext = await this.userContextHelper.generateSignedUrlForAttachments(userContext)
            notification.userTags = JSON.stringify(userContext)
            return notification
        }



        @httpPost("/reports")
        public async getNotificationReportBulk(request: any): Promise<NotificationReport[]> {
            const notificationIds: string[] = request.body
            if (_.isEmpty(notificationIds)) return []
            if (notificationIds.length > 10000) {
                throw new DataError("Request can't be served. List of notificationIds is too long.")
            }
            return this.notificationReportHelper.getNotificationReportBulk(notificationIds)
        }

        // TODO: Deprecate. Only gearFit relies on this, have asked them to migrate
        @httpPost("/sendTransactional")
        public async sendTransactional(req: express.Request, res: express.Response) {
            if (!req.body.appName || APP_NAMES.indexOf(req.body.appName) === -1) {
                throw new DataError("Invalid parameter appName")
            }

            if (!req.body.notificationType || NOTIFICATION_TYPES.indexOf(req.body.notificationType)) {
                throw new DataError("Invalid parameter notificationType")
            }

            await this.notificationHelper.sendNotification(req.body.notificationType, req.body.appName, req.body.notificationParams)
            res.status(202).send()
        }

        @httpGet("/taskReport/:taskId")
        public async getTaskReport(request: any): Promise<any> {
            const taskId: string = request.params.taskId
            return this.notificationReportHelper.getTaskReport(taskId)
        }

        @httpGet("/clickToCallReport/:notificationId")
        public async getClickToCallReport(request: any): Promise<ClickToCallReport> {
            const notificationId: string = request.params.notificationId
            return this.notificationReportHelper.getClickToCallReport(notificationId)
        }

        @httpPost("/clickToCallReport/:serviceProvider/:id")
        public async updateClickToCallReport(request: any): Promise<any> {
            this.logger.info(`NotificationController::updateClickToCallReport received status ${JSON.stringify(request.params)})`)
            const serviceProvider = request.params.serviceProvider
            const notificationId = request.params.id
            const clickToCallProviderReport = request.body
            await this.clickToCallNotificationHelper.handleReport(serviceProvider, notificationId,
                clickToCallProviderReport)
            return Promise.resolve({ message: "OK" })
        }

        /**
         * Checks if an existing notification is successfully processed
         * If yes, does nothing
         * If no, retries it (with a different service provider)
         */
        @httpPost("/retryCheck")
        public async notificationSendRetryCheck(request: any): Promise<any> {
            const notificationId: string = request.body.irisNotificationId
            const serviceProvider: string = request.body.irisServiceProvider
            const providerExternalId: string = request.body.irisProviderExternalId
            //can remove await from here
            await this.notificationHelper.notificationSendRetryCheck(notificationId, serviceProvider, providerExternalId)
            return Promise.resolve({ message: "OK" })
        }

        @httpPost("/user/history")
        public async getUserNotifications(request: any): Promise<PaginatedResults<Notification>> {
            const filterRequest: UserNotificationFilterRequest = request.body
            if (_.isEmpty(filterRequest.userIds)) {
                throw new DataErrorV3(new Error("userIds are required"))
            }
            this.commonUtils.checkFields({collection: filterRequest, fields: ["startDate", "endDate"]})
            return this.notificationHelper.getUserNotificationsPaginated(filterRequest)
        }

        @httpPost("/email/validate")
        public async validateEmail(request: any): Promise<EmailValidResponse> {
            const email = request.body.email
            const skipLocalCheck: boolean = !!request.body.skipLocalCheck
            const checkForLastXDays = request.body.checkForLastXDays || Constants.DEFAULT_EMAIL_VALIDATION_LAST_X_DAYS
            return this.notificationHelper.validateEmail(email, skipLocalCheck, checkForLastXDays)
        }

        @httpPost("/email/bulkValidate")
        public async bulkValidateEmail(request: any): Promise<EmailValidResponse[]> {
            const emails: string[] = request.body.emails
            if (emails.length > Constants.EMAIL_VALIDATION_MAX_BULK_LIMIT) {
                throw new DataErrorV3(new Error(`Max limit for bulk requests is ${Constants.EMAIL_VALIDATION_MAX_BULK_LIMIT}
                received ${emails.length}`))
            }
            const skipLocalCheck: boolean = !!request.body.skipLocalCheck
            const checkForLastXDays = request.body.checkForLastXDays || Constants.DEFAULT_EMAIL_VALIDATION_LAST_X_DAYS
            let promises = []
            for (let email of emails) {
                promises.push(this.notificationHelper.validateEmail(email, skipLocalCheck, checkForLastXDays))
            }
            return Promise.all(promises)
        }
    }

    return NotificationsController
}

