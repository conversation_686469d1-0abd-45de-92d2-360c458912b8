import {controller, httpPost, httpPut} from "inversify-express-utils"
import {Container, inject} from "inversify"
import {BASE_TYPES, ILogger} from "@curefit/base"
import TYPES from "./../ioc/types"
import {AppInfo, DeeplinkResponse, DeeplinkServices, GetAnalyticsResponse, LinkPayload} from "../../iris-common"
import * as _ from "lodash"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {Tenant} from "@curefit/user-common"
import {IFirebaseService} from "../service/FirebaseService"
import LinkHelper from "../helpers/LinkHelper"
import {IBranchIOService} from "../service/BranchIOService"

export function DeeplinkControllerFactory(kernel: Container) {

    @controller("/link")
    class DeeplinkController {

        constructor(
            @inject(TYPES.FirebaseService) private firebaseService: IFirebaseService,
            @inject(TYPES.BranchIOService) private branchioService: IBranchIOService,
            @inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService) {
        }

        async createDeepLink(request: any, update: boolean): Promise<DeeplinkResponse> {
            const { tenant = Tenant.CUREFIT_APP, link, appInfo, userId, preferredService }: { tenant: Tenant, link: LinkPayload, appInfo?: AppInfo, userId?: string, preferredService?: DeeplinkServices } = request.body
            const appVersion = _.get(appInfo, "appVersion")

            const weblink = link.weblink

            try {
                if (preferredService === DeeplinkServices.BRANCH || !preferredService) {
                    if (update) {
                        return {...await this.branchioService.editUniversalDeepLinkUrl(tenant, link), applink: link.applink, weblink}
                    }
                    return {...await this.branchioService.createUniversalDeepLinkUrl(tenant, link), applink: link.applink, weblink}
                }
                if (preferredService === DeeplinkServices.FIREBASE || _.isNil(appVersion) || LinkHelper.isFirebaseLinksSupported(appVersion)) {
                    const { url } = await this.firebaseService.createUniversalDeepLinkUrl(tenant, link, appInfo, userId)
                    return { url, applink: link.applink, universallink: LinkHelper.getUniversalLink(url), weblink }
                }
            } catch (error) {
                // this.rollbarService.sendError(new Error("Failed to create dynamic link: " + JSON.stringify(error)))
                this.logger.error("Error : " + error)
                error.message = "Failed to create deep link: " + JSON.stringify(error.message || error)
                throw error
            }
        }

        @httpPost("/create")
        public async createDeeplink(request: any): Promise<DeeplinkResponse> {
            return await this.createDeepLink(request, false)
        }

        @httpPut("/update")
        public async updateDeeplink(request: any): Promise<DeeplinkResponse> {
            return await this.createDeepLink(request, true)
        }

        @httpPost("/getAnalytics")
        public async getAnalyticsForCampaign(request: any): Promise<GetAnalyticsResponse> {
            this.logger.info(`DeeplinkController::getAnalyticsForCampaign received request ${JSON.stringify(request.body)}`)
            const {tenant = Tenant.CUREFIT_APP, start_date, end_date, creativeIdToUtmIdMap} = request.body
            return this.branchioService.getAnalytics(tenant, start_date, end_date, creativeIdToUtmIdMap)
        }

        @httpPost("/sendPackPurchaseEvent")
        public async sendPackPurchaseEvent(request: any): Promise<any> {
            this.logger.info(`DeeplinkController::sendPackPurchaseEvent received request ${JSON.stringify(request.body)}`)
            const {userId, purchaseEventData} = request.body
            return this.branchioService.sendPurchaseEvent(userId, purchaseEventData)
        }
    }

    return DeeplinkController
}
