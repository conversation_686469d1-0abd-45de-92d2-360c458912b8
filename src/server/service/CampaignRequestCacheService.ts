import {inject, injectable} from "inversify"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {IMultiCrudKeyValue, MultiRedisAccess, REDIS_TYPES} from "@curefit/redis-utils"
import {CacheAccessImpl, ICacheAccess, Key} from "@curefit/cache-utils"
import {SendCampaignNotificationsRequest} from "../../iris-common"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

class CampaignSingleCreativeRequestCacheKey implements Key {

    key: string

    constructor(taskId: string) {
        this.key = `iris_request__${taskId}`
    }

    hash(): string {
        return undefined
    }

    lookupKey(): string {
        return this.key
    }
}

class CampaignRequestCacheKey implements Key {

    key: string

    constructor(taskId: string) {
        this.key = `iris_campaign_request__${taskId}`
    }

    hash(): string {
        return undefined
    }

    lookupKey(): string {
        return this.key
    }
}

/*
 * Used for caching campaign requests
 * Used for send via one creative flow
 */
@injectable()
class CampaignRequestCacheService {

    private readonly campaignRequestCacheExpiryInSeconds: number = 60 * 60 * 24
    private readonly campaignSingleCreativeRequestCache: ICacheAccess<CampaignSingleCreativeRequestCacheKey, SendCampaignNotificationsRequest>
    private readonly campaignRequestCache: ICacheAccess<CampaignRequestCacheKey, SendCampaignNotificationsRequest>

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(REDIS_TYPES.MultiRedisAccess) multiRedisAccess: MultiRedisAccess,
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService
    ) {
        this.campaignSingleCreativeRequestCache = new CacheAccessImpl<CampaignSingleCreativeRequestCacheKey, SendCampaignNotificationsRequest>
            (multiCrudKeyValueDao.getICrudKeyValue("DEFAULT"))
        this.campaignRequestCache = new CacheAccessImpl<CampaignRequestCacheKey, SendCampaignNotificationsRequest>
            (multiCrudKeyValueDao.getICrudKeyValue("DEFAULT"))
    }

    async logSingleCreativeCampaignRequest(taskId: string,
                                           campaignRequest: SendCampaignNotificationsRequest): Promise<boolean> {
        this.logger.debug(`CampaignRequestCacheService::logSingleCreativeCampaignRequest Caching campaign request` +
            `taskId: ${taskId} request: ${JSON.stringify(campaignRequest)}`)
        try {
            await this.campaignSingleCreativeRequestCache.create(new CampaignSingleCreativeRequestCacheKey(taskId),
                campaignRequest, this.campaignRequestCacheExpiryInSeconds)
        } catch (error) {
            const errorMessage = `CampaignRequestCacheService::logSingleCreativeCampaignRequest Error while caching campaign request`
                + `taskId: ${taskId} request: ${JSON.stringify(campaignRequest)} error: ${error.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            throw error
        }
        return true
    }

    async getSingleCreativeCampaignRequest(taskId: string): Promise<SendCampaignNotificationsRequest> {
        this.logger.debug(`CampaignRequestCacheService::getSingleCreativeCampaignRequest Getting campaign request` +
            `taskId: ${taskId}`)
        try {
            return await this.campaignSingleCreativeRequestCache.get(new CampaignSingleCreativeRequestCacheKey(taskId))
        } catch (error) {
            const errorMessage = `CampaignRequestCacheService::getSingleCreativeCampaignRequest Error while getting campaign request`
                + `taskId: ${taskId} error: ${error.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            throw error
        }
    }

    async logCampaignRequest(taskId: string,
                                           campaignRequest: SendCampaignNotificationsRequest): Promise<boolean> {
        this.logger.debug(`CampaignRequestCacheService::logCampaignRequest Caching campaign request` +
            `taskId: ${taskId} request: ${JSON.stringify(campaignRequest)}`)
        try {
            await this.campaignRequestCache.create(new CampaignRequestCacheKey(taskId),
                campaignRequest, this.campaignRequestCacheExpiryInSeconds)
        } catch (error) {
            const errorMessage = `CampaignRequestCacheService::logCampaignRequest Error while caching campaign request`
                + `taskId: ${taskId} request: ${JSON.stringify(campaignRequest)} error: ${error.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            throw error
        }
        return true
    }

    async getCampaignRequest(taskId: string): Promise<SendCampaignNotificationsRequest> {
        this.logger.debug(`CampaignRequestCacheService::getCampaignRequest Getting campaign request` +
            `taskId: ${taskId}`)
        try {
            return await this.campaignRequestCache.get(new CampaignRequestCacheKey(taskId))
        } catch (error) {
            const errorMessage = `CampaignRequestCacheService::getCampaignRequest Error while getting campaign request`
                + `taskId: ${taskId} error: ${error.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            throw error
        }
    }
}

export default CampaignRequestCacheService