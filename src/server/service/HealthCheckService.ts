import { BASE_TYPES, HttpClient, ILogger } from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

import { inject, injectable } from "inversify"

import {CommonUtils} from "../utils/CommonUtils"

export enum HealthCheckTypeEnum {
    PENDING_SEGMENT_TASKS = "PENDING_SEGMENT_TASKS",
    ASSESS_CAMPAIGN_HEALTH = "ASSESS_CAMPAIGN_HEALTH",
    SCALE_IN_SERVICE = "SCALE_IN_SERVICE"
}

@injectable()
class HealthCheckService {

    private readonly healthCheckEnabled: boolean = false

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(BASE_TYPES.HttpClient) private httpClient: HttpClient,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
    ) {
        this.healthCheckEnabled = CommonUtils.isEnvironmentProduction()
    }

    public isHealthCheckEnabled(): boolean {
        return this.healthCheckEnabled
    }

    public async pingHealthCheck(healthCheckType: HealthCheckTypeEnum) {
        if (!this.isHealthCheckEnabled()) {
            this.logger.debug(`HealthCheck::pingHealthCheck HealthCheck disabled, returning.`)
            return
        }

        let hcUrl

        switch (healthCheckType) {
            case HealthCheckTypeEnum.PENDING_SEGMENT_TASKS: {
                hcUrl = "https://hc-ping.com/874bde8a-486a-46f1-95b3-cc4ea446086f"
                break
            }
            case HealthCheckTypeEnum.ASSESS_CAMPAIGN_HEALTH: {
                hcUrl = "https://hc-ping.com/237f5390-2f28-4779-9028-f5c915ccca2c"
                break
            }
            case HealthCheckTypeEnum.SCALE_IN_SERVICE: {
                hcUrl = "https://hc-ping.com/65184be6-38a7-4e16-9b55-3264ba93ef17"
                break
            }
            default: {
                this.logger.error(`HealthCheck::pingHealthCheck Invalid health check type ${healthCheckType}`)
                return
            }
        }

        try {
            this.logger.debug(`HealthCheck::pingHealthCheck ${healthCheckType} Making call to HC to ` +
                `report success : ${hcUrl}`)
            await this.httpClient.get(hcUrl)
        } catch (err) {
            const errorMessage = `HealthCheck::pingHealthCheck HC call failed ${JSON.stringify(err)}`
            this.logger.error(errorMessage)
        }
    }
}

export default HealthCheckService