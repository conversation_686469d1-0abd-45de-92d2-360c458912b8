import {BASE_TYPES, ILogger} from "@curefit/base"
import {inject, injectable} from "inversify"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {IRIS_MODELS_TYPES} from "../../iris-models"
import {BaseCreative} from "@curefit/iris-common"
import {ICreativeReadonlyDao} from "@curefit/iris-models"
import {SendCampaignNotificationsRequest, UserTags} from "../../iris-common"
import TYPES from "../ioc/types"
import CampaignHelper from "../helpers/CampaignHelper"
import Constants from "../constants"
import featureUtils from "../utils/FeatureUtils"
import FeatureUtils from "../utils/FeatureUtils"


@injectable()
export class AlertService {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(IRIS_MODELS_TYPES.CreativeReadonlyDao) private creativeReadOnlyDao: ICreativeReadonlyDao,
        @inject(TYPES.CampaignHelper) private campaignHelper: CampaignHelper,
        @inject(TYPES.FeatureUtils) private featureUtils: FeatureUtils,
    ) {

    }

    // creative blacklisting alerts
    public async alertOwnerCreativeAddedToBlacklist(creativeId: string) {
        try {

            this.logger.info(`AlertService::alertOwnerCreativeAddedToBlacklist Sending blacklisted alert for ${creativeId}`)
            let creative: BaseCreative =  await this.creativeReadOnlyDao.findOne({creativeId})
            const blacklistConfig = this.featureUtils.getBlacklistCreativeConfig({"defaultRecipient": Constants.BLACKLIST_ALERT_RECIPIENT, "ccRecipients": Constants.BLACKLIST_ALERT_CC_RECIPIENTS, "noAuthorCCRecipients": [], "creativeId": Constants.CREATIVE_BLACKLIST_NOTIFY_EMAIL_CREATIVE})
            const sendRecipient = creative.author ? creative.author : blacklistConfig.defaultRecipient
            const userContexts: UserTags[] = [
                {
                    "emailId": sendRecipient,
                    "emailCC": creative.author ? blacklistConfig.ccRecipients : [...blacklistConfig.ccRecipients, ...blacklistConfig.noAuthorCCRecipients],
                    "tags": {}
                }
            ]

            const sendCampaignNotificationsRequest: SendCampaignNotificationsRequest = {
                userContexts,
                campaignId: Constants.CREATIVE_BLACKLIST_NOTIFY_EMAIL_CAMPAIGN,
                creativeIds: [blacklistConfig.creativeId],  // make one and add from config; should be different for stage prod etc
                globalTags: {
                    "creativeId": creativeId
                }
            }

            await this.campaignHelper.sendCampaignMessages(sendCampaignNotificationsRequest)
        }
        catch (ex) {
            let errMsg = `BlacklistCreativeService::alertOwnerCreativeAddedToBlacklist Error alerting ${creativeId} to blacklist; error: ${ex}`
            this.logger.error(errMsg)
            await this.rollbarService.sendError(new Error(errMsg))
        }
    }

    // the following creatives are in blacklist

}