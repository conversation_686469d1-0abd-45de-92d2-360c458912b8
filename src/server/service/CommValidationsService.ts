import {BASE_TYPES, ILogger} from "@curefit/base"
import {inject, injectable} from "inversify"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {CommValidation, CommValidationType, ExpireValidationRequest} from "../../iris-common/src/commValidations"
import {ICommValidationsDao} from "../../iris-models/src/daos/ICommValidationsDao"
import {IRIS_MODELS_TYPES} from "../../iris-models"
import UserProfileAttributeUtils from "../utils/UserProfileAttributeUtils"
import Constants from "../constants"
import * as _ from "lodash"
import TYPES from "../ioc/types"
import {AthenaServiceV2} from "./AthenaServiceV2"
import {AthenaTaskEntry, RequestType, TaskResponse} from "@curefit/athena-service-client"
import FeatureUtils from "../utils/FeatureUtils"

const memoize = require("memoizee")

interface CreativeBlacklistResponse {
    creativeId: string,
    blacklisted: <PERSON>olean,
}


const IDENTIFY_BLACKLIST_MOZART_JOB_CONFIG_ID: string = "mark-blacklist-irisCreative"
const IDENTIFY_BLACKLIST_REF_ID: string = "IDENTIFY_BLACKLIST_CREATIVE"

@injectable()
export class CommValidationsService {
    // function memcached for 15 min
    public cachedIsBlacklistedCreative = memoize(this.isBlacklistedCreative, {promise: true, maxAge: 900000})

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(IRIS_MODELS_TYPES.CommValidationsDao) private commValidationsDao: ICommValidationsDao,
        @inject(TYPES.UserProfileAttributeUtils) private userProfileAttributeUtils: UserProfileAttributeUtils,
        @inject(TYPES.AthenaServiceV2) private athenaService: AthenaServiceV2,
        @inject(TYPES.FeatureUtils) private featureUtils: FeatureUtils
    ) {

    }

    // this method will be called by the success job from athena
    public async bulkAddCreativesToBlacklist(creativeIdList: string[]): Promise<CreativeBlacklistResponse[]> {
      try {
          let expireAt: Date = new Date()
          expireAt.setDate(expireAt.getDate() + 7)

          let commValidations: CommValidation[] = creativeIdList.map(creativeId => ({type: "CREATIVE", entityId: creativeId, expireAt}))

          const results = await this.commValidationsDao.bulkCreate(commValidations)

          return creativeIdList.map((creativeId, index) => ({
              creativeId,
              blacklisted: results[index]
          }))
      }
      catch (ex) {
          let errMsg = `CommValidationsService::bulkAddCreativeToBlacklist Error processing request for creativeIdList: ${JSON.stringify(creativeIdList)} ; error: ${ex}`
          this.logger.error(errMsg)
          await this.rollbarService.sendError(new Error(errMsg))
          throw new Error(ex)
      }
    }

    //add creative to blacklist
    public async addCreativeToBlacklist(creativeId: string): Promise<CreativeBlacklistResponse> {
        try {
            let expireAt: Date = new Date()
            expireAt.setDate(expireAt.getDate() + 7)
            const blacklisted: Boolean = await this.commValidationsDao.create({type: "CREATIVE", entityId: creativeId, expireAt})

            return {creativeId, blacklisted}
        }
        catch (ex) {
            let errMsg = `CommValidationsService::addCreativeToBlacklist Error processing request for creativeId: ${creativeId} ; error: ${ex}`
            this.logger.error(errMsg)
            await this.rollbarService.sendError(new Error(errMsg))
            throw new Error(ex)
        }
    }

    //remove creatives from blacklist - updates the expire time to current time
    public async expireCreativeBlacklist(creativeIdList: string[]): Promise<Boolean[]> {
        try {
            let expireList: ExpireValidationRequest[] = creativeIdList.map(creativeId => ({type: "CREATIVE", entityId: creativeId}))
            return await this.commValidationsDao.expireValidation(expireList)
        }
        catch (ex) {
            let errMsg = `CommValidationsService::expireCreativeBlacklist failed with error: ${ex}`
            this.logger.error(errMsg)
            await this.rollbarService.sendError(new Error(errMsg))
            throw new Error(errMsg)
        }
    }


    public async findAllBlacklisted(type: CommValidationType): Promise<CommValidation[]> {
        try {
            return await this.commValidationsDao.findAllBlockingValidations(type)
        }
        catch (ex) {
            let errMsg = `CommValidationService::findAllBlacklisted failed with error: ${ex}`
            this.logger.error(errMsg)
            await this.rollbarService.sendError(new Error(errMsg))
            throw new Error(errMsg)
        }
    }

    //check if creative is blacklisted
    public async isBlacklistedCreative(creativeId: string): Promise<Boolean> {
        try {

            return await this.commValidationsDao.isBlockingValidation("CREATIVE", creativeId)
        }
        catch (ex) {
            let errMsg = `CommValidationService::isBlacklistedCreative failed with error: ${ex}`
            this.logger.error(errMsg)
            await this.rollbarService.sendError(new Error(errMsg))
            return false
        }
    }

    //  queries trino to find all creatives that need to be blacklisted , streams that using mozart and calls the blacklist creative api
    public async identifyAndMarkCreativeBlacklist(): Promise<AthenaTaskEntry> {
        const blacklistCreativeConfig = this.featureUtils.getBlacklistCreativeConfig({blacklistedFailReasons: [], failureRatio: 0.95, minSendCount: 100, timeWindow: 7})
        const failReasons = blacklistCreativeConfig.blacklistedFailReasons.map((reason: any) => `'${reason}'`).join(",")

        const query: string = Constants.getBlacklistCreativeQuery(blacklistCreativeConfig.timeWindow, failReasons, blacklistCreativeConfig.failureRatio, blacklistCreativeConfig.minSendCount)

        this.logger.info(`CommValidationsService::executeBlacklistCreativeTrinoQuery query: ${query}`)
        return await this.athenaService.executeQueryOnTrino(query, IDENTIFY_BLACKLIST_REF_ID, IDENTIFY_BLACKLIST_MOZART_JOB_CONFIG_ID, RequestType.RUN_QUERY_AND_STREAM_RESULT)
    }

    public async isUnsubscribedFromCommunications(channel: string, userId: string, isTransactional: boolean): Promise<Boolean> {
        try {
            let userLimits = await this.userProfileAttributeUtils.getCachedUserAttribute(
                userId, UserProfileAttributeUtils.USER_PROFILE_ATTRIBUTE_KEY_COMMUNICATION_LIMITS, Constants.APP_ID_CUREFIT)
            userLimits = JSON.parse(userLimits)
            const dailyLimits = _.get(userLimits, "daily", [])

            if (_.isEmpty(dailyLimits)) {
                this.logger.info(`user ${userId} has no daily limits set`)
                return false
            }

            const objective: string = isTransactional ? Constants.TRANSACTIONAL_COMM_BLOCK_OBJECTIVE : Constants.PROMO_COMM_BLOCK_OBJECTIVE

            const filteredEntries = dailyLimits.filter((entry: any) => {
                const descriptions = entry.description
                const isChannel = descriptions.some((desc: any) => desc.dimensionname === "channel" && desc.dimensionvalue === channel)
                const isObjective = descriptions.some((desc: any) => desc.dimensionname === "objective" && desc.dimensionvalue === objective)
                return isChannel && isObjective
            })


            let checkUnsubscribed: boolean = true
            for (const entry of filteredEntries) {
                let isUnsubscribed = entry.unsubscribed
                let hasVertical: boolean = entry.description.some((desc: any) => desc.dimensionname === "vertical")
                if (isUnsubscribed && !hasVertical) {
                    return true
                }
                if (hasVertical) {
                    checkUnsubscribed = checkUnsubscribed && isUnsubscribed
                }
            }

            return checkUnsubscribed
        }
        catch (ex) {
            let errMsg = `CommValidationService::isUnsubscribedFromCommunications failed with error: ${ex} for userId: ${userId}`
            this.logger.error(errMsg)
            return false
        }
    }

}