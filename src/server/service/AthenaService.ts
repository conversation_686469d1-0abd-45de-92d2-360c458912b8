import {inject, injectable} from "inversify"
import {BASE_TYPES, <PERSON><PERSON>} from "@curefit/base"
import {
    AthenaClient,
    ColumnInfo,
    Datum,
    GetQueryExecutionCommand,
    GetQueryExecutionCommandOutput,
    GetQueryResultsCommand,
    GetQueryResultsCommandInput,
    GetQueryResultsCommandOutput,
    GetQueryResultsInput,
    Row,
    StartQueryExecutionCommand,
    StartQueryExecutionCommandInput,
    StartQueryExecutionCommandOutput
} from "@aws-sdk/client-athena"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import Constants from "../constants"

const Queue = require("async/queue")
const _ = require("lodash")


interface AthenaOptions {
    workGroup?: string
    fetchData?: boolean
    queryId?: string
}
@injectable()
export class AthenaService {
    private athenaClient: AthenaClient
    private q: any

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
    ) {
        this.athenaClient = new AthenaClient({
            region: "ap-south-1",
            maxAttempts: 5
        })

        /* Create an async queue to handle polling for query results */
        this.q = Queue((id: string, cb: Function) => {
            this.startPolling(id)
                .then((data) => { return cb(null, data) })
                .catch((err) => {
                    this.logger.error(`Failed to poll query: ${err}`)
                    this.rollbarService.sendError(err)
                    return cb(err)
                })
        }, 5)

    }
    public async executeQuery(sql: string, options?: AthenaOptions): Promise<{data: any, queryId: string}> {
        /* Make a SQL query and display results */
        return this.makeQuery(sql, options)
            .then(({data, queryId}) => {
                this.logger.debug(`rowCount: ${data?.length}`)
                return {data, queryId}
            })
            .catch((err) => {
                this.logger.error(`Failed to poll query: ${err}`)
                this.rollbarService.addErrorMeta({querySql: sql})
                this.rollbarService.sendError(err)
                throw err
            })
    }

    private async makeQuery(sql: string, options?: AthenaOptions) {
        const params = {
            QueryString: sql,
            ResultConfiguration: { OutputLocation: Constants.ATHENA_OUTPUT_S3_LOCATION },
            QueryExecutionContext: { Database: Constants.ATHENA_DB },
            WorkGroup: options?.workGroup ?? "platforms-v2",
        } as StartQueryExecutionCommandInput

        let queryId: string
        if (options?.queryId) {
            queryId = options.queryId
        } else {
            /* Make API call to start the query execution */
            const output: StartQueryExecutionCommandOutput = await this.athenaClient.send(new StartQueryExecutionCommand(params))
            queryId = output.QueryExecutionId
        }
        if (options?.fetchData === false) {
            return {queryId}
        }
        return new Promise(async (resolve, reject) => {
            this.q.push(queryId, (err: any, qid: string) => {
                if (err) return reject(err)
                /* Once query completed executing, get and process results */
                return this.buildResults(qid)
                    .then((data) => { return resolve({data, queryId: queryId}) })
                    .catch((err) => { return reject(err) })
            })
        })
    }

    private async buildResults(query_id: string, max?: number, page?: string) {
        const max_num_results = max ?? Constants.ATHENA_RESULT_SIZE
        const page_token = page
        const that = this
        return new Promise((resolve, reject) => {
            const params = {
                QueryExecutionId: query_id,
                MaxResults: max_num_results,
                NextToken: page_token
            } as GetQueryResultsInput

            let dataBlob: any[] = []
            go(params)

            /* Get results and iterate through all pages */
            function go(params: GetQueryResultsInput) {
                getResults(params)
                    .then((res: any) => {
                        dataBlob = _.concat(dataBlob, res.list)
                        if (res.next) {
                            params.NextToken = res.next
                            return go(params)
                        } else return resolve(dataBlob)
                    }).catch((err: any) => { return reject(err) })
            }

            /* Process results merging column names and values into a JS object */
            function getResults(params: GetQueryResultsCommandInput) {
                return new Promise((resolve, reject) => {
                    that.athenaClient.send(new GetQueryResultsCommand(params), (err: any, data: GetQueryResultsCommandOutput) => {
                        if (err) return reject(err)
                        const list: any[] = []
                        const header = that.buildHeader(data.ResultSet.ResultSetMetadata.ColumnInfo)
                        const top_row = _.map(_.head(data.ResultSet.Rows).Data, (n: Datum) => { return n.VarCharValue })
                        const resultSet = (_.difference(header, top_row).length > 0) ? data.ResultSet.Rows : _.drop(data.ResultSet.Rows)
                        resultSet.forEach((item: Row) => {
                            list.push(_.zipObject(header, _.map(item.Data, (n: Datum) => {return n.VarCharValue })))
                        })
                        return resolve({next: ("NextToken" in data) ? data.NextToken : undefined, list: list})
                    })
                })
            }
        })
    }
    private async startPolling(id: string) {
        const that = this
        return new Promise((resolve, reject) => {
            function poll(id: string) {
                that.athenaClient.send(new GetQueryExecutionCommand({QueryExecutionId: id}), (err: any, data: GetQueryExecutionCommandOutput) => {
                    if (err) {
                        return reject(err)
                    }
                    if (data.QueryExecution.Status.State === "SUCCEEDED") {
                        that.logger.info("queryExecutionCommandOutput", data)
                        return resolve(id)
                    } else if (["FAILED", "CANCELLED"].includes(data.QueryExecution.Status.State)) {
                        that.rollbarService.addErrorMeta(data)
                        that.logger.error(JSON.stringify(data))
                        return reject(new Error(`Query ${data.QueryExecution.Status.State}`))
                    }
                    else { setTimeout(poll, Constants.ATHENA_POLL_INTERVAL, id) }
                })
            }
            poll(id)
        })
    }

    private buildHeader(columns: ColumnInfo[]) {
        return _.map(columns, (i: ColumnInfo) => i.Name)
    }
}
