import {inject, injectable} from "inversify"
import {ICrudKeyValue, IMultiCrudKeyValue, REDIS_TYPES} from "@curefit/redis-utils"
import {CacheAccessImpl, ICacheAccess} from "@curefit/cache-utils"
import {IrisLoadBalancerKey, IrisLoadBalancerValue, ProviderTry} from "../models/IrisLoadBalancerKey"
import {BASE_TYPES, ILogger} from "@curefit/base"
import Constants from "../constants"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

@injectable()
class LoadBalancerService {

    protected loadBalancerCache: ICacheAccess<IrisLoadBalancerKey, any>
    private crudDao: ICrudKeyValue
    constructor
    (
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
    ) {
        this.crudDao = multiCrudKeyValueDao.getICrudKeyValue("REDIS_CACHE")
        this.loadBalancerCache = new CacheAccessImpl<IrisLoadBalancerKey, any>(this.crudDao)
    }

    /**
     * Find next service provider from Load balancer cache
     * Gives preference to priority provider
     * If no valid provider is found,
     * marks all the providers as VALID
     */
    public async getNextProvider(taskId: string, allCountryServiceProviders: string[], priorityProvider?: string): Promise<string> {
        const triedProviders = await this.loadBalancerCache.get(IrisLoadBalancerKey.from(taskId))
        if (!triedProviders) {
            if (priorityProvider && allCountryServiceProviders.includes(priorityProvider)) {
                if (await this.isProviderValid(priorityProvider)) return priorityProvider
            }
        }
        const lbValue = triedProviders || {providersTried: []}
        for (let p of allCountryServiceProviders) {
            if (await this.isProviderValid(p) && this.notPresent(p, lbValue)) return p
        }
        if (!triedProviders) {
            await this.markAllProviderValid(allCountryServiceProviders)
            return priorityProvider || (allCountryServiceProviders.length > 0 ? allCountryServiceProviders[0] : null)
        }
        this.logger.error("No serving provider found for taskId: " + taskId)
        return null
    }

    /**
     * Creates attempt for a notification Id entry in cache
     */
    public async createProviderEntry(taskId: string, provider: string): Promise<any> {
        const lbValue: IrisLoadBalancerValue = await this.loadBalancerCache.get(IrisLoadBalancerKey.from(taskId)) || {providersTried: []}
        lbValue.providersTried.push(new ProviderTry(provider))
        await this.loadBalancerCache.create(IrisLoadBalancerKey.from(taskId), IrisLoadBalancerValue.getValue(lbValue), Constants.RETRY.EXPIRY_TIME_LB_KEY_IN_SEC)
    }

    /**
     * Increase count of consecutive failed attempts
     * for a provider in cache
     */
    public async updateProviderCounter(serviceProvider: string, country: string, success?: boolean) {
        if (success) {
            await this.loadBalancerCache.upsert(IrisLoadBalancerKey.from(serviceProvider, "counter"), 0)
        }
        else {
            let value = await this.loadBalancerCache.get(IrisLoadBalancerKey.from(serviceProvider, "counter")) || 0
            if (Number(value) >= Constants.RETRY.INVALIDATE_PROVIDER_SERVING_STATUS) {
                await this.markProviderInvalid(serviceProvider, country)
                await this.loadBalancerCache.upsert(IrisLoadBalancerKey.from(serviceProvider, "counter"), 0)
            }
            else {
                await this.loadBalancerCache.incrementBy(IrisLoadBalancerKey.from(serviceProvider, "counter"), 1)
            }
        }
    }

    /**
     * Marks a Provider as INVALID in cache
     * Does not mark the provider INVALID if
     * all other provider are also marked INVALID
     */
    private async markProviderInvalid(serviceProvider: string, country: string) {
        this.rollbarService.sendError(new Error(`${Constants.RETRY.INVALIDATE_PROVIDER_SERVING_STATUS} consecutive attempts failed with provider ${serviceProvider}`))

        // check if some provider is still valid
        const validServiceProviders = Constants.getValidServiceProviders(country)
        let ifAllServiceProvidersInvalid = true
        for (let provider of validServiceProviders) {
            if (provider !== serviceProvider) {
                if (await this.isProviderValid(provider)) {
                    ifAllServiceProvidersInvalid = false
                    break
                }
            }
        }
        if (ifAllServiceProvidersInvalid) {
            await this.markAllProviderValid(validServiceProviders)
        }
        else {
            await this.loadBalancerCache.create(IrisLoadBalancerKey.from(serviceProvider, "provider_status"), 0, Constants.RETRY.EXPIRY_TIME_PROVIDER_SERVING_STATUS_IN_SEC)
        }
    }

    /**
     * Checks if a provider is already tried
     * Takes list of all tried providers as a parameter
     */
    private notPresent(provider: string, lbValue: IrisLoadBalancerValue): boolean {
        if (!lbValue) return true
        for (let p of lbValue.providersTried) {
            if (p.provider === provider) {
                return false
            }
        }
        return true
    }

    /**
     * Marks all service providers as VALID in cache
     */
    private async markAllProviderValid(allCountryServiceProviders: string[]) {
        for (let provider of allCountryServiceProviders) {
            await this.loadBalancerCache.upsert(IrisLoadBalancerKey.from(provider, "provider_status"), 1)
        }
    }

    /**
     * Checks if a provider is VALID in cache
     * INVALID if provider_status = 0
     */
    private async isProviderValid(provider: string): Promise<boolean> {
        const servingStatusOfProvider = await this.loadBalancerCache.get(IrisLoadBalancerKey.from(provider, "provider_status"))
        if (servingStatusOfProvider === 0) return false
        return true
    }
}

export default LoadBalancerService