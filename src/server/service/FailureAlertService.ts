import {BASE_TYPES, ILogger} from "@curefit/base"

import {inject, injectable} from "inversify"
import * as _ from "lodash"

import TYPES from "../ioc/types"
import {MetricUtils} from "../utils/MetricUtils"
import {CacheAccessImpl, ICacheAccess, Key} from "@curefit/cache-utils"
import {IMultiCrudKeyValue, REDIS_TYPES} from "@curefit/redis-utils"
import {ICommunicationCampaignReadonlyDao, IRIS_MODELS_TYPES} from "../../iris-models"
import {SendCampaignNotificationsRequest, UserTags} from "../../iris-common"
import CampaignHelper from "../helpers/CampaignHelper"
import FeatureUtils from "../utils/FeatureUtils"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

class CampaignFailureAlertKey implements Key {

    key: string

    constructor(campaignId: string, creativeId: string) {
        this.key = `iris_failure_alert___${campaignId}_${creativeId}`
    }

    hash(): string {
        return undefined
    }

    lookupKey(): string {
        return this.key
    }
}

@injectable()
class FailureAlertService {

    private campaignFailureAlertCache: ICacheAccess<CampaignFailureAlertKey, boolean>
    private readonly defaultCampaignAlertCoolOffPeriodInSeconds: number = 3600
    private readonly defaultMinProcessCount: number = 50
    private readonly defaultFailurePercentThreshold: number = 30

    private readonly campaignFailureAlertCampaignId: string = "CUREFIT_IRIS_FAILURE_ALERTS"
    private readonly campaignFailureAlertEmailCreativeId: string = "EMAIL_IRIS_CAMPAIGN_FAILURE_ALERT"

    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(TYPES.FeatureUtils) private featureUtils: FeatureUtils,
        @inject(TYPES.CampaignHelper) private campaignHelper: CampaignHelper,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
        @inject(IRIS_MODELS_TYPES.CommunicationCampaignReadonlyDao) private communicationCampaignDao: ICommunicationCampaignReadonlyDao,
    ) {
        this.campaignFailureAlertCache = new CacheAccessImpl<CampaignFailureAlertKey, boolean>
            (multiCrudKeyValueDao.getICrudKeyValue("BUMBLE"))
    }

    public async assessCampaignHealth(timeRange: string): Promise<any> {
        /*
            timeRange: time range to check metrics in
            failurePercentThreshold: threshold at which alert is triggered
            minProcessCount: number of processed notifications below which alert is not triggered
         */

        const minProcessCount = _.get(this.featureUtils.getFailureAlertConfig(), "minProcessCount",
            this.defaultMinProcessCount)
        const failurePercentThreshold = _.get(this.featureUtils.getFailureAlertConfig(), "failurePercentThreshold",
                this.defaultFailurePercentThreshold)

        const failureCountForEachCampaign = await this.metricUtils.getFailureCountForEachCampaignAndCreative(timeRange)
        const processCountForEachCampaign = await this.metricUtils.getProcessCountForEachCampaignAndCreative(timeRange)

        for (const key of processCountForEachCampaign.keys()) {
            const processCount = processCountForEachCampaign.get(key)
            const failureCount = failureCountForEachCampaign.get(key) || 0

            if ((processCount >= minProcessCount) &&
                (failureCount / processCount * 100 > failurePercentThreshold)) {
                const [ campaignId, creativeId ] = key.split("$$")
                await this.triggerAlert(campaignId, creativeId, processCount, failureCount)
            }
        }
    }

    public async triggerAlert(campaignId: string, creativeId: string, processCount: number,
                              failureCount: number): Promise<any> {
        const alertKey = new CampaignFailureAlertKey(campaignId, creativeId)
        const alertSent = await this.campaignFailureAlertCache.get(alertKey)
        if (alertSent) {
            // Alert already sent recently; don't do anything
            return
        }

        const campaign = await this.communicationCampaignDao.findOne({ campaignId })
        if (campaign.type === "PROMO") {
            this.logger.info(`FailureAlertService::triggerAlert Skipping for promo campaign ${campaignId}`)
            return
        }

        const failureAlertConfig = _.get(this.featureUtils.getFailureAlertConfig(), campaign.vertical, null)
        if (!failureAlertConfig) {
            const errorMessage = `FailureAlertService::triggerAlert No config found for vertical ${campaign.vertical}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            return
        }

        const pocEmails = _.get(failureAlertConfig, "pocEmails", [])
        if (pocEmails.length === 0) {
            const errorMessage = `FailureAlertService::triggerAlert No poc emails found for vertical ${campaign.vertical}`
            this.logger.error(errorMessage)
            return
        }

        const userContexts: UserTags[] = pocEmails.map((pocEmail: string) => {
            return { emailId: pocEmail }
        })

        this.logger.info(`FailureAlertService::triggerAlert Sending failure alert for ${campaignId} ${creativeId}` +
        `to: ${JSON.stringify(userContexts)} failureCount: ${failureCount} processCount: ${processCount}`)

        const sendCampaignNotificationsRequest: SendCampaignNotificationsRequest = {
            userContexts,
            campaignId: this.campaignFailureAlertCampaignId,
            creativeIds: [this.campaignFailureAlertEmailCreativeId],
            globalTags: {
                processCount: processCount.toString(),
                failureCount: failureCount.toString(),
                campaignId,
                creativeId,
                failureRate: (Math.round(failureCount / processCount * 100)).toString()
            }
        }

        await this.campaignHelper.sendCampaignMessages(sendCampaignNotificationsRequest)
        const campaignAlertCoolOffPeriodInSeconds = _.get(failureAlertConfig, "coolOffPeriodInSeconds",
            this.defaultCampaignAlertCoolOffPeriodInSeconds)
        await this.campaignFailureAlertCache.create(alertKey, true, campaignAlertCoolOffPeriodInSeconds)
    }
}

export default FailureAlertService