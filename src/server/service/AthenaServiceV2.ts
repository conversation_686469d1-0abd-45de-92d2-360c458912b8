import {inject, injectable} from "inversify"
import {BASE_TYPES, Logger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {
    ATHENA_SERVICE_CLIENT_TYPES, AthenaQueryOutput, AthenaTaskEntry,
    IAthenaService,
    QueryClient,
    RequestType, TaskResponse,
    TrinoNamespace
} from "@curefit/athena-service-client"
import {AthenaTaskRequest} from "@curefit/athena-service-client/dist/models/AthenaModels"

@injectable()
export class AthenaServiceV2 {
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(ATHENA_SERVICE_CLIENT_TYPES.AthenaService) private athenaService: IAthenaService
    ) {}

    public async executeQueryOnTrino(query: string, refId: string, mozartJobConfigId: string, requestType: RequestType): Promise<AthenaTaskEntry> {

        this.athenaService.setQueryClient(QueryClient.TRINO)
        let metaInfo: {
            [key: string]: any;
        } = {}

        const taskRequest: AthenaTaskRequest = {
            source: "IRIS",
            query: query,
            refId: refId,
            athenaWorkGroup: "platforms-v2",
            metaInfo: metaInfo,
            requestType: requestType,
            mozartJobConfigId: mozartJobConfigId,  // make it configurable basis env
            trinoNamespace: TrinoNamespace.PLATFORMS
        }
        return await this.athenaService.createTask(taskRequest)
    }

}