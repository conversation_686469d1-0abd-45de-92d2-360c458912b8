import {BASE_TYPES, ILogger} from "@curefit/base"

import {inject, injectable} from "inversify"
import {
    IJob,
    ISubmitJobPayload,
    MOZART_CLIENT_TYPES,
    MozartService as MozartServiceClient
} from "@curefit/mozart-client/dist"
import Constants from "../constants"

const fetch = require("node-fetch")

@injectable()
class MozartService {

    public static MOZART_JOB_CONFIG_ID_SEND_USER_TO_QUEUE = "iris_send_user"
    public static MOZART_JOB_CONFIG_ID_SEND_USER_TO_QUEUE_TRANSIENT_SEGMENT = "iris_send_user_transient"
    public static MOZART_JOB_CONFIG_ID_DOWNLOAD_SEGMENT = "segment_download_user"
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(MOZART_CLIENT_TYPES.MozartService) private mozartServiceClient: MozartServiceClient
    ) {
    }

    public async startJob(jobConfigId: string, inputFile: string, metaData: any, userId: string): Promise<IJob> {
        let job: ISubmitJobPayload = {
            jobConfigId,
            inputFile,
            metaData,
            createdBy: userId,
            source: Constants.IRIS,
            payload: []
        }
        return this.mozartServiceClient.submitJob(job)
    }

    public async getJob(id: string): Promise<IJob> {
        return this.mozartServiceClient.getJobById(+id)
    }
}

export default MozartService