import {inject, injectable} from "inversify"
import TYPES from "../ioc/types"
import {ICommunicationCampaignReadonlyDao, ICreativeReadonlyDao, IRIS_MODELS_TYPES} from "../../iris-models"
import {BaseCreative, ClickToCallCreative, Notification} from "../../iris-common"
import CampaignHelper from "../helpers/CampaignHelper"
import Constants from "../constants"
import {KnowlarityHelper} from "../helpers/KnowlarityHelper"
import {SolutionsInfiniHelper} from "../helpers/SolutionsInfiniHelper"
import {BASE_TYPES, ILogger} from "@curefit/base"

@injectable()
class RetryService {
    constructor
    (
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(TYPES.CampaignHelper) private campaignHelper: CampaignHelper,
        @inject(TYPES.KnowlarityHelper) private knowlarityHelper: KnowlarityHelper,
        @inject(TYPES.SolutionsInfiniHelper) private solutionsInfiniHelper: SolutionsInfiniHelper,
        @inject(IRIS_MODELS_TYPES.CommunicationCampaignReadonlyDao) private communicationCampaignDao: ICommunicationCampaignReadonlyDao,
        @inject(IRIS_MODELS_TYPES.CreativeReadonlyDao) private creativeDao: ICreativeReadonlyDao,

    ) {
    }

    public async retryOneExistingNotification(notification: Notification, serviceProvider: string, providerExternalId: string) {
        const campaign = await this.communicationCampaignDao.findOne({ campaignId: notification.campaignId })
        const creative = await this.creativeDao.findOne({ creativeId: notification.creativeId })
        if (await this.isNotificationSentSuccess(notification, creative, serviceProvider, providerExternalId) !== true) {
            await this.campaignHelper.sendExistingNotifications([notification], campaign, creative)
        }
    }

    private async isNotificationSentSuccess(notification: Notification, creative: BaseCreative, serviceProvider: string, providerExternalId: string): Promise<boolean> {
        switch (creative.type) {
            case "PUSH_NOTIFICATION":
                break
            case "EMAIL":
                break
            case "SMS":
                break
            case "OBD":
                break
            case "IN_APP_NOTIFICATION":
                break
            case "CLICK_TO_CALL":
                return await this.isClickToCallSuccess(notification, creative, serviceProvider, providerExternalId)
                break
            default:
                this.logger.error(creative.type + " : No such creative type exist")
        }
    }

    private async isClickToCallSuccess(notification: Notification, creative: ClickToCallCreative, serviceProvider: string, providerExternalId: string): Promise<boolean> {
        switch (serviceProvider) {
            case Constants.SOLUTIONS_INFINI_SMS_CONFIG.NAME:
                return await this.solutionsInfiniHelper.isClickToCallSuccess(providerExternalId, creative)
                break
            case Constants.KNOWLARITY_CLICK_TO_CALL_CONFIG.NAME:
                return true
                break
            default:
                return true
        }
    }

}

export default RetryService