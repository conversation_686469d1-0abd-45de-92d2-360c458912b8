import { BASE_TYPES, Configuration, FetchUtilV2, ILogger } from "@curefit/base"
import * as _ from "lodash"
import { inject, injectable } from "inversify"
import { NotificationSegmentTask } from "@curefit/iris-common"
import Constants from "../constants"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
export class CampaignManagerService {

    private backendConfig: any
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(BASE_TYPES.FetchUtilV2) private fetchUtilV2: FetchUtilV2,
        @inject(BASE_TYPES.Configuration) private appConfig: Configuration,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
    ) {
        this.backendConfig = _.get(this.appConfig.getConfiguration(), "campaignManagerConfig")
    }

    /* Update campaign status on segment download failure */
    public async campaignStatusUpdateOnSegmentDownloadFailure(errMsg: string, notificationSegmentTask: NotificationSegmentTask) {
        try {
            const {campaignId , status, segmentId, userId, taskId} = notificationSegmentTask
            this.logger.info(`CampaignManagerService:: Campaign status update for segment download failed called for irisCampaignId : ${campaignId} , notificationSegmentTaskId : ${taskId}`)
            const requestBody = {
                irisCampaignId: campaignId ,
                errMsg,
                metadata: {
                    status,
                    segmentId, 
                    userId, 
                    taskId
                }
            }
            const response = await fetch(
                this.backendConfig.baseUrl + Constants.CAMPAIGN_MANAGER_SEGMENT_DOWNLOAD_FAILURE,
                this.fetchUtilV2.post({body: requestBody as any})
            )
            if (!response.ok) {
                let errMsg: string = "CampaignManagerService:: Campaign status update on segment download failed"
                this.logger.error(errMsg)
                this.rollbarService.sendError(new Error(errMsg))
            }
        }
        catch (err) {
            let errMsg: string = `CampaignManagerService:: Sending campaign status update request on Segment Download failure failed with error: ${err}`
            this.logger.error(errMsg)
            this.rollbarService.sendError(new Error(errMsg))
        }
    }

}