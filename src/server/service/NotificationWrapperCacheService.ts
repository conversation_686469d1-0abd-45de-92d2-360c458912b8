import {inject, injectable} from "inversify"
import {BASE_TYPES, ILogger} from "@curefit/base"

import {CacheAccessImpl, ICacheAccess, Key} from "@curefit/cache-utils"
import {NotificationWrapper} from "../../iris-common"
import {IMultiCrudKeyValue, MultiRedisAccess, REDIS_TYPES} from "@curefit/redis-utils"

export class NotificationWrapperCacheKey implements Key {

    key: string

    constructor(notificationId: string) {
        this.key = `iris_nwc_${notificationId}`
    }

    hash(): string {
        return undefined
    }

    lookupKey(): string {
        return this.key
    }

}

const NOTIFICATION_WRAPPER_CACHE_TTL_IN_SECONDS = 60 * 60 // 60 minutes

@injectable()
class NotificationWrapperCacheService {

    private notificationWrapperCache: ICacheAccess<NotificationWrapperCacheKey, NotificationWrapper>

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,

    ) {
        this.notificationWrapperCache = new CacheAccessImpl<NotificationWrapperCacheKey, NotificationWrapper>
            (multiCrudKeyValueDao.getICrudKeyValue("NOTIFICATION_WRAPPER_CACHE"))

    }


    public async cacheNotification(notificationWrapper: NotificationWrapper): Promise<boolean> {
        return this.notificationWrapperCache.create(
            new NotificationWrapperCacheKey(notificationWrapper.notification.notificationId),
            notificationWrapper,
            NOTIFICATION_WRAPPER_CACHE_TTL_IN_SECONDS
        )
    }

    public async getCachedNotification(notificationId: string): Promise<NotificationWrapper> {
        return this.notificationWrapperCache.get(new NotificationWrapperCacheKey(notificationId))
    }

}

export default NotificationWrapperCacheService