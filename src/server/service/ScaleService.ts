import {BASE_TYPES, ILogger} from "@curefit/base"
import {CacheAccessImpl, ICacheAccess} from "@curefit/cache-utils"
import {IMultiCrudKeyValue, REDIS_TYPES} from "@curefit/redis-utils"
import {ILock, ILockAccess, LOCK_TYPES} from "@curefit/lock-utils"
import {ScaleResourceTypeEnum} from "@curefit/voyager-common"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

import * as _ from "lodash"
import {inject, injectable} from "inversify"

import {ScaleHistoryKey} from "../models/ScaleHistoryKey"
import {ICreativeReadWriteDao, IRIS_MODELS_TYPES} from "../../iris-models"
import {IScaleService, VOYAGER_CLIENT_TYPES} from "@curefit/voyager-client"
import TYPES from "../ioc/types"
import FeatureUtils from "../utils/FeatureUtils"
import {ScaleApplicationParams} from "@curefit/voyager-common"
import {CommonUtils} from "../utils/CommonUtils"


@injectable()
class ScaleService {

    /*
        Most promotional traffic in iris comes via send to segment flow.
        Unfortunately iris takes some time to warm up before it can process at full speed.

        To ensure time sensitive communication does not get delayed, we preemptively scale out the server and workers

        The scaling strategy itself is naive. Simply scale the service as soon as a new segment job is started
        For the next SCALE_OUT_BUFFER_PERIOD_IN_SECONDS, we shall not attempt to scale in the service

        We take a lock within scale in and scale out operations to avoid race which might arise if a new segment
        job request comes in right around SCALE_OUT_BUFFER_PERIOD_IN_SECONDS
     */

    // SCALE_OUT_BUFFER_PERIOD_IN_SECONDS is the buffer time after a job is started during which we do not scale in
    private static SCALE_OUT_BUFFER_PERIOD_IN_SECONDS = 15 * 60

    private readonly scaleHistoryCache: ICacheAccess<ScaleHistoryKey, boolean>
    private readonly channelsEnabledForScaling = ["SERVER", "EMAIL", "PUSH_NOTIFICATION", "SMS", "WHATSAPP"]

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(LOCK_TYPES.LockAccess) private lockAccess: ILockAccess,
        @inject(TYPES.FeatureUtils) private featureUtils: FeatureUtils,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(VOYAGER_CLIENT_TYPES.IScaleService) private voyagerScaleService: IScaleService,
        @inject(IRIS_MODELS_TYPES.CreativeReadWriteDao) private creativeReadWriteDao: ICreativeReadWriteDao,
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
    ) {

        this.scaleHistoryCache = new CacheAccessImpl<ScaleHistoryKey, boolean>
            (multiCrudKeyValueDao.getICrudKeyValue("BUMBLE"))
    }

    private async getChannels(creativeIds: string[]): Promise<string[]> {
        const creatives = await this.creativeReadWriteDao.find(
            { condition: { creativeId: { $in: creativeIds }}})
        return _.uniq(creatives.map(creative => { return creative.type }))
    }

    /*
        We call this whenever a send to segment job is triggered
        Scaling out just increases min replicas.
        Multiple invocations are not a problem as the HPA will be patched with the same values
     */
    public async scaleOut(creativeIds: string[]): Promise<any> {
        const scalingLock = await this.getScalingLock()
        try {
            this.logger.info(`ScaleService::scaleOut Scaling SERVER`)
            await this.scaleDeployment("SERVER", true)
            await this.scaleHistoryCache.create(
                new ScaleHistoryKey("SERVER"), true, ScaleService.SCALE_OUT_BUFFER_PERIOD_IN_SECONDS)

            const channels = await this.getChannels(creativeIds)
            for (const channel of channels) {
                await this.scaleOutChannel(channel)
            }
        } catch (err) {
            const errorMessage = `ScaleService::scaleOut error ` +
                `while scaling. Error: ${err.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
        } finally {
            await this.lockAccess.unlockResource(scalingLock)
        }

    }

    public async scaleOutChannel(channel: string): Promise<any> {
        this.logger.info(`ScaleService::scaleChannel Scaling CHANNEL ${channel}`)
        await this.scaleDeployment(channel, true)
        await this.scaleHistoryCache.create(
            new ScaleHistoryKey(channel), true, ScaleService.SCALE_OUT_BUFFER_PERIOD_IN_SECONDS)
    }

    /*
        We call this regularly via a cron (every 10 minutes)
        Scaling in just decreases min replicas.
        We ensure that this is only called SCALE_OUT_BUFFER_PERIOD_IN_SECONDS after a segment job is initiated
     */
    public async scaleIn(): Promise<any> {
        const scalingLock = await this.getScalingLock()
        try {
            // For each channel which we scale out
            for (const channel of this.channelsEnabledForScaling) {

                // Check if it was scaled within SCALE_OUT_BUFFER_PERIOD_IN_SECONDS
                const recentlyScaledOut = await this.scaleHistoryCache.get(new ScaleHistoryKey(channel))

                // If not, then reduce the min replicas
                if (!recentlyScaledOut) {
                    await this.scaleDeployment(channel, false)
                }
            }
        } catch (err) {
            const errorMessage = `ScaleService::scaleIn error while scaling. ` +
                `Error: ${err.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
        } finally {
            await this.lockAccess.unlockResource(scalingLock)
        }
    }

    /*
        Methods to scale in/out server, workers.
        Picks up config from serviceConfigs collection

        - Selects the appropriate deployment to scale based on channel
        - scaleOut = true  => Scale out
        - scaleOut = false => Scale in
     */
    private async scaleDeployment(channel: string, scaleOut: boolean): Promise<any> {
        this.logger.info(`ScaleService::scaleDeployment Scaling channel ${channel} scaleOut: ${scaleOut}`)

        if (!CommonUtils.isEnvironmentProduction()) {
            this.logger.info(`ScaleService::scaleDeployment env not production; not scaling`)
            return
        }

        if (this.channelsEnabledForScaling.indexOf(channel) === -1) {
            const errorMessage = `ScaleService::incrementActiveSegmentJobCount No preemptive scaling rules defined ` +
                `for channel ${channel}`
            this.logger.info(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            return
        }

        const config: {[key: string]: ScaleApplicationParams} = (scaleOut ?
            this.featureUtils.getScaleOutConfig() : this.featureUtils.getScaleInConfig())
        const scaleApplicationParams: ScaleApplicationParams = config[channel]

        this.logger.info(`ScaleService::scaleDeployment scaleApplicationParams: ` +
            `${JSON.stringify(scaleApplicationParams)}`)

        return this.voyagerScaleService.scaleApplication({
            resourceType: ScaleResourceTypeEnum.APPLICATION,
            scaleApplicationParams
        })
    }

    /*
        Creates a lock for the scaling operation
     */
    private async getScalingLock(): Promise<ILock> {
        return this.lockAccess.lockResourceV2(
            `iris_scale_deployment`,
            30 * 1000
        )
    }

    public async getScalingHistoryKeys(): Promise<any> {
        const scalingHistoryKeys: any = {}

        for (const channel of this.channelsEnabledForScaling) {
            scalingHistoryKeys[channel] = await this.scaleHistoryCache.get(new ScaleHistoryKey(channel))
        }
        return scalingHistoryKeys
    }

    public async removeScalingHistoryKeys(channel?: string): Promise<any> {
        const targetChannels = channel ? [ channel ] : this.channelsEnabledForScaling
        for (const channel of targetChannels) {
            this.logger.info(`ScaleService::scaleDeployment removing scaling history key for channel ${channel}`)
            await this.scaleHistoryCache.delete(new ScaleHistoryKey(channel))
        }
        return true
    }

}

export default ScaleService