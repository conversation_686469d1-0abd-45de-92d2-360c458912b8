import {inject, injectable} from "inversify"
import {BASE_TYPES, Configuration, FetchUtil, FetchUtilV2, Lo<PERSON>} from "@curefit/base"
import {
    AdFormat,
    AdsPartners,
    BranchDeeplinkAnalyticsRequest, BranchDeeplinkAnalyticsResponse,
    BranchLink, GetAnalyticsResponse,
    LinkPayload,
    LinkTypes
} from "../../iris-common"
import {Tenant} from "@curefit/user-common"
import Constants from "../constants"
import LinkHelper from "../helpers/LinkHelper"
import { NotFoundError, ValidationError } from "@curefit/error-client"
import * as moment from "moment"
import * as _ from "lodash"
import {PurchaseEvent} from "@curefit/iris-common"

const fetch = require("node-fetch")

export interface IBranchIOService {
    createUniversalDeepLinkUrl(tenant: Tenant, payload: LinkPayload): Promise<{ url: string }>
    editUniversalDeepLinkUrl(tenant: Tenant, payload: LinkPayload): Promise<{ url: string }>
    getAnalytics(tenant: Tenant, start_date: string, end_date: string, creativeIdToUtmIdMap: [string, string][]): Promise<GetAnalyticsResponse>
    sendPurchaseEvent(userId: string, purchaseEventData: any): Promise<any>
}

export interface BranchioPayload {
    branch_key: string
    branch_secret?: string
    type?: number
    channel?: string
    feature?: string
    campaign?: string
    data: {
        $desktop_url: string,
        $android_url: string,
        $ios_url: string,
        $deeplink_path: string
        $canonical_url?: string
        $marketing_title?: string
        $og_title?: string
        $og_description?: string
        $og_image_url?: string
        $og_type?: string
        "~feature"?: string
        "~campaign"?: string
        "~channel"?: string
        "~campaign_id": string
        "~customer_campaign"?: string
        "~stage"?: string
        "~tags"?: string[]
        "~secondary_publisher"?: string
        "~customer_secondary_publisher"?: string
        "~creative_name"?: string
        "~creative_id"?: string
        "~ad_set_name"?: string
        "~customer_ad_set_name"?: string
        "~ad_name"?: string
        "~ad_id"?: string
        "~customer_ad_name"?: string
        "~keyword"?: string
        "~customer_keyword"?: string
        "~placement"?: string
        "~placement_id"?: string
        "~customer_placement"?: string
        "~sub_site_name"?: string
        "~customer_sub_site_name"?: string
        $android_deepview: string
        $ios_deepview: string
        $desktop_deepview: string
        $ios_passive_deepview: string
        $android_passive_deepview: string
    }
};

@injectable()
class BranchIOService implements IBranchIOService {
    private cfAPIConfig: any
    private branchBackendConfig: any
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
        @inject(BASE_TYPES.Configuration) private appConfig: Configuration) {
        this.cfAPIConfig = _.get(this.appConfig.getConfiguration(), "cfAPIConfig")
        this.branchBackendConfig = _.get(this.appConfig.getConfiguration(), "branchBackendConfig")
    }

    updateLink(applink: LinkPayload, deeplink: string) {
        if (!deeplink) return
        let link = new URL(deeplink)
        if ( applink.linkCategory === LinkTypes.ADS && applink.adFormat) {
            const adPartnerQueryParams: any = AdsPartners[applink.adPartner].params
            switch (applink.adFormat) {
                case AdFormat.APP_INSTALL:
                    Object.keys(adPartnerQueryParams.app).forEach((key) => {
                        let value = adPartnerQueryParams.app[key]
                        if (key === "$3p") {
                            value = `a_${value}`
                        }
                        link.searchParams.set(key, value)
                    })
                    break
                case AdFormat.CUSTOM_LINK:
                case AdFormat.DISPLAY_LINK:
                    Object.keys(adPartnerQueryParams.disaply).forEach((key) => {
                        let value = adPartnerQueryParams.disaply[key]
                        if (key === "$3p") {
                            value = `a_${value}`
                        }
                        link.searchParams.set(key, value)
                    })
                    break
            }
        }
        return link.href
    }

    getChannel(link: BranchLink) {
        const {
            media_source = ""
        } = link || {}
        let channel = media_source
        // Returning channel same as ads partner in case the type of link is dispay or search|custom
        if (link.linkCategory === LinkTypes.ADS && (link.adFormat === AdFormat.CUSTOM_LINK || link.adFormat === AdFormat.DISPLAY_LINK)) {
            channel = `a_${link.adPartner}`
        }
        return channel
    }

    showRedirectMWeb(link: BranchLink) {
        const {
            redirectMWeb = false,
        } = link || {}
        let redirect = redirectMWeb
        // always redirect to m-web incase type of link is dispay or search|custom
        if (link.linkCategory === LinkTypes.ADS && (link.adFormat === AdFormat.CUSTOM_LINK || link.adFormat === AdFormat.DISPLAY_LINK)) {
            redirect = true
        }
        return redirect
    }

    getPayload(tenant: Tenant, link: BranchLink, edit?: Boolean): BranchioPayload {

        const { apiKey, branchSecret } = Constants.BranchConfigMap[tenant]
        const deepLink = LinkHelper.createBranchLink(link)

        const {
            analyticsInfo = {},
            linkRedirection = {},
            socialMetaTagInfo = {},
            weblink = null
        } = link || {}

        const webUrl = weblink ? LinkHelper.getWeblink(link) : ""
        const channel = this.getChannel(link)
        const redirectMWeb = this.showRedirectMWeb(link)

        const payload: BranchioPayload = {
            branch_key: apiKey,
            campaign: link.campaign_name,
            type: analyticsInfo.type,
            channel: channel,
            feature: link.medium,
            data: {
                $desktop_url: webUrl,
                $android_url: redirectMWeb ? webUrl : link.androidUrl,
                $ios_url: redirectMWeb ? webUrl : link.iosUrl,
                $deeplink_path: deepLink,
                $canonical_url: analyticsInfo.canonicalUrl,
                $marketing_title: analyticsInfo.marketingTitle,
                $og_title: socialMetaTagInfo?.socialTitle,
                $og_description: socialMetaTagInfo?.socialDescription,
                $og_image_url: socialMetaTagInfo?.socialImageLink,
                $og_type: socialMetaTagInfo?.socialType,
                "~feature": link.medium,
                "~campaign": link.campaign_name,
                "~channel": channel,
                "~campaign_id": analyticsInfo.campaign_id,
                "~customer_campaign": analyticsInfo.customer_campaign,
                "~stage": analyticsInfo.stage,
                "~tags": analyticsInfo.tags,
                "~secondary_publisher": analyticsInfo.secondary_publisher,
                "~customer_secondary_publisher": analyticsInfo.customer_secondary_publisher,
                "~creative_name": analyticsInfo.creative_name,
                "~creative_id": analyticsInfo.creative_id,
                "~ad_set_name": analyticsInfo.ad_set_name,
                "~customer_ad_set_name": analyticsInfo.customer_ad_set_name,
                "~ad_name": analyticsInfo.ad_name,
                "~ad_id": analyticsInfo.ad_id,
                "~customer_ad_name": analyticsInfo.customer_ad_name,
                "~keyword": analyticsInfo.keyword,
                "~customer_keyword": analyticsInfo.customer_keyword,
                "~placement": analyticsInfo.placement,
                "~placement_id": analyticsInfo.placement_id,
                "~customer_placement": analyticsInfo.customer_placement,
                "~sub_site_name": analyticsInfo.sub_site_name,
                "~customer_sub_site_name": analyticsInfo.customer_sub_site_name,
                $android_deepview: linkRedirection.android_deepview,
                $ios_deepview: linkRedirection.ios_deepview,
                $desktop_deepview: linkRedirection.desktop_deepview,
                $ios_passive_deepview: linkRedirection.ios_passive_deepview,
                $android_passive_deepview: linkRedirection.android_passive_deepview,
            }
        }

        if (edit) {
            payload.branch_secret = branchSecret
        }

        return payload

    }

    editUniversalDeepLinkUrl(tenant: Tenant, link: BranchLink): Promise<{
        url: string;
    }> {
        if (!(tenant in Constants.BranchConfigMap)) {
            throw new NotFoundError(`${tenant} is not registered as a valid tenant for deep links`)
        }
        const { url } = Constants.BranchConfigMap[tenant]
        const payload: BranchioPayload = this.getPayload(tenant, link, true)

        const endPoint = `${url}/url?url=${escape(LinkHelper.removeQueryParams(link.link))}`
        this.logger.info("request of branch-io edit" + endPoint)
        return fetch(endPoint, this.fetchHelper.put(payload)).then((response: any) => {
            return this.fetchHelper.parseResponse<any>(response).then((res) => {
                this.logger.info("response of branch-io edit " + JSON.stringify(res))
                return { url: this.updateLink(link, LinkHelper.removeQueryParams(link.link)) }
            }) 
        })
    }

    createUniversalDeepLinkUrl(tenant: Tenant, link: BranchLink): Promise<{
        url: string;
    }> {
        if (!(tenant in Constants.BranchConfigMap)) {
            throw new NotFoundError(`${tenant} is not registered as a valid tenant for deep links`)
        }
        const { url } = Constants.BranchConfigMap[tenant]
        const payload: BranchioPayload = this.getPayload(tenant, link)

        this.logger.info("request of branchIo " + JSON.stringify(payload))
        return fetch(`${url}/url`, this.fetchHelper.post(payload)).then((response: any) => {
            return this.fetchHelper.parseResponse<any>(response).then((res) => {
                this.logger.info("response of branchIo2 " + JSON.stringify(res))
                return { url: this.updateLink(link, res.url) }
            })
        })
    }

    async getAnalytics(tenant: Tenant, start_date: string, end_date: string, creativeIdToUtmIdMap: [string, string][]): Promise<GetAnalyticsResponse> {

        if (!(tenant in Constants.BranchConfigMap)) {
            throw new NotFoundError(`${tenant} is not registered as a valid tenant for deep links`)
        }
        if (!moment(start_date, "YYYY-MM-DD", true).isValid() || !moment(start_date, "YYYY-MM-DD", true).isValid()) {
            throw new ValidationError(new Error(`Dates should be in YYYY-MM-DD format`))
        }
        if (moment(end_date).diff(moment(start_date), "days") >= 7) {
            throw new ValidationError(new Error(`Difference between start_date and end_date should be less than 7 days `))
        }

        const utmIds: string[] = []
        for (let pair of creativeIdToUtmIdMap) {
            utmIds.push(pair[1])
        }

        const {url, apiKey, branchSecret} = Constants.BranchConfigMap[tenant]
        const payload: BranchDeeplinkAnalyticsRequest = {
            aggregation: "unique_count",
            branch_key: apiKey,
            branch_secret: branchSecret,
            data_source: "eo_click",
            dimensions: ["last_attributed_touch_data_tilde_feature", "last_attributed_touch_data_tilde_channel", "last_attributed_touch_data_tilde_campaign", "name"],
            end_date: end_date,
            filters: {"last_attributed_touch_data_tilde_campaign": utmIds},
            start_date: start_date,
            zero_fill: true
        }

        this.logger.info(`BranchIOService::getAnalytics request payload ${JSON.stringify(payload)}`)
        const response = await fetch(`${url}/query/analytics`, this.fetchHelper.post(payload)).then((response: any) => {
            return this.fetchHelper.parseResponse<BranchDeeplinkAnalyticsResponse>(response) })
        this.logger.info("BranchIOService::getAnalytics response of branchIo2 " + JSON.stringify(response))

        let responseMap: Map<string, number> = new Map<string, number>()
        for (let result of response.results) {
            responseMap.set(result.result.last_attributed_touch_data_tilde_campaign, result.result.unique_count)
        }
        this.logger.info(`BranchIOService::getAnalytics responseMap ${JSON.stringify(responseMap)}`)
        let analyticsResponse: GetAnalyticsResponse = {response: []}
        for (let pair of creativeIdToUtmIdMap) {
            analyticsResponse.response.push([pair[0], pair[1], responseMap.get(pair[1])])
        }
        this.logger.info(`BranchIOService::getAnalytics analyticsResponse ${JSON.stringify(responseMap)}`)
        return analyticsResponse
    }

    async sendPurchaseEvent(userId: string, purchaseEventData: PurchaseEvent) {
        const device = await this.getDevice(userId);
        const branchPayload = await this.getPurchaseEventPayload(device, userId, purchaseEventData, this.branchBackendConfig)
        this.logger.info(`BranchIOService::sendPurchaseEvent branchPayload ${JSON.stringify(branchPayload)}`)
        return await fetch(this.branchBackendConfig.url, this.fetchHelper.post(branchPayload, this.branchBackendConfig.headers))
    }

    async getDevice(userId: string) {
        const url = `${this.cfAPIConfig.baseUrl}/device/recent/app/loggedIn?userId=${userId}`
        const response = await fetch(url, this.fetchHelper.get(this.cfAPIConfig.headers))
        const value = await this.fetchHelper.parseResponse<any>(response)
        const device = value.data
        this.logger.info(`Device data for userId - ${userId} -> ${device}`)
        return device
    }

    async getPurchaseEventPayload(device: any, userId: string, purchaseEventData: PurchaseEvent, branchBackendConfig: any): Promise<any> {
        const branchPayload = {
            "branch_key": branchBackendConfig.headers["branch_key"],
            "customer_event_alias": "FITNESS_PURCHASE_PLATFORM_EVENT",
            "name": 'PURCHASE',
            "content_items": [{
                $price: purchaseEventData.price,
                $sku: purchaseEventData.productId,
                $product_name: purchaseEventData.productName
            }],
            "user_data": {
                "developer_identity": userId,
                "os": this.getOS(device),
                "ip": device?.activeDevice?.ip
            }
        }
        const advertiserId = this.getAdvertiserId(device)
        if (!_.isNil(advertiserId)) {
            const {advertKey, advertValue} = advertiserId
            const keyValuePair = {
                [advertKey]: advertValue
            }
            Object.assign(branchPayload["user_data"], keyValuePair)
        }
        return branchPayload
    }

    getOS(device: any): "Android" | "iOS" {
        switch (device?.activeDevice?.osName?.toLowerCase()) {
            case "android": return "Android"
            case "ios": return "iOS"
            default: return undefined
        }
    }

    getAdvertiserId(device: any): { advertKey: string, advertValue: string} {
        const os = this.getOS(device)
        if (_.isNil(os)) {
            return undefined
        }
        const advertiserId = device?.activeDevice?.advertiserId?.split("|")?.[0]
        if (_.isNil(advertiserId)) {
            return undefined
        }
        switch (os) {
            case "Android": return {
                advertKey: "aaid",
                advertValue: advertiserId
            }
            case "iOS": return {
                advertKey: "idfa",
                advertValue: advertiserId
            }
        }
    }
}

export default BranchIOService
