import {inject, injectable} from "inversify"
import {BASE_TYPES, Configuration, FetchUtil, Logger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import * as _ from "lodash"
import {TimeUtil} from "@curefit/util-common"

export enum AnalyticsEventName {
    INBOUND_CALL_NO_CM = "inbound_call_no_cm",
}

@injectable()
export class DatalakeMetricService {

    private backendConfig: any;
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.Configuration) private appConfig: Configuration,
        @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil
    ) {
        this.backendConfig = _.get(this.appConfig.getConfiguration(), "datalakeMetricBackendConfig")
    }

    private publishEvent(eventName: AnalyticsEventName, body: any): void {
        try {
            const headers = {
                "Content-type": "application/json",
                "x-api-key": this.backendConfig.apiKey
            }
            if (body == null) {
                return
            }
            const request: any = {
                eventName: eventName,
                payload: [body]
            }
            if (request.payload.length === 0) {
                return
            }
            fetch(
                this.backendConfig.baseUrl,
                this.fetchHelper.post({headers: headers, body: request})
            )
        } catch (e) {
            const errorMessage = `Error while sending inbound event, error: - ${e.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
        }
    }

    private getPayloadForInboundCallReport(inboundCallingReport: any) {

        const startTime = _.isEmpty(inboundCallingReport.starttime) ? null : new Date(inboundCallingReport.starttime * 1000)
        const endTime = _.isEmpty(inboundCallingReport.endtime) ? null : new Date(inboundCallingReport.endtime * 1000)

        const startTimeString = _.isNil(startTime) ? null :
            TimeUtil.getMomentForDate(startTime,"Asia/Kolkata").format("YYYY-MM-DDTHH:mm:ss.SSS") + "Z";

        const endTimeString = _.isNil(endTime) ? null :
            TimeUtil.getMomentForDate(endTime,"Asia/Kolkata").format("YYYY-MM-DDTHH:mm:ss.SSS") + "Z";

        return {
            caller: inboundCallingReport.caller,
            receiver: inboundCallingReport.receiver ? inboundCallingReport.receiver : "NO_CM_FOUND",
            startTime: startTimeString,
            endTime: endTimeString,
            duration: inboundCallingReport.duration,
            billsec: inboundCallingReport.billsec,
            status: inboundCallingReport.dialstatus,
            recordPath: inboundCallingReport.recordpath,
            bridgeNumber: inboundCallingReport.virtualnumber,
            externalId: inboundCallingReport.callId
        }
    }

    public publishInboundCallingNoCMEvent(inboundCallingReport: any) {
        const payload = this.getPayloadForInboundCallReport(inboundCallingReport);
        this.publishEvent(AnalyticsEventName.INBOUND_CALL_NO_CM, payload);
    }
}