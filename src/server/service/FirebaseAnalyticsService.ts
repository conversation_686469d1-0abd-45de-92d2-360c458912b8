import {BASE_TYPES, FetchUtil, Logger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {Tenant} from "@curefit/user-common"

import * as _ from "lodash"
import {inject, injectable} from "inversify"

import Constants from "../constants"
import TYPES from "../ioc/types"
import {MetricUtils, ProfilerType} from "../utils/MetricUtils"
import {CommonUtils} from "../utils/CommonUtils"
import {
    FirebaseAnalyticsEvent,
    FirebasePurchaseEvent,
    FirebaseConversionEvent,
    FirebaseCustomEvent,
    FirebaseNotificationEvent
} from "../../iris-common"
import IdentityService from "./IdentityService"

export interface IFirebaseAnalyticsService {
    sendPurchaseEvent(userId: string, purchaseEventData: FirebasePurchaseEvent, tenant?: Tenant): Promise<{ success: boolean, response?: any }>
    sendPurchaseSuccessEvent(userId: string, transactionId: string, value: number, currency?: string, itemName?: string, tenant?: Tenant): Promise<{ success: boolean, response?: any }>
    sendConversionEvent(userId: string, conversionEventData: FirebaseConversionEvent, tenant?: Tenant): Promise<{ success: boolean, response?: any }>
    sendCustomEvent(userId: string, customEventData: FirebaseCustomEvent, tenant?: Tenant): Promise<{ success: boolean, response?: any }>
    sendNotificationEvent(userId: string, notificationEventData: FirebaseNotificationEvent, tenant?: Tenant): Promise<{ success: boolean, response?: any }>
    getAnalytics(tenant: Tenant, startDate: string, endDate: string, eventNames: string[]): Promise<any>
}

interface FirebaseAnalyticsPayload {
    client_id: string
    user_id?: string
    timestamp_micros?: number
    user_properties?: { [key: string]: any }
    events: FirebaseAnalyticsEvent[]
}

interface FirebaseAnalyticsConfig {
    projectId: string
    apiKey: string
    measurementId: string
    apiSecret: string
    url: string
    debugUrl: string
}

@injectable()
class FirebaseAnalyticsService implements IFirebaseAnalyticsService {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(BASE_TYPES.FetchUtilV2) private fetchHelper: FetchUtil,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(TYPES.CommonUtils) private commonUtils: CommonUtils,
        @inject(TYPES.IdentityService) private identityService: IdentityService
    ) {
    }

    async sendPurchaseEvent(userId: string, purchaseEventData: FirebasePurchaseEvent, tenant: Tenant = Tenant.CUREFIT_APP): Promise<{ success: boolean, response?: any }> {
        const device = await this.getDevice(userId)
        const config = this.getFirebaseAnalyticsConfig(tenant)
        
        const event: FirebaseAnalyticsEvent = {
            name: "purchase",
            params: {
                transaction_id: purchaseEventData.transaction_id,
                value: purchaseEventData.value,
                currency: purchaseEventData.currency,
                items: purchaseEventData.items,
                coupon: purchaseEventData.coupon,
                shipping: purchaseEventData.shipping,
                tax: purchaseEventData.tax
            }
        }

        const payload = await this.buildAnalyticsPayload(device, userId, [event])
        return this.sendAnalyticsEvent(payload, config)
    }

    async sendPurchaseSuccessEvent(userId: string, transactionId: string, value: number, currency: string = "INR", itemName?: string, tenant: Tenant = Tenant.CUREFIT_APP): Promise<{ success: boolean, response?: any }> {
        const device = await this.getDevice(userId)
        const config = this.getFirebaseAnalyticsConfig(tenant)
        
        const event: FirebaseAnalyticsEvent = {
            name: "purchase_success",
            params: {
                transaction_id: transactionId,
                value: value,
                currency: currency,
                item_name: itemName,
                quantity: 1,
                platform: "backend",
                debug_mode: !this.commonUtils.isEnvironmentProductionOrAlpha()
            }
        }

        const payload = await this.buildAnalyticsPayload(device, userId, [event])
        return this.sendAnalyticsEvent(payload, config)
    }

    async sendConversionEvent(userId: string, conversionEventData: FirebaseConversionEvent, tenant: Tenant = Tenant.CUREFIT_APP): Promise<{ success: boolean, response?: any }> {
        const device = await this.getDevice(userId)
        const config = this.getFirebaseAnalyticsConfig(tenant)
        
        const event: FirebaseAnalyticsEvent = {
            name: conversionEventData.event_name,
            params: {
                ...conversionEventData.event_params,
                timestamp_micros: conversionEventData.timestamp_micros
            }
        }

        const payload = await this.buildAnalyticsPayload(device, userId, [event], conversionEventData.user_properties)
        return this.sendAnalyticsEvent(payload, config)
    }

    async sendCustomEvent(userId: string, customEventData: FirebaseCustomEvent, tenant: Tenant = Tenant.CUREFIT_APP): Promise<{ success: boolean, response?: any }> {
        const device = await this.getDevice(userId)
        const config = this.getFirebaseAnalyticsConfig(tenant)
        
        const event: FirebaseAnalyticsEvent = {
            name: customEventData.event_name,
            params: {
                ...customEventData.parameters,
                session_id: customEventData.session_id,
                engagement_time_msec: customEventData.engagement_time_msec
            }
        }

        const payload = await this.buildAnalyticsPayload(device, userId, [event])
        return this.sendAnalyticsEvent(payload, config)
    }

    async sendNotificationEvent(userId: string, notificationEventData: FirebaseNotificationEvent, tenant: Tenant = Tenant.CUREFIT_APP): Promise<{ success: boolean, response?: any }> {
        const device = await this.getDevice(userId)
        const config = this.getFirebaseAnalyticsConfig(tenant)
        
        const event: FirebaseAnalyticsEvent = {
            name: notificationEventData.event_type,
            params: {
                notification_id: notificationEventData.notification_id,
                campaign_id: notificationEventData.campaign_id,
                creative_id: notificationEventData.creative_id,
                timestamp: notificationEventData.timestamp,
                app_id: notificationEventData.app_id
            }
        }

        const payload = await this.buildAnalyticsPayload(device, userId, [event])
        return this.sendAnalyticsEvent(payload, config)
    }

    async getAnalytics(tenant: Tenant, startDate: string, endDate: string, eventNames: string[]): Promise<any> {
        // Firebase Analytics doesn't have a direct query API like Branch
        // This would typically use Google Analytics Reporting API or Firebase Analytics Data API
        this.logger.info(`FirebaseAnalyticsService::getAnalytics Analytics retrieval not yet implemented for Firebase`)
        return { message: "Analytics retrieval for Firebase Analytics is not yet implemented. Use Google Analytics Reporting API or Firebase console." }
    }

    private async buildAnalyticsPayload(device: any, userId: string, events: FirebaseAnalyticsEvent[], userProperties?: { [key: string]: any }): Promise<FirebaseAnalyticsPayload> {
        const clientId = this.getClientId(device, userId)
        
        const payload: FirebaseAnalyticsPayload = {
            client_id: clientId,
            user_id: userId,
            timestamp_micros: Date.now() * 1000,
            events: events
        }

        if (userProperties) {
            payload.user_properties = userProperties
        }

        // Add device-specific properties
        if (device?.activeDevice) {
            payload.user_properties = {
                ...payload.user_properties,
                platform: this.getOS(device),
                app_version: device.activeDevice.appVersion,
                device_model: device.activeDevice.deviceModel
            }
        }

        return payload
    }

    private async sendAnalyticsEvent(payload: FirebaseAnalyticsPayload, config: FirebaseAnalyticsConfig): Promise<{ success: boolean, response?: any }> {
        const timerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF, "sendFirebaseAnalyticsEvent")
        
        try {
            const url = `${config.url}?measurement_id=${config.measurementId}&api_secret=${config.apiSecret}`
            
            const apiLatencyTimerContext = this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "FirebaseAnalytics")
            
            this.logger.info(`FirebaseAnalyticsService::sendAnalyticsEvent sending payload: ${JSON.stringify(payload)}`)
            
            const response = await this.commonUtils.fetchWithRetry(3, 1000, url, this.fetchHelper.post(payload))
            
            this.metricUtils.endProfilingTimer(apiLatencyTimerContext)
            
            const parsedResponse = await this.fetchHelper.parseResponse<any>(response)
            
            this.logger.info(`FirebaseAnalyticsService::sendAnalyticsEvent response: ${JSON.stringify(parsedResponse)}`)
            
            this.metricUtils.endProfilingTimer(timerContext)
            return { success: true, response: parsedResponse }
            
        } catch (error) {
            const errorMessage = `FirebaseAnalyticsService::sendAnalyticsEvent Failed for payload ${JSON.stringify(payload)} with error ${error.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            this.metricUtils.endProfilingTimer(timerContext)
            return { success: false }
        }
    }

    private getFirebaseAnalyticsConfig(tenant: Tenant): FirebaseAnalyticsConfig {
        const config = Constants.FirebaseAnalyticsConfigMap[tenant]
        if (!config) {
            throw new Error(`Firebase Analytics configuration not found for tenant: ${tenant}`)
        }
        return config
    }

    private async getDevice(userId: string): Promise<any> {
        try {
            return await this.identityService.getDevice(userId)
        } catch (error) {
            this.logger.warn(`FirebaseAnalyticsService::getDevice Failed to get device for userId: ${userId}. Error: ${error.toString()}`)
            return null
        }
    }

    private getClientId(device: any, userId: string): string {
        // Use advertising ID if available, otherwise fall back to user ID
        const advertiserId = device?.activeDevice?.advertiserId?.split("|")?.[0]
        return advertiserId || userId
    }

    private getOS(device: any): string {
        return device?.activeDevice?.osName || "unknown"
    }
}

export default FirebaseAnalyticsService
