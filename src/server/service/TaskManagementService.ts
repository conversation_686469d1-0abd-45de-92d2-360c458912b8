import {inject, injectable} from "inversify"
import {BASE_TYPES, Configuration, FetchUtil, ILogger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import * as _ from "lodash"
import {ExternalCommunicationCampaign} from "@curefit/iris-common"

@injectable()
export class TaskManagementService {

    private urlConfig: any;

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.Configuration) private appConfig: Configuration,
        @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil
    ) {
        this.urlConfig = _.get(this.appConfig.getConfiguration(), "taskManagement")
    }

    public async pushIVRLeadToTelesales(customerPhoneNumber: string, virtualNumber: string) {
        try {
            fetch(this.urlConfig.baseUrl + "v1/telesalesLead/add/ivr",
                this.fetchHelper.post({customerPhoneNumber: customerPhoneNumber, virtualNumber: virtualNumber}));
        } catch (e) {
            const errorMessage = `Error while pushing ivr lead to telesales for customerNumber ${customerPhoneNumber}, error: - ${e.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
        }
    }

    public async pushIVRLeadToPMS(
        notificationId: string,
        externalCampaignId?: ExternalCommunicationCampaign,
        fullReport?: any
    ) {
        try {
            await fetch(
                this.urlConfig.baseUrl + "v1/pmsEvents/ivr/missed/create",
                this.fetchHelper.post({
                    notificationId,
                    externalCampaignId,
                    ...fullReport
                })
            )
        } catch (e) {
            const errorMessage = `Error while pushing ivr lead to PMS for customerNumber ${fullReport.customerPhoneNumber}, error: ${e.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
        }
    }
}