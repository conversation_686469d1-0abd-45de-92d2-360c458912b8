import {BASE_TYPES, ILogger} from "@curefit/base"
import {
    BaseCreative,
    ClickToCallCreative,
    CreativeType,
    EmailCreative,
    InAppNotificationCreative,
    OBDCreative,
    PNCreative,
    registrationStatusType,
    SendCampaignNotificationsRequest,
    SendCampaignNotificationsResponse,
    SlackCreative,
    SMSCreative,
    TemplateEngine,
    WhatsappCreative
} from "../../iris-common"
import {ICreativeReadWriteDao, IRIS_MODELS_TYPES} from "../../iris-models"

import {inject, injectable} from "inversify"
import {ObjectID} from "mongodb"

import Constants from "../constants"
import {snsTopic} from "../enums"
import TYPES from "../ioc/types"
import {DataError} from "@curefit/error-client"
import CampaignHelper from "../helpers/CampaignHelper"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {
    Action as CampaignManagerAction,
    ActionType,
    AndroidPNMeta,
    Asset,
    AssetState,
    AssetType,
    AssetUseType,
    CampaignDeliveryType,
    ChildAsset,
    ClickToCallMeta,
    Creative,
    CreativeMetaType,
    CreativeType as CampaignMangerCreativeType,
    EmailMeta,
    InAppNotificationMeta,
    OBDMeta,
    PNMeta,
    SlackMeta,
    SMSMeta,
    TrayPriority,
    WhatsAppMeta,
    WhatsappServiceProvider
} from "@curefit/campaign-manager-common"
import {
    CAMPAIGN_MANAGER_CLIENT_TYPES,
    ICreativeService as CampaignMangerCreativeService
} from "@curefit/campaign-manager-client"
import {EventData, EVENTS_TYPES, EventsService} from "@curefit/events-util"
import {AWSUtils} from "../utils/AWSUtils"


@injectable()
class CreativeService {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(EVENTS_TYPES.EventsService) private eventsService: EventsService,
        @inject(IRIS_MODELS_TYPES.CreativeReadWriteDao) private creativeDao: ICreativeReadWriteDao,
        @inject(TYPES.CampaignHelper) private campaignHelper: CampaignHelper,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(CAMPAIGN_MANAGER_CLIENT_TYPES.CreativeService) private campaignMangerCreativeService: CampaignMangerCreativeService
    ) {
    }

    public async updateRegistrationStatus(creativeId: string,
                                          registrationStatus: registrationStatusType,
                                          templateId?: string): Promise<boolean> {

        const creative = await this.creativeDao.findOne({ _id: new ObjectID(creativeId) })
        if (!creative) { return }

        const updateParams: any = { $set: { registrationStatus } }

        // If creative type is SMS and a templateId was sent in request, update that as well
        if (creative.type === "SMS" && templateId) {
            updateParams["$set"]["smsParams.templateId"] = templateId.trim()
        }

        await this.creativeDao.findOneAndUpdatePartial(
                { _id: new ObjectID(creativeId) }, updateParams)

        if (creative.type === "SMS" || creative.type === "WHATSAPP") {
            //send alert to relevant audience
            this.sendCreativeStatusUpdateAlert(creative, registrationStatus).then( (response: SendCampaignNotificationsResponse) => {
                this.logger.debug(`updateRegistrationStatus:: send Email alert creative: ${creativeId} response: ${JSON.stringify(response)}`)
            }).catch(err => {
                this.logger.error(`updateRegistrationStatus::error ${err.toString()}`)
                this.rollbarService.sendError(err)
            })
        }

        const eventData: EventData<any> = new EventData(Constants.IRIS + "_" + Constants.SNS_EVENT_CREATIVE_REGISTRATION_STATUS_UPDATE, null, Date.now(),
            { creativeId, registrationStatus }, new Map<string, any>())
        return this.eventsService.publishMessage(
            AWSUtils.getAwsSNSTopicName(snsTopic.CREATIVE_REGISTRATION_STATUS_UPDATE, false), eventData)
    }

    private sendCreativeStatusUpdateAlert(creative: BaseCreative, registrationStatus: registrationStatusType): Promise<SendCampaignNotificationsResponse> {
        if (!creative.author) {
            throw new Error(`author not found for creative: ${creative.creativeId}`)
        }

        let sendNotifications: SendCampaignNotificationsRequest = {
            campaignId: "CUREFIT_IRIS_SMS_TEMPLATE_UPDATE_ALERT",
            creativeIds: ["EMAIL_CREATIVE_TEMPLATE_UPDATE_ALERT"],
            userContexts: [{
                emailId: creative.author,
                tags: {}
            }],
            globalTags: {
                "creativeId": creative.creativeId,
                "status": registrationStatus
            }
        }
        if (registrationStatus === "PENDING") {
            sendNotifications.userContexts[0].emailCC = [Constants.IT_SUPPORT_EMAIL]
        }

        return this.campaignHelper.sendCampaignMessages(sendNotifications)
    }

    private checkCreativeTemplateEngineValidity(templateEngine: TemplateEngine): boolean {
        if (!templateEngine) {
            throw new DataError("Please select a template engine")
        }
        if (templateEngine === "mustache") {
            throw new DataError(Constants.MUSTACHE_DEPRECATED_ERROR_MESSAGE)
        }
        return true
    }

    public needsRegistration(creativeType: CreativeType): boolean {
        return ["SMS", "WHATSAPP"].includes(creativeType)
    }

    public async addCreative(creative: BaseCreative): Promise<BaseCreative> {

        switch (creative.type) {
            case "EMAIL": {
                this.checkCreativeTemplateEngineValidity((<EmailCreative> creative).emailParams.templateEngine)
                break
            }
            case "IN_APP_NOTIFICATION": {
                this.checkCreativeTemplateEngineValidity(
                    (<InAppNotificationCreative> creative).inAppNotificationParams.templateEngine)
                break
            }
            case "PUSH_NOTIFICATION": {
                this.checkCreativeTemplateEngineValidity((<PNCreative> creative).pushNotificationParams.templateEngine)
                break
            }
            case "SMS": {
                this.checkCreativeTemplateEngineValidity((<SMSCreative> creative).smsParams.templateEngine)
                /*
                    We prevent certain templating expressions in SMS which can modify the rendered output
                    Templates need to be registered with TRAI, modifying templates will result in them getting rejected
                 */

                const forbiddenExpressions = ["#if", "#unless", "#each"]
                for (const forbiddenExpression of forbiddenExpressions) {
                    if (creative.body.includes(forbiddenExpression)) {
                        throw new Error(`SMS creatives cannot contain ${forbiddenExpression} statements`)
                    }
                }
                break
            }
            case "SLACK":
            case "CLICK_TO_CALL":
            case "WHATSAPP":
            case "OBD": {
                break
            }
            default: {
                throw new Error(`Creative type ${creative.type} not supported`)
            }
        }

        creative.registrationStatus = this.needsRegistration(creative.type) ?
            creative.type === "WHATSAPP" ? "PENDING" : "CREATED"
            : "ACTIVE"
        return this.creativeDao.create(creative)
    }

    public static getAllParsableText(creative: BaseCreative): string [] {

        switch (creative.type) {
            case "EMAIL": {
                return [creative.title, creative.body]
            }
            case "IN_APP_NOTIFICATION": {
                const inAppNotificationCreative: InAppNotificationCreative = <InAppNotificationCreative> creative
                return [
                    inAppNotificationCreative.title || "", inAppNotificationCreative.body || "",
                    inAppNotificationCreative.inAppNotificationParams.actionTitle || "",
                    inAppNotificationCreative.inAppNotificationParams.actionUrl || "",
                    inAppNotificationCreative.inAppNotificationParams.expiry || "",
                    inAppNotificationCreative.inAppNotificationParams.scheduledAt || "",
                    inAppNotificationCreative.inAppNotificationParams.image || ""
                ]
            }
            case "PUSH_NOTIFICATION": {
                const pushNotificationCreative: PNCreative = <PNCreative> creative
                return [
                    pushNotificationCreative.title || "", pushNotificationCreative.body || "",
                    pushNotificationCreative.pushNotificationParams.action,
                    pushNotificationCreative.pushNotificationParams.image,
                ]
            }
            case "SLACK": {
                const slackCreative: SlackCreative = <SlackCreative>creative
                return [slackCreative.title, slackCreative.slackParams.blocksJsonAsString]
            }
            case "CLICK_TO_CALL":
            case "WHATSAPP":
            case "SMS":
            case "OBD": {
                return [creative.body || ""]
            }
            default: {
                throw new Error(`Creative type ${creative.type} not supported`)
            }
        }
    }
    private getActions(pushNotificationCreative: PNCreative): CampaignManagerAction[] {
        let campaignManagerActions: CampaignManagerAction[] = []
        for (let irisAction of pushNotificationCreative.pushNotificationParams.actions) {
            let campaignManagerAction: CampaignManagerAction = {
                actionType:  <ActionType> "NAVIGATION",
                backupTitle: undefined,
                backupUrl: undefined,
                title: irisAction.title,
                url: irisAction.url

            }
            campaignManagerActions.push(campaignManagerAction)
        }
        return campaignManagerActions

    }

    private getTrayPriority(pushNotificationCreative: PNCreative): TrayPriority {
        if (pushNotificationCreative.pushNotificationParams.sortKey != null) {
            switch (pushNotificationCreative.pushNotificationParams.sortKey) {
                case "A": {
                    return <TrayPriority>"HIGH"
                }
                case "B": {
                    return <TrayPriority>"MEDIUM"
                }
                case "C": {
                    return <TrayPriority>"LOW"
                }
            }
        }
    }

    public getCreativeMeta(creative: BaseCreative): CreativeMetaType {
        switch (creative.type) {
            case "EMAIL": {
                const emailCreative: EmailCreative = <EmailCreative> creative
                let creativeMeta: EmailMeta = {
                    creativeType: <CampaignMangerCreativeType>"EMAIL",
                    cta: undefined,
                    footer: undefined,
                    from: !!emailCreative.emailParams.from ? emailCreative.emailParams.from : "",
                    fromName: !!emailCreative.emailParams.fromName ? emailCreative.emailParams.fromName : "",
                    generatedAttachmentNames: [],
                    serviceProvider: emailCreative.emailParams.serviceProvider,
                    templateEngine: !!emailCreative.emailParams.templateEngine ?
                        emailCreative.emailParams.templateEngine : undefined
                }
                return creativeMeta
            }
            case "IN_APP_NOTIFICATION": {
                const inAppNotificationCreative: InAppNotificationCreative = <InAppNotificationCreative> creative
                let creativeMeta: InAppNotificationMeta = {
                    creativeType: <CampaignMangerCreativeType>"IN_APP_NOTIFICATION",
                    actionTitle: inAppNotificationCreative.inAppNotificationParams.actionTitle,
                    actionUrl: inAppNotificationCreative.inAppNotificationParams.actionUrl,
                    backgroundColor: inAppNotificationCreative.inAppNotificationParams.backgroundColor,
                    expiry: inAppNotificationCreative.inAppNotificationParams.expiry,
                    image: !!inAppNotificationCreative.inAppNotificationParams.image ?
                        inAppNotificationCreative.inAppNotificationParams.image : "",
                    scheduledAt: inAppNotificationCreative.inAppNotificationParams.scheduledAt,
                    textColor: inAppNotificationCreative.inAppNotificationParams.textColor,
                    type: inAppNotificationCreative.inAppNotificationParams.type,
                    templateEngine: !!inAppNotificationCreative.inAppNotificationParams.templateEngine ?
                        inAppNotificationCreative.inAppNotificationParams.templateEngine : undefined

                }
                return creativeMeta
            }
            case "PUSH_NOTIFICATION": {
                const pushNotificationCreative: PNCreative = <PNCreative> creative
                let creativeMeta: PNMeta = {
                    creativeType: <CampaignMangerCreativeType>"PUSH_NOTIFICATION",
                    actions: !!pushNotificationCreative.pushNotificationParams.actions ?
                        this.getActions(pushNotificationCreative) : [],
                    androidMeta: <AndroidPNMeta> {soundFile: pushNotificationCreative.pushNotificationParams.sound},
                    badgeIcon: undefined,
                    badgeId: "",
                    deepLink: pushNotificationCreative.pushNotificationParams.action,
                    groupKey: !!pushNotificationCreative.pushNotificationParams.groupKey ?
                        pushNotificationCreative.pushNotificationParams.groupKey : "",
                    iosMeta: undefined,
                    maxAppVersion: pushNotificationCreative.pushNotificationParams.maxAppVersion ?
                        pushNotificationCreative.pushNotificationParams.maxAppVersion : undefined,
                    metadata: {},
                    minAppVersion: pushNotificationCreative.pushNotificationParams.minAppVersion ?
                        pushNotificationCreative.pushNotificationParams.minAppVersion : undefined,
                    notificationChannel: "",
                    notificationType: pushNotificationCreative.pushNotificationParams.notificationType,
                    overwriteKey: !!pushNotificationCreative.pushNotificationParams.overwriteKey ?
                        pushNotificationCreative.pushNotificationParams.overwriteKey : "",
                    sortKey: !!pushNotificationCreative.pushNotificationParams.sortKey ?
                        pushNotificationCreative.pushNotificationParams.sortKey : "",
                    subTitle: !!pushNotificationCreative.pushNotificationParams.subTitle ?
                        pushNotificationCreative.pushNotificationParams.subTitle : "",
                    timeToLiveInSeconds: !!pushNotificationCreative.pushNotificationParams.timeToLiveInSeconds ?
                        pushNotificationCreative.pushNotificationParams.timeToLiveInSeconds : undefined,
                    trayPriority: this.getTrayPriority(pushNotificationCreative) || undefined,
                    templateEngine: !!pushNotificationCreative.pushNotificationParams.templateEngine ?
                        pushNotificationCreative.pushNotificationParams.templateEngine : undefined,
                    chronometerProperties: pushNotificationCreative.pushNotificationParams.chronometerProperties ? pushNotificationCreative.pushNotificationParams.chronometerProperties : undefined
                }
                return creativeMeta
            }
            case "SLACK": {
                const slackCreative: SlackCreative = <SlackCreative> creative
                let creativeMeta: SlackMeta = {
                    creativeType: <CampaignMangerCreativeType>"SLACK",
                    blocksJsonAsString: slackCreative.slackParams.blocksJsonAsString
                }
                return creativeMeta
            }
            case "CLICK_TO_CALL": {
                const clickToCallCreative: ClickToCallCreative = <ClickToCallCreative> creative
                let creativeMeta: ClickToCallMeta = {
                    creativeType: <CampaignMangerCreativeType>"CLICK_TO_CALL",
                    isConfidential: clickToCallCreative.clickToCallParams.isConfidential || Constants.isConfidential(clickToCallCreative),
                    serviceProvider: clickToCallCreative.clickToCallParams.serviceProvider,
                    serviceAccount: clickToCallCreative.clickToCallParams.serviceAccount

                }
                return creativeMeta

            }
            case "WHATSAPP": {
                const whatsappCreative: WhatsappCreative = <WhatsappCreative> creative
                let creativeMeta: WhatsAppMeta = {
                    creativeType: <CampaignMangerCreativeType>"WHATSAPP",
                    fromNumber: whatsappCreative.whatsappParams.fromNumber,
                    serviceProvider: <WhatsappServiceProvider>whatsappCreative.whatsappParams.serviceProvider,
                    templateId: !!whatsappCreative.whatsappParams.templateId ?
                        whatsappCreative.whatsappParams.templateId : ""

                }
                return creativeMeta
            }
            case "SMS": {
                const smsCreative: SMSCreative = <SMSCreative> creative
                let creativeMeta: SMSMeta = {
                    creativeType: <CampaignMangerCreativeType>"SMS",
                    sender: !!smsCreative.smsParams.sender ?
                        smsCreative.smsParams.sender : "",
                    serviceProvider: smsCreative.smsParams.serviceProvider,
                    templateId: !!smsCreative.smsParams.templateId ?
                        smsCreative.smsParams.templateId : "",
                    templateEngine: !!smsCreative.smsParams.templateEngine ?
                        smsCreative.smsParams.templateEngine : undefined

                }
                return creativeMeta
            }
            case "OBD": {
                const obdCreative: OBDCreative = <OBDCreative> creative
                let creativeMeta: OBDMeta = {
                    creativeType: <CampaignMangerCreativeType>"OBD",
                    sender: !!obdCreative.obdParams.sender ?
                        obdCreative.obdParams.sender : "",
                    serviceProvider: obdCreative.obdParams.serviceProvider,
                    useCaseId: obdCreative.obdParams.useCaseId
                }
                return creativeMeta
            }
            default: {
                throw new Error(`Creative type ${creative.type} of creative ${creative.creativeId} not supported`)
            }
        }
    }

    private async getChildAssets(creative: BaseCreative): Promise<ChildAsset[]> {
        const pushNotificationCreative: PNCreative = <PNCreative>creative
        let childAssetIcon, childAssetImage: ChildAsset
        if (pushNotificationCreative.pushNotificationParams.icon.startsWith("http")) {
            let assetIcon: Asset = {
                assetState: <AssetState>"ACTIVE",
                assetType: <AssetType>"IMAGE",
                description: undefined,
                fileName: creative.creativeId.concat("_ICON"),
                generatedFileName: undefined,
                isEditable: false,
                videoUrl: "",
                mediaUrl: pushNotificationCreative.pushNotificationParams.icon,
                name: creative.creativeId.concat("_ICON"),
                tags: []
            }
            let assetIconResponse = await this.campaignMangerCreativeService.createAssetInBulk([assetIcon])
            childAssetIcon = {
                id: assetIconResponse[0].id,
                url: pushNotificationCreative.pushNotificationParams.icon,
                assetUseType: AssetUseType.LARGE_ICON
            }
        }
        if (!!pushNotificationCreative.pushNotificationParams.image && pushNotificationCreative.pushNotificationParams.image.startsWith("http")) {
            let assetImage: Asset = {
                assetState: <AssetState>"ACTIVE",
                assetType: <AssetType>"IMAGE",
                description: "",
                fileName: creative.creativeId.concat("_IMAGE"),
                generatedFileName: "",
                isEditable: false,
                mediaUrl: pushNotificationCreative.pushNotificationParams.image,
                videoUrl: "",
                name: creative.creativeId.concat("_IMAGE"),
                tags: []
            }
            let assetImageResponse = await this.campaignMangerCreativeService.createAssetInBulk([assetImage])
            childAssetImage = {
                id: assetImageResponse[0].id,
                url: pushNotificationCreative.pushNotificationParams.image,
                assetUseType: AssetUseType.IMAGE
            }

        }
        return !!childAssetIcon ?
            (!!childAssetImage ? [childAssetIcon, childAssetImage] : [childAssetIcon]) :
            (!!childAssetImage ? [childAssetImage] : [])
    }

    public getAssetStateFromRegistrationStatus(regStatus: registrationStatusType): AssetState {
        if (!!regStatus) {
            return undefined
        }
        switch (regStatus.toString()) {
            case "ACTIVE": {
                return <AssetState>"ACTIVE"
            }
            case "PENDING": {
                return <AssetState>"PENDING_APPROVAL"
            }
            case "REJECTED": {
                return <AssetState>"REJECTED"
            }
            case "CREATED": {
                return <AssetState>"INACTIVE"
            }
            default: {
                return undefined
            }
        }
    }

    public async irisCreativeToCampaignManagerCreative(irisCreative: BaseCreative): Promise<Creative> {
        let campaignManagerCreative: Creative = {
            irisCreativeId: irisCreative.creativeId,
            irisCreativeEntityId: (irisCreative as any)._id.toString(),
            creativeState: !!irisCreative.registrationStatus ? this.getAssetStateFromRegistrationStatus(irisCreative.registrationStatus) : undefined,
            creativeType: !!irisCreative.type ? <CampaignMangerCreativeType>irisCreative.type : undefined,
            description: "",
            message: !!irisCreative.body ? irisCreative.body : "",
            meta: this.getCreativeMeta(irisCreative),
            name: irisCreative.name,
            objective: !!irisCreative.objective ? irisCreative.objective : "",
            tags: [],
            title: irisCreative.title,
            childAssets: irisCreative.type === "PUSH_NOTIFICATION" ?
                await this.getChildAssets(irisCreative) : [],
            type: CampaignDeliveryType.TRANSACTION
        }
        return campaignManagerCreative

        // }
    }
}

export default CreativeService