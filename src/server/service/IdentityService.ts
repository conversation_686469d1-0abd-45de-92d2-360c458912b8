import {BASE_TYPES, Configuration, FetchUtil, Logger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {DeviceInfo} from "@curefit/device-common"

import {inject, injectable} from "inversify"
import * as _ from "lodash"
const fetch = require("node-fetch")

import TYPES from "../ioc/types"
import {MetricUtils} from "../utils/MetricUtils"
import Constants from "../constants"

@injectable()
class IdentityService {
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(BASE_TYPES.Configuration) private appConfig: Configuration,
    ) {
    }

    async fetchDevices(appId: string, userId: string): Promise<DeviceInfo[]> {
        const identityServiceRootURL = _.get(this.appConfig.getConfiguration(), "identityURL")

        // Teamfit uses empId, cult app uses identity id
        let identityFetchDevicesURL
        switch (appId) {
            case Constants.APP_ID_TEAMFIT: {
                identityFetchDevicesURL = `${identityServiceRootURL}/user/fetch/deviceInfo?empId=${userId}`
                break
            }
            case Constants.APP_ID_CULTAPP: {
                identityFetchDevicesURL = `${identityServiceRootURL}/user/fetch/deviceInfoByIdentityId?identityId=${userId}`
            }
        }

        const headers = {
            "X_TENANT": "CUREFIT",
            "X_NAMESPACE": appId
        }

        try {
            const response = await fetch(identityFetchDevicesURL, this.fetchHelper.get(headers))
            return this.fetchHelper.parseResponse<DeviceInfo[]>(response)
        } catch (error) {
            const errorMessage = `IdentityService::fetchDevices Error while fetching devices ${error.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            return []
        }
    }

}
export default IdentityService
