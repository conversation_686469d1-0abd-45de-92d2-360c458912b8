import {inject, injectable} from "inversify"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {BASE_TYPES, Logger} from "@curefit/base"
import {UserTags} from "../../iris-common"
import * as moment from "moment"
import _ = require("lodash")
import Constants from "../constants"
import HandlebarsUtils from "../utils/HandlebarsUtils"
import {FunctionTag} from "../../iris-common/src/functionalTags"
import TYPES from "../ioc/types"
import UserProfileAttributeUtils from "../utils/UserProfileAttributeUtils"

export enum FUNCTIONAL_TAGS_FUNCTIONS {
    TIME_DIFFERENCE = "GETTIMEDIFF",
    INDEX_VALUE = "GETINDEX",
    REGEX_GLOBAL = "REGEX_GLOBAL",
    EPOCH_TO_DATE = "EPOCHTODATE",
    SUM = "SUM",
    MINUS = "MINUS",
    PRODUCT = "PRODUCT",
    DIVIDE = "DIVIDE"
}

@injectable()
export class FunctionalTagsService {
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(TYPES.UserProfileAttributeUtils) private userProfileAttributeUtils: UserProfileAttributeUtils,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
    ) {
    }

    /* Takes in time in UNIX milliseconds and returns difference from now */
    private getTimeDiffFromNow(time: string, prevEnrichedTags: UserTags, units: moment.unitOfTime.Diff = "days"): string {
        let first = time.startsWith("{{") ? HandlebarsUtils.render(time, prevEnrichedTags.tags) : +time
        if (!isNaN(+first)) {
            first = +first // If stored as epoch, convert to number and evaluate
        }
        this.logger.debug(`FunctionalTagsService::getTimeDiffFromToday first ${first}`)
        if ( !moment(first).isValid()) {
            this.logger.error(`FunctionalTagsService::getTimeDiffFromToday Input time ${time} of value ${first} is not a valid time `)
            throw new Error(`UserContextHelper::getTimeDiffFromToday Input time ${time} of value ${first} is not a valid time`)
        }
        return (Math.abs(moment().diff(moment(first), units))).toString()
    }

    /* Returns the nth value from a list of values. First Value is at index 0, second at 1 and so on. */
    private getIndexValue(index: number, arr: string, prevEnrichedTags: UserTags): string {
        let stringList = arr.startsWith("{{") ? HandlebarsUtils.render(arr, prevEnrichedTags.tags) : arr
        let list
        try {
            list = arr.startsWith("{{") ? stringList.split(",") : JSON.parse(stringList)
        } catch (e) {
            let err = new Error(`FunctionalTagsService::getIndexValue The string ${stringList} is not a valid string list ${e}`)
            this.logger.error(`FunctionalTagsService::getIndexValue The string ${stringList} is not a valid string list ${e}`)
            throw err
        }
        if (_.isNaN(index) || !list.length || list.length <= index || index < 0) {
            const err = `FunctionalTagsService::getIndexValue There are ${list.length} elements in ${JSON.stringify(list)} input index ${index}`
            this.logger.error(err)
            throw new Error(err)
        }
        return list[index]
    }

    /* Uses userPreferredCenter to create global attribute name and uses that  */
    async getRegexBasedAttributeValue(attrStatic: string, attrStaticEnd: string, attributeToSearch: string, attributeProperty: string, prevEnrichedTags: UserTags): Promise<string> {
        let finalAttributeValue = attrStatic
        this.logger.debug(`FunctionalTags::getRegexBasedAttributeValue attrStatic: ${attrStatic} attributeToSearch: ${attributeToSearch}, attributeProperty: ${attributeProperty}`)
        this.logger.debug(`FunctionalTags::getRegexBasedAttributeValue Tags: ${JSON.stringify(prevEnrichedTags.tags)}`)
        let preferredAttr = (HandlebarsUtils.render("{{" + attributeToSearch + "}}", prevEnrichedTags.tags)).toLowerCase()
        try {
            this.logger.info(`FunctionalTags::getRegexBasedAttributeValue Static attribute value: ${finalAttributeValue} and preferredAttr: ${preferredAttr}`)
            let globalAttributeKey = finalAttributeValue + preferredAttr + attrStaticEnd
            this.logger.info(`FunctionalTags::getRegexBasedAttributeValue GlobalAttribute Key: ${globalAttributeKey}`)
            let attributes = await this.userProfileAttributeUtils.getInterpretedCachedGlobalAttributes("curefit", [globalAttributeKey])
            let attributeValue: any
            attributes.forEach((value: any, key: string) => {
                // this.logger.debug(`Global Attributes key: ${key} value ${value} typeOf ${typeof attributeValue}`)
                if (key === globalAttributeKey)
                    attributeValue = JSON.parse(value)
                this.logger.debug(`FunctionalTags::getRegexBasedAttributeValue Global Attributes key: ${key} attributevalue ${attributeValue} typeOf ${typeof attributeValue}`)
            })

            if (typeof attributeValue[attributeProperty] !== undefined) // true
                this.logger.info(`FunctionalTags::getRegexBasedAttributeValue Property exists with values: ${attributeValue[attributeProperty]}`)
            else {
                this.logger.error(`FunctionalTags::getRegexBasedAttributeValue Error while fetching key from  typeof ${typeof attributeValue} value ${attributeValue[attributeProperty]}
                attributeProperty ${attributeProperty}`)
            }
            let finalValue =  attributeValue[attributeProperty]
            this.logger.debug(`FunctionalTags::getRegexBasedAttributeValue finalValue: ${finalValue}`)
            return finalValue

        } catch (ex) {
            let err = new Error(`FunctionalTagsService::getRegexBasedAttributeValue The attr ${preferredAttr} is not a valid object error: ${ex}`)
            this.logger.error(`FunctionalTagsService::getRegexBasedAttributeValue The attr ${preferredAttr} is not a valid object error : ${ex}`)
            throw err
        }
    }

    /* Calculates the sum of n numbers */
    private sum(numbers: string[], prevEnrichedTags: UserTags): string {
        let total: number = 0
        for (let value of numbers) {
            let num = value.includes("{{") ? HandlebarsUtils.render(value, prevEnrichedTags.tags) : value
            if (isNaN(+num)) {
                const err = `FunctionalTagsService::sum num ${num} of value ${value} is not a valid number`
                this.logger.error(err)
                throw new Error(err)
            }
            total += +num
        }
        return total.toString()
    }


    /* Calculates the product of n numbers */
    private product(numbers: string[], prevEnrichedTags: UserTags): string {
        let total: number = 1
        for (let value of numbers) {
            let num = value.includes("{{") ? HandlebarsUtils.render(value, prevEnrichedTags.tags) : value
            if (isNaN(+num)) {
                const err = `FunctionalTagsService::sum num ${num} of value ${value} is not a valid number`
                this.logger.error(err)
                throw new Error(err)
            }
            total *= +num
        }
        return total.toString()
    }

    /* Calculates the difference of 2 numbers */
    private minus(minuend: string, subtrahend: string, prevEnrichedTags: UserTags): string {
        const first = minuend.startsWith("{{") ? +HandlebarsUtils.render(minuend, prevEnrichedTags.tags) : +minuend
        const second = subtrahend.startsWith("{{") ? +HandlebarsUtils.render(subtrahend, prevEnrichedTags.tags) : +subtrahend
        if (isNaN(first) || isNaN(second) || second >= first) {
            const err = `FunctionalTagsService::subtract minuend ${minuend} of value ${first} or subtrahend ${subtrahend} of value ${second} is not a valid number`
            this.logger.error(err)
            throw new Error(err)
        }
        return (first - second).toString()
    }

    /* Calculates the quotient of 2 numbers */
    private divide(dividend: string, divisor: string, prevEnrichedTags: UserTags, numOfDecimalPlaces = 0): string {
        const first = dividend.startsWith("{{") ? +HandlebarsUtils.render(dividend, prevEnrichedTags.tags) : +dividend
        const second = divisor.startsWith("{{") ? +HandlebarsUtils.render(divisor, prevEnrichedTags.tags) : +divisor
        if (isNaN(first) || isNaN(second) || isNaN(numOfDecimalPlaces)) {
            const err = `FunctionalTagsService::subtract divident ${dividend} of value ${first} or divisor ${divisor} of value ${second} or numOfDecimalPlaces ${numOfDecimalPlaces} is not a valid number`
            this.logger.error(err)
            throw new Error(err)
        }
        return (first / second).toFixed(numOfDecimalPlaces)
    }

    /* Returns the date in DD MMM YY Format. Example: 29 Sep 21 */
    private epochToDate(epoch: string, prevEnrichedTags: UserTags): string {
        let milliseconds = epoch.startsWith("{{") ? HandlebarsUtils.render(epoch, prevEnrichedTags.tags) : +epoch
        if (!isNaN(+milliseconds)) {
            milliseconds = +milliseconds // If stored as epoch, convert to number and evaluate
        }
        this.logger.debug(`FunctionalTagsService::getTimeDiffFromToday first ${milliseconds}`)
        if ( !moment(milliseconds).isValid()) {
            this.logger.error(`FunctionalTagsService::getTimeDiffFromToday Input time ${milliseconds} is not a valid time `)
            throw new Error(`UserContextHelper::getTimeDiffFromToday Input time ${milliseconds} is not a valid time`)
        }
        return moment(milliseconds).format("DD MMM YY")
    }

    public async evaluatePersonalisedAttributes(attr: string[], prevEnrichedTags: UserTags): Promise<Map<string, any>> {
        const response: Map<string, any> = new Map()
        for (let attribute of attr) {
            let funcName: string = attribute.substring(0, attribute.indexOf("("))
            this.logger.debug(`Extracted func: ${funcName}`)
            switch (funcName) {
                case FUNCTIONAL_TAGS_FUNCTIONS.TIME_DIFFERENCE: {
                    let args: string[] = attribute.substring(attribute.indexOf("(") + 1, attribute.indexOf(")")).split(",")
                    this.logger.debug(`arg0 ${args[0]} and arg1 ${args[1]} `)
                    let diffInDays
                    try {
                        this.validateMinMaxArgs(args, 1, 2)
                        diffInDays = !!args[1] ? this.getTimeDiffFromNow(args[0], prevEnrichedTags, <moment.unitOfTime.Diff> args[1]) :
                            this.getTimeDiffFromNow(args[0], prevEnrichedTags)
                    } catch (e) {
                        this.logger.error(`FunctionalTagsService::evaluatePersonalisedAttributes unable to find time-diff due to ${e}`)
                        break
                    }
                    response.set(attribute, diffInDays)
                    break
                }
                case FUNCTIONAL_TAGS_FUNCTIONS.INDEX_VALUE: {
                    const index: string[] = attribute.substring(attribute.indexOf("(") + 1, attribute.indexOf(")")).split(",", 1)
                    let list = attribute.substring(attribute.indexOf(",") + 1, attribute.indexOf(")"))
                    let indexVal
                    try {
                        indexVal = this.getIndexValue(+index[0], list, prevEnrichedTags)
                    } catch (e) {
                        this.logger.error(`FunctionalTagsService::evaluatePersonalisedAttributes unable to find getIndex due to ${e}`)
                        break
                    }
                    response.set(attribute, indexVal)
                    break
                }
                case FUNCTIONAL_TAGS_FUNCTIONS.REGEX_GLOBAL: {
                    const args: string[] = attribute.substring(attribute.indexOf("(") + 1, attribute.indexOf(")")).split(",")
                    this.logger.debug(`arg0 ${args[0]} and arg1 ${args[1]} `)
                    let attributeProperty = args[1]
                    let attrName = args[0].substring(args[0].indexOf("{") + 1, args[0].indexOf("}"))
                    let attrStaticStart = args[0].substring(0, args[0].indexOf("{"))
                    let attrStaticEnd = args[0].substring(args[0].indexOf("}") + 1, args[0].length)
                    this.logger.debug(`attrName ${attrName} and attrStatic ${attrStaticStart}`)
                    let attributeVal
                    try {
                        attributeVal = await this.getRegexBasedAttributeValue(attrStaticStart, attrStaticEnd, attrName, attributeProperty, prevEnrichedTags)
                        this.logger.debug(`FunctionalTagsService::evaluatePersonalisedAttributes attribute value ${attributeVal}`)
                    } catch (e) {
                        this.logger.error(`FunctionalTagsService::evaluatePersonalisedAttributes unable to compute regex global func due to ${e}`)
                        throw e
                        break
                    }
                    response.set(attribute, attributeVal)
                    break
                }
                case FUNCTIONAL_TAGS_FUNCTIONS.EPOCH_TO_DATE: {
                    let args: string[] = attribute.substring(attribute.indexOf("(") + 1, attribute.indexOf(")")).split(",")
                    let date
                    try {
                        this.validateMinMaxArgs(args, 1, 1)
                        date = this.epochToDate(args[0], prevEnrichedTags)
                    } catch (e) {
                        this.logger.error(`FunctionalTagsService::evaluatePersonalisedAttributes unable to convert to date due to ${e}`)
                        break
                    }
                    response.set(attribute, date)
                    break
                }
                case FUNCTIONAL_TAGS_FUNCTIONS.SUM: {
                    let args: string[] = attribute.substring(attribute.indexOf("(") + 1, attribute.indexOf(")")).split(",")
                    let sum
                    try {
                        this.validateMinMaxArgs(args, 2, Infinity)
                        sum = this.sum(args, prevEnrichedTags)
                    } catch (e) {
                        this.logger.error(`FunctionalTagsService::evaluatePersonalisedAttributes unable to find sum due to ${e}`)
                        break
                    }
                    response.set(attribute, sum)
                    break
                }
                case FUNCTIONAL_TAGS_FUNCTIONS.PRODUCT: {
                    let args: string[] = attribute.substring(attribute.indexOf("(") + 1, attribute.indexOf(")")).split(",")
                    let product
                    try {
                        this.validateMinMaxArgs(args, 2, Infinity)
                        product = this.product(args, prevEnrichedTags)
                    } catch (e) {
                        this.logger.error(`FunctionalTagsService::evaluatePersonalisedAttributes unable to find product due to ${e}`)
                        break
                    }
                    response.set(attribute, product)
                    break
                }
                case FUNCTIONAL_TAGS_FUNCTIONS.MINUS: {
                    let args: string[] = attribute.substring(attribute.indexOf("(") + 1, attribute.indexOf(")")).split(",")
                    let difference
                    try {
                        this.validateMinMaxArgs(args, 2, 2)
                        difference = this.minus(args[0], args[1], prevEnrichedTags)
                    } catch (e) {
                        this.logger.error(`FunctionalTagsService::evaluatePersonalisedAttributes unable to minus due to ${e}`)
                        break
                    }
                    response.set(attribute, difference)
                    break
                }
                case FUNCTIONAL_TAGS_FUNCTIONS.DIVIDE: {
                    let args: string[] = attribute.substring(attribute.indexOf("(") + 1, attribute.indexOf(")")).split(",")
                    let quotient
                    try {
                        this.validateMinMaxArgs(args, 2, 3)
                        quotient = !!args[2] ?  this.divide(args[0], args[1], prevEnrichedTags, +args[2]) :
                            this.divide(args[0], args[1], prevEnrichedTags)
                    } catch (e) {
                        this.logger.error(`FunctionalTagsService::evaluatePersonalisedAttributes unable to divide due to ${e}`)
                        break
                    }
                    response.set(attribute, quotient)
                    break
                }
                default: {
                    this.logger.error(`No personalized function found by the name ${funcName}`)
                }

            }
        }
        return response
    }

    public getAllFunctions(): Record<string, FunctionTag> {
        return Constants.ALL_SUPPORTED_FUNCTIONAL_TAGS

    }

    private validateMinMaxArgs(args: string[], minArgs: number, maxArgs: number) {
        if (args.length > maxArgs || args.length < minArgs) {
           throw new Error(`FunctionalTagsService::validateMinMaxArgs validation failed. Got args: ${args.length} minArgs: ${minArgs} maxArgs: ${maxArgs}`)
        }
    }

}