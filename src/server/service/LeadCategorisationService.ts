import {inject, injectable} from "inversify"
import {BASE_TYPES, Configuration, FetchUtil, ILogger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import * as _ from "lodash"

@injectable()
export class LeadCategorisationService {

    private urlConfig: any;

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.Configuration) private appConfig: Configuration,
        @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil
    ) {
        this.urlConfig = _.get(this.appConfig.getConfiguration(), "leadCategorisation")
    }

    public async categoriseLeadType(inboundCallProviderReport: any) {
        try {
            fetch(this.urlConfig.baseUrl + "audio_calls/lead_generation",
                this.fetchHelper.post(inboundCallProviderReport))
        } catch (e) {
            const errorMessage = `Error while categoring ivr lead for customerNumber ${inboundCallProviderReport.customerPhoneNumber}, error: - ${e.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
        }
    }
}