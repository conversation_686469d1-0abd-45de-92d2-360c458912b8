import {BaseDelayedQueue<PERSON><PERSON><PERSON>, IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {DataError} from "@curefit/error-client"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

import {inject, injectable} from "inversify"
import {ObjectID} from "mongodb"
import * as _ from "lodash"

import {
    IBasicNotificationDao,
    ICommunicationCampaignReadonlyDao,
    INotificationTaskReadWriteDao,
    IRIS_MODELS_TYPES,
    NotificationTaskSchema
} from "../../iris-models"
import TYPES from "../ioc/types"
import {
    Notification,
    NotificationTask,
    QueueConstants,
    SendCampaignNotificationsRequest,
    UserTags
} from "../../iris-common"
import CampaignHelper from "../helpers/CampaignHelper"
import {<PERSON>Helper} from "../helpers/MozartHelper"
import FeatureUtils from "../utils/FeatureUtils"
import {MetricUtils} from "../utils/MetricUtils"
import NotificationStatusUpdatePublisher from "../publishers/NotificationStatusUpdatePublisher"

@injectable()
class NotificationEventProcessingService extends BaseDelayedQueueHandler {

    constructor(
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(TYPES.FeatureUtils) private featureUtils: FeatureUtils,
        @inject(SQS_CLIENT_TYPES.QueueService) queueService: IQueueService,
        @inject(TYPES.CampaignHelper) private campaignHelper: CampaignHelper,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(IRIS_MODELS_TYPES.NotificationTaskSchema) private ntSchema: NotificationTaskSchema,
        @inject(TYPES.MozartHelper) private mozartHelper: MozartHelper,
        @inject(IRIS_MODELS_TYPES.BasicNotificationDao) private notificationDao: IBasicNotificationDao,
        @inject(IRIS_MODELS_TYPES.NotificationTaskReadWriteDao) private notificationTaskDao: INotificationTaskReadWriteDao,
        @inject(IRIS_MODELS_TYPES.CommunicationCampaignReadonlyDao) private communicationCampaignDao: ICommunicationCampaignReadonlyDao,
        @inject(TYPES.NotificationStatusUpdatePublisher) private notificationStatusUpdatePublisher: NotificationStatusUpdatePublisher,
    @inject(BASE_TYPES.ILogger) private logger: ILogger
    ) {
        super(QueueConstants.getQueueName("IRIS_EVENTS"), queueService)
    }

    async handle(message: string, attributes: { [key: string]: any }): Promise<boolean> {
        try {
            this.logger.debug(`Processing event with message ${message} and attr ${JSON.stringify(attributes)}`)
            const jsonData: {[key: string]: string} = JSON.parse(message)
            const notificationId = attributes["notificationId"] ? attributes["notificationId"].StringValue : null
            const eventType = attributes["eventType"] ? attributes["eventType"].StringValue : null
            const timestamp: Date = new Date(+(attributes["timestamp"] ? attributes["timestamp"].StringValue : 0))

            if (!eventType) {
                this.logger.error(`Unable to process ${message} with attributes ${attributes}`)
                throw new DataError("Unable to process event")
            }

            if (eventType.toLowerCase() === "process_task") {
                const taskId = jsonData["taskId"]
                await this.processTask(taskId)
            } else {
                if (!notificationId) {
                    this.logger.error(`Unable to process ${message} with attributes ${attributes}`)
                    throw new DataError("Unable to process event")
                }

                const notification = await this.notificationDao.get(notificationId)
                if (notification) {
                    await this.processNotificationEvent(notification, eventType, jsonData, timestamp)
                } else {
                    this.logger.error(`NotificationEventProcessingService::handle No notification found for ${notificationId}`)
                }

            }
            return Promise.resolve(true)
        } catch (err) {
            const errorMessage = `NotificationEventProcessingService::handle error: ${err.toString()} for message: ` +
                `${message} and attr ${JSON.stringify(attributes)}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            return Promise.reject(err)
        }
    }

    async processNotificationEvent(notification: Notification, eventType: string,
                                   jsonData: any, timestamp: Date): Promise<any> {
        const entityId: string = jsonData["entityId"]
        const entityType: string = jsonData["entityType"]
        const notificationId = notification.notificationId

        let props: {[key: string]: any} = { }

        this.logger.debug(`NotificationEventProcessingService::handle ${eventType.toLowerCase()} message ` +
            `for notificationId: ${notificationId} at timestamp: ${timestamp}`)

        switch (eventType.toLowerCase()) {
            case "received": {
                // Process only if PN delivery via CF API enabled
                if (!this.featureUtils.isPushNotificationDeliveryReportViaCFAPIEnabled()) {
                    break
                }
                props["receivedAt"] = timestamp
                if (notification.status !== "READ") {
                    props["status"] = "DELIVERED"
                }
                this.metricUtils.incrementNotificationStatusCounter(
                    notification.campaignId, notification.creativeId, "DELIVERED", "IRIS_EVENTS")
                await this.notificationStatusUpdatePublisher.publish([{ notification, status: "DELIVERED",
                    timestamp: timestamp.getTime() }])
                break
            }
            case "processed" :
                props["processedAt"] = timestamp
                break
            case "opened": {
                props["openedAt"] = timestamp
                props["status"] = "READ"
                this.metricUtils.incrementNotificationStatusCounter(
                    notification.campaignId, notification.creativeId, "READ", "IRIS_EVENTS")
                await this.notificationStatusUpdatePublisher.publish([{ notification, status: "READ",
                    timestamp: timestamp.getTime()}])
                break
            }
            case "converted": {
                let n = await this.notificationDao.get(notificationId)
                if (n && !n.convertedAt) {
                    props["convertedAt"] = timestamp
                    props["convertedEntityId"] = entityId
                    props["convertedEntityType"] = entityType
                }
                break
            }
            default:
                this.logger.error(`Unknown event ${eventType} in message ${JSON.stringify(jsonData)}`)
                throw new DataError("Unknown event")
        }
        if (Object.keys(props).length > 0) {
            this.logger.debug("Updating notification by id " + notificationId + " with " + JSON.stringify(props))
            if (!(await this.notificationDao.updateProps(notificationId, props))) {
                const msg: string = "Unable to update notification by id " + notificationId
                this.logger.error(msg)
                //No point blocking the incoming message by throwing error
                //This will ensure that we can clear the notifications db once in a while.
                //Uncommenting till we get a better alerting framework/have to do cleanup
                //throw new DataError(msg)
            }
        } else {
            this.logger.info("Nothing to update: notification " + notificationId + " with " + JSON.stringify(props))
        }
    }

    async processTask(taskId: string) {
        this.logger.info("Trying to process notificationTask " + taskId)
        let nt: NotificationTask = await this.notificationTaskDao.findOne({_id: new ObjectID(taskId)})
        if (nt.status === "CREATED") {
            if (!this.isNotificationTaskValid(nt)) {
                nt.status = "INVALID"
                await this.notificationTaskDao.findOneAndUpdate({_id: new ObjectID(taskId)}, nt)
                this.logger.info("Invalid task, not processing", JSON.stringify(nt))
                return
            }

            //Tasks created in 5 minutes before this task, for same campaign, same creative and same user
            const fiveMinutesInMilliSec = 5 * 60 * 1000
            const oldNT = await this.notificationTaskDao.findOne({
                createdDate: {
                    $gte: new Date( new Date((<any>nt)["createdDate"]).getTime() - fiveMinutesInMilliSec),
                    $lt: (<any>nt)["createdDate"]
                },

                campaignId: nt.campaignId,
                creativeIds: nt.creativeIds,
                "userContexts.userId": nt.userContexts[0].userId

            })

            if (oldNT) {
                this.logger.info(`For task ${taskId}, found possible duplicate task ${(<any>oldNT)["_id"]}. Not processing`)
                nt.status = "DUPLICATE"
                await this.notificationTaskDao.findOneAndUpdate({_id: new ObjectID(taskId)}, nt)
                return
            } else {
                if (!nt.schedule) {
                    await this.startTask(taskId, nt)
                }
                else {
                    await this.scheduleTask(taskId, nt)
                }
            }
            return
        } else {
            this.logger.info("notificationTask " + taskId + " in state " + nt.status + ". Not processing.")
        }
    }

    private maptoObj(strMap: Map<string, string>) {
        let obj: {[key: string]: string} = {}
        for (let [k, v] of strMap) {
            // We don’t escape the key '__proto__'
            // which can cause problems on older engines
            obj[k] = v
        }
        return obj
    }

    async scheduleTask(taskId: string, nt: NotificationTask) {
        await this.mozartHelper.scheduleTask(taskId, nt.schedule.date)
        nt.status = "SCHEDULED"
        nt.progress = 0
        this.logger.info("Processing notificationTask " + taskId + " with " + nt.userContexts.length + " user(s)")
        await this.notificationTaskDao.findOneAndUpdate({_id: new ObjectID(taskId)}, nt)
        return
    }

    async pickScheduledTask(taskId: string) {
        let nt: NotificationTask = await this.notificationTaskDao.findOne({_id: new ObjectID(taskId)})
        await this.startTask(taskId, nt)
    }

    async startTask(taskId: string, nt: NotificationTask) {
        if (nt.status === "CANCELLED") return
        nt.status = "STARTED"
        nt.progress = 0
        this.logger.info("Processing notificationTask " + taskId + " with " + nt.userContexts.length + " user(s)")
        await this.notificationTaskDao.findOneAndUpdate({_id: new ObjectID(taskId)}, nt)
        //Do this async
        this.completeTask(taskId, nt)
        return
    }

    async completeTask(taskId: string, nt: NotificationTask) {
        try {
            let userContexts: UserTags[] = []
            for (let uc of nt.userContexts) {
                let tagsMap = uc.tags instanceof Map ? this.maptoObj(<any>uc.tags) : uc.tags
                let tempUC: UserTags = {userId: uc.userId, tags: tagsMap}
                userContexts.push(tempUC)
            }
            this.logger.info("Added " + userContexts.length + " users from notification user contexts to complete task in")
            const userContextChunks = _.chunk(userContexts, 100)
            this.logger.info("Split notificationTask " + taskId + " into " + userContextChunks.length + " chunks")

            const campaign = await this.communicationCampaignDao.findOne({ campaignId: nt.campaignId })

            const total = userContexts.length
            let processed = 0
            let i = 0

            for (let ucc of userContextChunks) {

                // will be called for each chunk of size 100, does this need optimization?
                const nt_temp: NotificationTask = await this.notificationTaskDao.findById(taskId)

                if (nt_temp.status === "CANCELLED") return

                let sendNotificationTask: SendCampaignNotificationsRequest = {
                    taskId: taskId,
                    campaignId: nt.campaignId,
                    creativeIds: nt.creativeIds,
                    globalTags: this.maptoObj(<any>nt.globalTags),
                    userContexts: <any>ucc
                }
                this.logger.info("Processing chunk " + i + " of task " + taskId)
                //Object is modified! Careful with saving notification task
                await this.campaignHelper.sendCampaignMessages(sendNotificationTask, true, 100 / userContextChunks.length)
                if (campaign.type === "TRANSACTION") {
                    processed += ucc.length
                    nt.progress = processed * 100.0 / total
                    i++
                    //Bypassing DAO for speed
                    await this.ntSchema.mongooseModel.update({_id: new ObjectID(taskId)}, {progress: (processed * 100.0 / total)})
                }
            }
            if (campaign.type === "TRANSACTION") {
                nt.status = "COMPLETED"
                nt.progress = 100
                await this.ntSchema.mongooseModel.update({_id: new ObjectID(taskId)}, {progress: 100, status: "COMPLETED"})
            }
        } catch (err) {
            this.logger.error(`Processing task ${taskId} failed with error ${err}`)
            if (err instanceof DataError) {
                await this.ntSchema.mongooseModel.update({_id: new ObjectID(taskId)}, {status: "INVALID"})
            } else {
                await this.ntSchema.mongooseModel.update({_id: new ObjectID(taskId)}, {status: "FAILED"})
            }
        }

    }

    async cancelTask(taskId: string): Promise<boolean> {
        let nt: NotificationTask = await this.notificationTaskDao.findById(taskId)
        if (nt == null) {
            this.logger.error(`could not cancel task with id ${taskId}`)
            this.rollbarService.sendError(new Error(`could not cancel task with id ${taskId}, task not found`))
            return Promise.reject(false)
        }
        nt = Object.assign(nt, {status: "CANCELLED"})
        await this.notificationTaskDao.update(taskId, nt)
        return Promise.resolve(true)
    }

    private isNotificationTaskValid(nt: NotificationTask): boolean {
        if (!nt.creativeIds || nt.creativeIds.length === 0 || !nt.campaignId
            || (!nt.userContexts || nt.userContexts.length === 0)
        ) {
            return false
        }
        return true
    }
}

export default NotificationEventProcessingService
