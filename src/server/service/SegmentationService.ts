import {BASE_TYPES, ILogger} from "@curefit/base"
import {DownloadUsersResponse} from "@curefit/segmentation-service-client/dist/src/rest/IUserSegmentClient"
import {IUserSegmentClient, SEGMENTATION_CLIENT_TYPES} from "@curefit/segmentation-service-client/dist"
import {AthenaDownloadTaskResponse, ITransientSegmentClient} from "@curefit/segmentation-service-client/dist/src/rest/ITransientSegmentClient"
import {inject, injectable} from "inversify"

@injectable()
class SegmentationService {

    static SEGMENT_DOWNLOAD_SOURCE = "IRIS"

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(SEGMENTATION_CLIENT_TYPES.UserSegmentClient) private userSegmentClient: IUserSegmentClient,
        @inject(SEGMENTATION_CLIENT_TYPES.TransientSegmentClient) private transientSegmentClient: ITransientSegmentClient
    ) {
    }

    public async downloadSegment(segmentId: string, taskId: string, userId: string, appId: string): Promise<any> {
        return await this.userSegmentClient.publishUsersToIris(+segmentId, taskId, userId, appId)
    }

    public async downloadTransientSegment(notificationSegmentTaskId: string, segmentId: string, userId: string, campaignId: string, appId: string): Promise<any> {
        try {
            const segmentServiceResponse: AthenaDownloadTaskResponse = await this.transientSegmentClient.downloadTransientSegment(notificationSegmentTaskId, Number(segmentId), userId, campaignId, "IRIS", appId)
            return {
                athenaTaskId: !!segmentServiceResponse ? segmentServiceResponse.id : null
            }
        }
        catch (err) {
            this.logger.error(`Download transient segment call to segmentation failed with err: ${err}`)
            return {
                athenaTaskId: null
            }
        }
    }
}

export default SegmentationService