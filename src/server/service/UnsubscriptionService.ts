import {BASE_TYPES, ILogger} from "@curefit/base"

import {inject, injectable} from "inversify"
import Constants from "../constants"
import {CommonUtils} from "../utils/CommonUtils"
const CryptoJS = require("crypto-js")

@injectable()
class UnsubscriptionService {

    private readonly SECRET_KEY: string = "c1VzQcZEGKQeNMzssLuVuI91srvQRUiEvfxzz5cL686xTMZ5C2IZ96akYqnZ"

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger
    ) {
    }

    private generateToken(token: string): string {
        const b64 = CryptoJS.AES.encrypt(token, this.SECRET_KEY).toString()
        const e64 = CryptoJS.enc.Base64.parse(b64)
        return e64.toString(CryptoJS.enc.Hex)
    }

    private decodeToken(token: string): string {
        const reb64 = CryptoJS.enc.Hex.parse(token)
        const bytes = reb64.toString(CryptoJS.enc.Base64)
        const decrypt = CryptoJS.AES.decrypt(bytes, this.SECRET_KEY)
        return decrypt.toString(CryptoJS.enc.Utf8)
    }

    public generateEmailUnsubscriptionUrl(email: string, appId: string): string {
        if (!email)
            return null

        let baseUrl
        if (CommonUtils.isEnvironmentProduction()) {
            switch (appId) {
                case Constants.APP_ID_SUGARFIT:
                    baseUrl = Constants.USER_EMAIL_UNSUBSCRIPTION_BASE_URL_PRODUCTION
                    break
                case Constants.APP_ID_CULTSPORT:
                    baseUrl = Constants.USER_EMAIL_UNSUBSCRIPTION_BASE_URL_PRODUCTION_CULTSPORT
                    break
                default:
                    baseUrl = Constants.USER_EMAIL_UNSUBSCRIPTION_BASE_URL_PRODUCTION
            }
        } else if (CommonUtils.isEnvironmentAlpha()) {
            switch (appId) {
                case Constants.APP_ID_SUGARFIT:
                    baseUrl = Constants.USER_EMAIL_UNSUBSCRIPTION_BASE_URL_PRODUCTION
                    break
                case Constants.APP_ID_CULTSPORT:
                    baseUrl = Constants.USER_EMAIL_UNSUBSCRIPTION_BASE_URL_ALPHA_CULTSPORT
                    break
                default:
                    baseUrl = Constants.USER_EMAIL_UNSUBSCRIPTION_BASE_URL_PRODUCTION
            }
        } else {
            switch (appId) {
                case Constants.APP_ID_SUGARFIT:
                    baseUrl = Constants.USER_EMAIL_UNSUBSCRIPTION_BASE_URL_STAGE
                    break
                case Constants.APP_ID_CULTSPORT:
                    baseUrl = Constants.USER_EMAIL_UNSUBSCRIPTION_BASE_URL_STAGE_CULTSPORT
                    break
                default:
                    baseUrl = Constants.USER_EMAIL_UNSUBSCRIPTION_BASE_URL_STAGE
            }
        }

        return `${baseUrl}?token=${this.generateToken(email)}`
    }

    public decodeEmailToken(token: string): any {
        return {
            email: this.decodeToken(token)
        }
    }

    public decodeUnsubscriptionToken(creativeType: string, token: string): Promise<any> {
        if (creativeType === "email") {
            return this.decodeEmailToken(token)
        }
    }
}

export default UnsubscriptionService