import {DeviceInfo} from "@curefit/device-common"
import {NotFoundErrorV2} from "@curefit/error-client"
import {BASE_TYPES, Logger} from "@curefit/base"
import { Action } from "@curefit/apps-common"

import {inject, injectable} from "inversify"

import {IFirebaseService} from "./FirebaseService"
import TYPES from "../ioc/types"
import IdentityService from "./IdentityService"
import {MetricUtils} from "../utils/MetricUtils"
import {NotificationType} from "../../iris-common"
import FirebaseAdminService from "./FirebaseAdminService"
import FeatureUtils from "../utils/FeatureUtils"
import Constants from "../constants"


export interface NotificationPayload {
    title: string
    body: string
    android_channel_id?: string
    sound: string
    icon: string
    image?: string
    // TODO data should not be optional field
    data?: {
        action: string,
        analyticData: NotificationAnalyticData
    },
    /* Google reserves "data" key for internal use.
       In previous versions we were sending data, which was not recognised.
       Changing key name from "data" to "payload" fixes this.
       TODO: Remove the "data" key from the payload eventually
     */
    payload?: {
        action: string,
        analyticData: NotificationAnalyticData,
        meta?: {[data: string]: string} //needed for interactive PNs
    }
    subTitle?: string
    groupKey?: string
    smallIcon?: string
    actions: Action[]
    sortKey?: string
    overwriteKey?: string,
    category?: string,
    chronometerProperties?: {   // for displaying an ongoing counting timer in the PN
        showChronometer: boolean,
        chronometerDirection: "up" | "down",
        chronometerTimestamp: number,   //Time upto/from which we need to count down/up
        overrideTimestampValue?: string
    },
    silentNotification?: boolean,
    timeoutAfter?: number, // auto collapse
    ongoing?: boolean, // Set true for sticky notifications
}

interface NotificationAnalyticData {
    notificationId: string,
    campaignId?: string,
    creativeId?: string,
    notificationType?: NotificationType,
    minAppVersion?: string
}

const ANDROID_APP_VERSION_FOR_NOTIFICATION = 4.23
@injectable()
class PushNotificationService {

    constructor(
        @inject(TYPES.FirebaseService) private firebaseService: IFirebaseService,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(TYPES.IdentityService) private identityService: IdentityService,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(TYPES.FirebaseAdminService) private firebaseAdminService: FirebaseAdminService,
        @inject(TYPES.FeatureUtils) private featureUtils: FeatureUtils,
    ) {
    }

    async sendNotification(userId: string, payload: NotificationPayload, activeDevice: DeviceInfo, notificationId: string,
                           isTransactional: boolean, timeToLive: number, appId: string,
                           dryRun?: boolean): Promise<{ success: boolean, messageId?: string, failReason?: string }> {

        if (!activeDevice) {
            throw new NotFoundErrorV2("User does not have active device", { meta: "NO_DEVICE" })
        }

        if (!activeDevice.pushNotificationToken) {
            throw new NotFoundErrorV2("Device push notification token missing", { meta: "MISSING_PN_TOKEN" })
        }

        this.logger.debug(`PushNotificationService::sendNotification Sending the nudge, ` +
            `${JSON.stringify(payload)} to ${activeDevice.userId} and token ${activeDevice.pushNotificationToken} ` +
            `and appId ${activeDevice.appId}`)

        const appVersion = Math.round((Number(activeDevice.appVersion) + Number.EPSILON) * 100) / 100
        this.logger.debug(`PushNotificationService::sendNotification app version : ${appVersion}`)

        // We have enabled notifee only for appIds curefit and sugarfit
        // Min app versions for both these are present in the config
        const useNotifee = (appId === Constants.APP_ID_CUREFIT || appId === Constants.APP_ID_SUGARFIT)
            && appVersion >= Number(this.featureUtils.notifeeMessagingMinAppVersion(appId)) && !payload.silentNotification

        if (useNotifee) {
            this.logger.debug(`PushNotificationService::sendNotification Using notifee for notificationId: ` +
                `${notificationId} appId: ${appId} version: ${Number(activeDevice.appVersion)} ${appVersion} ` +
                `target version: ${Number(this.featureUtils.notifeeMessagingMinAppVersion(appId))}`)

            return this.firebaseAdminService.sendMessageViaNotifee(activeDevice.pushNotificationToken, payload,
                isTransactional, timeToLive, dryRun, appId, activeDevice.osName.toLowerCase())

        } else {
            this.logger.debug(`PushNotificationService::sendNotification Not using notifee for notificationId: ` +
                `${notificationId} appId: ${appId} version: ${Number(activeDevice.appVersion)} ${appVersion} ` +
                `target version: ${Number(this.featureUtils.firebaseAdminMessagingMinAppVersion(appId))}`)

            return this.firebaseAdminService.sendMessageWithoutNotifee(activeDevice.pushNotificationToken, payload,
                isTransactional, timeToLive, dryRun, appId)

        }
    }
}

export default PushNotificationService
