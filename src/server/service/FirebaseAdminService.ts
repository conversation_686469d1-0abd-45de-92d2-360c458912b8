import {BASE_TYPES, FetchUtilV2, <PERSON><PERSON>} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

import {inject, injectable} from "inversify"
import * as firebaseAdmin from "firebase-admin"
import * as _ from "lodash"
import {StringUtils} from "turbocommons-ts"
import {Notification} from "@notifee/react-native/dist/types/Notification"
import {AndroidGroupAlertBehavior, AndroidStyle} from "@notifee/react-native/dist/types/NotificationAndroid"

import TYPES from "../ioc/types"
import {MetricUtils, ProfilerType} from "../utils/MetricUtils"
import {CommonUtils} from "../utils/CommonUtils"
import {NotificationPayload} from "./PushNotificationService"
import Constants from "../constants"
import {app} from "firebase-admin/lib/firebase-namespace-api"
import App = app.App
import { IRIS_MODELS_TYPES } from "../../iris-models"
import { IFirebaseReportDao } from "../../iris-models/src/daos/IFirebaseReportDao"
import { FirebaseReport } from "@curefit/iris-common"
import { GoogleAuth } from "google-auth-library"

const https = require("https")
const fetch = require("node-fetch")

@injectable()
class FirebaseAdminService {

    private readonly firebaseAdmin: any = {}
    private fcmDataGoogleAuth: any

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(BASE_TYPES.FetchUtilV2) private fetchUtilV2: FetchUtilV2,
        @inject(IRIS_MODELS_TYPES.FirebaseReportDao) private firebaseReportDao: IFirebaseReportDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(TYPES.CommonUtils) private commonUtils: CommonUtils
    ) {

        /*
            We do not init these locally since we do not inject the keys into the environment
            Non existence of keys causes firebase admin to fail, which does not let the app start up
         */

        if (CommonUtils.isEnvironmentLocal()) {
            return
        }
        
        /*
            Create a GoogleAuth instance using Firebase Service Account
        */
        this.fcmDataGoogleAuth = new GoogleAuth({
            credentials: JSON.parse(process.env.GOOGLE_FCMDATA_SERVICE_ACCOUNT || "{}"),
            scopes: ["https://www.googleapis.com/auth/cloud-platform"]
        })

        /*
            CUREFIT app; the main one
         */
        this.firebaseAdmin[Constants.APP_ID_CUREFIT] = firebaseAdmin.initializeApp({
            credential: firebaseAdmin.credential.cert({
                projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
                privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY,
                clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL
            }),
            httpAgent: new https.Agent({
                keepAlive: true,
                timeout: 50000,
                maxSockets: 20,
                maxFreeSockets: 2,
            })
        }, Constants.APP_ID_CUREFIT)

        /*
            LIVEFIT app; first international app; deprecated since we bought onyx
         */
        this.firebaseAdmin[Constants.APP_ID_LIVEFIT] = firebaseAdmin.initializeApp({
            credential: firebaseAdmin.credential.cert({
                projectId: process.env.FIREBASE_ADMIN_PROJECT_ID_LIVEFIT,
                privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY_LIVEFIT,
                clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL_LIVEFIT
            }),
            httpAgent: new https.Agent({
                keepAlive: true,
                timeout: 50000,
                maxSockets: 20,
                maxFreeSockets: 2,
            })
        }, Constants.APP_ID_LIVEFIT)

        /*
            SUGARFIT app; Wellness/Care handles this
         */
        this.firebaseAdmin[Constants.APP_ID_SUGARFIT] = firebaseAdmin.initializeApp({
            credential: firebaseAdmin.credential.cert({
                projectId: process.env.FIREBASE_ADMIN_PROJECT_ID_SUGARFIT,
                privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY_SUGARFIT,
                clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL_SUGARFIT
            }),
            httpAgent: new https.Agent({
                keepAlive: true,
                timeout: 50000,
                maxSockets: 20,
                maxFreeSockets: 2,
            })
        }, Constants.APP_ID_SUGARFIT)

        /*
            ULTRAFIT app; Sugarfit team this
        */
        this.firebaseAdmin[Constants.APP_ID_ULTRAFIT] = firebaseAdmin.initializeApp({
            credential: firebaseAdmin.credential.cert({
                projectId: process.env.FIREBASE_ADMIN_PROJECT_ID_ULTRAFIT,
                privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY_ULTRAFIT,
                clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL_ULTRAFIT
            }),
            httpAgent: new https.Agent({
                keepAlive: true,
                timeout: 50000,
                maxSockets: 20,
                maxFreeSockets: 2,
            })
        }, Constants.APP_ID_ULTRAFIT)

        /*
            Trainer app; Cult owns this
         */
        this.firebaseAdmin[Constants.APP_ID_CULTAPP] = firebaseAdmin.initializeApp({
            credential: firebaseAdmin.credential.cert({
                projectId: process.env.FIREBASE_ADMIN_PROJECT_ID_CULTAPP,
                privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY_CULTAPP,
                clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL_CULTAPP
            }),
            httpAgent: new https.Agent({
                keepAlive: true,
                timeout: 50000,
                maxSockets: 20,
                maxFreeSockets: 2,
            })
        }, Constants.APP_ID_CULTAPP)

        /*
            Teamfit app; Platforms owns this
         */
        this.firebaseAdmin[Constants.APP_ID_TEAMFIT] = firebaseAdmin.initializeApp({
            credential: firebaseAdmin.credential.cert({
                projectId: process.env.FIREBASE_ADMIN_PROJECT_ID_TEAMFIT,
                privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY_TEAMFIT,
                clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL_TEAMFIT
            }),
            httpAgent: new https.Agent({
                keepAlive: true,
                timeout: 50000,
                maxSockets: 20,
                maxFreeSockets: 2,
            })
        }, Constants.APP_ID_TEAMFIT)

        this.firebaseAdmin[Constants.APP_ID_CULTWATCH] = firebaseAdmin.initializeApp({
            credential: firebaseAdmin.credential.cert({
                projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
                privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY,
                clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL
            }),
            httpAgent: new https.Agent({
                keepAlive: true,
                timeout: 50000,
                maxSockets: 20,
                maxFreeSockets: 2,
            })
        }, Constants.APP_ID_CULTWATCH)

    }

    async sendMessageViaNotifee(token: string, notificationPayload: NotificationPayload, isTransactional: boolean,
                                timeToLive: number, dryRun: boolean, appId: string,
                                osName: string): Promise<{ success: boolean, messageId?: string, failReason?: string }> {

        const notifeePayload: Notification = {
            id: notificationPayload.overwriteKey,
            title: notificationPayload.title,

            // Send subtitle only for android; iOS subtitles are over emphasised, product call to not show them
            subtitle: osName.toLowerCase() === "android" ? notificationPayload.subTitle : undefined,

            body: notificationPayload.body,
            data: {
                action: notificationPayload.payload.action,
                analyticData: JSON.stringify(notificationPayload.payload.analyticData),
                meta: !!notificationPayload.payload.meta ? JSON.stringify(notificationPayload.payload.meta) : undefined
            },
            android: {
                ongoing: notificationPayload.ongoing,
                style: StringUtils.isUrl(notificationPayload.image) ? {
                    type: AndroidStyle.BIGPICTURE,
                    picture: notificationPayload.image
                } : {
                    type: AndroidStyle.BIGTEXT,
                    text: notificationPayload.body
                },
                smallIcon: "notify_logo",
                largeIcon: StringUtils.isUrl(notificationPayload.icon) ? notificationPayload.icon : undefined,
                sound: notificationPayload.sound,
                channelId: isTransactional ? Constants.PUSH_NOTIFICATION_TRANSACTIONAL_CHANNEL :
                    Constants.PUSH_NOTIFICATION_PROMOTIONAL_CHANNEL,
                pressAction: { id: notificationPayload.payload.action, launchActivity: "default" },

                //Used for grouping of PNs. This constant is same on App Side.
                groupId: Constants.PUSH_NOTIFICATION_DEFAULT_GROUP_ID,
                groupAlertBehavior: notificationPayload.groupKey ? AndroidGroupAlertBehavior.ALL : undefined,

                actions: notificationPayload.actions && notificationPayload.actions.length ?
                    notificationPayload.actions.map(action => {
                        return { title: action.title, pressAction: {
                            id: action.url,
                            launchActivity: notificationPayload.category === "INTERACTIVE_HABIT_CARD" ? undefined : "default"
                        } }
                }) : undefined,
                sortKey: notificationPayload.sortKey,
                showTimestamp: true,

                showChronometer: !!notificationPayload.chronometerProperties ? notificationPayload.chronometerProperties.showChronometer : undefined,
                chronometerDirection: !!notificationPayload.chronometerProperties && !!notificationPayload.chronometerProperties.showChronometer ? notificationPayload.chronometerProperties.chronometerDirection : undefined,
                timestamp: !!notificationPayload.chronometerProperties && !!notificationPayload.chronometerProperties.showChronometer ? notificationPayload.chronometerProperties.chronometerTimestamp : Date.now(),

                timeoutAfter: notificationPayload.timeoutAfter,
            },
            ios: {
                sound: notificationPayload.sound || "default",
                threadId: notificationPayload.groupKey || undefined,
                categoryId: notificationPayload.category || undefined
            }
        }

        const category = osName.toLowerCase() === "ios" ? notificationPayload.category : undefined
        const isDataNotification = category === "DATA_NOTIFICATION"
        const firebaseMessage: firebaseAdmin.messaging.TokenMessage = {
            notification: osName.toLowerCase() === "ios" ? {
                title: notificationPayload.title,
                body: notificationPayload.body
            } : undefined,
            android: {
                priority: Constants.PUSH_NOTIFICATION_FCM_PRIORITY_HIGH,
                ttl: timeToLive
            },
            apns: {
                headers: {
                    /*
                        https://developer.apple.com/documentation/usernotifications/setting_up_a_remote_notification_server/sending_notification_requests_to_apns/
                    */
                    "apns-priority": isDataNotification ? "5" : "10",
                    "apns-push-type": isDataNotification ? "background" : "alert",
                    "apns-topic": appId === Constants.APP_ID_CUREFIT ?
                        CommonUtils.isEnvironmentStage() ?
                            Constants.IOS_BUNDLE_ID_CUREFIT_STAGE : Constants.IOS_BUNDLE_ID_CUREFIT
                        : CommonUtils.isEnvironmentStage() ?
                            Constants.IOS_BUNDLE_ID_LIVEFIT_STAGE : Constants.IOS_BUNDLE_ID_LIVEFIT
                },
                payload: {
                    aps: {
                        mutableContent: true,
                        category: category,
                        contentAvailable: isDataNotification
                    }
                },
                fcmOptions: {
                    imageUrl: StringUtils.isUrl(notificationPayload.image) ? notificationPayload.image : undefined
                }
            },
            data: {
                notifee: JSON.stringify(notifeePayload)
            },
            token
        }

        return this.sendMessage(firebaseMessage, dryRun, appId)
    }

    async sendMessageWithoutNotifee(token: string, notificationPayload: NotificationPayload, isTransactional: boolean,
                                    timeToLive: number, dryRun: boolean,
                                    appId: string): Promise<{ success: boolean, messageId?: string, failReason?: string }> {
        let channelId: string = isTransactional ? Constants.PUSH_NOTIFICATION_TRANSACTIONAL_CHANNEL :
            Constants.PUSH_NOTIFICATION_PROMOTIONAL_CHANNEL
        if (notificationPayload.android_channel_id) {
            channelId = notificationPayload.android_channel_id
        }
        const firebaseMessage: firebaseAdmin.messaging.TokenMessage = {
            notification: {
                title: notificationPayload.title,
                body: notificationPayload.body,
                imageUrl: StringUtils.isUrl(notificationPayload.image) ? notificationPayload.image : undefined
            },
            android: {
                priority: Constants.PUSH_NOTIFICATION_FCM_PRIORITY_HIGH,
                ttl: timeToLive,
                notification: {
                    sound: notificationPayload.sound,
                    channelId,
                    priority: "default"
                }
            },
            apns: {
                headers: {
                    "apns-priority": "10",
                    "apns-push-type": "alert"
                },
                payload: {
                    aps: {
                        mutableContent: true,
                        sound: notificationPayload.sound,
                        threadId: notificationPayload.groupKey || undefined,
                    }
                },
                fcmOptions: {
                    imageUrl: StringUtils.isUrl(notificationPayload.image) ? notificationPayload.image : undefined
                }
            },
            data: {
                // New versions of the curefit app use `data.payload` directly
                payload: JSON.stringify(notificationPayload.payload),

                // Some old versions of the `curefit` (< 8.63), and `teamfit` depend on `data.action` and `data.analyticData`
                // We should remove this eventually
                action: notificationPayload.payload.action,
                analyticData: JSON.stringify(notificationPayload.payload.analyticData)
            },
            token
        }

        return this.sendMessage(firebaseMessage, dryRun, appId)
    }

    /*
        This method sends the payload via FCM admin
        We currently support 5 apps, identified by `appId`
        The correct admin object is extracted from the `this.firebaseAdmin` object using this `appId`
     */
    async sendMessage(messagePayload: any, dryRun: boolean,
                      appId: string): Promise<{ success: boolean, messageId?: string, failReason?: string }> {
        const message = `FirebaseAdminService::sendMessage Sending message` +
            `Payload : ${JSON.stringify(messagePayload)} `
        this.logger.info(message)

        let result: any
        try {
            let firebaseAdmin: App = this.firebaseAdmin[appId.toLowerCase()]

            if (!firebaseAdmin) { throw new Error(`Firebase admin not found for app id ${appId}`) }

            const apiLatencyTimerContext =
                this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "FirebaseAdminSDK")
            const response = await firebaseAdmin.messaging().send(messagePayload, dryRun)
            this.metricUtils.endProfilingTimer(apiLatencyTimerContext)

            /*
                Response is of type "projects/cult-155917/messages/0:1610132033837447%b4329a63b4329a63"
                messageId is the last part
             */
            const messageId = response.split("/").slice(-1)[0]
            result = { "success": true, messageId }

        } catch (err) {
            let errorCode: string = _.get(err, "code", "")

            /*
                FCM Errors are of the type "messaging/*"
                Ref: https://firebase.google.com/docs/cloud-messaging/send-message#admin_sdk_error_reference
             */

            if (errorCode.startsWith(Constants.FCM_ADMIN_RESPONSE_ERROR_CODE_PREFIX)) {
                errorCode = errorCode.split("/")[1]
                result = {
                    success: false,
                    failReason: `${Constants.FCM_ADMIN_FAIL_REASON_PREFIX}_${errorCode}`
                }
            } else {
                // Unexpected error; Raise Rollbar
                const errorMessage = `FirebaseAdminService::sendMessage Failed with error ${err.toString()}. ` +
                    `Payload sent: ${JSON.stringify(messagePayload)} `
                this.logger.error(errorMessage)
                this.rollbarService.sendError(new Error(errorMessage))
                result = { success: false }
            }
        }

        return result
    }

    /*
        Returns the OAuth2 access token for fcmdata APIs
     */
    async getFcmDataAccessToken(): Promise<String> {
        if (! this.fcmDataGoogleAuth) {
            throw new Error("Failed to create Google Auth")
        }
        const client = await this.fcmDataGoogleAuth.getClient()
        const data = await client.getAccessToken()
        return data.token
    }

    /* 
        This method fetches the delivery data reports by calling the firebase API  
    */
    public async getDeliveryDataReportsFromFirebase(): Promise<any> {
        try {
            this.logger.info(`FirebaseAdminService::getDeliveryReports weekly scheduled call`)
            const authToken = await this.getFcmDataAccessToken()
            const headers = {
                "Authorization": `Bearer ${authToken}`,
                "Content-Type": "application/json"
            }
            const response = await fetch(
                Constants.getFirebaseDeliveryDataUrl(Constants.FIREBASE_PROJECT, Constants.FIREBASE_APP_ID),
                this.fetchUtilV2.get({headers: headers}))
            const responseData = await this.fetchUtilV2.parseResponse<any>(response)
            this.logger.info(`FirebaseAdminService::getDeliveryDataReportsFromFirebase response: ${responseData} `)
            return responseData
        }
        catch (err) {
            this.logger.error(`FirebaseAdminService::getDeliveryDataReportsFromFirebase failed with error: ${err}`)
            throw err
        }

    }

    /* 
        This method saves the delivery data reports fetched from firebase to the iris-db 
    */
    public async createWeeklyReport(reportData: FirebaseReport[]): Promise<Boolean[]> {
        try {
            this.logger.info("FirebaseAdminService::createWeeklyReport firebase report save to DB")
            return await this.firebaseReportDao.bulkCreate(reportData)
        }
        catch (err) {
            this.logger.error(`FirebaseAdminService:createWeeklyReport failed with error: ${err}`)
            throw err
        }
    }
}

export default FirebaseAdminService

