import {BASE_TYPES, ILogger} from "@curefit/base"
import {IMultiCrudKeyValue, MultiRedisAccess, REDIS_TYPES} from "@curefit/redis-utils"
import {CacheAccessImpl, ICacheAccess, Key} from "@curefit/cache-utils"
import {CreativeType} from "../../iris-common"

import {inject, injectable} from "inversify"

import Constants from "../constants"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

import TYPES from "../ioc/types"
import FeatureUtils from "../utils/FeatureUtils"

const hash = require("object-hash")

class NotificationLogKey implements Key {

    key: string

    constructor(object: any) {
        this.key = `iris_notification__${hash.MD5(object)}`
    }

    hash(): string {
        return undefined
    }

    lookupKey(): string {
        return this.key
    }
}

class PromotionalNotificationLogKey implements Key {

    key: string

    constructor(userId: string, creativeType: CreativeType) {
        this.key = `iris_promo_notification__${userId}_${creativeType}`
    }

    hash(): string {
        return undefined
    }

    lookupKey(): string {
        return this.key
    }
}

@injectable()
class NotificationLogService {

    private notificationLogCache: ICacheAccess<NotificationLogKey, boolean>
    private promotionalNotificationLogCache: ICacheAccess<PromotionalNotificationLogKey, boolean>

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(REDIS_TYPES.MultiRedisAccess) multiRedisAccess: MultiRedisAccess,
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.FeatureUtils) private featureUtils: FeatureUtils
    ) {
        this.notificationLogCache = new CacheAccessImpl<NotificationLogKey, boolean>
            (multiCrudKeyValueDao.getICrudKeyValue("BUMBLE"))
        this.promotionalNotificationLogCache = new CacheAccessImpl<PromotionalNotificationLogKey, boolean>
            (multiCrudKeyValueDao.getICrudKeyValue("BUMBLE"))
    }

    private loadServiceConfigs(): {
                    notificationExpiry: Map<CreativeType, number>,
                    promotionalNotificationThrottlingLimit: Map<CreativeType, number>} {
        // Load service configs
        const notificationExpiry = new Map<CreativeType, number> (
            this.featureUtils.getConfigValue(Constants.DUPLICATE_NOTIFICATION_EXPIRY_KEY, Constants.DUPLICATE_NOTIFICATION_EXPIRY_DEFAULT))
        const promotionalNotificationThrottlingLimit = new Map<CreativeType, number> (
            this.featureUtils.getConfigValue(Constants.PROMOTIONAL_NOTIFICATION_THROTTLING_LIMIT_KEY,
                Constants.PROMOTIONAL_NOTIFICATION_THROTTLING_LIMIT_DEFAULT))
        return {
            notificationExpiry,
            promotionalNotificationThrottlingLimit
        }
    }

    async logNotification(notification: any, userId: string, creativeType: CreativeType,
                          isTransactional: boolean, dryRun?: boolean): Promise<boolean> {
        this.logger.debug(`NotificationLogService::logNotification Entering log notification for` +
            `${JSON.stringify(notification)} for user ${userId} ${creativeType} ${isTransactional} ${dryRun}`)

        // Load service configs
        const {
            notificationExpiry,
            promotionalNotificationThrottlingLimit
        } = this.loadServiceConfigs()

        let expiry
        // Mark notification log for duplicate checks
        try {
            this.logger.debug(`NotificationLogService::logNotification logging notificationLogCache ` +
                `${JSON.stringify(notification)} for user ${userId}`)

            expiry = dryRun ? 0 : notificationExpiry.get(creativeType)
        expiry && await this.notificationLogCache.create(
                new NotificationLogKey({ ...notification, userId }), true, expiry)
        } catch (error) {
            const errorMessage = `NotificationLogService::logNotification Error while logging notificationLogCache ` +
                `${JSON.stringify(notification)} ${userId} ${creativeType}. Error: ${error.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
        }

        // Mark notification for throttling
        // - Only for promotional notifications
        // - Only for channels where throttling limit is not 0
        if (!isTransactional && promotionalNotificationThrottlingLimit.get(creativeType)) {
            try {
                this.logger.debug(`NotificationLogService::logNotification logging promotionalNotificationLogCache ` +
                    `${JSON.stringify(notification)} for user ${userId} and creative ${creativeType}`)

                expiry = dryRun ? 0 : promotionalNotificationThrottlingLimit.get(creativeType)
                expiry && await this.promotionalNotificationLogCache.create(
                    new PromotionalNotificationLogKey(userId, creativeType), true, expiry)
            } catch (error) {
                const errorMessage = `NotificationLogService::logNotification Error while logging ` +
                    `promotionalNotificationLogCache ${JSON.stringify(notification)} ${userId} ${creativeType}. ` +
                    `Error: ${error.toString()}`
                this.logger.error(errorMessage)
                this.rollbarService.sendError(new Error(errorMessage))
            }
        }
        return true
    }

    public async isDuplicate(notification: any, userId: string, dryRun?: boolean) {
        this.logger.debug(`NotificationLogService::isDuplicate Entering for ${JSON.stringify(notification)} user ${userId}`)

        try {
            const notificationExists =
                await this.notificationLogCache.get(new NotificationLogKey({ ...notification, userId }))
            this.logger.debug(`NotificationLogService::isDuplicate notificationExists: ${notificationExists}`)
            return dryRun ? false : notificationExists === true
        } catch (error) {
            const errorMessage = `NotificationLogService::isDuplicate Error while fetching NotificationLogKey ` +
                `${JSON.stringify(notification)} ${userId}. Error: ${error.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            return false
        }
    }

    public async throttleNotification(userId: string, creativeType: CreativeType,
                                      isTransactional: boolean, dryRun?: boolean): Promise<boolean> {
        this.logger.debug(`NotificationLogService::throttleNotification Entering for user ${userId} ` +
            `${creativeType} ${isTransactional} ${dryRun}`)

        // Load service configs
        const {
            promotionalNotificationThrottlingLimit
        } = this.loadServiceConfigs()

        // Transactional notifications do not get throttled
        if (isTransactional) {
            return false
        }

        // Config allows us to throttle selective channels
        if (promotionalNotificationThrottlingLimit.get(creativeType) === 0) {
            return false
        }

        try {
            const notificationRecentlySent =
                await this.promotionalNotificationLogCache.get(new PromotionalNotificationLogKey(userId, creativeType))
            this.logger.debug(`NotificationLogService::throttleNotification notificationRecentlySent: ${notificationRecentlySent}`)
            return dryRun ? false : notificationRecentlySent === true
        } catch (error) {
            const errorMessage = `NotificationLogService::throttleNotification Error while fetching ` +
                `PromotionalNotificationLogKey ${userId} ${creativeType}. Error: ${error.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            return false
        }
    }

    public async getPromotionalNotificationKey(userId: string, creativeType: CreativeType): Promise<boolean> {
        return this.promotionalNotificationLogCache.get(new PromotionalNotificationLogKey(userId, creativeType))
    }

    public async evictPromotionalNotificationLogKey(userId: string, creativeType: CreativeType): Promise<boolean> {
        try {
            await this.promotionalNotificationLogCache.delete(new PromotionalNotificationLogKey(userId, creativeType))
        } catch (error) {
            this.logger.error(`NotificationLogService::evictPromotionalNotificationLogKey error: ${error.toString()}`)
        }
        return true
    }

}

export default NotificationLogService