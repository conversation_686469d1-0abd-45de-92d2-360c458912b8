import {BASE_TYPES, Configuration, FetchUtil, Logger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {Tenant} from "@curefit/user-common"

import * as _ from "lodash"
import {inject, injectable} from "inversify"
const fetch = require("node-fetch")

import Constants from "../constants"
import {NotificationPayload} from "./PushNotificationService"
import TYPES from "../ioc/types"
import {MetricUtils, ProfilerType} from "../utils/MetricUtils"
import {AndroidInfo, IFirebaseDynamicPayload, IOSInfo} from "../models/Applinks"
import {AppInfo, LinkPayload, PurchaseEvent} from "../../iris-common"
const { v4: uuidv4 } = require('uuid')
import LinkHelper from "../helpers/LinkHelper"
import {CommonUtils} from "../utils/CommonUtils"

export interface IFirebaseService {
    sendNudgeViaLegacyApi(token: string, payload: any, sendPayloadInsideData: boolean, notificationId: string, isTransactional: boolean,
                          timeToLive: number, appId?: string, dryRun?: boolean): Promise<{ success: boolean, messageId?: string, failReason?: string }>
    createUniversalDeepLinkUrl(tenant: Tenant, link: LinkPayload, appInfo?: AppInfo, userId?: string): Promise<{ url: string }>
    sendPurchaseEvent(userId: string, purchaseEventData: any): Promise<any>
}

interface FirebasePayload {
    collapse_key?: string,
    time_to_live: number,
    notification?: any,
    data?: any,
    priority?: any,
    "content-available": boolean,
    registration_ids: string[],
    dry_run?: boolean
}

const CFAppAndroidConfig = (link: LinkPayload, appInfo: AppInfo): AndroidInfo  => {
    const payload: AndroidInfo = { androidPackageName: "fit.cure.android" }
    if (link.redirectMWeb) {
        payload.androidFallbackLink = LinkHelper.appendHttps(link.weblink)
    }
    const minAndroidVC = _.get(appInfo, "minAndroidVersionCode", undefined)
    if (minAndroidVC) {
        payload.androidMinPackageVersionCode = minAndroidVC
    }
    return payload
}

const CFAppIOSConfig = (link: LinkPayload): IOSInfo => {
    const payload: IOSInfo = {
        iosBundleId: "fit.cure.ios",
        iosCustomScheme: "curefit",
        iosAppStoreId: "1217794588"
    }
    if (link.redirectMWeb) {
        payload.iosFallbackLink = LinkHelper.appendHttps(link.weblink)
    }
    return payload
}

const CF_APP_DOMAIN_URI_PREFIX = "dl.cure.fit"

@injectable()
class FirebaseService implements IFirebaseService {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(TYPES.CommonUtils) private commonUtils: CommonUtils,
        @inject(BASE_TYPES.Configuration) private appConfig: Configuration
    ) {
    }

    // sendPayloadInsideData added for supporting lower versions after adding image support in android.
    async sendNudgeViaLegacyApi(token: string, notificationPayload: NotificationPayload, sendPayloadInsideData: boolean,
                                notificationId: string, isTransactional: boolean, timeToLive: number, appId?: string,
                                dryRun?: boolean): Promise<{ success: boolean, messageId?: string, failReason?: string }> {

        notificationPayload.android_channel_id = isTransactional ? Constants.PUSH_NOTIFICATION_TRANSACTIONAL_CHANNEL :
            Constants.PUSH_NOTIFICATION_PROMOTIONAL_CHANNEL
        const firebasePayload: FirebasePayload = {
            time_to_live: timeToLive,
            notification: notificationPayload,
            data: sendPayloadInsideData === true ? notificationPayload : null,
            "content-available": true,
            priority: isTransactional ? Constants.PUSH_NOTIFICATION_FCM_PRIORITY_HIGH :
                Constants.PUSH_NOTIFICATION_FCM_PRIORITY_NORMAL,
            registration_ids: [ token ]
        }

        this.logger.debug(`FirebaseService::sendNudge: firebasePayload: ${JSON.stringify(firebasePayload)}`)

        // For testing. Dry run sends request without actually sending notifications to the device
        if (dryRun) { firebasePayload.dry_run = dryRun}

        // Passing the data as separate key and as well within  notification key  to cater to android and ios respectively.
        const finalPayload = sendPayloadInsideData === true ? firebasePayload : Object.assign(firebasePayload, notificationPayload)
        return this.sendFirebaseMessageViaLegacyApi(finalPayload, notificationId, appId)
    }

    async sendFirebaseMessageViaLegacyApi(payload: FirebasePayload, notificationId: string, appId?: string) {
        const timerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF, "sendFirebaseMessage")
        const apiKey = Constants.getFireBaseApiKey(appId)
        try {
            const apiLatencyTimerContext =
                this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "Firebase")
            const response = await this.commonUtils.fetchWithRetry(5, 1000,
                Constants.getFireBaseUrl("/fcm/send"),
                this.fetchHelper.post(payload, { Authorization: apiKey }))
            this.metricUtils.endProfilingTimer(apiLatencyTimerContext)

            const parsedResponse = await this.fetchHelper.parseResponse<any>(response)
            const messageId = _.get(_.head(_.get(parsedResponse, "results", [])), "message_id")

            let result: any

            if (!messageId) {
                this.logger.info(`FirebaseService::sendFirebaseMessage message id missing for ` +
                    `notificationId: ${notificationId} token: ${payload.registration_ids} ` +
                    `response: ${JSON.stringify(parsedResponse)}`)

                const error = _.get(_.head(_.get(parsedResponse, "results", [])), "error")
                const failReason = error ? `${Constants.FCM_FAIL_REASON_PREFIX}_${error}` : undefined

                result = { "success": false, failReason }

            } else {
                this.logger.debug(`FirebaseService::sendFirebaseMessage message id present for ` +
                    `notificationId: ${notificationId} token: ${payload.registration_ids} ` +
                    `response: ${JSON.stringify(parsedResponse)}`)

                result = { "success": true, messageId }
            }

            this.metricUtils.endProfilingTimer(timerContext)
            return result
        } catch (error) {
            const errorMessage = `FirebaseService::sendFirebaseMessage Failed for payload ` +
                `${JSON.stringify(payload)} with error ${error.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            return { "success": false }
        }
    }

    public async createUniversalDeepLinkUrl(tenant: Tenant, link: LinkPayload, appInfo: AppInfo,
                                            userId?: string): Promise<{ url: string; }> {
        const applink = LinkHelper.createFirebaseLink(link)
        const redirect = !!link.enableForcedRedirect
        /* construct cure.fit app firebase deep link url */
        const payload: IFirebaseDynamicPayload = {
            dynamicLinkInfo: {
                link: applink,
                domainUriPrefix: CF_APP_DOMAIN_URI_PREFIX,
                androidInfo: CFAppAndroidConfig(link, appInfo),
                iosInfo: CFAppIOSConfig(link),
                navigationInfo: {
                    enableForcedRedirect: redirect
                },
                analyticsInfo: link.analyticsInfo,
                // firebase doesn't accept og_type meta
                socialMetaTagInfo: _.omit(link.socialMetaTagInfo, "socialType")
            },
            suffix: {
                option: "SHORT"
            }
        }

        const { url, apiKey } = Constants.FirebaseConfigMap[tenant]

        let full_url = `${url}/shortLinks?key=${apiKey}`
        if (userId) {
            full_url = `${full_url}&quotaUser=${userId}`
        }

        this.logger.debug(`FirebaseService::createUniversalDeepLinkUrl request of firebase ` +
            `url ${JSON.stringify(payload)}`)
        return fetch(full_url, this.fetchHelper.post(payload)).then((response: any) => {
            this.logger.debug(`FirebaseService::createUniversalDeepLinkUrl response of firebase ` +
                `links ${JSON.stringify(response)}`)
            return this.fetchHelper.parseResponse<any>(response).then((res) => {
                this.logger.debug(`FirebaseService::createUniversalDeepLinkUrl parsed response of firebase links ` +
                    `links ${JSON.stringify(res)}`)
                return { url: res.shortLink }
            })
        })
    }

    async sendPurchaseEvent(userId: string, purchaseEventData: PurchaseEvent) {
        this.logger.info(`rahul_logs_this: sendPurchaseEvent - Starting for userId: ${userId}`)
        this.logger.info(`rahul_logs_this: sendPurchaseEvent - Input data: ${JSON.stringify(purchaseEventData)}`)

        try {
            this.logger.info(`rahul_logs_this: sendPurchaseEvent - Getting device for userId: ${userId}`)
            const device = await this.getDevice(userId);
            this.logger.info(`rahul_logs_this: sendPurchaseEvent - Device data: ${JSON.stringify(device)}`)

            const firebasePayload = await this.getPurchaseEventPayload(device, userId, purchaseEventData)
            this.logger.info(`FirebaseService::sendPurchaseEvent firebasePayload ${JSON.stringify(firebasePayload)}`)

            this.logger.info(`rahul_logs_this: sendPurchaseEvent - Getting Firebase config`)

            // First try to get config from environment-specific configuration (STAGE.json, ALPHA.json, etc.)
            const appConfig = this.appConfig.getConfiguration()
            const envFirebaseConfig = _.get(appConfig, "firebaseAnalytics")

            // Fallback to constants if environment config is not available
            const constantsConfig = Constants.FirebaseAnalyticsConfigMap[Tenant.CUREFIT_APP]

            let measurementId = envFirebaseConfig?.measurementId || constantsConfig?.measurementId
            let apiSecret = envFirebaseConfig?.apiSecret || constantsConfig?.apiSecret
            let baseUrl = envFirebaseConfig?.baseUrl || constantsConfig?.url || "https://www.google-analytics.com/mp/collect"
            let timeout = envFirebaseConfig?.timeout || 10000
            let debugMode = envFirebaseConfig?.debugMode || false

            // Final fallback for Alpha environment (backward compatibility)
            if (!measurementId || !apiSecret) {
                this.logger.info(`rahul_logs_this: sendPurchaseEvent - Using hardcoded fallback Firebase config`)
                measurementId = measurementId || "G-03XGFXZPY3"
                apiSecret = apiSecret || "ogM9tVtsTTm3AuKxWxyuHQ"
            }

            if (!measurementId || !apiSecret) {
                throw new Error("Firebase Analytics configuration is missing or incomplete")
            }

            this.logger.info(`rahul_logs_this: sendPurchaseEvent - Using config source: ${envFirebaseConfig ? 'environment-specific' : 'constants/fallback'}`)

            const url = `${baseUrl}?measurement_id=${measurementId}&api_secret=${apiSecret}`
            this.logger.info(`rahul_logs_this: sendPurchaseEvent - Firebase URL: ${url}`)

            const response = await fetch(url, this.fetchHelper.post(firebasePayload))

            this.logger.info(`rahul_logs_this: sendPurchaseEvent - Parsing Firebase response`)
            const parsedResponse = await this.fetchHelper.parseResponse<any>(response)
            this.logger.info(`FirebaseService::sendPurchaseEvent response: ${JSON.stringify(parsedResponse)}`)

            return parsedResponse

        } catch (error) {
            const errorMessage = `FirebaseService::sendPurchaseEvent Failed for userId ${userId} with error ${error.toString()}`

            if (error.response) {
                this.logger.error(`rahul_logs_this: sendPurchaseEvent - HTTP Error Status: ${error.response.status}`)
                this.logger.error(`rahul_logs_this: sendPurchaseEvent - HTTP Error Body: ${JSON.stringify(error.response.data)}`)
            }

            if (error.code) {
                this.logger.error(`rahul_logs_this: sendPurchaseEvent - Error Code: ${error.code}`)
            }

            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            throw error
        }
    }

    async getDevice(userId: string) {
        try {
            const cfAPIConfig = _.get(this.appConfig.getConfiguration(), "cfAPIConfig")
            if (!cfAPIConfig || !cfAPIConfig.baseUrl) {
                this.logger.warn(`FirebaseService::getDevice cfAPIConfig is missing, using fallback for userId: ${userId}`)
                return null
            }

            const url = `${cfAPIConfig.baseUrl}/device/recent/app/loggedIn?userId=${userId}`
            const response = await fetch(url, this.fetchHelper.get(cfAPIConfig.headers))
            const value = await this.fetchHelper.parseResponse<any>(response)
            const device = value.data
            return device
        } catch (error) {
            this.logger.warn(`rahul_logs_this: getDevice - Error type: ${error.constructor.name}`)

            if (error.response) {
                this.logger.warn(`rahul_logs_this: getDevice - HTTP Error Status: ${error.response.status}`)
                this.logger.warn(`rahul_logs_this: getDevice - HTTP Error Body: ${JSON.stringify(error.response.data)}`)
            }

            this.logger.warn(`FirebaseService::getDevice Failed to get device for userId: ${userId}. Error: ${error.toString()}`)
            return null
        }
    }

    async getPurchaseEventPayload(device: any, userId: string, purchaseEventData: PurchaseEvent): Promise<any> {
        this.logger.info(`rahul_logs_this: getPurchaseEventPayload - Input purchase data: ${JSON.stringify(purchaseEventData)}`)
        this.logger.info(`rahul_logs_this: getPurchaseEventPayload - Device availability: ${device ? 'available' : 'null'}`)

        this.logger.info(`rahul_logs_this: getPurchaseEventPayload - Getting client ID`)
        const clientId = this.getClientId(device, userId)

        // generating session data for attribution
        const sessionId = this.generateSessionId()
        const engagementTime = this.generateEngagementTime()

        // Parse and validate price
        const priceValue = parseFloat(purchaseEventData.price)
        if (isNaN(priceValue)) {
            this.logger.warn(`rahul_logs_this: getPurchaseEventPayload - Invalid price value: ${purchaseEventData.price}, defaulting to 0`)
        }

        // Generate transaction ID
        const transactionId = `${purchaseEventData.productId}_${Date.now()}`
        const timestamp = (Date.now() * 1000).toString()

        // Get device-specific information
        this.logger.info(`rahul_logs_this: getPurchaseEventPayload - Getting device-specific information`)
        const os = this.getOS(device)
        const advertiserInfo = this.getAdvertiserId(device)

        this.logger.info(`rahul_logs_this: getPurchaseEventPayload - Device OS: ${os || 'unknown'}`)
        this.logger.info(`rahul_logs_this: getPurchaseEventPayload - Advertiser info: ${JSON.stringify(advertiserInfo)}`)

        // Build event parameters
        const eventParams: any = {
            transaction_id: transactionId,
            value: priceValue,
            currency: "INR",
            item_name: purchaseEventData.productName,
            quantity: 1,
            platform: "backend",
            session_id: sessionId,
            engagement_time_msec: engagementTime,
            logged_in: true,
            pack_type: purchaseEventData.packType || "none"
        }

        // Add OS information if available
        if (os) {
            eventParams.os = os
            this.logger.info(`rahul_logs_this: getPurchaseEventPayload - Added OS parameter: ${os}`)
        }

        // Add advertiser ID if available
        if (advertiserInfo) {
            eventParams[advertiserInfo.key] = advertiserInfo.value
            this.logger.info(`rahul_logs_this: getPurchaseEventPayload - Added advertiser ID: ${advertiserInfo.key}=${advertiserInfo.value}`)
        }

        const firebasePayload: any = {
            client_id: clientId,
            user_id: userId,
            timestamp_micros: timestamp,
            events: [{
                name: "purchase",
                params: eventParams
            }]
        }

        this.logger.info(`rahul_logs_this: getPurchaseEventPayload - Final payload size: ${JSON.stringify(firebasePayload).length} bytes`)

        return firebasePayload
    }
    private getClientId(device: any, userId: string): string {
        this.logger.info(`rahul_logs_this: getClientId - Getting client ID for userId: ${userId}`)

        // Priority order for client ID:
        // 1. DeviceId + UserId combination (best for unique identification)
        // 2. Device advertising ID (good for attribution)
        // 3. User ID (fallback)
        // 4. Random UUID (if no device/user info available)

        if (device?.deviceId && userId) {
            const combinedClientId = `${device.deviceId}_${userId}`
            return combinedClientId
        }

        if (device?.activeDevice?.advertiserId) {
            const fullAdvertiserId = device.activeDevice.advertiserId
            const advertiserId = fullAdvertiserId.split("|")?.[0]
            return advertiserId
        }

        if (userId) {
            return userId
        }

        // Generate random UUID for attribution when no device/user info available
        const randomClientId = uuidv4()
        return randomClientId
    }



    private generateSessionId(): string {
        const timestamp = Date.now()
        const randomComponent = Math.random().toString(36).substring(2, 8)
        const sessionId = `${timestamp}_${randomComponent}`
        return sessionId
    }

    private generateEngagementTime(): number {
        const minEngagement = 1000   // 1 second minimum
        const maxEngagement = 30000  // 30 seconds maximum
        const engagementTime = Math.floor(Math.random() * (maxEngagement - minEngagement + 1)) + minEngagement
        return engagementTime
    }

    private getOS(device: any): "Android" | "iOS" | undefined {
        this.logger.info(`rahul_logs_this: getOS - Checking device OS`)
        const osName = device?.activeDevice?.osName?.toLowerCase()
        this.logger.info(`rahul_logs_this: getOS - Raw OS name: ${osName}`)

        switch (osName) {
            case "android":
                this.logger.info(`rahul_logs_this: getOS - Detected Android`)
                return "Android"
            case "ios":
                this.logger.info(`rahul_logs_this: getOS - Detected iOS`)
                return "iOS"
            default:
                this.logger.info(`rahul_logs_this: getOS - Unknown or missing OS`)
                return undefined
        }
    }

    private getAdvertiserId(device: any): { key: string, value: string } | undefined {
        this.logger.info(`rahul_logs_this: getAdvertiserId - Getting advertiser ID`)
        const os = this.getOS(device)

        if (_.isNil(os)) {
            this.logger.info(`rahul_logs_this: getAdvertiserId - No OS detected, cannot get advertiser ID`)
            return undefined
        }

        const fullAdvertiserId = device?.activeDevice?.advertiserId
        this.logger.info(`rahul_logs_this: getAdvertiserId - Full advertiser ID: ${fullAdvertiserId}`)

        const advertiserId = fullAdvertiserId?.split("|")?.[0]
        if (_.isNil(advertiserId)) {
            this.logger.info(`rahul_logs_this: getAdvertiserId - No advertiser ID found`)
            return undefined
        }

        this.logger.info(`rahul_logs_this: getAdvertiserId - Parsed advertiser ID: ${advertiserId}`)

        switch (os) {
            case "Android":
                this.logger.info(`rahul_logs_this: getAdvertiserId - Android AAID detected`)
                return {
                    key: "aaid",
                    value: advertiserId
                }
            case "iOS":
                this.logger.info(`rahul_logs_this: getAdvertiserId - iOS IDFA detected`)
                return {
                    key: "idfa",
                    value: advertiserId
                }
            default:
                this.logger.info(`rahul_logs_this: getAdvertiserId - Unknown OS, cannot determine advertiser ID type`)
                return undefined
        }
    }
    
}

export default FirebaseService
