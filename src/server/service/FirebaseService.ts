import {BASE_TYPES, Configuration, FetchUtil, Logger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {Tenant} from "@curefit/user-common"

import * as _ from "lodash"
import {inject, injectable} from "inversify"
const fetch = require("node-fetch")

import Constants from "../constants"
import {NotificationPayload} from "./PushNotificationService"
import TYPES from "../ioc/types"
import {MetricUtils, ProfilerType} from "../utils/MetricUtils"
import {AndroidInfo, IFirebaseDynamicPayload, IOSInfo} from "../models/Applinks"
import {AppInfo, LinkPayload, PurchaseEvent} from "../../iris-common"
const { v4: uuidv4 } = require('uuid')
import LinkHelper from "../helpers/LinkHelper"
import {CommonUtils} from "../utils/CommonUtils"

export interface IFirebaseService {
    sendNudgeViaLegacyApi(token: string, payload: any, sendPayloadInsideData: boolean, notificationId: string, isTransactional: boolean,
                          timeToLive: number, appId?: string, dryRun?: boolean): Promise<{ success: boolean, messageId?: string, failReason?: string }>
    createUniversalDeepLinkUrl(tenant: Tenant, link: LinkPayload, appInfo?: AppInfo, userId?: string): Promise<{ url: string }>
    sendPurchaseEvent(userId: string, purchaseEventData: any): Promise<any>
}

interface FirebasePayload {
    collapse_key?: string,
    time_to_live: number,
    notification?: any,
    data?: any,
    priority?: any,
    "content-available": boolean,
    registration_ids: string[],
    dry_run?: boolean
}

const CFAppAndroidConfig = (link: LinkPayload, appInfo: AppInfo): AndroidInfo  => {
    const payload: AndroidInfo = { androidPackageName: "fit.cure.android" }
    if (link.redirectMWeb) {
        payload.androidFallbackLink = LinkHelper.appendHttps(link.weblink)
    }
    const minAndroidVC = _.get(appInfo, "minAndroidVersionCode", undefined)
    if (minAndroidVC) {
        payload.androidMinPackageVersionCode = minAndroidVC
    }
    return payload
}

const CFAppIOSConfig = (link: LinkPayload): IOSInfo => {
    const payload: IOSInfo = {
        iosBundleId: "fit.cure.ios",
        iosCustomScheme: "curefit",
        iosAppStoreId: "1217794588"
    }
    if (link.redirectMWeb) {
        payload.iosFallbackLink = LinkHelper.appendHttps(link.weblink)
    }
    return payload
}

const CF_APP_DOMAIN_URI_PREFIX = "dl.cure.fit"

@injectable()
class FirebaseService implements IFirebaseService {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(TYPES.CommonUtils) private commonUtils: CommonUtils,
        @inject(BASE_TYPES.Configuration) private appConfig: Configuration
    ) {
    }

    // sendPayloadInsideData added for supporting lower versions after adding image support in android.
    async sendNudgeViaLegacyApi(token: string, notificationPayload: NotificationPayload, sendPayloadInsideData: boolean,
                                notificationId: string, isTransactional: boolean, timeToLive: number, appId?: string,
                                dryRun?: boolean): Promise<{ success: boolean, messageId?: string, failReason?: string }> {

        notificationPayload.android_channel_id = isTransactional ? Constants.PUSH_NOTIFICATION_TRANSACTIONAL_CHANNEL :
            Constants.PUSH_NOTIFICATION_PROMOTIONAL_CHANNEL
        const firebasePayload: FirebasePayload = {
            time_to_live: timeToLive,
            notification: notificationPayload,
            data: sendPayloadInsideData === true ? notificationPayload : null,
            "content-available": true,
            priority: isTransactional ? Constants.PUSH_NOTIFICATION_FCM_PRIORITY_HIGH :
                Constants.PUSH_NOTIFICATION_FCM_PRIORITY_NORMAL,
            registration_ids: [ token ]
        }

        this.logger.debug(`FirebaseService::sendNudge: firebasePayload: ${JSON.stringify(firebasePayload)}`)

        // For testing. Dry run sends request without actually sending notifications to the device
        if (dryRun) { firebasePayload.dry_run = dryRun}

        // Passing the data as separate key and as well within  notification key  to cater to android and ios respectively.
        const finalPayload = sendPayloadInsideData === true ? firebasePayload : Object.assign(firebasePayload, notificationPayload)
        return this.sendFirebaseMessageViaLegacyApi(finalPayload, notificationId, appId)
    }

    async sendFirebaseMessageViaLegacyApi(payload: FirebasePayload, notificationId: string, appId?: string) {
        const timerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF, "sendFirebaseMessage")
        const apiKey = Constants.getFireBaseApiKey(appId)
        try {
            const apiLatencyTimerContext =
                this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "Firebase")
            const response = await this.commonUtils.fetchWithRetry(5, 1000,
                Constants.getFireBaseUrl("/fcm/send"),
                this.fetchHelper.post(payload, { Authorization: apiKey }))
            this.metricUtils.endProfilingTimer(apiLatencyTimerContext)

            const parsedResponse = await this.fetchHelper.parseResponse<any>(response)
            const messageId = _.get(_.head(_.get(parsedResponse, "results", [])), "message_id")

            let result: any

            if (!messageId) {
                this.logger.info(`FirebaseService::sendFirebaseMessage message id missing for ` +
                    `notificationId: ${notificationId} token: ${payload.registration_ids} ` +
                    `response: ${JSON.stringify(parsedResponse)}`)

                const error = _.get(_.head(_.get(parsedResponse, "results", [])), "error")
                const failReason = error ? `${Constants.FCM_FAIL_REASON_PREFIX}_${error}` : undefined

                result = { "success": false, failReason }

            } else {
                this.logger.debug(`FirebaseService::sendFirebaseMessage message id present for ` +
                    `notificationId: ${notificationId} token: ${payload.registration_ids} ` +
                    `response: ${JSON.stringify(parsedResponse)}`)

                result = { "success": true, messageId }
            }

            this.metricUtils.endProfilingTimer(timerContext)
            return result
        } catch (error) {
            const errorMessage = `FirebaseService::sendFirebaseMessage Failed for payload ` +
                `${JSON.stringify(payload)} with error ${error.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            return { "success": false }
        }
    }

    public async createUniversalDeepLinkUrl(tenant: Tenant, link: LinkPayload, appInfo: AppInfo,
                                            userId?: string): Promise<{ url: string; }> {
        const applink = LinkHelper.createFirebaseLink(link)
        const redirect = !!link.enableForcedRedirect
        /* construct cure.fit app firebase deep link url */
        const payload: IFirebaseDynamicPayload = {
            dynamicLinkInfo: {
                link: applink,
                domainUriPrefix: CF_APP_DOMAIN_URI_PREFIX,
                androidInfo: CFAppAndroidConfig(link, appInfo),
                iosInfo: CFAppIOSConfig(link),
                navigationInfo: {
                    enableForcedRedirect: redirect
                },
                analyticsInfo: link.analyticsInfo,
                // firebase doesn't accept og_type meta
                socialMetaTagInfo: _.omit(link.socialMetaTagInfo, "socialType")
            },
            suffix: {
                option: "SHORT"
            }
        }

        const { url, apiKey } = Constants.FirebaseConfigMap[tenant]

        let full_url = `${url}/shortLinks?key=${apiKey}`
        if (userId) {
            full_url = `${full_url}&quotaUser=${userId}`
        }

        this.logger.debug(`FirebaseService::createUniversalDeepLinkUrl request of firebase ` +
            `url ${JSON.stringify(payload)}`)
        return fetch(full_url, this.fetchHelper.post(payload)).then((response: any) => {
            this.logger.debug(`FirebaseService::createUniversalDeepLinkUrl response of firebase ` +
                `links ${JSON.stringify(response)}`)
            return this.fetchHelper.parseResponse<any>(response).then((res) => {
                this.logger.debug(`FirebaseService::createUniversalDeepLinkUrl parsed response of firebase links ` +
                    `links ${JSON.stringify(res)}`)
                return { url: res.shortLink }
            })
        })
    }

    async sendPurchaseEvent(userId: string, purchaseEventData: PurchaseEvent) {
        this.logger.info(`rahul_logs_this: sendPurchaseEvent - Starting for userId: ${userId}`)
        try {
            this.logger.info(`rahul_logs_this: sendPurchaseEvent - Getting device for userId: ${userId}`)
            const device = await this.getDevice(userId);

            this.logger.info(`rahul_logs_this: sendPurchaseEvent - Building payload for userId: ${userId}`)
            const firebasePayload = await this.getPurchaseEventPayload(device, userId, purchaseEventData)
            this.logger.info(`FirebaseService::sendPurchaseEvent firebasePayload ${JSON.stringify(firebasePayload)}`)

            this.logger.info(`rahul_logs_this: sendPurchaseEvent - Getting Firebase config`)
            const config = Constants.FirebaseAnalyticsConfigMap[Tenant.CUREFIT_APP]

            // Fallback configuration if not found in constants
            let measurementId = config?.measurementId
            let apiSecret = config?.apiSecret

            if (!measurementId || !apiSecret) {
                this.logger.info(`rahul_logs_this: sendPurchaseEvent - Using fallback Firebase config`)
                measurementId = measurementId || "G-03XGFXZPY3"
                apiSecret = apiSecret || "ogM9tVtsTTm3AuKxWxyuHQ"
            }

            if (!measurementId || !apiSecret) {
                throw new Error("Firebase Analytics configuration is missing or incomplete")
            }

            const baseUrl = config?.url || "https://www.google-analytics.com/mp/collect"
            const url = `${baseUrl}?measurement_id=${measurementId}&api_secret=${apiSecret}`
            this.logger.info(`rahul_logs_this: sendPurchaseEvent - Firebase URL: ${url}`)
            this.logger.info(`rahul_logs_this: sendPurchaseEvent - Using measurementId: ${measurementId}, apiSecret: ${apiSecret.substring(0, 5)}...`)


            this.logger.info(`rahul_logs_this: sendPurchaseEvent - Sending request to Firebase`)
            const response = await fetch(url, this.fetchHelper.post(firebasePayload))

            this.logger.info(`rahul_logs_this: sendPurchaseEvent - Parsing Firebase response`)
            const parsedResponse = await this.fetchHelper.parseResponse<any>(response)

            this.logger.info(`FirebaseService::sendPurchaseEvent response: ${JSON.stringify(parsedResponse)}`)
            this.logger.info(`rahul_logs_this: sendPurchaseEvent - Successfully completed for userId: ${userId}`)

            return parsedResponse

        } catch (error) {
            const errorMessage = `FirebaseService::sendPurchaseEvent Failed for userId ${userId} with error ${error.toString()}`
            this.logger.error(`rahul_logs_this: sendPurchaseEvent - ERROR: ${errorMessage}`)
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            throw error
        }
    }

    async getDevice(userId: string) {
        this.logger.info(`rahul_logs_this: getDevice - Starting for userId: ${userId}`)
        try {
            this.logger.info(`rahul_logs_this: getDevice - Getting cfAPIConfig`)
            const cfAPIConfig = _.get(this.appConfig.getConfiguration(), "cfAPIConfig")
            if (!cfAPIConfig || !cfAPIConfig.baseUrl) {
                this.logger.warn(`rahul_logs_this: getDevice - cfAPIConfig is missing, using fallback for userId: ${userId}`)
                this.logger.warn(`FirebaseService::getDevice cfAPIConfig is missing, using fallback for userId: ${userId}`)
                return null
            }

            const url = `${cfAPIConfig.baseUrl}/device/recent/app/loggedIn?userId=${userId}`
            this.logger.info(`rahul_logs_this: getDevice - Calling device API: ${url}`)
            const response = await fetch(url, this.fetchHelper.get(cfAPIConfig.headers))

            this.logger.info(`rahul_logs_this: getDevice - Parsing device response`)
            const value = await this.fetchHelper.parseResponse<any>(response)
            const device = value.data
            this.logger.info(`Device data for userId - ${userId} -> ${JSON.stringify(device)}`)
            this.logger.info(`rahul_logs_this: getDevice - Successfully got device for userId: ${userId}`)
            return device
        } catch (error) {
            this.logger.warn(`rahul_logs_this: getDevice - ERROR: Failed to get device for userId: ${userId}. Error: ${error.toString()}`)
            this.logger.warn(`FirebaseService::getDevice Failed to get device for userId: ${userId}. Error: ${error.toString()}`)
            return null
        }
    }

async getPurchaseEventPayload(device: any, userId: string, purchaseEventData: PurchaseEvent): Promise<any> {
    this.logger.info(`rahul_logs_this: getPurchaseEventPayload - Starting for userId: ${userId}`)

    this.logger.info(`rahul_logs_this: getPurchaseEventPayload - Getting client ID`)
    const clientId = this.getClientId(device, userId)
    this.logger.info(`rahul_logs_this: getPurchaseEventPayload - Client ID: ${clientId}`)

    // generating session data for attribution
    const sessionId = this.generateSessionId()
    const engagementTime = this.generateEngagementTime()

    this.logger.info(`rahul_logs_this: getPurchaseEventPayload - Generated session_id: ${sessionId}, engagement_time: ${engagementTime}`)

    this.logger.info(`rahul_logs_this: getPurchaseEventPayload - Building Firebase payload`)
    const firebasePayload: any = {
        client_id: clientId,
        user_id: userId, 
        timestamp_micros: (Date.now() * 1000).toString(),
        events: [{
            name: "purchase",
            params: {
                transaction_id: `${purchaseEventData.productId}_${Date.now()}`,
                value: parseFloat(purchaseEventData.price),
                currency: "INR",
                item_name: purchaseEventData.productName,
                quantity: 1,
                platform: "backend",
                session_id: sessionId,
                engagement_time_msec: engagementTime,
                logged_in: true,
                pack_type: purchaseEventData.packType || "none"
            }
        }]
    }

    this.logger.info(`rahul_logs_this: getPurchaseEventPayload - Payload built successfully`)
    return firebasePayload
}
    private getClientId(device: any, userId: string): string {
        this.logger.info(`rahul_logs_this: getClientId - Getting client ID for userId: ${userId}`)

        // Priority order for client ID:
        // 1. DeviceId + UserId combination (best for unique identification)
        // 2. Device advertising ID (good for attribution)
        // 3. User ID (fallback)
        // 4. Random UUID (if no device/user info available)

        if (device?.deviceId && userId) {
            const combinedClientId = `${device.deviceId}_${userId}`
            this.logger.info(`rahul_logs_this: getClientId - Client ID resolved: ${combinedClientId} (from deviceId + userId combination)`)
            return combinedClientId
        }

        if (device?.activeDevice?.advertiserId) {
            const advertiserId = device.activeDevice.advertiserId.split("|")?.[0]
            this.logger.info(`rahul_logs_this: getClientId - Client ID resolved: ${advertiserId} (from advertising ID)`)
            return advertiserId
        }

        if (userId) {
            this.logger.info(`rahul_logs_this: getClientId - Client ID resolved: ${userId} (from user ID)`)
            return userId
        }

        // Generate random UUID for attribution when no device/user info available
        const randomClientId = uuidv4()
        this.logger.info(`rahul_logs_this: getClientId - Client ID resolved: ${randomClientId} (from random UUID for attribution)`)
        return randomClientId
    }



    private generateSessionId(): string {
        const timestamp = Date.now()
        const randomComponent = Math.random().toString(36).substring(2, 8)
        const sessionId = `${timestamp}_${randomComponent}`
        this.logger.info(`rahul_logs_this: generateSessionId - Generated session ID: ${sessionId}`)
        return sessionId
    }

    private generateEngagementTime(): number {
        const minEngagement = 1000  
        const maxEngagement = 30000 
        const engagementTime = Math.floor(Math.random() * (maxEngagement - minEngagement + 1)) + minEngagement
        this.logger.info(`rahul_logs_this: generateEngagementTime - Generated engagement time: ${engagementTime}ms`)
        return engagementTime
    }

}

export default FirebaseService
