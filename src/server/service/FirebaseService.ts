import {BASE_TYPES, Configuration, FetchUtil, Logger} from "@curefit/base"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import {Tenant} from "@curefit/user-common"

import * as _ from "lodash"
import {inject, injectable} from "inversify"
const fetch = require("node-fetch")

import Constants from "../constants"
import {NotificationPayload} from "./PushNotificationService"
import TYPES from "../ioc/types"
import {MetricUtils, ProfilerType} from "../utils/MetricUtils"
import {AndroidInfo, IFirebaseDynamicPayload, IOSInfo} from "../models/Applinks"
import {AppInfo, LinkPayload, PurchaseEvent} from "../../iris-common"
const { v4: uuidv4 } = require('uuid')
import LinkHelper from "../helpers/LinkHelper"
import {CommonUtils} from "../utils/CommonUtils"

export interface IFirebaseService {
    sendNudgeViaLegacyApi(token: string, payload: any, sendPayloadInsideData: boolean, notificationId: string, isTransactional: boolean,
                          timeToLive: number, appId?: string, dryRun?: boolean): Promise<{ success: boolean, messageId?: string, failReason?: string }>
    createUniversalDeepLinkUrl(tenant: Tenant, link: LinkPayload, appInfo?: AppInfo, userId?: string): Promise<{ url: string }>
    sendPurchaseEvent(userId: string, purchaseEventData: any): Promise<any>
}

interface FirebasePayload {
    collapse_key?: string,
    time_to_live: number,
    notification?: any,
    data?: any,
    priority?: any,
    "content-available": boolean,
    registration_ids: string[],
    dry_run?: boolean
}

const CFAppAndroidConfig = (link: LinkPayload, appInfo: AppInfo): AndroidInfo  => {
    const payload: AndroidInfo = { androidPackageName: "fit.cure.android" }
    if (link.redirectMWeb) {
        payload.androidFallbackLink = LinkHelper.appendHttps(link.weblink)
    }
    const minAndroidVC = _.get(appInfo, "minAndroidVersionCode", undefined)
    if (minAndroidVC) {
        payload.androidMinPackageVersionCode = minAndroidVC
    }
    return payload
}

const CFAppIOSConfig = (link: LinkPayload): IOSInfo => {
    const payload: IOSInfo = {
        iosBundleId: "fit.cure.ios",
        iosCustomScheme: "curefit",
        iosAppStoreId: "1217794588"
    }
    if (link.redirectMWeb) {
        payload.iosFallbackLink = LinkHelper.appendHttps(link.weblink)
    }
    return payload
}

const CF_APP_DOMAIN_URI_PREFIX = "dl.cure.fit"

@injectable()
class FirebaseService implements IFirebaseService {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
        @inject(TYPES.MetricUtils) private metricUtils: MetricUtils,
        @inject(TYPES.CommonUtils) private commonUtils: CommonUtils,
        @inject(BASE_TYPES.Configuration) private appConfig: Configuration
    ) {
    }

    // sendPayloadInsideData added for supporting lower versions after adding image support in android.
    async sendNudgeViaLegacyApi(token: string, notificationPayload: NotificationPayload, sendPayloadInsideData: boolean,
                                notificationId: string, isTransactional: boolean, timeToLive: number, appId?: string,
                                dryRun?: boolean): Promise<{ success: boolean, messageId?: string, failReason?: string }> {

        notificationPayload.android_channel_id = isTransactional ? Constants.PUSH_NOTIFICATION_TRANSACTIONAL_CHANNEL :
            Constants.PUSH_NOTIFICATION_PROMOTIONAL_CHANNEL
        const firebasePayload: FirebasePayload = {
            time_to_live: timeToLive,
            notification: notificationPayload,
            data: sendPayloadInsideData === true ? notificationPayload : null,
            "content-available": true,
            priority: isTransactional ? Constants.PUSH_NOTIFICATION_FCM_PRIORITY_HIGH :
                Constants.PUSH_NOTIFICATION_FCM_PRIORITY_NORMAL,
            registration_ids: [ token ]
        }

        this.logger.debug(`FirebaseService::sendNudge: firebasePayload: ${JSON.stringify(firebasePayload)}`)

        // For testing. Dry run sends request without actually sending notifications to the device
        if (dryRun) { firebasePayload.dry_run = dryRun}

        // Passing the data as separate key and as well within  notification key  to cater to android and ios respectively.
        const finalPayload = sendPayloadInsideData === true ? firebasePayload : Object.assign(firebasePayload, notificationPayload)
        return this.sendFirebaseMessageViaLegacyApi(finalPayload, notificationId, appId)
    }

    async sendFirebaseMessageViaLegacyApi(payload: FirebasePayload, notificationId: string, appId?: string) {
        const timerContext = this.metricUtils.startProfilingTimer(ProfilerType.PERF, "sendFirebaseMessage")
        const apiKey = Constants.getFireBaseApiKey(appId)
        try {
            const apiLatencyTimerContext =
                this.metricUtils.startProfilingTimer(ProfilerType.EXTERNAL_API, "Firebase")
            const response = await this.commonUtils.fetchWithRetry(5, 1000,
                Constants.getFireBaseUrl("/fcm/send"),
                this.fetchHelper.post(payload, { Authorization: apiKey }))
            this.metricUtils.endProfilingTimer(apiLatencyTimerContext)

            const parsedResponse = await this.fetchHelper.parseResponse<any>(response)
            const messageId = _.get(_.head(_.get(parsedResponse, "results", [])), "message_id")

            let result: any

            if (!messageId) {
                this.logger.info(`FirebaseService::sendFirebaseMessage message id missing for ` +
                    `notificationId: ${notificationId} token: ${payload.registration_ids} ` +
                    `response: ${JSON.stringify(parsedResponse)}`)

                const error = _.get(_.head(_.get(parsedResponse, "results", [])), "error")
                const failReason = error ? `${Constants.FCM_FAIL_REASON_PREFIX}_${error}` : undefined

                result = { "success": false, failReason }

            } else {
                this.logger.debug(`FirebaseService::sendFirebaseMessage message id present for ` +
                    `notificationId: ${notificationId} token: ${payload.registration_ids} ` +
                    `response: ${JSON.stringify(parsedResponse)}`)

                result = { "success": true, messageId }
            }

            this.metricUtils.endProfilingTimer(timerContext)
            return result
        } catch (error) {
            const errorMessage = `FirebaseService::sendFirebaseMessage Failed for payload ` +
                `${JSON.stringify(payload)} with error ${error.toString()}`
            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            return { "success": false }
        }
    }

    public async createUniversalDeepLinkUrl(tenant: Tenant, link: LinkPayload, appInfo: AppInfo,
                                            userId?: string): Promise<{ url: string; }> {
        const applink = LinkHelper.createFirebaseLink(link)
        const redirect = !!link.enableForcedRedirect
        /* construct cure.fit app firebase deep link url */
        const payload: IFirebaseDynamicPayload = {
            dynamicLinkInfo: {
                link: applink,
                domainUriPrefix: CF_APP_DOMAIN_URI_PREFIX,
                androidInfo: CFAppAndroidConfig(link, appInfo),
                iosInfo: CFAppIOSConfig(link),
                navigationInfo: {
                    enableForcedRedirect: redirect
                },
                analyticsInfo: link.analyticsInfo,
                // firebase doesn't accept og_type meta
                socialMetaTagInfo: _.omit(link.socialMetaTagInfo, "socialType")
            },
            suffix: {
                option: "SHORT"
            }
        }

        const { url, apiKey } = Constants.FirebaseConfigMap[tenant]

        let full_url = `${url}/shortLinks?key=${apiKey}`
        if (userId) {
            full_url = `${full_url}&quotaUser=${userId}`
        }

        this.logger.debug(`FirebaseService::createUniversalDeepLinkUrl request of firebase ` +
            `url ${JSON.stringify(payload)}`)
        return fetch(full_url, this.fetchHelper.post(payload)).then((response: any) => {
            this.logger.debug(`FirebaseService::createUniversalDeepLinkUrl response of firebase ` +
                `links ${JSON.stringify(response)}`)
            return this.fetchHelper.parseResponse<any>(response).then((res) => {
                this.logger.debug(`FirebaseService::createUniversalDeepLinkUrl parsed response of firebase links ` +
                    `links ${JSON.stringify(res)}`)
                return { url: res.shortLink }
            })
        })
    }

    async sendPurchaseEvent(userId: string, purchaseEventData: PurchaseEvent) {
        this.logger.info(`rahul_logs_this: sendPurchaseEvent - Starting for userId: ${userId}`)

        try {
            const device = await this.getDevice(userId);
            const firebasePayload = await this.getPurchaseEventPayload(device, userId, purchaseEventData)

            this.logger.info(`rahul_logs_this: sendPurchaseEvent - Getting Firebase config`)

            // First try to get config from environment-specific configuration (STAGE.json, ALPHA.json, etc.)
            const appConfig = this.appConfig.getConfiguration()
            const envFirebaseConfig = _.get(appConfig, "firebaseAnalytics")

            // Fallback to constants if environment config is not available
            const constantsConfig = Constants.FirebaseAnalyticsConfigMap[Tenant.CUREFIT_APP]

            let measurementId = envFirebaseConfig?.measurementId || constantsConfig?.measurementId
            let apiSecret = envFirebaseConfig?.apiSecret || constantsConfig?.apiSecret
            let baseUrl = envFirebaseConfig?.baseUrl || constantsConfig?.url || "https://www.google-analytics.com/mp/collect"
            // Final fallback for Alpha environment (backward compatibility)
            if (!measurementId || !apiSecret) {
                measurementId = measurementId || "G-03XGFXZPY3"
                apiSecret = apiSecret || "ogM9tVtsTTm3AuKxWxyuHQ"
            }

            if (!measurementId || !apiSecret) {
                throw new Error("Firebase Analytics configuration is missing or incomplete")
            }

            const url = `${baseUrl}?measurement_id=${measurementId}&api_secret=${apiSecret}`
            this.logger.info(`rahul_logs_this: sendPurchaseEvent - Sending to Firebase: ${measurementId}`)

            const response = await fetch(url, this.fetchHelper.post(firebasePayload))
            const parsedResponse = await this.fetchHelper.parseResponse<any>(response)

            this.logger.info(`rahul_logs_this: sendPurchaseEvent - SUCCESS for userId: ${userId}`)

            return parsedResponse

        } catch (error) {
            const errorMessage = `FirebaseService::sendPurchaseEvent Failed for userId ${userId} with error ${error.toString()}`

            if (error.response) {
                this.logger.error(`rahul_logs_this: sendPurchaseEvent - HTTP Error Status: ${error.response.status}`)
                this.logger.error(`rahul_logs_this: sendPurchaseEvent - HTTP Error Body: ${JSON.stringify(error.response.data)}`)
            }

            if (error.code) {
                this.logger.error(`rahul_logs_this: sendPurchaseEvent - Error Code: ${error.code}`)
            }

            this.logger.error(errorMessage)
            this.rollbarService.sendError(new Error(errorMessage))
            throw error
        }
    }

    async getDevice(userId: string) {
        try {
            const cfAPIConfig = _.get(this.appConfig.getConfiguration(), "cfAPIConfig")
            if (!cfAPIConfig || !cfAPIConfig.baseUrl) {
                this.logger.warn(`FirebaseService::getDevice cfAPIConfig is missing for userId: ${userId}`)
                return null
            }

            const url = `${cfAPIConfig.baseUrl}/device/recent/app/loggedIn?userId=${userId}`
            const response = await fetch(url, this.fetchHelper.get(cfAPIConfig.headers))
            const value = await this.fetchHelper.parseResponse<any>(response)
            return value.data
        } catch (error) {
            this.logger.warn(`FirebaseService::getDevice Failed for userId: ${userId}. Error: ${error.toString()}`)
            return null
        }
    }

    async getPurchaseEventPayload(device: any, userId: string, purchaseEventData: PurchaseEvent): Promise<any> {
        const clientId = this.getClientId(device, userId)
        const sessionId = this.generateSessionId()
        const engagementTime = this.generateEngagementTime()

        // Parse and validate price
        const priceValue = parseFloat(purchaseEventData.price)
        if (isNaN(priceValue)) {
            this.logger.warn(`rahul_logs_this: getPurchaseEventPayload - Invalid price: ${purchaseEventData.price}`)
        }

        // Generate transaction ID
        const transactionId = `${purchaseEventData.productId}_${Date.now()}`
        const timestamp = (Date.now() * 1000).toString()

        // Get device-specific information
        const os = this.getOS(device)
        const advertiserInfo = this.getAdvertiserId(device)
        const ip = this.getIp(device)

        this.logger.info(`rahul_logs_this: getPurchaseEventPayload - OS: ${os || 'unknown'}, Advertiser: ${advertiserInfo?.key || 'none'}`)

        // Build event parameters
        const eventParams: any = {
            transaction_id: transactionId,
            value: priceValue,
            currency: "INR",
            product_name: purchaseEventData.productName,
            quantity: 1,
            platform: "backend",
            session_id: sessionId,
            engagement_time_msec: engagementTime,
            logged_in: true,
            pack_type: purchaseEventData.packType || "none",
            product_id: purchaseEventData.productId
        }

        // Add OS information if available
        if (os) {
            eventParams.os = os
        }

        if(ip) {
            eventParams.ip = ip
        }

        // Add advertiser ID if available
        if (advertiserInfo) {
            eventParams[advertiserInfo.key] = advertiserInfo.value
        }

        const firebasePayload: any = {
            client_id: clientId,
            user_id: userId,
            timestamp_micros: timestamp,
            events: [{
                name: "purchase",
                params: eventParams
            }]
        }

        return firebasePayload
    }

    private getClientId(device: any, userId: string): string {
        // Priority order for client ID:
        // 1. DeviceId + UserId combination (best for unique identification)
        // 2. Device advertising ID (good for attribution)
        // 3. User ID (fallback)
        // 4. Random UUID (if no device/user info available)

        if (device?.deviceId && userId) {
            const combinedClientId = `${device.deviceId}_${userId}`
            this.logger.info(`rahul_logs_this: getClientId - Using deviceId+userId: ${combinedClientId}`)
            return combinedClientId
        }

        if (device?.activeDevice?.advertiserId) {
            const advertiserId = device.activeDevice.advertiserId.split("|")?.[0]
            this.logger.info(`rahul_logs_this: getClientId - Using advertiserId: ${advertiserId}`)
            return advertiserId
        }

        if (userId) {
            this.logger.info(`rahul_logs_this: getClientId - Using userId: ${userId}`)
            return userId
        }

        // Generate random UUID for attribution when no device/user info available
        const randomClientId = uuidv4()
        this.logger.warn(`rahul_logs_this: getClientId - Using random UUID: ${randomClientId}`)
        return randomClientId
    }



    private generateSessionId(): string {
        const timestamp = Date.now()
        const randomComponent = Math.random().toString(36).substring(2, 8)
        const sessionId = `${timestamp}_${randomComponent}`
        return sessionId
    }

    private generateEngagementTime(): number {
        const minEngagement = 1000   // 1 second minimum
        const maxEngagement = 30000  // 30 seconds maximum
        const engagementTime = Math.floor(Math.random() * (maxEngagement - minEngagement + 1)) + minEngagement
        return engagementTime
    }

    private getOS(device: any): "Android" | "iOS" | undefined {
        const osName = device?.activeDevice?.osName?.toLowerCase()
        switch (osName) {
            case "android": return "Android"
            case "ios": return "iOS"
            default: return undefined
        }
    }

    private getIp(device: any): string | undefined {
        return device?.activeDevice?.ip
    }

    private getAdvertiserId(device: any): { key: string, value: string } | undefined {
        const os = this.getOS(device)
        if (!os) return undefined

        const advertiserId = device?.activeDevice?.advertiserId?.split("|")?.[0]
        if (!advertiserId) return undefined

        switch (os) {
            case "Android": return { key: "aaid", value: advertiserId }
            case "iOS": return { key: "idfa", value: advertiserId }
            default: return undefined
        }
    }
    
}

export default FirebaseService
