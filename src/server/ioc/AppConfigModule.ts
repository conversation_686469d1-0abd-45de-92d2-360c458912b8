import "reflect-metadata"
import * as Inversify from "inversify"
import {BASE_TYPES} from "@curefit/base"
import {WhatsappConfig} from "../helpers/whatsapp/WhatsappNotificationHelper"
import TYPES from "./types"

export function AppConfigKernelModule(kernel: Inversify.Container) {
    return new Inversify.ContainerModule((bind: Inversify.interfaces.Bind) => {
        bind<string>(BASE_TYPES.ConfigurationDirectory).toConstantValue("conf")
        bind<WhatsappConfig>(TYPES.WhatsappServiceConfiguration).toDynamicValue((context: Inversify.interfaces.Context) => {
            const appConfig = context.container.get<any>(BASE_TYPES.Configuration).getConfiguration()
            return appConfig.whatsappConfig
        }).inSingletonScope()
    })

}
