import * as Inversify from "inversify"
import {Container} from "inversify"

import "reflect-metadata"
import {makeLoggerMiddleware} from "inversify-logger-middleware"

import {MongoModule} from "@curefit/mongo-utils"
import {BaseKernelModule} from "@curefit/base"
import {CacheModule} from "@curefit/cache-utils"
import {RedisModule} from "@curefit/redis-utils"
import {SqsClientModule} from "@curefit/sqs-client"
import {DeviceModelsModule} from "@curefit/device-models"
import {ErrorCommonModule} from "@curefit/error-common"
import {MysqlModule} from "@curefit/mysql-utils"
import {RashiClientModule} from "@curefit/rashi-client/dist"
import {MozartClientModule} from "@curefit/mozart-client/dist"
import {SegmentationClientModule} from "@curefit/segmentation-service-client/dist"
import {ServerModule} from "@curefit/server"
import {VoyagerClientKernelModule} from "@curefit/voyager-client"
import { LockModule } from "@curefit/lock-utils"
import { SSOModule } from "@curefit/cf-commons-middleware/dist/ioc"

import {IrisModelsModule} from "../../iris-models"
import {IrisClientModule} from "../../iris-client"
import {AppConfigKernelModule} from "./AppConfigModule"
import {ServerKernelModule, WorkerKernelModule} from "./KernelModule"
import { ConfigStoreClientModule } from "@curefit/config-store-client"
import {CampaignManagerClientModule} from "@curefit/campaign-manager-client"
import { UserClientModule } from "@curefit/user-client"
import {EventsModule} from "@curefit/events-util"
import {AthenaServiceClientModule} from "@curefit/athena-service-client"

const kernel: Inversify.Container = new Container({defaultScope: "Singleton"})

if (process.env.NODE_ENV === "development") {
    const logger = makeLoggerMiddleware()
    kernel.applyMiddleware(logger)
}

kernel.load(AppConfigKernelModule(kernel))
kernel.load(BaseKernelModule(kernel))
kernel.load(CacheModule(kernel))
kernel.load(RedisModule(kernel))
kernel.load(MongoModule(kernel))
kernel.load(DeviceModelsModule(kernel))
kernel.load(IrisClientModule(kernel))
kernel.load(IrisModelsModule(kernel))
kernel.load(SqsClientModule(kernel))
kernel.load(ErrorCommonModule(kernel))
kernel.load(EventsModule(kernel))
kernel.load(MysqlModule(kernel))
kernel.load(LockModule(kernel))
kernel.load(MozartClientModule(kernel))
kernel.load(SegmentationClientModule(kernel))
kernel.load(ServerModule(kernel))
kernel.load(VoyagerClientKernelModule(kernel))
kernel.load(SSOModule(kernel))
kernel.load(AthenaServiceClientModule(kernel))

kernel.load(CampaignManagerClientModule(kernel))
if (process.env.DEPLOYMENT_TYPE === "server") {
    kernel.load(ServerKernelModule(kernel))
} else {
    kernel.load(WorkerKernelModule(kernel))
}

kernel.load(RashiClientModule(kernel))
kernel.load(ConfigStoreClientModule(kernel))
kernel.load(UserClientModule(kernel))
export default kernel
