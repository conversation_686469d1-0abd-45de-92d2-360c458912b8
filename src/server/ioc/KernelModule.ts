import * as Inversify from "inversify"

import TYPES from "./types"

import {UserCommunicationCampaignControllerFactory} from "../controllers/UserCommunicationCampaignController"
import {CreativeControllerFactory} from "../controllers/CreativeController"
import {CommunicationCampaignControllerFactory} from "../controllers/CommunicationCampaignController"
import {NotificationsControllerFactory} from "../controllers/NotificationsController"
import {IPushNotificationHelper, PushNotificationHelperFactory} from "../helpers/PushNotificationHelper"
import {EmailNotificationHelperFactory, IEmailNotificationHelper} from "../helpers/EmailNotificationHelper"
import {ISESHelper, SESHelperFactory} from "../helpers/SESHelper"
import CampaignHelper from "../helpers/CampaignHelper"
import {IOBDNotificationHelper, OBDNotificationHelperFactory} from "../helpers/OBDNotificationHelper"
import {
    ClickToCallNotificationHelperFactory,
    IClickToCallNotificationHelper
} from "../helpers/ClickToCallNotificationHelper"
import SMSNotificationHelper from "../helpers/SMS/SMSNotificationHelper"
import {IKaleyraOBDHelper, KaleyraHelperFactory} from "../helpers/KaleyraOBDHelper"
import {RedisCounterDataLayerImpl} from "../../bumble/elasticache/RedisCounterDataLayerImpl"
import {MysqlNamespaceDataLayerImpl} from "../../bumble/mysql/MysqlNamespaceDataLayerImpl"
import {CounterDataLayer} from "../../bumble/services/CounterDataLayer"
import {NamespaceDataLayer} from "../../bumble/services/NamespaceDataLayer"
import {BumbleService} from "../../bumble/services/BumbleService"
import {BumbleServiceImpl} from "../../bumble/services/BumbleServiceImpl"
import NotificationEventProcessingService from "../service/NotificationEventProcessingService"
import {IInAppNotificationHelper, InAppNotificationHelperFactory} from "../helpers/InAppNotificationHelper"
import NotificationHelper from "../helpers/NotificationHelper"
import ClevertapService from "../helpers/ClevertapService"
import {INotificationService} from "../helpers/INotificationService"
import SESDeliveryQueueConsumer from "../consumers/SESDeliveryConsumer"
import NotificationQueueConsumer from "../consumers/NotificationQueueConsumer"

import {AttachmentHelper} from "../helpers/AttachmentHelper"
import {MetricUtils} from "../utils/MetricUtils"
import {MozartHelper} from "../helpers/MozartHelper"
import NotificationReportHelper from "../helpers/NotificationReportHelper"
import {KnowlarityHelper} from "../helpers/KnowlarityHelper"
import {AWSUtils} from "../utils/AWSUtils"
import LoadBalancerService from "../service/LoadBalancerService"
import RetryService from "../service/RetryService"
import {SolutionsInfiniHelper} from "../helpers/SolutionsInfiniHelper"
import NotificationAttemptHelper from "../helpers/NotificationAttemptHelper"

import IdentityService from "../service/IdentityService"
import FirebaseService, {IFirebaseService} from "../service/FirebaseService"
import PushNotificationService from "../service/PushNotificationService"
import {InAppNotificationControllerFactory} from "../controllers/InAppNotificationController"
import {ApiKeyMappingCache} from "../auth/ApiKeyMappingCache"
import {ApiKeyMappingReadonlyDaoMongoImpl, ApiKeyMappingSchema, IApiKeyMappingReadonlyDao} from "../models/ApiKey"
import BranchIOService, {IBranchIOService} from "../service/BranchIOService"
import FirebaseAnalyticsService, {IFirebaseAnalyticsService} from "../service/FirebaseAnalyticsService"
import {DeeplinkControllerFactory} from "../controllers/DeeplinkController"
import {FirebaseAnalyticsControllerFactory} from "../controllers/FirebaseAnalyticsController"
import {
    IWhatsappNotificationHelper,
    WhatsappNotificationHelperFactory
} from "../helpers/whatsapp/WhatsappNotificationHelper"
import {UserUtils} from "../utils/UserUtils"
import {CommonUtils} from "../utils/CommonUtils"
import EmailOpenEventsConsumer from "../consumers/EmailOpenEventsConsumer"

import SMSNotificationConsumer from "../consumers/SMSNotificationConsumer"
import CTCNotificationConsumer from "../consumers/CTCNotificationConsumer"
import PNNotificationConsumer from "../consumers/PNNotificationConsumer"
import EmailNotificationConsumer from "../consumers/EMAILNotificationConsumer"
import OBDNotificationConsumer from "../consumers/OBDNotificationConsumer"
import IANNotificationConsumer from "../consumers/IANNotificationConsumer"
import CommunicationWorkerConfigHelper from "../helpers/CommunicationWorkerConfigHelper"
import {CommunicationWorkerConfigControllerFactory} from "../controllers/CommunicationWorkerConfigController"
import WhatsappNotificationConsumer from "../consumers/WhatsappNotificationConsumer"
import NotificationProcessorQueueConsumer from "../consumers/NotificationProcessorQueueConsumer"
import PNDeliveryEventConsumer from "../consumers/PNDeliveryEventConsumer"
import CreativeService from "../service/CreativeService"
import UserProfileAttributeUtils from "../utils/UserProfileAttributeUtils"
import {DebugControllerFactory} from "../controllers/DebugController"
import FeatureUtils from "../utils/FeatureUtils"
import SegmentationService from "../service/SegmentationService"
import MozartJobUpdateConsumer from "../consumers/MozartJobUpdateConsumer"
import MozartService from "../service/MozartService"
import SegmentBatchConsumer from "../consumers/SegmentBatchConsumer"
import NotificationLogService from "../service/NotificationLogService"
import ScheduledNotificationConsumer from "../consumers/ScheduledNotificationConsumer"
import NotificationStatusUpdatePublisher from "../publishers/NotificationStatusUpdatePublisher"
import CampaignRequestCacheService from "../service/CampaignRequestCacheService"
import NotificationStatusUpdateConsumer from "../consumers/NotificationStatusUpdateConsumer"
import AppEventsConsumer from "../consumers/AppEventsConsumer"
import UnsubscriptionService from "../service/UnsubscriptionService"
import {UnsubscriptionControllerFactory} from "../controllers/UnsubscriptionController"
import SolutionsInfiniSMSHelper from "../helpers/SMS/SolutionsInfiniSMSHelper"
import PushNotificationTokenNotRegisteredPublisher from "../publishers/PushNotificationTokenNotRegisteredPublisher"
import UserContextHelper from "../helpers/UserContextHelper"
import UserInteractionPublisher from "../publishers/UserInteractionPublisher"
import FirebaseAdminService from "../service/FirebaseAdminService"
import {IrisBotControllerFactory} from "../controllers/IrisBotController"
import {CronControllerFactory} from "../controllers/CronControler"
import HealthCheckService from "../service/HealthCheckService"
import FailureAlertService from "../service/FailureAlertService"
import FirebaseAppRemoveEventsConsumer from "../consumers/FirebaseAppRemoveEventsConsumer"
import ScaleService from "../service/ScaleService"
import NotificationWrapperCacheService from "../service/NotificationWrapperCacheService"
import GupshupSMSHelper from "../helpers/SMS/GupshupSMSHelper"
import { SlackAuthMappingReadWriteDaoMongoImpl, ISlackAuthMappingReadOnlyDao, ISlackAuthMappingReadWriteDao, SlackAuthMappingReadOnlyDaoMongoImpl, SlackAuthMappingSchema } from "../models/Slack"
import { SlackNotificationHelperFactory, ISlackNotificationHelper } from "../helpers/SlackNotificationHelper"
import ValueFirstWhatsappHelper from "../helpers/whatsapp/ValueFirstWhatsappHelper"
import KaleyraWhatsappHelper from "../helpers/whatsapp/KaleyraWhatsappHelper"
import CampaignExecutionNotificationConsumer from "../consumers/CampaignExecutionNotificationConsumer"
import {AthenaService} from "../service/AthenaService"
import {FunctionalTagsService} from "../service/FunctionalTagsService"
import {FunctionalTagsControllerFactory} from "../controllers/FunctionalTagsController"
import {ReacherHelper, IReacherHelper} from "../helpers/ReacherHelper"
import EmailBounceQueueConsumer from "../consumers/EmailBounceQueueConsumer"
import SMSDeliveryConsumer from "../consumers/SMSDeliveryConsumer"
import {AttachmentControllerFactory} from "../controllers/AttachmentController"
import {KafkaUtils} from "../utils/KafkaUtils"
import SprinklrWhatsappHelper from "../helpers/whatsapp/SprinklrWhatsappHelper"
import {DatalakeMetricService} from "../service/DatalakeMetricService"
import {TaskManagementService} from "../service/TaskManagementService"
import { FirebaseReportsControllerFactory } from "../controllers/FirebaseReportsController"
import { CampaignManagerService } from "../service/CampaignManagerService"
import FonadaSMSHelper from "../helpers/SMS/FonadaSMSHelper"
import {CommValidationsService} from "../service/CommValidationsService"
import {AlertService} from "../service/AlertService"
import {LeadCategorisationService} from "../service/LeadCategorisationService"
import {AthenaServiceV2} from "../service/AthenaServiceV2"

export function ServerKernelModule(kernel: Inversify.Container) {
    return new Inversify.ContainerModule((bind: Inversify.interfaces.Bind) => {

        UserCommunicationCampaignControllerFactory(kernel)
        CommunicationWorkerConfigControllerFactory(kernel)
        CreativeControllerFactory(kernel)
        DebugControllerFactory(kernel)
        UnsubscriptionControllerFactory(kernel)
        CommunicationCampaignControllerFactory(kernel)
        NotificationsControllerFactory(kernel)
        IrisBotControllerFactory(kernel)
        InAppNotificationControllerFactory(kernel)
        DeeplinkControllerFactory(kernel)
        FirebaseAnalyticsControllerFactory(kernel)
        CronControllerFactory(kernel)
        FunctionalTagsControllerFactory(kernel)
        AttachmentControllerFactory(kernel)
        FirebaseReportsControllerFactory(kernel)

        bind<IClickToCallNotificationHelper>(TYPES.ClickToCallNotificationHelper)
            .to(ClickToCallNotificationHelperFactory(kernel)).inSingletonScope()
        bind<IPushNotificationHelper>(TYPES.PushNotificationHelper)
            .to(PushNotificationHelperFactory(kernel)).inSingletonScope()
        bind<IEmailNotificationHelper>(TYPES.EmailNotificationHelper)
            .to(EmailNotificationHelperFactory(kernel)).inSingletonScope()
        bind<IOBDNotificationHelper>(TYPES.OBDNotificationHelper)
            .to(OBDNotificationHelperFactory(kernel)).inSingletonScope()
        bind<ISESHelper>(TYPES.SESHelper)
            .to(SESHelperFactory(kernel)).inSingletonScope()
        bind<SMSNotificationHelper>(TYPES.SMSNotificationHelper)
            .to(SMSNotificationHelper).inSingletonScope()
        bind<IKaleyraOBDHelper>(TYPES.KaleyraOBDHelper)
            .to(KaleyraHelperFactory(kernel)).inSingletonScope()
        bind<IInAppNotificationHelper>(TYPES.InAppNotificationHelper)
            .to(InAppNotificationHelperFactory(kernel)).inSingletonScope()
        bind<IWhatsappNotificationHelper>(TYPES.WhatsappNotificationHelper)
            .to(WhatsappNotificationHelperFactory(kernel)).inSingletonScope()
        bind<ISlackNotificationHelper>(TYPES.SlackNotificationHelper)
            .to(SlackNotificationHelperFactory(kernel)).inSingletonScope()
        bind<CampaignHelper>(TYPES.CampaignHelper)
            .to(CampaignHelper).inSingletonScope()
        bind<ScheduledNotificationConsumer>(TYPES.ScheduledNotificationConsumer)
            .to(ScheduledNotificationConsumer).inSingletonScope()
        bind<CommunicationWorkerConfigHelper>(TYPES.CommunicationWorkerConfigHelper)
            .to(CommunicationWorkerConfigHelper).inSingletonScope()

        bind<SMSNotificationConsumer>(TYPES.SMSNotificationConsumer)
            .to(SMSNotificationConsumer).inSingletonScope()
        bind<CTCNotificationConsumer>(TYPES.CTCNotificationConsumer)
            .to(CTCNotificationConsumer).inSingletonScope()
        bind<PNNotificationConsumer>(TYPES.PNNotificationConsumer)
            .to(PNNotificationConsumer).inSingletonScope()
        bind<EmailNotificationConsumer>(TYPES.EmailNotificationConsumer)
            .to(EmailNotificationConsumer).inSingletonScope()
        bind<OBDNotificationConsumer>(TYPES.OBDNotificationConsumer)
            .to(OBDNotificationConsumer).inSingletonScope()
        bind<IANNotificationConsumer>(TYPES.IANNotificationConsumer)
            .to(IANNotificationConsumer).inSingletonScope()
        bind<WhatsappNotificationConsumer>(TYPES.WhatsappNotificationConsumer)
            .to(WhatsappNotificationConsumer).inSingletonScope()
        bind<CreativeService>(TYPES.CreativeService)
            .to(CreativeService).inSingletonScope()
        bind<CommValidationsService>(TYPES.CommValidationsService)
            .to(CommValidationsService).inSingletonScope()
        bind<MozartService>(TYPES.MozartService)
            .to(MozartService).inSingletonScope()

        bind<BumbleService>(TYPES.BumbleService)
            .to(BumbleServiceImpl).inSingletonScope()
        bind<NamespaceDataLayer>(TYPES.NamespaceDataLayer)
            .to(MysqlNamespaceDataLayerImpl).inSingletonScope()
        bind<CounterDataLayer>(TYPES.CounterDataLayer)
            .to(RedisCounterDataLayerImpl).inSingletonScope()

        bind<NotificationEventProcessingService>(TYPES.NotificationEventProcessingService)
            .to(NotificationEventProcessingService).inSingletonScope()
        bind<NotificationHelper>(TYPES.NotificationHelper)
            .to(NotificationHelper).inSingletonScope()
        bind<NotificationReportHelper>(TYPES.NotificationReportHelper)
            .to(NotificationReportHelper).inSingletonScope()
        bind<NotificationProcessorQueueConsumer>(TYPES.NotificationProcessorQueueConsumer)
            .to(NotificationProcessorQueueConsumer).inSingletonScope()
        bind<INotificationService>(TYPES.INotificationService)
            .to(ClevertapService)
        bind<SESDeliveryQueueConsumer>(TYPES.SESDeliveryQueueConsumer)
            .to(SESDeliveryQueueConsumer).inSingletonScope()
        bind<PNDeliveryEventConsumer>(TYPES.PNDeliveryEventConsumer)
            .to(PNDeliveryEventConsumer).inSingletonScope()
        bind<MozartJobUpdateConsumer>(TYPES.MozartJobUpdateConsumer)
            .to(MozartJobUpdateConsumer).inSingletonScope()
        bind<SegmentBatchConsumer>(TYPES.SegmentBatchConsumer)
            .to(SegmentBatchConsumer).inSingletonScope()
        bind<NotificationQueueConsumer>(TYPES.NotificationQueueConsumer)
            .to(NotificationQueueConsumer).inSingletonScope()
        bind<EmailOpenEventsConsumer>(TYPES.EmailOpenEventsConsumer)
            .to(EmailOpenEventsConsumer).inSingletonScope()
        bind<FirebaseAppRemoveEventsConsumer>(TYPES.FirebaseAppRemoveEventsConsumer)
            .to(FirebaseAppRemoveEventsConsumer).inSingletonScope()
        bind<AttachmentHelper>(TYPES.AttachmentHelper)
            .to(AttachmentHelper).inSingletonScope()
        bind<MetricUtils>(TYPES.MetricUtils)
            .to(MetricUtils).inSingletonScope()
        bind<AWSUtils>(TYPES.AWSUtils)
            .to(AWSUtils).inSingletonScope()
        bind<KafkaUtils>(TYPES.KafkaUtils)
            .to(KafkaUtils).inSingletonScope()
        bind<MozartHelper>(TYPES.MozartHelper)
            .to(MozartHelper).inSingletonScope()
        bind<KnowlarityHelper>(TYPES.KnowlarityHelper)
            .to(KnowlarityHelper).inSingletonScope()
        bind<SolutionsInfiniHelper>(TYPES.SolutionsInfiniHelper)
            .to(SolutionsInfiniHelper).inSingletonScope()
        bind<LoadBalancerService>(TYPES.LoadBalancerService)
            .to(LoadBalancerService).inSingletonScope()
        bind<RetryService>(TYPES.RetryService)
            .to(RetryService).inSingletonScope()
        bind<NotificationAttemptHelper>(TYPES.NotificationAttemptHelper)
            .to(NotificationAttemptHelper).inSingletonScope()
        bind<IdentityService>(TYPES.IdentityService)
            .to(IdentityService).inSingletonScope()
        bind<SegmentationService>(TYPES.SegmentationService)
            .to(SegmentationService).inSingletonScope()

        bind<IFirebaseService>(TYPES.FirebaseService)
            .to(FirebaseService).inSingletonScope()
        bind<PushNotificationService>(TYPES.PushNotificationService)
            .to(PushNotificationService).inSingletonScope()

        bind<ApiKeyMappingCache>(TYPES.ApiKeyMappingCache)
            .to(ApiKeyMappingCache).inSingletonScope()
        bind<IApiKeyMappingReadonlyDao>(TYPES.ApiKeyMappingReadonlyDao)
            .to(ApiKeyMappingReadonlyDaoMongoImpl).inSingletonScope()
        bind<ApiKeyMappingSchema>(TYPES.ApiKeyMappingSchema)
            .to(ApiKeyMappingSchema).inSingletonScope()
        bind<SlackAuthMappingSchema>(TYPES.SlackAuthMappingSchema)
            .to(SlackAuthMappingSchema).inSingletonScope()
        bind<ISlackAuthMappingReadOnlyDao>(TYPES.SlackAuthMappingReadOnlyDao)
            .to(SlackAuthMappingReadOnlyDaoMongoImpl).inSingletonScope()
        bind<ISlackAuthMappingReadWriteDao>(TYPES.SlackAuthMappingReadWriteDao)
            .to(SlackAuthMappingReadWriteDaoMongoImpl).inSingletonScope()
        bind<IBranchIOService>(TYPES.BranchIOService)
            .to(BranchIOService).inSingletonScope()
        bind<IFirebaseAnalyticsService>(TYPES.FirebaseAnalyticsService)
            .to(FirebaseAnalyticsService).inSingletonScope()
        bind<UserUtils>(TYPES.UserUtils)
            .to(UserUtils).inSingletonScope()
        bind<CommonUtils>(TYPES.CommonUtils)
            .to(CommonUtils).inSingletonScope()
        bind<UserProfileAttributeUtils>(TYPES.UserProfileAttributeUtils)
            .to(UserProfileAttributeUtils).inSingletonScope()
        bind<FeatureUtils>(TYPES.FeatureUtils)
            .to(FeatureUtils).inSingletonScope()
        bind<NotificationLogService>(TYPES.NotificationLogService)
            .to(NotificationLogService).inSingletonScope()
        bind<CampaignRequestCacheService>(TYPES.CampaignRequestCacheService)
            .to(CampaignRequestCacheService).inSingletonScope()
        bind<NotificationStatusUpdatePublisher>(TYPES.NotificationStatusUpdatePublisher)
            .to(NotificationStatusUpdatePublisher).inSingletonScope()
        bind<PushNotificationTokenNotRegisteredPublisher>(TYPES.PushNotificationTokenNotRegisteredPublisher)
            .to(PushNotificationTokenNotRegisteredPublisher).inSingletonScope()
        bind<UserInteractionPublisher>(TYPES.UserInteractionPublisher)
            .to(UserInteractionPublisher).inSingletonScope()
        bind<NotificationStatusUpdateConsumer>(TYPES.NotificationStatusUpdateConsumer)
            .to(NotificationStatusUpdateConsumer).inSingletonScope()
        bind<AppEventsConsumer>(TYPES.AppEventsConsumer)
            .to(AppEventsConsumer).inSingletonScope()
        bind<UnsubscriptionService>(TYPES.UnsubscriptionService)
            .to(UnsubscriptionService).inSingletonScope()
        bind<SolutionsInfiniSMSHelper>(TYPES.SolutionsInfiniSMSHelper)
            .to(SolutionsInfiniSMSHelper).inSingletonScope()
        bind<ValueFirstWhatsappHelper>(TYPES.ValueFirstWhatsappHelper)
            .to(ValueFirstWhatsappHelper).inSingletonScope()
        bind<KaleyraWhatsappHelper>(TYPES.KaleyraWhatsappHelper)
            .to(KaleyraWhatsappHelper).inSingletonScope()
        bind<GupshupSMSHelper>(TYPES.GupshupSMSHelper)
            .to(GupshupSMSHelper).inSingletonScope()
        bind<FonadaSMSHelper>(TYPES.FonadaSMSHelper)
            .to(FonadaSMSHelper).inSingletonScope()
        bind<UserContextHelper>(TYPES.UserContextHelper)
            .to(UserContextHelper).inSingletonScope()
        bind<FirebaseAdminService>(TYPES.FirebaseAdminService)
            .to(FirebaseAdminService).inSingletonScope()
        bind<HealthCheckService>(TYPES.HealthCheckService)
            .to(HealthCheckService).inSingletonScope()
        bind<FailureAlertService>(TYPES.FailureAlertService)
            .to(FailureAlertService).inSingletonScope()
        bind<ScaleService>(TYPES.ScaleService)
            .to(ScaleService).inSingletonScope()
        bind<NotificationWrapperCacheService>(TYPES.NotificationWrapperCacheService)
            .to(NotificationWrapperCacheService).inSingletonScope()
        bind<CampaignExecutionNotificationConsumer>(TYPES.CampaignExecutionNotificationConsumer)
            .to(CampaignExecutionNotificationConsumer).inSingletonScope()
        bind<AthenaService>(TYPES.AthenaService)
            .to(AthenaService).inSingletonScope()
        bind<FunctionalTagsService>(TYPES.FunctionalTagsService)
            .to(FunctionalTagsService).inSingletonScope()
        bind<IReacherHelper>(TYPES.ReacherHelper)
            .to(ReacherHelper).inSingletonScope()
        bind<EmailBounceQueueConsumer>(TYPES.EmailBounceQueueConsumer)
            .to(EmailBounceQueueConsumer).inSingletonScope()
        bind<SMSDeliveryConsumer>(TYPES.SMSDeliveryConsumer)
            .to(SMSDeliveryConsumer).inSingletonScope()
        bind<SprinklrWhatsappHelper>(TYPES.SprinklrWhatsappHelper)
            .to(SprinklrWhatsappHelper).inSingletonScope()
        bind<DatalakeMetricService>(TYPES.DatalakeMetricService)
            .to(DatalakeMetricService).inSingletonScope()
        bind<TaskManagementService>(TYPES.TaskManagementService)
            .to(TaskManagementService).inSingletonScope()
        bind<CampaignManagerService>(TYPES.CampaignManagerService)
            .to(CampaignManagerService).inSingletonScope()
        bind<AlertService>(TYPES.AlertService)
            .to(AlertService).inSingletonScope()
        bind<LeadCategorisationService>(TYPES.LeadCategorisationService)
            .to(LeadCategorisationService).inSingletonScope()
        bind<AthenaServiceV2>(TYPES.AthenaServiceV2)
            .to(AthenaServiceV2).inSingletonScope()
    })
}

export function WorkerKernelModule(kernel: Inversify.Container) {
    return new Inversify.ContainerModule((bind: Inversify.interfaces.Bind) => {

        bind<IClickToCallNotificationHelper>(TYPES.ClickToCallNotificationHelper)
            .to(ClickToCallNotificationHelperFactory(kernel)).inSingletonScope()
        bind<IPushNotificationHelper>(TYPES.PushNotificationHelper)
            .to(PushNotificationHelperFactory(kernel)).inSingletonScope()
        bind<IEmailNotificationHelper>(TYPES.EmailNotificationHelper)
            .to(EmailNotificationHelperFactory(kernel)).inSingletonScope()
        bind<IOBDNotificationHelper>(TYPES.OBDNotificationHelper)
            .to(OBDNotificationHelperFactory(kernel)).inSingletonScope()
        bind<ISESHelper>(TYPES.SESHelper)
            .to(SESHelperFactory(kernel)).inSingletonScope()
        bind<SMSNotificationHelper>(TYPES.SMSNotificationHelper)
            .to(SMSNotificationHelper).inSingletonScope()
        bind<IKaleyraOBDHelper>(TYPES.KaleyraOBDHelper)
            .to(KaleyraHelperFactory(kernel)).inSingletonScope()
        bind<IInAppNotificationHelper>(TYPES.InAppNotificationHelper)
            .to(InAppNotificationHelperFactory(kernel)).inSingletonScope()
        bind<IWhatsappNotificationHelper>(TYPES.WhatsappNotificationHelper)
            .to(WhatsappNotificationHelperFactory(kernel)).inSingletonScope()
        bind<ISlackNotificationHelper>(TYPES.SlackNotificationHelper)
            .to(SlackNotificationHelperFactory(kernel)).inSingletonScope()
        bind<CampaignHelper>(TYPES.CampaignHelper)
            .to(CampaignHelper).inSingletonScope()

        bind<CommunicationWorkerConfigHelper>(TYPES.CommunicationWorkerConfigHelper)
            .to(CommunicationWorkerConfigHelper).inSingletonScope()

        bind<SMSNotificationConsumer>(TYPES.SMSNotificationConsumer)
            .to(SMSNotificationConsumer).inSingletonScope()
        bind<CTCNotificationConsumer>(TYPES.CTCNotificationConsumer)
            .to(CTCNotificationConsumer).inSingletonScope()
        bind<PNNotificationConsumer>(TYPES.PNNotificationConsumer)
            .to(PNNotificationConsumer).inSingletonScope()
        bind<EmailNotificationConsumer>(TYPES.EmailNotificationConsumer)
            .to(EmailNotificationConsumer).inSingletonScope()
        bind<OBDNotificationConsumer>(TYPES.OBDNotificationConsumer)
            .to(OBDNotificationConsumer).inSingletonScope()
        bind<IANNotificationConsumer>(TYPES.IANNotificationConsumer)
            .to(IANNotificationConsumer).inSingletonScope()
        bind<WhatsappNotificationConsumer>(TYPES.WhatsappNotificationConsumer)
            .to(WhatsappNotificationConsumer).inSingletonScope()
        bind<CreativeService>(TYPES.CreativeService)
            .to(CreativeService).inSingletonScope()
        bind<CommValidationsService>(TYPES.CommValidationsService)
            .to(CommValidationsService).inSingletonScope()

        bind<BumbleService>(TYPES.BumbleService)
            .to(BumbleServiceImpl).inSingletonScope()
        bind<NamespaceDataLayer>(TYPES.NamespaceDataLayer)
            .to(MysqlNamespaceDataLayerImpl).inSingletonScope()
        bind<CounterDataLayer>(TYPES.CounterDataLayer)
            .to(RedisCounterDataLayerImpl).inSingletonScope()

        bind<NotificationEventProcessingService>(TYPES.NotificationEventProcessingService)
            .to(NotificationEventProcessingService).inSingletonScope()
        bind<NotificationHelper>(TYPES.NotificationHelper)
            .to(NotificationHelper).inSingletonScope()
        bind<NotificationReportHelper>(TYPES.NotificationReportHelper)
            .to(NotificationReportHelper).inSingletonScope()
        bind<NotificationProcessorQueueConsumer>(TYPES.NotificationProcessorQueueConsumer)
            .to(NotificationProcessorQueueConsumer).inSingletonScope()
        bind<INotificationService>(TYPES.INotificationService)
            .to(ClevertapService)
        bind<SESDeliveryQueueConsumer>(TYPES.SESDeliveryQueueConsumer)
            .to(SESDeliveryQueueConsumer).inSingletonScope()
        bind<NotificationQueueConsumer>(TYPES.NotificationQueueConsumer)
            .to(NotificationQueueConsumer).inSingletonScope()
        bind<AttachmentHelper>(TYPES.AttachmentHelper)
            .to(AttachmentHelper).inSingletonScope()
        bind<MetricUtils>(TYPES.MetricUtils)
            .to(MetricUtils).inSingletonScope()
        bind<AWSUtils>(TYPES.AWSUtils)
            .to(AWSUtils).inSingletonScope()
        bind<KafkaUtils>(TYPES.KafkaUtils)
            .to(KafkaUtils).inSingletonScope()
        bind<MozartHelper>(TYPES.MozartHelper)
            .to(MozartHelper).inSingletonScope()
        bind<KnowlarityHelper>(TYPES.KnowlarityHelper)
            .to(KnowlarityHelper).inSingletonScope()
        bind<SolutionsInfiniHelper>(TYPES.SolutionsInfiniHelper)
            .to(SolutionsInfiniHelper).inSingletonScope()
        bind<LoadBalancerService>(TYPES.LoadBalancerService)
            .to(LoadBalancerService).inSingletonScope()
        bind<RetryService>(TYPES.RetryService)
            .to(RetryService).inSingletonScope()
        bind<NotificationAttemptHelper>(TYPES.NotificationAttemptHelper)
            .to(NotificationAttemptHelper).inSingletonScope()
        bind<IdentityService>(TYPES.IdentityService)
            .to(IdentityService).inSingletonScope()

        bind<IFirebaseService>(TYPES.FirebaseService)
            .to(FirebaseService).inSingletonScope()
        bind<PushNotificationService>(TYPES.PushNotificationService)
            .to(PushNotificationService).inSingletonScope()

        bind<ApiKeyMappingCache>(TYPES.ApiKeyMappingCache)
            .to(ApiKeyMappingCache).inSingletonScope()
        bind<IApiKeyMappingReadonlyDao>(TYPES.ApiKeyMappingReadonlyDao)
            .to(ApiKeyMappingReadonlyDaoMongoImpl).inSingletonScope()
        bind<ApiKeyMappingSchema>(TYPES.ApiKeyMappingSchema)
            .to(ApiKeyMappingSchema).inSingletonScope()
        bind<IBranchIOService>(TYPES.BranchIOService)
            .to(BranchIOService).inSingletonScope()
        bind<IFirebaseAnalyticsService>(TYPES.FirebaseAnalyticsService)
            .to(FirebaseAnalyticsService).inSingletonScope()
        bind<CommonUtils>(TYPES.CommonUtils)
            .to(CommonUtils).inSingletonScope()
        bind<UserProfileAttributeUtils>(TYPES.UserProfileAttributeUtils)
            .to(UserProfileAttributeUtils).inSingletonScope()
        bind<FeatureUtils>(TYPES.FeatureUtils)
            .to(FeatureUtils).inSingletonScope()
        bind<NotificationLogService>(TYPES.NotificationLogService)
            .to(NotificationLogService).inSingletonScope()
        bind<NotificationStatusUpdatePublisher>(TYPES.NotificationStatusUpdatePublisher)
            .to(NotificationStatusUpdatePublisher).inSingletonScope()
        bind<PushNotificationTokenNotRegisteredPublisher>(TYPES.PushNotificationTokenNotRegisteredPublisher)
            .to(PushNotificationTokenNotRegisteredPublisher).inSingletonScope()
        bind<UserInteractionPublisher>(TYPES.UserInteractionPublisher)
            .to(UserInteractionPublisher).inSingletonScope()
        bind<SolutionsInfiniSMSHelper>(TYPES.SolutionsInfiniSMSHelper)
            .to(SolutionsInfiniSMSHelper).inSingletonScope()
        bind<ValueFirstWhatsappHelper>(TYPES.ValueFirstWhatsappHelper)
            .to(ValueFirstWhatsappHelper).inSingletonScope()
        bind<KaleyraWhatsappHelper>(TYPES.KaleyraWhatsappHelper)
            .to(KaleyraWhatsappHelper).inSingletonScope()
        bind<GupshupSMSHelper>(TYPES.GupshupSMSHelper)
            .to(GupshupSMSHelper).inSingletonScope()
        bind<FonadaSMSHelper>(TYPES.FonadaSMSHelper)
            .to(FonadaSMSHelper).inSingletonScope()
        bind<UserContextHelper>(TYPES.UserContextHelper)
            .to(UserContextHelper).inSingletonScope()
        bind<FirebaseAdminService>(TYPES.FirebaseAdminService)
            .to(FirebaseAdminService).inSingletonScope()
        bind<NotificationWrapperCacheService>(TYPES.NotificationWrapperCacheService)
            .to(NotificationWrapperCacheService).inSingletonScope()

        bind<AthenaService>(TYPES.AthenaService)
            .to(AthenaService).inSingletonScope()


        bind<IReacherHelper>(TYPES.ReacherHelper)
            .to(ReacherHelper).inSingletonScope()
        bind<FunctionalTagsService>(TYPES.FunctionalTagsService)
            .to(FunctionalTagsService).inSingletonScope()

        bind<SprinklrWhatsappHelper>(TYPES.SprinklrWhatsappHelper)
            .to(SprinklrWhatsappHelper).inSingletonScope()

        bind<DatalakeMetricService>(TYPES.DatalakeMetricService)
            .to(DatalakeMetricService).inSingletonScope()

        bind<TaskManagementService>(TYPES.TaskManagementService)
            .to(TaskManagementService).inSingletonScope()

        bind<LeadCategorisationService>(TYPES.LeadCategorisationService)
            .to(LeadCategorisationService).inSingletonScope()

        bind<AthenaServiceV2>(TYPES.AthenaServiceV2)
            .to(AthenaServiceV2).inSingletonScope()

    })
}
