

const TYPES = {
    ApiKeyMappingCache: Symbol("IRIS::ApiKeyMappingCache"),
    ApiKeyMappingSchema: Symbol("IRIS::ApiKeyMappingSchema"),
    ApiKeyMappingReadonlyDao: Symbol("IRIS::ApiKeyMappingReadonlyDao"),
    SlackAuthMappingCache: Symbol("IRIS::SlackAuthMappingCache"),
    SlackAuthMappingSchema: Symbol("IRIS::SlackAuthMappingSchema"),
    SlackAuthMappingReadOnlyDao: Symbol("IRIS::SlackAuthMappingReadOnlyDao"),
    SlackAuthMappingReadWriteDao: Symbol("IRIS::SlackAuthMappingReadWriteDao"),
    SESHelper: Symbol("IRIS::SESHelper"),
    KaleyraOBDHelper: Symbol("IRIS::KaleyraOBDHelper"),
    ClickToCallNotificationHelper: Symbol("IRIS::ClickToCallNotificationHelper"),
    PushNotificationHelper: Symbol("IRIS::PushNotificationHelper"),
    OBDNotificationHelper: Symbol("IRIS::OBDNotificationHelper"),
    EmailNotificationHelper: Symbol("IRIS::EmailNotificationHelper"),
    InAppNotificationHelper: Symbol("IRIS::InAppNotificationHelper"),
    SMSNotificationHelper: Symbol("IRIS::SMSNotificationHelper"),
    CampaignHelper: Symbol("IRIS::CampaignHelper"),
    SegmentationHelper: Symbol("IRIS::SegmentationHelper"),
    BumbleService: Symbol("IRIS::BumbleService"),
    NamespaceDataLayer: Symbol("IRIS::NamespaceDataLayer"),
    CounterDataLayer: Symbol("IRIS::CounterDataLayer"),
    ScheduledNotificationConsumer: Symbol("IRIS::ScheduledNotificationConsumer"),
    SMSNotificationConsumer: Symbol("IRIS::SMSNotificationConsumer"),
    EmailNotificationConsumer: Symbol("IRIS::EmailNotificationConsumer"),
    PNNotificationConsumer: Symbol("IRIS::PNNotificationConsumer"),
    CTCNotificationConsumer: Symbol("IRIS::CTCNotificationConsumer"),
    IANNotificationConsumer: Symbol("IRIS::IANNotificationConsumer"),
    OBDNotificationConsumer: Symbol("IRIS::OBDNotificationConsumer"),
    WhatsappNotificationConsumer: Symbol("IRIS::WhatsappNotificationConsumer"),
    CreativeService: Symbol("IRIS::CreativeService"),
    CommunicationWorkerConfigHelper: Symbol("IRIS::CommunicationWorkerConfigHelper"),
    MozartService: Symbol("IRIS::MozartService"),
    ScaleService: Symbol("IRIS::ScaleService"),
    CampaignExecutionNotificationConsumer: Symbol("IRIS::CampaignExecutionNotificationConsumer"),
    EmailBounceQueueConsumer: Symbol("IRIS::EmailBounceQueueConsumer"),
    SMSDeliveryConsumer: Symbol("IRIS::SMSDeliveryConsumer"),

    WhatsappNotificationHelper: Symbol("IRIS::WhatsappNotificationHelper"),
    ValueFirstWhatsappHelper: Symbol("IRIS::ValueFirstWhatsappHelper"),
    KaleyraWhatsappHelper: Symbol("IRIS::KaleyraWhatsappHelper"),
    SlackNotificationHelper: Symbol("IRIS::SlackNotificationHelper"),
    WhatsappServiceConfiguration: Symbol("IRIS::WhatsappServiceConfiguration"),
    NotificationEventProcessingService: Symbol("IRIS::NotificationEventProcessingService"),
    NotificationHelper: Symbol("IRIS::NotificationHelper"),
    NotificationReportHelper: Symbol("IRIS::NotificationReportHelper"),
    INotificationService: Symbol("IRIS::INotificationService"),
    NotificationProcessorQueueConsumer: Symbol("IRIS::NotificationProcessorQueueConsumer"),
    SESDeliveryQueueConsumer: Symbol("IRIS::SESDeliveryQueueConsumer"),
    PNDeliveryEventConsumer: Symbol("IRIS::PNDeliveryEventConsumer"),
    NotificationQueueConsumer: Symbol("IRIS::NotificationQueueConsumer"),
    EmailOpenEventsConsumer: Symbol("IRIS::EmailOpenEventsConsumer"),
    FirebaseAppRemoveEventsConsumer: Symbol("IRIS::FirebaseAppRemoveEventsConsumer"),
    MozartJobUpdateConsumer: Symbol("IRIS::MozartJobUpdateConsumer"),
    SegmentBatchConsumer: Symbol("IRIS::SegmentBatchConsumer"),
    AttachmentHelper: Symbol("IRIS::AttachmentHelper"),
    MetricUtils: Symbol("IRIS::MetricUtils"),
    AWSUtils: Symbol("IRIS::AWSUtils"),
    KafkaUtils: Symbol("IRIS:KafkaUtils"),
    MozartHelper: Symbol("IRIS::MozartHelper"),
    KnowlarityHelper: Symbol("IRIS::KnowlarityHelper"),
    SolutionsInfiniHelper: Symbol("IRIS::SolutionsInfiniHelper"),
    LoadBalancerService: Symbol("IRIS::LoadBalancerService"),
    RetryService: Symbol("IRIS::RetryService"),
    NotificationAttemptHelper: Symbol("IRIS::NotificationAttemptHelper"),
    FirebaseService: Symbol("IRIS::FirebaseService"),
    PushNotificationService: Symbol("IRIS::PushNotificationService"),
    IdentityService: Symbol("IRIS::IdentityService"),
    BranchIOService: Symbol("IRIS::BranchIOService"),
    FirebaseAnalyticsService: Symbol("IRIS::FirebaseAnalyticsService"),
    UserUtils: Symbol("IRIS::UserUtils"),
    CommonUtils: Symbol("IRIS::CommonUtils"),
    UserProfileAttributeUtils: Symbol("IRIS::UserProfileAttributeUtils"),
    FeatureUtils: Symbol("IRIS::FeatureUtils"),
    SegmentationService: Symbol("IRIS::SegmentationService"),
    NotificationLogService: Symbol("IRIS::NotificationLogService"),
    CampaignRequestCacheService: Symbol("IRIS::CampaignRequestCacheService"),
    NotificationStatusUpdatePublisher: Symbol("IRIS::NotificationStatusUpdatePublisher"),
    PushNotificationTokenNotRegisteredPublisher: Symbol("IRIS::PushNotificationTokenNotRegisteredPublisher"),
    UserInteractionPublisher: Symbol("IRIS::UserInteractionPublisher"),
    NotificationStatusUpdateConsumer: Symbol("IRIS::NotificationStatusUpdateConsumer"),
    AppEventsConsumer: Symbol("IRIS::AppEventsConsumer"),
    UnsubscriptionService: Symbol("IRIS::UnsubscriptionService"),
    SolutionsInfiniSMSHelper: Symbol("IRIS::SolutionsInfiniSMSHelper"),
    GupshupSMSHelper: Symbol("IRIS::GupshupSMSHelper"),
    FonadaSMSHelper: Symbol("IRIS::FonadaSMSHelper"),
    UserContextHelper: Symbol("IRIS::UserContextHelper"),
    FirebaseAdminService: Symbol("IRIS::FirebaseAdminService"),
    HealthCheckService: Symbol("IRIS::HealthCheckService"),
    FailureAlertService: Symbol("IRIS::FailureAlertService"),
    NotificationWrapperCacheService: Symbol("IRIS::NotificationWrapperCacheService"),
    CampaignManagerCreativeService: Symbol("CampaignManager::CreativeService"),
    AthenaService: Symbol("IRIS::AthenaService"),
    ReacherHelper: Symbol("IRIS::ReacherHelper"),
    FunctionalTagsService: Symbol("IRIS::FunctionalTagsService"),
    SprinklrWhatsappHelper: Symbol("IRIS::SprinklrWhatsappHelper"),
    DatalakeMetricService: Symbol("IRIS::DatalakeMetricService"),
    TaskManagementService: Symbol("IRIS::TaskManagementService"),
    CampaignManagerService: Symbol("IRIS::CampaignManagerService"),
    CommValidationsService: Symbol("IRIS::CommValidationsService"),
    AlertService: Symbol("IRIS::AlertService"),
    LeadCategorisationService: Symbol("IRIS::LeadCategorisationService"),
    AthenaServiceV2: Symbol("IRIS::AthenaServiceV2")
}

export default TYPES