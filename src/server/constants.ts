import {C2<PERSON>erviceAccount, ClickTo<PERSON>allCreative, CreativeType, IRIS_QUEUE_NAMES, ServiceProvider} from "../iris-common"
import {Tenant} from "@curefit/user-common"
import {HourMin} from "@curefit/base-common/dist/src/models/Common"
import {CommonUtils} from "./utils/CommonUtils"
import {FunctionTag} from "@curefit/iris-common"

class Constants {
    static SMS: string = "SMS"
    static OBD: string = "OBD"
    static WHATSAPP: string = "WHATSAPP"
    static EMAIL: string = "EMAIL"
    static CTC: string = "CTC"
    static PN: string = "PN"
    static IAN: string = "IAN"

    static SMS_PR_JOB_QUEUE: IRIS_QUEUE_NAMES = "IRIS_SMS_JOBS"
    static OBD_PR_JOB_QUEUE: IRIS_QUEUE_NAMES = "IRIS_OBD_JOBS"
    static WHATSAPP_PR_JOB_QUEUE: IRIS_QUEUE_NAMES = "IRIS_WHATSAPP_JOBS"
    static EMAIL_PR_JOB_QUEUE: IRIS_QUEUE_NAMES = "IRIS_EMAIL_JOBS"
    static CTC_PR_JOB_QUEUE: IRIS_QUEUE_NAMES = "IRIS_CTC_JOBS"
    static PN_PR_JOB_QUEUE: IRIS_QUEUE_NAMES = "IRIS_PN_JOBS"
    static IAN_PR_JOB_QUEUE: IRIS_QUEUE_NAMES = "IRIS_IAN_JOBS"
    static MOZART_JOB_STATUS_UPDATES_QUEUE: IRIS_QUEUE_NAMES = "MOZART_JOB_STATUS_UPDATES"
    static MOZART_SEGMENT_BATCH_QUEUE: IRIS_QUEUE_NAMES = "MOZART_SEGMENT_BATCH"
    static SCHEDULED_NOTIFICATIONS_QUEUE: IRIS_QUEUE_NAMES = "IRIS_SCHEDULED_NOTIFICATIONS"
    static NOTIFICATION_STATUS_UPDATE_QUEUE: IRIS_QUEUE_NAMES = "IRIS_NOTIFICATION_STATUS_UPDATE"
    static CAMPAIGN_EXECUTION_NOTIFICATION_QUEUE: IRIS_QUEUE_NAMES = "IRIS_CAMPAIGN_EXECUTION_NOTIFICATION"
    static SQS_EMAIL_BOUNCE_ALL: IRIS_QUEUE_NAMES = "IRIS_EMAIL_BOUNCE_ALL"
    static SMS_CALLBACKS: IRIS_QUEUE_NAMES = "IRIS_SMS_CALLBACKS"

    static PENDING_SEGMENT_TASK_ALERT_THRESHOLD_RATIO: number = 0.9

    static APP_ID_CUREFIT: string = "curefit"
    static APP_ID_LIVEFIT: string = "livefit"
    static APP_ID_TEAMFIT: string = "teamfit"
    static APP_ID_CULTAPP: string = "cult-app"
    static APP_ID_SUGARFIT: string = "sugarfit"
    static APP_ID_ULTRAFIT: string = "ultrafit"
    static APP_ID_CULTSPORT: string = "cultsport"
    static APP_ID_CULTWATCH: string = "cultwatch"

    static INTERNAL_APP_IDS: string[] = [Constants.APP_ID_TEAMFIT, Constants.APP_ID_CULTAPP]

    static TENANT_HEADER_KEY: string = "x-tenant-id"

    static IOS_BUNDLE_ID_CUREFIT: string = "fit.cure.ios"
    static IOS_BUNDLE_ID_CUREFIT_STAGE: string = "fit.cure.ios.beta"
    static IOS_BUNDLE_ID_LIVEFIT: string = "fit.cure.intl.ios"
    static IOS_BUNDLE_ID_LIVEFIT_STAGE: string = "fit.cure.intl.ios.beta"

    static SMS_LENGTH: number = 180

    static THROTTLE_RESERVOIR_REFRESH_INTERVAL: number = 60 * 1000
    static EMAIL_THROTTLE_RESERVOIR_REFRESH_INTERVAL: number = 1000         //1sec
    static WHATSAPP_THROTTLE_RESERVOIR_REFRESH_INTERVAL: number = 1000         //1sec
    static SQS_INIT_WAIT_TIME: number = 30 * 1000

    static OBD_WORKER_BATCH_SIZE: number = 10
    static SMS_WORKER_BATCH_SIZE: number = 10
    static EMAIL_WORKER_BATCH_SIZE: number = 10
    static PN_WORKER_BATCH_SIZE: number = 10
    static IAN_WORKER_BATCH_SIZE: number = 10
    static CTC_WORKER_BATCH_SIZE: number = 10
    static WHATSAPP_WORKER_BATCH_SIZE: number = 10

    // These limits are added to prevent misuse; only applicable on promotional traffic
    static OBD_PER_MIN_LIMIT: number = 2000
    static SMS_PER_MIN_LIMIT: number = 25000

    /*
        SES Limits:
            txn     us-east-1
            - Sending Quota: Send 885100 emails per 24 hour period
            - Max Send Rate: 400 emails/second
            promo   ap-south-1
            - Send 4007500 emails per 24 hour period
            - Max Send Rate: 500 emails/second
        TODO: SES Reputation should be monitored. Maybe use AWS CLI to show this on prometheus dashboard
        https://console.aws.amazon.com/ses/home?region=us-east-1#reputation-dashboard:
    */
    static EMAIL_PER_MIN_LIMIT: number = 20000
    static EMAIL_PER_SEC_LIMIT: number = 400
    static PN_PER_MIN_LIMIT: number = 50000 // PNs don't cost us, but need to limit app traffic at any given point
    static IAN_PER_MIN_LIMIT: number = 10000
    static CTC_PER_MIN_LIMIT: number = 2000
    static WHATSAPP_PER_MIN_LIMIT: number = 420
    static WHATSAPP_PER_SEC_LIMIT: number = 60

    static OBD_WORKER_DELAY: number = 0
    static SMS_WORKER_DELAY: number = 0
    static EMAIL_WORKER_DELAY: number = 0
    static PN_WORKER_DELAY: number = 0
    static IAN_WORKER_DELAY: number = 0
    static CTC_WORKER_DELAY: number = 0
    static WHATSAPP_WORKER_DELAY: number = 1000 / 60        // Ensures we process 60 requests in any 1 second window

    static USER_PROFILE_ATTRIBUTE_KEY_FULL_NAME = "fullname"
    static USER_PROFILE_ATTRIBUTE_KEY_PHONE = "phone"
    static USER_PROFILE_ATTRIBUTE_KEY_EMAIL = "email"
    static USER_PROFILE_ATTRIBUTE_KEY_APP_ID = "appid"
    static USER_PROFILE_ATTRIBUTE_KEY_IS_SUBSCRIBED = "issubscribed"
    static USER_PROFILE_ATTRIBUTE_KEY_TIMEZONE = "timezone"

    // Profile attributes should written like __attribute__
    static USER_PROFILE_ATTRIBUTE_TAG_PREFIX = "___"
    static USER_PROFILE_ATTRIBUTE_TAG_SUFFIX = "___"
    static USER_PROFILE_ATTRIBUTE_TAG_REG_EXP = /___.*?___/

    static GLOBAL_ATTRIBUTE_TAG_PREFIX = "---"
    static GLOBAL_ATTRIBUTE_TAG_SUFFIX = "---"
    static GLOBAL_ATTRIBUTE_TAG_REG_EXP = /---.*?---/

    static PERSONALIZED_ATTRIBUTE_TAG_PREFIX = "$$$"
    static PERSONALIZED_ATTRIBUTE_TAG_SUFFIX = "$$$"
    static PERSONALIZED_ATTRIBUTE_TAG_REG_EXP = /\$\$\$.*?\$\$\$/

    static IRIS_FEATURE_CONFIG: string = "IRIS_FEATURE_CONFIG"

    static GLOBAL_DND_START_CONFIG_KEY: string = "globalDNDStart"
    static GLOBAL_DND_END_CONFIG_KEY: string = "globalDNDEnd"
    // 22:00 local time
    static GLOBAL_DND_START_DEFAULT: HourMin = {
        hour: 22,
        min: 0
    }
    // 06:00 local time
    static GLOBAL_DND_END_DEFAULT: HourMin = {
        hour: 6,
        min: 0
    }
    static DUPLICATE_NOTIFICATION_EXPIRY_KEY: string = "duplicateNotificationExpiry"
    static DUPLICATE_NOTIFICATION_EXPIRY_DEFAULT: [CreativeType, number][] = [
        ["PUSH_NOTIFICATION", 120],
        ["SMS", 120],
        ["EMAIL", 120],
        ["IN_APP_NOTIFICATION", 120],
        ["OBD", 120],
        ["CLICK_TO_CALL", 120],
        ["WHATSAPP", 120],
        ["SLACK", 120]]

    static PROMOTIONAL_NOTIFICATION_THROTTLING_LIMIT_KEY: string = "promotionalNotificationThrottlingLimit"
    static PROMOTIONAL_NOTIFICATION_THROTTLING_LIMIT_DEFAULT: [CreativeType, number][] = [
        ["PUSH_NOTIFICATION", 120],
        ["SMS", 120],
        ["EMAIL", 120],
        ["IN_APP_NOTIFICATION", 120],
        ["OBD", 120],
        ["CLICK_TO_CALL", 120],
        ["WHATSAPP", 120],
        ["SLACK", 120]]

    static PUSH_NOTIFICATION_DEFAULT_TIME_TO_LIVE_IN_SECONDS = 24 * 60 * 60 // 24 hours
    static PUSH_NOTIFICATION_MAX_TIME_TO_LIVE_IN_SECONDS = 28 * 24 * 60 * 60 // 28 days
    static PUSH_NOTIFICATION_AUTO_COLLAPSE_TIME_IN_MILLISECONDS = 10 * 60 * 60 * 1000 //10 hours
    static PUSH_NOTIFICATION_DEFAULT_GROUP_ID = "123" //This is used to group all PNs. Id is same on App side as well.

    static THROTTLED_NOTIFICATION_SCHEDULE_DELAY_IN_MINUTES_CONFIG_KEY: string = "throttledNotificationScheduleDelayInMinutes"
    static THROTTLED_NOTIFICATION_SCHEDULE_DELAY_IN_MINUTES_DEFAULT: number = 5

    static IAN_REDIS_KEY_EXPIRY: number = 24 * 60 * 60
    static GOOGLE_CLIENT_ID: string = "294300848933-u77ft79u2ociqso6c036lurujvgc5tc5.apps.googleusercontent.com"
    static GOOGLE_CLIENT_SECRET: string = "5eMmn7a-YbHjUan2tPo4hrnc"
    static GOOGLE_CALLBACK_URL: string = "/auth/google/callback"
    static GOOGLE_SCOPE_URL1: string = "https://www.googleapis.com/auth/plus.login"
    static GOOGLE_SCOPE_URL2: string = "https://www.googleapis.com/auth/plus.profile.emails.read"
    static SESSION_SECRET_KEY: string = "EatFit Iris"

    static SQS_BATCH_SIZE: number = 10

    static BUMBLE_NAMESPACE: string = "UserCom"
    static BUMBLE_DEFAULT_NAMESPACE: string = "curefit"

    static BUMBLE_DIMENSION_CHANNEL: string = "channel"
    static BUMBLE_DIMENSION_VERTICAL: string = "vertical"
    static BUMBLE_DIMENSION_OBJECTIVE: string = "objective"
    static REQUEST_BODY_SIZE_LIMIT: string = "200kb"

    //16000 as limit on TEXT in mysql is 64kb (max possible 4b per character)
    static USERTAG_DB_ENTRY_SIZE_LIMIT: number = 16000
    // TRAI regulations enforce tag lenght to be max 30 characters
    static SMS_TEMPLATE_VALUE_MAX_LENGTH: number = 30

    // We support both old and new version of tags.
    // The old tags are not supported by handleabars (parsing error due to "|")
    // There could be old creatives still using it (using mustache for parsing), hence cannot simply remove it
    static USER_EMAIL_TAG_OLD: string = "_|*user_email*|_"
    static USER_EMAIL_TAG: string = "__user_email__"
    static USER_PHONE_TAG_OLD: string = "_|*user_phone*|_"
    static USER_PHONE_TAG: string = "__user_phone__"
    static USER_NAME_TAG_OLD: string = "_|*user_name*|_"
    static USER_NAME_TAG: string = "__user_name__"
    static USER_SUBSCRIBED_OLD: string = "_|*user_subscribed*|_"
    static USER_SUBSCRIBED: string = "__user_subscribed__"
    static PREFERRED_CENTER: string = "preferred_center"
    static PLAY_PREFERRED_CENTER: string = "play_preferred_center"
    static PLAY_PREFERRED_WORKOUT: string = "play_preferred_workout"
    static CITY_ID = "cityid"
    static USER_ID_TAG: string = "__user_id__"

    static USER_EMAIL_UNSUBSCRIPTION_URL_TAG: string = "__user_unsubscribe_email_url__"
    static USER_EMAIL_UNSUBSCRIPTION_BASE_URL_PRODUCTION: string = " https://www.cure.fit/unsubscribe/d"
    static USER_EMAIL_UNSUBSCRIPTION_BASE_URL_PRODUCTION_CULTSPORT: string = " https://www.cultsport.com/unsubscribe/d"

    static USER_EMAIL_UNSUBSCRIPTION_BASE_URL_ALPHA_CULTSPORT: string = " https://alpha.cultsport.com/unsubscribe/d"

    static USER_EMAIL_UNSUBSCRIPTION_BASE_URL_STAGE: string = " https://stage1234.cure.fit/unsubscribe/d"
    static USER_EMAIL_UNSUBSCRIPTION_BASE_URL_STAGE_CULTSPORT: string = " https://stage1234.cure.fit/unsubscribe/d"

    static EMAIL_EXTERNALID_PREFIX = "Email_SES_"
    static SMS_SI_EXTERNALID_PREFIX = "SMS_Solution_Infini_"
    static SMS_GUPSHUP_EXTERNALID_PREFIX = "SMS_GUPSHUP_"
    static SMS_FONADA_EXTERNALID_PREFIX = "SMS_FONADA_"
    static WHATSAPP_EXTERNALID_PREFIX = "Whatsapp_Value_First_"
    static FIREBASE_EXTERNALID_PREFIX = "FIREBASE_"
    static FIREBASE_MESSAGE_ACCEPTED_EVENT = "MESSAGE_ACCEPTED"
    static FIREBASE_MESSAGE_DELIVERED_EVENT = "MESSAGE_DELIVERED"

    static NOTIFICATION_META_PAGE_SIZE: number = 25
    static NOTIFICATION_META_BULK_UPDATE_SIZE_LIMIT: number = 100

    static DRY_RUN_CAMPAIGN_PREFIX = "CUREFIT_DRY_RUN"

    static IRIS: string = "iris"

    static SNS_EVENT_USER_INTERACTION: string = "user_interaction"
    static SNS_EVENT_CREATIVE_REGISTRATION_STATUS_UPDATE = "creative_registration_status_update"
    static SNS_EVENT_NOTIFICATION_STATUS_UPDATE: string = "notification_status_update"
    static SNS_PUSH_NOTIFICATION_TOKEN_NOT_REGISTERED: string = "push_notification_token_not_registered"
    static SNS_EMAIL_VALIDATION_REPORTS: string = "email_validation_reports"
    static SNS_EMAIL_BOUNCE_REPORT: string = "email_bounce_reports"

    //Service Providers
    static FIREBASE: ServiceProvider = "FIREBASE"
    static SOLUTIONS_INFINI: ServiceProvider = "SOLUTIONS_INFINI"
    static KNOWLARITY: ServiceProvider = "KNOWLARITY"
    static VALUE_FIRST: ServiceProvider = "VALUE_FIRST"
    static KALEYRA: ServiceProvider = "KALEYRA"
    static SPRINKLR: ServiceProvider = "SPRINKLR"

    //Retry Constants
    static RETRY = {
        "RETRY_CHECK_TIME_IN_MS": 10000,
        "INVALIDATE_PROVIDER_SERVING_STATUS": 5,
        "EXPIRY_TIME_PROVIDER_SERVING_STATUS_IN_SEC": 300,
        "EXPIRY_TIME_LB_KEY_IN_SEC": 3000,
    }

    static USER_ID_TYPES = {CUREFIT_USER_ID: "CUREFIT_USER_ID", EMAIL_ID: "EMAIL_ID", PHONE_NUMBER: "PHONE_NUMBER"}

    // FCM configurations
    static PUSH_NOTIFICATION_TRANSACTIONAL_CHANNEL: string = "curefit_notification_id"
    static PUSH_NOTIFICATION_PROMOTIONAL_CHANNEL: string = "curefit_promo_id"
    static PUSH_NOTIFICATION_FCM_PRIORITY_HIGH: ("high" | "normal") = "high"
    static PUSH_NOTIFICATION_FCM_PRIORITY_NORMAL: ("high" | "normal") = "normal"

    /*
        Error codes for firebase admin sdk (FCM HTTP v1)
        https://firebase.google.com/docs/cloud-messaging/send-message#admin_sdk_error_reference
     */
    static FCM_ADMIN_FAIL_REASON_PREFIX: string = "FCM_ADMIN"
    static FCM_ADMIN_RESPONSE_ERROR_CODE_PREFIX: string = "messaging/"
    static FCM_ADMIN_NOT_REGISTERED_ERROR: string = "registration-token-not-registered"

    /*
        Error codes for legacy FCM api
        https://firebase.google.com/docs/cloud-messaging/http-server-ref
     */
    static FCM_FAIL_REASON_PREFIX: string = "FCM"
    static FCM_NOT_REGISTERED_ERROR: string = "NotRegistered"

    static IT_SUPPORT_EMAIL: string = "<EMAIL>"

    static KALEYRA_WHATSAPP_CONF = {
        defaultNumber: "+918951862986",
        SID: process.env.KALEYRA_WHATSAPP_SID,
        apiKey: process.env.KALEYRA_WHATSAPP_APIKEY
    }

    //supporting 5 template variables now
    static SPRINKLR_WHATSAPP_CONF = {
        defaultNumber: "************",
        apiKey: "t4ux3qrg8pqpvqvggfbj3yt6",
        authKey: "Bearer " + "hOunpdYrzF1VsxbNb0JWvB3cqL3JoMQhWKpdJwhIoQxhMmMyYWZhZC1hYzVhLTNkNzUtOGQyZi04YmFjMWQxZjdjNGI=",
        baseUrl: "https://api2.sprinklr.com/prod4/api/v2/deflect",
        callbackUrl: this.getIrisPublicBaseUrl() + "/notifications/whatsappDelivery/sprinklr",
        variableOrder: ["_c_63f086fb277802357b132bce", "_c_63f08767277802357b137a2c",
            "_c_63f087bc277802357b13b1c0", "_c_63f08849277802357b140cf3", "_c_63f087e9277802357b13ce4f"],
        attachmentVariable: "_c_636b777f67f59a3cf152b794"
    }

    static getSprinklrWhatsappAccountId(fromNumber: number): string {
        switch (fromNumber) {
            case ************:
                return "*********"
            case ************:
                return "*********"
            case ************:
                return "*********"
            case ************:
                return "*********"
            case ************:
                return "*********"
            case ************:
                return "*********"
            case ************:
                return "*********"
            case ************:
                return "*********"
            default:
            {
                throw new Error(`getSprinklrWhatsappAccountId: No account found for fromNumber ${fromNumber}`)
            }
        }
    }

    static getGlobalRateLimits(worker: string) {
        switch (worker) {
            case "SMS": {
                return 10000
            }
            case "EMAIL": {
                return 3000
            }
            case "OBD": {
                return 2000
            }
            case "CTC": {
                return 2000
            }
            case "WHATSAPP": {
                return 3000
            }
            default: {
                return 100000
            }
        }
    }

    //Service Account selection for Calling
    static C2C_SERVICE_ACCOUNT = {
        CUREFIT: <C2CServiceAccount>"CUREFIT",
        SUGARFIT: <C2CServiceAccount>"SUGARFIT",
        SUGARFIT_EMR_CALLING: <C2CServiceAccount> "SUGARFIT_EMR_CALLING",
        SUGARFIT_OPS_CALLING: <C2CServiceAccount> "SUGARFIT_OPS_CALLING",
        SUGARFIT_EMR_CALLING_DOCTOR: <C2CServiceAccount> "SUGARFIT_EMR_CALLING_DOCTOR",
        ONEFITPLUS: <C2CServiceAccount> "ONEFITPLUS",
    }

    static getFireBaseUrl(path: string = "") {
        return "https://fcm.googleapis.com" + path
    }

    static getFireBaseDataUrl(path: string= "") {
        return "https://fcmdata.googleapis.com" + path
    }

    static getFireBaseApiKey(appId?: string) {
        if (!appId) {
            appId = this.APP_ID_CUREFIT
        }

        if (CommonUtils.isEnvironmentProductionOrAlpha()) {
            if (appId.toLowerCase() === this.APP_ID_CUREFIT) {
                return "key=AIzaSyAKVsUjmDo1eJaLvoZKaE7w91OItTDqdkE"
            } else if (appId.toLowerCase() === this.APP_ID_TEAMFIT) {
                return "key=AAAASHiAYn4:APA91bEYsCG-SNY2aBI1-wJbUnmNd31hhuVr9t_pi0HNuX_RhlIA4mhwQ_W7Ts1C6itQ2GQ89B7TnOZInKSnAXNndJlcA51roCFKMwqi4MDxyzYntXKh1LbMIPt0qbQDKHHgsh1VEBfm"
            } else if (appId.toLowerCase() === this.APP_ID_LIVEFIT) {
                return "key=AAAAtuaDAAI:APA91bHELVb1UDLV8r8wlbeG1pFmjSG-HTlgnC2FSJPritguwJzLGbl_e5gbYBxWa1M3NeumkFy7dwKP3MzCx5Y5CI3c-o3ME0xz8Yd2j1awc1MLMmEEQNYYUWF1A4xV8NqprDPWEPXo"
            }
        } else {
            if (appId.toLowerCase() === this.APP_ID_CUREFIT) {
                return "key=AIzaSyAKVsUjmDo1eJaLvoZKaE7w91OItTDqdkE"
            } else if (appId.toLowerCase() === this.APP_ID_TEAMFIT) {
                return "key=AAAASHiAYn4:APA91bEYsCG-SNY2aBI1-wJbUnmNd31hhuVr9t_pi0HNuX_RhlIA4mhwQ_W7Ts1C6itQ2GQ89B7TnOZInKSnAXNndJlcA51roCFKMwqi4MDxyzYntXKh1LbMIPt0qbQDKHHgsh1VEBfm"
            } else if (appId.toLowerCase() === this.APP_ID_LIVEFIT) {
                return "key=AAAAtuaDAAI:APA91bHELVb1UDLV8r8wlbeG1pFmjSG-HTlgnC2FSJPritguwJzLGbl_e5gbYBxWa1M3NeumkFy7dwKP3MzCx5Y5CI3c-o3ME0xz8Yd2j1awc1MLMmEEQNYYUWF1A4xV8NqprDPWEPXo"
            }
        }
    }

    static getBlacklistCreativeQuery(timeWindow: number, failReasons: string, failureRatio: number, minSendCount: number): string {
        return "SELECT creativeId FROM (" +
            "SELECT creativeId, " +
            "SUM(CASE WHEN failReason IN (" + failReasons + ") THEN 1 ELSE 0 END) * 1.00 / NULLIF((COUNT(*) - SUM(CASE WHEN failReason = 'CREATIVE_BLACKLISTED' THEN 1 ELSE 0 END)),0) AS blacklist_ratio, " +
            "(COUNT(*) - SUM(CASE WHEN failReason = 'CREATIVE_BLACKLISTED' THEN 1 ELSE 0 END)) AS sendCount " +
            "FROM pk_curefitplatforms_cfdb.notification " +
            "WHERE DATE(createdAt) > DATE_ADD('day', -" + timeWindow + ", CURRENT_DATE) " +
            "GROUP BY creativeId" +
            ") AS blacklist_check " +
            "WHERE blacklist_ratio > " + failureRatio + " " +
            "AND sendCount > " + minSendCount
    }

    static getFirebaseDeliveryDataUrl(firebaseProject : string , appId : string){
        return this.getFireBaseDataUrl(`/v1beta1/projects/${firebaseProject}/androidApps/${appId}/deliveryData`)
    }
    
    static FIREBASE_PROJECT= "cult-155917"

    static FIREBASE_APP_ID= "1:************:android:36f534ca8e233356"

    static KALEYRA_SOIP_URL = "https://cloud-api.in.kaleyra.io/v1/HXIN1701440790IN/messages/soip"

    static SOLUTIONS_INFINI_SMS_CONFIG = {
        NAME: "SOLUTIONS_INFINI",
        API_URL: {
            "INDIA": "https://api-alerts.kaleyra.com/v4/?method=sms&api_key={{{apiKey}}}&to={{{to}}}&sender={{{sender}}}&message={{{message}}}&dlrurl={{{dlrurl}}}&format=json&custom=1,2&flash=0",
            "DUBAI": "https://api-global.kaleyra.com/v4/?method=sms&api_key={{{apiKey}}}&to={{{to}}}&sender={{{sender}}}&message={{{message}}}&dlrurl={{{dlrurl}}}",
            "USA-CANADA": "https://api-global.kaleyra.com/v4/?method=sms&api_key={{{apiKey}}}&to={{{to}}}&sender={{{sender}}}&message={{{message}}}&dlrurl={{{dlrurl}}}"
        },
        API_KEY: {
            TRANSACTIONAL: {
                "INDIA": "Abd31c725ac71f196172d40d4bffd1f64",
                "DUBAI": "A9e15bcf3ddcef8c96242cbf65cdf0c61"
            },
            PROMOTIONAL: {
                "INDIA": "A9f91f167400b8ca62da72de3c63f5919",
                "DUBAI": "A56668e0c0b6594ad29cd816c2fe0b09d"
            },
            OTP_ROUTE: {
                "INDIA": "Ae148efad3b6f61d9b77d5e22417576ca",
                "DUBAI": "A9e15bcf3ddcef8c96242cbf65cdf0c61",
                "USA-CANADA": "A40a25052e59fc5ee6f67822b6d184f53"
            }
        },
        DELIVERY_URL: {
            STAGE: "https://iris-api.stage.curefit.co/notifications/ext/smsDelivery?custom={{id}}&delivat={delivat}&sid={sid}",
            PRODUCTION: "https://iris-api.curefit.co/notifications/ext/smsDelivery?custom={{id}}&delivat={delivat}&sid={sid}"
        },
        STATUS_LIST: {
            FAILED: [
                "ABSENT-SUB",                           // Telecom services do not provide service for a particular number. / Mobile Subscriber not reachable.
                "BARRED",                               // End user has enabled message barring system. / Subscriber only accepts messages from Closed User Group [CUG].
                "BLACKLST",
                "BLACKLIST",                            // The mobile number to which you want to send is blacklisted.
                "DNDNUMB",
                "DUPLICATE",
                "EXPIRED",                              // SMS expired after multiple re-try.
                "FAILED",                               // SMS expired due to roaming limitations. / Failed to process the message at the operator level.
                "HANDSET-BUSY",                         // Subscriber is in busy condition.
                "HANDSET-ERR",                          // Problem with Handset or handset failed to get the complete message. / Handset does not support the incoming messages.
                "INV-NUMBER",
                "INVALID-SUB",                          // The number does not exist. / Failed to locate the number in the HLR database.
                "MEMEXEC",                              // Handset memory full.
                "MOB-OFF",
                "NET-ERR",                              // Subscriber’s operator not supported. / Gateway mobile switching error.
                "NO-DLR-OPTR",
                "OUTPUT-REJ",
                "REJECTD",                              // SMS Rejected as the number is blacklisted by the operator.
                "REJECTED",                             // SMS Rejected as the number is blacklisted by the operator.
                "REJECTED-MULTIPART",                   // Validation fail [SMS over 160 characters].
                "SERIES-BLK",
                "SERIES-BLOCK",                         // Series blocked by the operator.
                "SENDER-ID-NOT-FOUND",
                "SNDRID-NOT-ALLOTED",
                "SERVER-ERR",
                "TEMPLATE-NOT-FOUND",
                "TIME-OUT-PROM",                        // Off Promotional Hours.    Time out for promotional SMS.
                "UNDELIV",                              // Failed due to network errors.
                "TL-ID-INVLD",                          // The template ID that customer using is Invalid.
                "TEMPLATE-ID-NOT-FOUND",
                "INV-TEMPLATE-MATCH",
                "TEMPLATE-NOT-MATCHED",
                "HEADER-NOT-REGISTERED-FOR-TEMPLATE",
                "TEMPLATE-INACTIVE",
                "PRFT-NT-MTCH",                         // This error code is received for Service Explicit content only. When the end-user is registered under DND. This is completely based on the DND preference selected by the end-user.
                "INVALID_TEMPLATE_ID",
                "TEMPLATE-BLACKLISTED",
                "TEMPLATE-VARIABLE-EXCEEDED-MAX-LENGTH",
                "OPTOUT-REJ",
                "OUTPUT-REJ",
                "NUM-LIMIT",
                "SERIES-BLOCK",
                "INV-NUMBER",
                "NOT-OPTIN",
                "SUBMIT-FAIL",
                "MAX-LIMIT",                            // The message could not be delivered as it exceeded the maximum character limit.
                "CON-DND",                              // Contact is marked as DND
                "QUEUED",                               // SMS is queued
                "NO-DLR-OPTR",                          // Awaiting Report from Operator
                "SNDRID-NOT-ALLOTED",                   // Sender ID not allocated.
                "TL-NT-MTCHD",                          // Template Mismatch. The template does not match the template registered on the DLT platform.
                "SDR-TL-MSMAT",                         // Sender ID Template Mismatch The sender ID used along with the template ID do not match as per the registration in DLT portal.
                "TL-NT-FOUND",                          // Template Not Found  The message content sent was not found in the DLT platform as a registered template.
                "TL-LMT-EXCED",                         // Template Limit Exceeded The variable content sent exceeds the allowed limit.
                "TL-INACTIVE",                          // Template Inactive   The message template sent is in inactive status at the DLT platform level.
                "KL-TEMPLATE-ID-NOT-FOUND",             // Template ID Missing SMS not sent as matching template ID not found for content.
                "INV-MOB-PATTRN",                       // Invalid mobile number   The recipient mobile number is invalid.
                "INV-ENTIY-ID",                         // Invalid Entity ID   The Entity ID that customer used is Invalid.
                "SNDR-NT-REG",                          // Sender Not Registered   Sender ID is not registered in the DLT Platform.
                "SNDR-NT-REGD",                         // Sender Not Registered   Sender ID is not registered in the DLT Platform.
                "KLR-INV-NUMBER",                       // Invalid mobile number   The recipient mobile number is invalid.
                "URL-NT-SENT",                          // URL is invalid   SMS not sent as replaceable URL details not sent in API.
                "URL-SHRT-FAILED",                      // SMS not sent as URL shortening failed. Please contact <NAME_EMAIL>.
                "TL-BLKLST",	                        // Template Blacklisted	The message template pushed has been blacklisted at the DLT platform level.
                "SNDR-NT-MTCH",	                        // Sender Not Registered	The sender ID used is not registered in the DLT platform level.
            ],
            RED_FLAG: ["NO-CREDITS", "INF-CREDIT",      // Insufficient Credits
                "SPAM",                                 // Spam SMS
                "NOTALLOWED",                           // Country not Enabled
            ]
        }
    }

    static GUPSHUP_SMS_CONFIG = {
        NAME: "GUPSHUP",
        API_URL: {
            "INDIA": "https://enterprise.smsgupshup.com/GatewayAPI/rest?method=SendMessage&send_to={{to}}&msg={{message}}&msg_type=TEXT&userid={{gupshupAccountId}}&auth_scheme=plain&password={{gupshupAccountPassword}}&v=1.1&format=text&msg_id={{notifId}}",
            "DUBAI": "https://api-global.kaleyra.com/v4/?method=sms&api_key={{{apiKey}}}&to={{{to}}}&sender={{{sender}}}&message={{{message}}}&dlrurl={{{dlrurl}}}",
            "USA-CANADA": "https://api-global.kaleyra.com/v4/?method=sms&api_key={{{apiKey}}}&to={{{to}}}&sender={{{sender}}}&message={{{message}}}&dlrurl={{{dlrurl}}}"
        },
        ACCOUNT_CREDS: {
            TRANSACTIONAL: {
                "INDIA": {
                    "id": process.env.GUPSHUP_SMS_TRANSACTIONAL_ID,
                    "password": process.env.GUPSHUP_SMS_TRANSACTIONAL_PASSWORD
                }
            },
            PROMOTIONAL: {
                "INDIA": {
                    "id": process.env.GUPSHUP_SMS_PROMO_ID,
                    "password": process.env.GUPSHUP_SMS_PROMO_PASSWORD
                }
            },
            OTP_ROUTE: {
                "INDIA": {
                    "id": process.env.GUPSHUP_SMS_OTP_ID,
                    "password": process.env.GUPSHUP_SMS_OTP_PASSWORD
                }
            }
        }
    }
    
    static FONADA_SMS_CONFIG = {
        NAME: "FONADA",
        API_URL: "https://api.flash49.com/fe/api/v1/send?username={{username}}&password={{password}}&unicode=false&from={{from}}&to={{to}}&text={{text}}&dltContentId={{templateId}}",
        ACCOUNT_CREDS: {
            TRANSACTIONAL_PROMOTIONAL_COMMS: {
                "id": process.env.FONADA_SMS_TRANS_PROMO_ID,
                "password": process.env.FONADA_SMS_TRANS_PROMO_PASSWORD
            },
            OTP_ROUTE: {
                "id": process.env.FONADA_SMS_OTP_ID,
                "password": process.env.FONADA_SMS_OTP_PASSWORD 
            }
        }
    }


    static CLICK_TO_CALL_CONFIG = {
        SERVICE_PROVIDERS: {
            TRANSACTIONAL: {
                "INDIA": {
                    "SUGARFIT": ["SOLUTIONS_INFINI"],
                    "CUREFIT": ["SOLUTIONS_INFINI", "KNOWLARITY"]
                },
                "DUBAI": ["KNOWLARITY"]
            },
            CONFIDENTIAL: {
                "INDIA": {
                    "SUGARFIT": ["SOLUTIONS_INFINI"],
                    "CUREFIT": ["SOLUTIONS_INFINI", "KNOWLARITY"]
                },
                "DUBAI": ["KNOWLARITY"]
            }
        }
    }

    static VALUE_FIRST_CONFIG = {
        NAME: "VALUE_FIRST",
        API_URL: "https://api.myvfirst.com/psms/servlet/psms.JsonEservice"
    }

    static SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG = {
        API_URL: {
            "INDIA": "https://api-voice.kaleyra.com/v1/",
            "DUBAI": ""
        },
        API_KEY: {
            TRANSACTIONAL: {
                "INDIA": {
                    "SUGARFIT": "Aae077c7cdfce7378e0ab5782bbae42f8",
                    "CUREFIT": "Aabae2c987f37ee0f5427ffe6fb69e3cb",
                    "SUGARFIT_EMR_CALLING": "Aae077c7cdfce7378e0ab5782bbae42f8",
                    "SUGARFIT_OPS_CALLING": "A20d4770ef56208879575aa35079dec40",
                    "SUGARFIT_EMR_CALLING_DOCTOR": "A3c22cc8d2eef653a7f27e6da499500a5",
                    "ONEFITPLUS": "A0b4cbaeda9dc44a53ee31ee3363551dc",
                },
                "DUBAI": ""
            },
            CONFIDENTIAL: {
                "INDIA": "Abc3c4202cc0e99c2b288565d16289a57",
                "DUBAI": ""
            }
        },
        CALLBACK_URL: {
            TRANSACTIONAL: {
                STAGE: "https://iris-api.stage.curefit.co/notifications/clickToCallReport?notificationid={{id}}&caller={caller}&receiver={receiver}&starttime={starttime}&endtime={endtime}&duration={duration}&billsec={billsec}&status={status}&status1={status1}&status2={status2}&recordpath={recordpath}&callerid={callerid}",
                PRODUCTION: "https://iris-api.curefit.co/notifications/clickToCallReport?notificationid={{id}}&caller={caller}&receiver={receiver}&starttime={starttime}&endtime={endtime}&duration={duration}&billsec={billsec}&status={status}&status1={status1}&status2={status2}&recordpath={recordpath}&callerid={callerid}"
            },
            CONFIDENTIAL: {
                STAGE: "https://iris-api.stage.curefit.co/notifications/clickToCallReport?notificationid={{id}}&caller={caller}&receiver={receiver}&starttime={starttime}&endtime={endtime}&duration={duration}&billsec={billsec}&status={status}&status1={status1}&status2={status2}&callerid={callerid}",
                PRODUCTION: "https://iris-api.curefit.co/notifications/clickToCallReport?notificationid={{id}}&caller={caller}&receiver={receiver}&starttime={starttime}&endtime={endtime}&duration={duration}&billsec={billsec}&status={status}&status1={status1}&status2={status2}&callerid={callerid}"
            }
        },
        LOG_URL: {
            STAGE: "https://api-voice.kaleyra.com/v1",
            PRODUCTION: "https://api-voice.kaleyra.com/v1"
        }
    }

    static KNOWLARITY_CLICK_TO_CALL_CONFIG = {
        NAME: "KNOWLARITY",
        SR_NUMBER: {
            TRANSACTIONAL: "+************",
            CONFIDENTIAL: "+************"
        },
        CALLER_ID: {
            TRANSACTIONAL: {
                "INDIA": "+************",
                "DUBAI": "+************"
            },
            CONFIDENTIAL: {
                "INDIA": "+************",
                "DUBAI": "+************"
            }
        },
        CALLBACK_URL: {
            "STAGE": "https://iris-api.stage.curefit.co/notifications/clickToCallReport/KNOWLARITY",
            "PRODUCTION": "https://iris-api.curefit.co/notifications/clickToCallReport/KNOWLARITY"
        },
        /**
         * TODO: HTTP URLS!!? Check where these are used
         * Security issue, our keys can leak!
         */
        API_URL: {
            "INDIA": "http://etsrds.kapps.in/webapi/curefit/api/curefit_c2c_api.py",
            "DUBAI": "http://etsrds.kapps.in/webapi/curefit/api/curefit_c2c_api.py"
        },
        AUTHORIZATION: {
            TRANSACTIONAL: {
                "INDIA": "408489df-78e4-4a7f-bef9-5a559fe0fc73",
                "DUBAI": "408489df-78e4-4a7f-bef9-5a559fe0fc73"
            },
            CONFIDENTIAL: {
                "INDIA": "32e80e72-4f92-4022-94be-512076d01047",
                "DUBAI": "32e80e72-4f92-4022-94be-512076d01047"
            }
        },
        API_KEY: {
            "INDIA": "nDRUngYws3739teklCMYp9IufbQ8qjf212iyMkiG",
            "INDIA_OLD_WITHOUT_REPORT_CALLBACK": "GesxeTJGz52ReWg8UBb8w7fTtqaCy1107E6bNZmG",
            "DUBAI": "nDRUngYws3739teklCMYp9IufbQ8qjf212iyMkiG"
        }
    }

    static KALEYRA_CONFIG = {
        OBD_API_URL_V1: "https://api-voice.kaleyra.com/v1/index.php",
        OBD_API_URL_V2: "https://api-voice.kaleyra.com/v1/",
        OBD_API_KEY: {
            "INDIA": "A1bf5a66d73a8f1c6404eea75223a44dc",
            "DUBAI": "A79ae47fff4d275557ed2bcb7545979b5"
        },
        SUGARFIT_OBD_API_KEY: {
            "INDIA": "Aae077c7cdfce7378e0ab5782bbae42f8",
            "DUBAI": "A79ae47fff4d275557ed2bcb7545979b5"
        },
        OBD_CALLBACK_URL: {
            STAGE: "https://iris-api.stage.curefit.co/notifications/obdCallback?id={{id}}&obdid={obdid}&status={obdstatus}",
            PRODUCTION: "https://iris-api.curefit.co/notifications/obdCallback?id={{id}}&obdid={obdid}&status={obdstatus}"
        }
    }

    static AWS_CONFIG = {
        ARN_PREFIX_ONE: "arn:aws:sns:ap-south-1:035243212545:",
        CONFIGURATION_SET: {
            STAGE: "stage-iris",
            PRODUCTION: "production-iris"
        }
    }

    public static getConfigurationSet() {
        if (CommonUtils.isEnvironmentProductionOrAlpha()) {
            return this.AWS_CONFIG.CONFIGURATION_SET.PRODUCTION
        } else {
            return this.AWS_CONFIG.CONFIGURATION_SET.STAGE
        }
    }

    public static isWhitelisted(phone: string, email?: string) {
        if (!!email) {
            let domainName = email.substring(email.lastIndexOf("@") + 1)
            let whitelistedDomains = ["curefit.com", "v.curefit.com"]
            return whitelistedDomains.includes(domainName)
        } else {
            let whitelistedNumbers = ["+91-9680608808", "9680608808", "9888330112", "+971-563862168", "9379382542",
                "9900829903", "9953779675", "9739866848", "+971563862168", "+919895641059", "+917406469137",
                "+91-7406469137", "+91-7259508229", "7259508229", "+917259508229", "+91-9954249931", "9954249931",
                "9740213918", "9650071614", "9460372870", "9731231268", "8602086377", "8496007222", "7860314538",
                "9760612041", "+919939879451", "8851821592", "+918851821592", "7300579980", "9844883659", "8884564441",
                "+918884564441", "+91-8884564441", "+919962411256", "+918660897043", "+919590911869", "+919731231268",
                "+918510007976", "8510007976", "+919816927981", "9816927981", "9886208262",

                "919958127107", "9958127107", "+919958127107", "+91-9958127107", //Arjun--IRIS
                "919886886301", "9886886301", "+919886886301", "+91-9886886301", //Pravesh
                "918377877702", "8377877702", "+918377877702", "+91-8377877702", // Samarth
                "919662353145", "9662353145", "+919662353145", "+91-9662353145", //Kishan
                "919876097943", "9876097943", "+919876097943", "+91-9876097943", //Manbir
                "918899114444", "8899114444", "+918899114444", "+91-8899114444", //Sanyam
                "919066401535", "9066401535", "+919066401535", "+91-9066401535", //Mayank
                "918292337923", "8292337923", "+918292337923", "+91-8292337923", //Aditya Gupta Sugarfit
                "919654004473", "9654004473", "+919654004473", "+91-9654004473" // Gaurav Bansal
            ]
            return whitelistedNumbers.includes(phone)
        }
    }

    static checkIsWhiteListed(phone: string, email?: string) {
        return Constants.isWhitelisted(phone, email)
    }

    static getSolInfiniSMSTransactionalApiKey(country: string, phone: string, email: string) {
        let key = ""
        if (CommonUtils.isEnvironmentProductionOrAlpha() || Constants.isWhitelisted(phone, email)) {
            switch (country) {
                case "DUBAI":
                    key = null
                    break
                case "USA-CANADA":
                    key = null
                    break
                default:
                    key = Constants.SOLUTIONS_INFINI_SMS_CONFIG.API_KEY.TRANSACTIONAL.INDIA
            }
        }
        return key
    }

    static getSolInfiniSMSPromotionalApiKey(country: string, phone: string, email: string) {
        let key = ""
        if (CommonUtils.isEnvironmentProductionOrAlpha() || Constants.isWhitelisted(phone, email)) {
            switch (country) {
                case "DUBAI":
                    key = null
                    break
                case "USA-CANADA":
                    key = null
                    break
                default:
                    key = Constants.SOLUTIONS_INFINI_SMS_CONFIG.API_KEY.PROMOTIONAL.INDIA
            }
        }
        return key
    }

    static getSolInfiniSmsOTPRouteApiKey(country: string, phone: string, email: string) {
        let key = ""
        if (CommonUtils.isEnvironmentProductionOrAlpha() || Constants.isWhitelisted(phone, email)) {
            switch (country) {
                case "DUBAI":
                    key = Constants.SOLUTIONS_INFINI_SMS_CONFIG.API_KEY.OTP_ROUTE.DUBAI
                    break
                case "USA-CANADA":
                    key = Constants.SOLUTIONS_INFINI_SMS_CONFIG.API_KEY.OTP_ROUTE["USA-CANADA"]
                    break
                default:
                    key = Constants.SOLUTIONS_INFINI_SMS_CONFIG.API_KEY.OTP_ROUTE.INDIA
            }
        }
        return key
    }

    static isOtpRouteCreative(creativeId: string) {
        const otpWhitelistedCreativeIds = [
            // SMS
            "SMS_OTP_SERVICE", "SMS_OTP_VERIFICATION", "SMS_SF_OTP_SERVICES",
            //EMAIL
            "EMAIL_OTP_SERVICE", "EMAIL_CULT_EMPLOYEE_LOGIN_OTP",
            //OBD
            "OBD_OTP_SERVICES",
            //ONLY FOR TESTING
            "SMS_CEREBRUM_FONADA_TESTING_1_641"
        ]
        return otpWhitelistedCreativeIds.includes(creativeId)
    }



    static avoidDuplicateCheckCreative(creativeId: string) {
        const avoidDuplicateCheckCreativeIds = [
            // SMS
            //EMAIL
            //WHATSAPP
            "WHATSAPP_CEREBRUM_OFFLINE_REFUND_WITH_PAYOUT_LINK_V2_173",
            "WHATSAPP_CEREBRUM_POST_DELIVERY_FREE_INSTALLATION_MESSAGE_870",
            "WHATSAPP_CEREBRUM_POST_DELIVERY_PAID_INSTALLATION_MESSAGE_806",
            "WHATSAPP_CEREBRUM_POST_DELIVERY_INSTALLATION_ACK_MESSAGE_89"
        ]
        return avoidDuplicateCheckCreativeIds.includes(creativeId)
    }

    static getSolInfiniSendSMSTemplateUrl(country: string) {
        switch (country) {
            case "DUBAI":
                return Constants.SOLUTIONS_INFINI_SMS_CONFIG.API_URL.DUBAI
            case "USA-CANADA":
                return Constants.SOLUTIONS_INFINI_SMS_CONFIG.API_URL["USA-CANADA"]
            default:
                return Constants.SOLUTIONS_INFINI_SMS_CONFIG.API_URL.INDIA
        }
    }

    static getSOIPTemplateUrl(): string {
        return Constants.KALEYRA_SOIP_URL
    }

    static getSOIPApiKey(): string {
        return "A24b6b7f24cf79c36ad18d5c596b2192b"
    }

    static getGupshupSendSMSTemplateUrl(country: string) {
        switch (country) {
            case "DUBAI":
                return Constants.GUPSHUP_SMS_CONFIG.API_URL.DUBAI
            case "USA-CANADA":
                return Constants.GUPSHUP_SMS_CONFIG.API_URL["USA-CANADA"]
            default:
                return Constants.GUPSHUP_SMS_CONFIG.API_URL.INDIA
        }
    }

    static getKaleyraOBDTemplateUrlV1(country: string) {
        switch (country) {
            case "DUBAI":
                return Constants.KALEYRA_CONFIG.OBD_API_URL_V1
            default:
                return Constants.KALEYRA_CONFIG.OBD_API_URL_V1
        }
    }

    static getKaleyraOBDTemplateUrlV2(country: string) {
        switch (country) {
            case "DUBAI":
                return Constants.KALEYRA_CONFIG.OBD_API_URL_V2
            default:
                return Constants.KALEYRA_CONFIG.OBD_API_URL_V2
        }
    }

    static getKaleyraOBDCallbackUrl() {
        if (CommonUtils.isEnvironmentProductionOrAlpha()) {
            return Constants.KALEYRA_CONFIG.OBD_CALLBACK_URL.PRODUCTION
        } else {
            return Constants.KALEYRA_CONFIG.OBD_CALLBACK_URL.STAGE
        }
    }

    static getKaleyraOBDApiKey(country: string, phone: string, sender: string = "CUREFIT", valid?: boolean) {
        let key = ""
        if (sender === "SUGARFIT") {
            if (CommonUtils.isEnvironmentProductionOrAlpha() || Constants.isWhitelisted(phone) || valid) {
                switch (country) {
                    case "DUBAI":
                        key = Constants.KALEYRA_CONFIG.SUGARFIT_OBD_API_KEY.DUBAI
                        break
                    default:
                        key = Constants.KALEYRA_CONFIG.SUGARFIT_OBD_API_KEY.INDIA
                }
            }
        } else {
            if (CommonUtils.isEnvironmentProductionOrAlpha() || Constants.isWhitelisted(phone) || valid) {
                switch (country) {
                    case "DUBAI":
                        key = Constants.KALEYRA_CONFIG.OBD_API_KEY.DUBAI
                        break
                    default:
                        key = Constants.KALEYRA_CONFIG.OBD_API_KEY.INDIA
                }
            }
        }
        return key
    }

    static isConfidential(creative: ClickToCallCreative) {
        if (creative) {
            const confidentialCreatives = ["CLICK_TO_CALL_ORION_CALLING", "CLICK_TO_CALL_BHARAT_HEALTH"]
            return confidentialCreatives.includes(creative.creativeId)
        }
        return false
    }

    static getSolInfiniDeliveryUrl() {
        if (CommonUtils.isEnvironmentProductionOrAlpha()) {
            return Constants.SOLUTIONS_INFINI_SMS_CONFIG.DELIVERY_URL.PRODUCTION
        } else {
            return Constants.SOLUTIONS_INFINI_SMS_CONFIG.DELIVERY_URL.STAGE
        }
    }

    static getSolInfiniClickToCallApiUrl(country?: string) {
        switch (country) {
            case "DUBAI":
                return Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.API_URL.DUBAI
            default:
                return Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.API_URL.INDIA
        }
    }

    static getSolInfiniClickToCallApiKey(callerNumber: string, receiverNumber: string, isConfidential?: boolean, country?: string, valid?: boolean, serviceAccount?: C2CServiceAccount) {
        let key = ""
        if (CommonUtils.isEnvironmentProductionOrAlpha() || (Constants.isWhitelisted(callerNumber) && Constants.isWhitelisted(receiverNumber)) || valid) {
            switch (country) {
                case "DUBAI":
                    if (isConfidential) {
                        key = Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.API_KEY.CONFIDENTIAL.DUBAI
                    } else {
                        key = Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.API_KEY.TRANSACTIONAL.DUBAI
                    }
                    break
                default:
                    if (isConfidential) {
                        key = Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.API_KEY.CONFIDENTIAL.INDIA
                    } else {
                        switch (serviceAccount) {
                            case Constants.C2C_SERVICE_ACCOUNT.SUGARFIT: {
                                key = Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.API_KEY.TRANSACTIONAL.INDIA.SUGARFIT
                                break
                            }
                            case Constants.C2C_SERVICE_ACCOUNT.SUGARFIT_EMR_CALLING: {
                                key = Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.API_KEY.TRANSACTIONAL.INDIA.SUGARFIT_EMR_CALLING
                                break
                            }
                            case Constants.C2C_SERVICE_ACCOUNT.SUGARFIT_OPS_CALLING: {
                                key = Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.API_KEY.TRANSACTIONAL.INDIA.SUGARFIT_OPS_CALLING
                                break
                            }
                            case Constants.C2C_SERVICE_ACCOUNT.SUGARFIT_EMR_CALLING_DOCTOR: {
                                key = Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.API_KEY.TRANSACTIONAL.INDIA.SUGARFIT_EMR_CALLING_DOCTOR
                                break
                            }
                            case Constants.C2C_SERVICE_ACCOUNT.ONEFITPLUS: {
                                key = Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.API_KEY.TRANSACTIONAL.INDIA.ONEFITPLUS
                                break
                            }
                            default: {
                                key = Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.API_KEY.TRANSACTIONAL.INDIA.CUREFIT
                            }

                        }
                    }
            }
        }
        return key
    }

    static getSolInfiniClickToCallBackUrl(isConfidential?: boolean) {
        let key = ""
        if (CommonUtils.isEnvironmentProductionOrAlpha()) {
            if (isConfidential) {
                key = Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.CALLBACK_URL.CONFIDENTIAL.PRODUCTION
            } else {
                key = Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.CALLBACK_URL.TRANSACTIONAL.PRODUCTION
            }
        } else {
            if (isConfidential) {
                key = Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.CALLBACK_URL.CONFIDENTIAL.STAGE
            } else {
                key = Constants.SOLUTIONS_INFINI_CLICK_TO_CALL_CONFIG.CALLBACK_URL.TRANSACTIONAL.STAGE
            }
        }
        return key
    }

    static getKnowlarityClickToCallApiUrl(country?: string) {
        switch (country) {
            case "DUBAI":
                return Constants.KNOWLARITY_CLICK_TO_CALL_CONFIG.API_URL.DUBAI
            default:
                return Constants.KNOWLARITY_CLICK_TO_CALL_CONFIG.API_URL.INDIA
        }
    }

    static getKnowlarityClickToCallApiKey(callerNumber?: string, receiverNumber?: string, country?: string, valid?: boolean) {
        let key = ""
        if (CommonUtils.isEnvironmentProductionOrAlpha() || (Constants.isWhitelisted(callerNumber) && Constants.isWhitelisted(receiverNumber)) || valid) {
            switch (country) {
                case "DUBAI":
                    key = Constants.KNOWLARITY_CLICK_TO_CALL_CONFIG.API_KEY.DUBAI
                    break
                default:
                    key = Constants.KNOWLARITY_CLICK_TO_CALL_CONFIG.API_KEY.INDIA
            }
        }
        return key
    }

    static getKnowlarityClickToCallBackUrl(country?: string, valid?: boolean) {
        let key = ""
        if (CommonUtils.isEnvironmentProductionOrAlpha()) {
            key = Constants.KNOWLARITY_CLICK_TO_CALL_CONFIG.CALLBACK_URL.PRODUCTION
        } else {
            key = Constants.KNOWLARITY_CLICK_TO_CALL_CONFIG.CALLBACK_URL.STAGE
        }
        return key
    }

    static getKnowlarityClickToCallAppAuthorization(callerNumber: string, receiverNumber: string, country?: string, isConfidential?: boolean) {
        //Sugarfit has no account with Knowlarity therefore we dont have any check for serviceAccount here
        let key = ""
        if (CommonUtils.isEnvironmentProductionOrAlpha() || (Constants.isWhitelisted(callerNumber) && Constants.isWhitelisted(receiverNumber))) {
            switch (country) {
                case "DUBAI":
                    if (isConfidential) {
                        key = Constants.KNOWLARITY_CLICK_TO_CALL_CONFIG.AUTHORIZATION.CONFIDENTIAL.DUBAI
                    } else {
                        key = Constants.KNOWLARITY_CLICK_TO_CALL_CONFIG.AUTHORIZATION.TRANSACTIONAL.DUBAI
                    }
                    break
                default:
                    if (isConfidential) {
                        key = Constants.KNOWLARITY_CLICK_TO_CALL_CONFIG.AUTHORIZATION.CONFIDENTIAL.INDIA
                    } else {
                        key = Constants.KNOWLARITY_CLICK_TO_CALL_CONFIG.AUTHORIZATION.TRANSACTIONAL.INDIA
                    }
                    break
            }
        }
        return key
    }

    static getKnowlarityClickToCallCallerId(country?: string, isConfidential?: boolean, C2CServiceAccount?: C2CServiceAccount) {
        let key = ""
        switch (country) {
            case "DUBAI":
                if (isConfidential) {
                    key = Constants.KNOWLARITY_CLICK_TO_CALL_CONFIG.CALLER_ID.CONFIDENTIAL.DUBAI
                } else {
                    key = Constants.KNOWLARITY_CLICK_TO_CALL_CONFIG.CALLER_ID.TRANSACTIONAL.DUBAI
                }
                break
            default:
                if (isConfidential) {
                    key = Constants.KNOWLARITY_CLICK_TO_CALL_CONFIG.CALLER_ID.CONFIDENTIAL.INDIA
                } else {
                    key = Constants.KNOWLARITY_CLICK_TO_CALL_CONFIG.CALLER_ID.TRANSACTIONAL.INDIA
                }
                break
        }
        return key
    }

    static getValidServiceProviders(country?: string, isConfidential?: boolean, serviceAccount?: C2CServiceAccount) {
        let key = []
        switch (country) {
            case "DUBAI":
                if (isConfidential) {
                    key = Constants.CLICK_TO_CALL_CONFIG.SERVICE_PROVIDERS.CONFIDENTIAL.DUBAI
                } else {
                    key = Constants.CLICK_TO_CALL_CONFIG.SERVICE_PROVIDERS.TRANSACTIONAL.DUBAI
                }
                break
            default:
                if (isConfidential) {
                    key = serviceAccount === Constants.C2C_SERVICE_ACCOUNT.SUGARFIT ?
                        Constants.CLICK_TO_CALL_CONFIG.SERVICE_PROVIDERS.CONFIDENTIAL.INDIA.SUGARFIT :
                        Constants.CLICK_TO_CALL_CONFIG.SERVICE_PROVIDERS.CONFIDENTIAL.INDIA.CUREFIT
                } else {
                    key = serviceAccount === Constants.C2C_SERVICE_ACCOUNT.SUGARFIT ?
                        Constants.CLICK_TO_CALL_CONFIG.SERVICE_PROVIDERS.TRANSACTIONAL.INDIA.SUGARFIT :
                        Constants.CLICK_TO_CALL_CONFIG.SERVICE_PROVIDERS.TRANSACTIONAL.INDIA.CUREFIT
                }
                break
        }
        return key
    }

    static getIrisPublicBaseUrl() {
        return CommonUtils.isEnvironmentProductionOrAlpha() ?
            "https://iris-api.curefit.co" : "https://iris-api.stage.curefit.co"
    }

    static UNIVERSAL_LINK_DOMAIN: string = "https://dl.cure.fit/"
    static UNIVERSAL_LINK_STATIC_DOMAIN_PRODUCTION: string = "https://www.cure.fit/l/"
    static UNIVERSAL_LINK_STATIC_DOMAIN_STAGE: string = "https://stage1234.cure.fit/l/"

    static BranchConfigMap = {
        [Tenant.CUREFIT_APP]: {
            url: "https://api.branch.io/v1",
            apiKey: process.env.BRANCH_API_KEY_CUREFIT,
            branchSecret: process.env.BRANCH_SECRET_CUREFIT
        },
        [Tenant.LIVEFIT_APP]: {
            url: "https://api.branch.io/v1",
            apiKey: process.env.BRANCH_API_KEY_LIVEFIT,
            branchSecret: process.env.BRANCH_SECRET_LIVEFIT
        },
        [Tenant.SUGARFIT_APP]: {
            url: "https://api.branch.io/v1",
            apiKey: process.env.BRANCH_API_KEY_SUGARFIT,
            branchSecret: ""
        },
        [Tenant.CULTWATCH]: {
            url: "https://api.branch.io/v1",
            apiKey: process.env.BRANCH_API_KEY_CULTWATCH,
            branchSecret: process.env.BRANCH_SECRET_CULTWATCH
        }
    } as any

    static FirebaseConfigMap = {
        [Tenant.CUREFIT_APP]: {
            url: "https://firebasedynamiclinks.googleapis.com/v1",
            apiKey: process.env.FIREBASE_DYNAMIC_LINK_API_KEY_CUREFIT,
        },
        [Tenant.LIVEFIT_APP]: {
            url: "https://firebasedynamiclinks.googleapis.com/v1",
            apiKey: process.env.FIREBASE_DYNAMIC_LINK_API_KEY_LIVEFIT,
        }
    } as any

    static FirebaseAnalyticsConfigMap = {
        [Tenant.CUREFIT_APP]: {
            projectId: process.env.FIREBASE_ANALYTICS_PROJECT_ID_CUREFIT || "cult-155917",
            apiKey: process.env.FIREBASE_ANALYTICS_API_KEY_CUREFIT,
            measurementId: process.env.FIREBASE_ANALYTICS_MEASUREMENT_ID_CUREFIT,
            apiSecret: process.env.FIREBASE_ANALYTICS_API_SECRET_CUREFIT,
            url: "https://www.google-analytics.com/mp/collect",
            debugUrl: "https://www.google-analytics.com/debug/mp/collect"
        },
        [Tenant.LIVEFIT_APP]: {
            projectId: process.env.FIREBASE_ANALYTICS_PROJECT_ID_LIVEFIT || "livefit-app",
            apiKey: process.env.FIREBASE_ANALYTICS_API_KEY_LIVEFIT,
            measurementId: process.env.FIREBASE_ANALYTICS_MEASUREMENT_ID_LIVEFIT,
            apiSecret: process.env.FIREBASE_ANALYTICS_API_SECRET_LIVEFIT,
            url: "https://www.google-analytics.com/mp/collect",
            debugUrl: "https://www.google-analytics.com/debug/mp/collect"
        },
        [Tenant.CULTWATCH]: {
            projectId: process.env.FIREBASE_ANALYTICS_PROJECT_ID_CULTWATCH || "cultwatch-app",
            apiKey: process.env.FIREBASE_ANALYTICS_API_KEY_CULTWATCH,
            measurementId: process.env.FIREBASE_ANALYTICS_MEASUREMENT_ID_CULTWATCH,
            apiSecret: process.env.FIREBASE_ANALYTICS_API_SECRET_CULTWATCH,
            url: "https://www.google-analytics.com/mp/collect",
            debugUrl: "https://www.google-analytics.com/debug/mp/collect"
        }
    } as any

    static MUSTACHE_DEPRECATED_ERROR_MESSAGE: string = "Mustache has been deprecated, please use Handlebars for " +
        "templating. Do not forget to test your template with handlebars!"

    static ATHENA_DB: string = "pk_curefitplatforms_cfdb"
    static ATHENA_OUTPUT_S3_LOCATION: string = "s3://aws-athena-query-results-035243212545-ap-south-1/"
    static ATHENA_RESULT_SIZE = 1000
    static ATHENA_POLL_INTERVAL = 1000

    static ALL_SUPPORTED_FUNCTIONAL_TAGS: Record<string, FunctionTag> = {
            "GETTIMEDIFF": {
                arguments: [
                    {
                        name: "time",
                        type: "string",
                        required: true,
                        definition: "Value from which time difference is to be calculated",
                        constant: false
                    },
                    {
                        name: "units",
                        type : "string",
                        required: false,
                        definition: "Units in which the difference is to be calculated. \n Allowed values:" +
                            " days (DEFAULT) | hours | minutes | weeks | months | years",
                        constant: true
                    }

                ],
                description: "Calculates time difference between a time value & current time in specified unit",
                displayName: "TIME_DIFFERENCE",
                hasNargs: false,
                example: "TIME_DIFFERENCE(cult_packenddate, days)"
            },
        "REGEX_GLOBAL": {
            arguments: [
                {
                    name: "regexExpression",
                    type: "string",
                    required: true,
                    definition: "Regex used for filling global attribute inside {} in regex",
                    constant: true
                },
                {
                    name: "attributeProperty",
                    type: "string",
                    required: true,
                    definition: "Name of the collections property",
                    constant: false
                }
            ],
            description: "Returns the price attribute for preferred center stored inside centers collection",
            displayName: "REGEX_GLOBAL",
            hasNargs: false,
            example: "REGEX_GLOBAL(center_{preferred_center}, price_12m)"
        },
            "GETINDEX": {
                arguments: [
                    {
                        name: "index",
                        type: "number",
                        required: true,
                        definition: "Position in the list from which the value is required",
                        constant: true
                    },
                    {
                        name: "list",
                        type: "list",
                        required: true,
                        definition: "Name of the list from which we need the value",
                        constant: false
                    }
                ],
                description: "Returns the value at Nth Position in a list. First value is at index 0, second at index 1",
                displayName: "INDEX_VALUE",
                hasNargs: false,
                example: "GETINDEX(0, live_workout_format_affinity)"
            },
        "EPOCHTODATE": {
            arguments: [
                {
                    name: "epoch",
                    type: "number",
                    required: true,
                    definition: "Epoch value to be converted to date",
                    constant: true
                }
            ],
            description: "Converts epoch values & returns a date in the format DD-MMM-YY. Example: 29 Sep 21",
            displayName: "GETDATE",
            hasNargs: false,
            example: "GETDATE(cult_packenddate)"
        },
        "MINUS": {
            arguments: [
                {
                    name: "First Number",
                    type: "number",
                    required: true,
                    definition: "Number from which the value has to be subtracted",
                    constant: false
                },
                {
                    name: "Second Number",
                    type: "number",
                    required: true,
                    definition: "Value that will be subtracted",
                    constant: false
                }
            ],
            description: "Returns the difference between the first & second values",
            displayName: "SUBTRACT",
            hasNargs: false,
            example: "SUBTRACT(9,5)"
        },
        "DIVIDE": {
            arguments: [
                {
                    name: "divident",
                    type: "number",
                    required: true,
                    definition: "The number to be divided",
                    constant: false
                },
                {
                    name: "divisor",
                    type: "number",
                    required: true,
                    definition: "The number to be divided by",
                    constant: false
                },
                {
                    name: "roundOff",
                    type: "number",
                    required: false,
                    definition: "Number of decimal points to be rounded off to. Zero by default.",
                    constant: true
                }
            ],
            description: "Returns output of one number divided by another number",
            displayName: "DIVIDE",
            hasNargs: false,
            example: "DIVIDE( 5,2,1) will be 2.5"
        },
        "SUM": {
            arguments: [
                {
                    name: "number",
                    type: "number",
                    required: true,
                    definition: "The first value",
                    constant: false
                },
                {
                    name: "values",
                    type: "number",
                    required: false,
                    definition: "The values to be added to the first number. Can be more than 1 value separated by comma",
                    constant: false
                }
            ],
            description: "Returns the sum of two or more comma separated values",
            displayName: "SUM",
            hasNargs: true,
            example: "SUM( 5,2,2) will be 9"
        },
        "PRODUCT": {
            arguments: [
                {
                    name: "number",
                    type: "number",
                    required: true,
                    definition: "The first multiplicand",
                    constant: false
                },
                {
                    name: "value",
                    type: "number",
                    required: false,
                    definition: "The number to be multipled by\n" +
                        "There can be more than 1 number separated by comma\n",
                    constant: false
                }
            ],
            description: "Returns the product of N comma separated values",
            displayName: "PRODUCT",
            hasNargs: true,
            example: "PRODUCT( 5,2,2) will be 20"
        }
    }

    static EMAIL_VALIDATION_REGEX = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    static REACHER_API_BASE_LINK = "https://api.reacher.email/v0/check_email"
    static REACHER_EMAIL_VALIDATION_API_KEY = "dfd4af44-8fb1-11ec-95af-1935d61c5545"
    static DEFAULT_EMAIL_VALIDATION_LAST_X_DAYS = 30
    static EMAIL_VALIDATION_MAX_BULK_LIMIT = 10

    static NOTIFEE_MESSAGING_MIN_APP_VERSION_DEFAULT = {"curefit" : 8.7, "livefit" : 2.9, "sugarfit" : 1.0 }
    static FIREBASE_ADMIN_MESSAGING_MIN_APP_VERSION_STAGE = {"curefit" : 8.66, "livefit" : 2.1, "sugarfit" : 1.0 }
    static FIREBASE_ADMIN_MESSAGING_MIN_APP_VERSION_PRODUCTION = {"curefit" : 8.63, "livefit" : 3.1, "sugarfit" : 1.0 }
    static DEFAULT_FAILURE_ALERT_CONFIG_STAGE = {
        "minProcessCount": 0,
        "failurePercentThreshold": 30,
        "EAT":
            {
                "pocEmails":
                    [
                        "<EMAIL>"
                    ],
                "coolOffPeriodInSeconds": 3600
            },
        "CULT":
            {
                "pocEmails":
                    [
                        "<EMAIL>"
                    ],
                "coolOffPeriodInSeconds": 3600
            },
        "MIND":
            {
                "pocEmails":
                    [
                        "<EMAIL>"
                    ],
                "coolOffPeriodInSeconds": 3600
            },
        "CARE":
            {
                "pocEmails":
                    [
                        "<EMAIL>"
                    ],
                "coolOffPeriodInSeconds": 3600
            },
        "CUREFIT":
            {
                "pocEmails":
                    [
                        "<EMAIL>"
                    ],
                "coolOffPeriodInSeconds": 3600
            },
        "GYMFIT":
            {
                "pocEmails":
                    [
                        "<EMAIL>"
                    ],
                "coolOffPeriodInSeconds": 3600
            },
        "LIVE":
            {
                "pocEmails":
                    [
                        "<EMAIL>"
                    ],
                "coolOffPeriodInSeconds": 3600
            },
        "CULTGEAR":
            {
                "pocEmails":
                    [
                        "<EMAIL>"
                    ],
                "coolOffPeriodInSeconds": 3600
            }
    }
    static DEFAULT_FAILURE_ALERT_CONFIG_PRODUCTION = {
        "minProcessCount": 50,
        "failurePercentThreshold": 30,
        "EAT":
            {
                "pocEmails":
                    [
                        "<EMAIL>"
                    ],
                "coolOffPeriodInSeconds": 3600
            },
        "CULT":
            {
                "pocEmails":
                    [
                        "<EMAIL>"
                    ],
                "coolOffPeriodInSeconds": 3600
            },
        "MIND":
            {
                "pocEmails":
                    [
                        "<EMAIL>"
                    ],
                "coolOffPeriodInSeconds": 3600
            },
        "CARE":
            {
                "pocEmails":
                    [
                        "<EMAIL>"
                    ],
                "coolOffPeriodInSeconds": 3600
            },
        "CUREFIT":
            {
                "pocEmails":
                    [
                        "<EMAIL>",
                        "<EMAIL>"
                    ],
                "coolOffPeriodInSeconds": 3600
            },
        "GYMFIT":
            {
                "pocEmails":
                    [
                        "<EMAIL>"
                    ],
                "coolOffPeriodInSeconds": 3600
            },
        "LIVE":
            {
                "pocEmails": [] as string[],
                "coolOffPeriodInSeconds": 3600
            },
        "CULTGEAR":
            {
                "pocEmails":
                    [
                        "<EMAIL>"
                    ],
                "coolOffPeriodInSeconds": 3600
            }
    }

    static getDefaultFirebaseAdminMessagingMinAppVersion() {
        return CommonUtils.isEnvironmentProductionOrAlpha() ? this.FIREBASE_ADMIN_MESSAGING_MIN_APP_VERSION_PRODUCTION :
            this.FIREBASE_ADMIN_MESSAGING_MIN_APP_VERSION_STAGE
    }

    static getDefaultFailureAlertConfig() {
        return CommonUtils.isEnvironmentProductionOrAlpha() ? this.DEFAULT_FAILURE_ALERT_CONFIG_PRODUCTION :
            this.DEFAULT_FAILURE_ALERT_CONFIG_STAGE
    }

    static SQS_SMS_CALLBACK: string = "sms_callback"
    static SQS_SMS_CALLBACK_URL_STAGE = "https://sqs.ap-south-1.amazonaws.com/035243212545/stage-iris-sms-callbacks"
    static SQS_SMS_CALLBACK_URL_PRODUCTION = "https://sqs.ap-south-1.amazonaws.com/035243212545/production-iris-sms-callbacks"
    static SQS_SMS_CALLBACK_QUEUE_NAME_STAGE = "stage-iris-sms-callbacks"
    static SQS_SMS_CALLBACK_QUEUE_NAME_PRODUCTION = "production-iris-sms-callbacks"

    static getSMSCallbackSQSUrl() {
        return CommonUtils.isEnvironmentProductionOrAlpha() ? this.SQS_SMS_CALLBACK_URL_PRODUCTION :
            this.SQS_SMS_CALLBACK_URL_STAGE
    }
    static getSMSCallbackSQSQueueName() {
        return CommonUtils.isEnvironmentProductionOrAlpha() ? this.SQS_SMS_CALLBACK_QUEUE_NAME_PRODUCTION :
            this.SQS_SMS_CALLBACK_QUEUE_NAME_STAGE
    }

    static KALEYRA_ERRORS_TO_DESCRIPTION_MAP: Record<string, string> = {
        "BARRED": "End user has enabled a message barring system",
        "ABSENT-SUB": "Telecom services are not providing service for the particular number or Mobile Subscriber not reachable",
        "INVALID-SUB": "Number does not exist or fails to locate the number in the HLR database",
        "TEMPLATE-INACTIVE": "The message template sent is in an inactive status at the DLT platform level",
        "PRFT-NT-MTCH": "User is registered under DND",
        "E611": "The Type parameter is missing in the request",
        "MOB-OFF": "Mobile handset is in switched off mode",
        "HANDSET-BUSY": "Subscriber is in busy condition",
        "HANDSET-ERR": "Problem with Handset",
        "REJECTD": "SMS is rejected as the number is blacklisted by the operator",
        "EXPIRED": "SMS expired after multiple re-try",
        "DNDNUMB": "DND registered number",
        "INV-TEMPLATE-MATCH": "Template is not matching with the approved text",
        "UNDELIV": "Failed due to network errors",
        "HEADER-NOT-REGISTERED-FOR-TEMPLATE": "The Sender ID used along with the template ID do not match as per the registration in the DLT portal",
        "OPTOUT-REJ": "Unsubscribed from the group",
        "MEMEXEC": "Handset memory full",
        "SNDR-NT-MTCH": "Sender ID does not match Entity ID",
        "REJECTED-MULTIPART": "Validation failed (SMS over 160 characters)",
        "NET-ERR": "Subscribers operator not supported. Gateway mobile switching error"
    }

    static GUPSHUP_ERRORS_TO_DESCRIPTION_MAP: Record<string, string> = {
        "ABSENT_SUBSCRIBER": "Subscriber Unreachable",
        "UNKNOWN_SUBSCRIBER": "Invalid Number"
    }

    static FONADA_SUCCESS_SENT_STATUS_CODE = 200
    static FONADA_ERRORS_TO_DESCRIPTION_MAP: Record<string,string> = {
    }


    static TEXT_IMAGE_DOCUMENT_VIDEO = "TEXT_IMAGE_DOCUMENT_VIDEO"

    static WHATSAPP_PREFERENCE_UPDATE_CAMPAIGN_ID = "CUREFIT_WHATSAPP_PREFERENCES"
    static WHATSAPP_START_ACK_CREATIVE_ID = "WHATSAPP_START_ACK"
    static WHATSAPP_STOP_ACK_CREATIVE_ID = "WHATSAPP_STOP_ACK"

    static CUREFIT_INBOUND_CALLING_CAMPAIGN_ID = "CUREFIT_INBOUND_CALLING"
    static CLICK_TO_CALL_IN_CENTER_INQUIRY_CREATIVE_ID = "CLICK_TO_CALL_IN_CENTER_INQUIRY"
    
    static CAMPAIGN_MANAGER_SEGMENT_DOWNLOAD_FAILURE = "/v1/campaign/execution-status/segment-download-failure"

    static CREATIVE_TESTING_CAMPAIGN = "CUREFIT_CRM_CREATIVE_TEST"

    static CREATIVE_BLACKLIST_NOTIFY_EMAIL_CAMPAIGN = "CRM_TEST"

    static CREATIVE_BLACKLIST_NOTIFY_EMAIL_CREATIVE = "EMAIL_CEREBRUM_CREATIVE_BLACKLISTED_ALERT_18"

    static BLACKLIST_ALERT_RECIPIENT = "<EMAIL>"

    static BLACKLIST_ALERT_CC_RECIPIENTS = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]

    static TRANSACTIONAL_COMM_BLOCK_OBJECTIVE = "Reminders"

    static PROMO_COMM_BLOCK_OBJECTIVE = "Recommendations & more"

    static WHATSAPP_CHANNEL = "WHATSAPP"

}

export default Constants
