{"appName": "iris", "loggingDir": "/logs/iris", "rollbar": {"enabled": false, "key": "836cc4aefce547bd88b9d28b25716bed"}, "identityURL": "http://identity.stage.cure.fit.internal", "mongoConf": {"connections": [{"entitySpace": "DEFAULT", "instance": {"replicaSetName": "stage-shard-0", "host": "stage-pl-0.tfwfr.mongodb.net", "children": [{"host": "stage-shard-00-00-tfwfr.mongodb.net"}, {"host": "stage-shard-00-01-tfwfr.mongodb.net"}, {"host": "stage-shard-00-02-tfwfr.mongodb.net"}]}, "credentials": {"dbName": "curefit-stage", "replicaSetName": "stage-shard-0", "username": "service-access", "password": "xkcD32PCnGOfdril", "ssl": true, "authSource": "admin", "connectionOptions": {"minPoolSize": 1, "maxPoolSize": 6}}}], "isLeanQueryEnabled": true}, "redisConf": {"connections": [{"entitySpace": "DEFAULT", "hosts": [{"host": "curefit.y66lea.0001.aps1.cache.amazonaws.com", "port": 6379}], "redisOptions": {}, "isCluster": false}, {"entitySpace": "NOTIFICATION_WRAPPER_CACHE", "hosts": [{"host": "curefit.y66lea.0001.aps1.cache.amazonaws.com", "port": 6379}], "redisOptions": {}, "isCluster": false}, {"entitySpace": "REDIS_CACHE", "hosts": [{"host": "curefit.y66lea.0001.aps1.cache.amazonaws.com", "port": 6379}], "redisOptions": {}, "isCluster": false}, {"entitySpace": "RASHI_REDIS_CACHE", "hosts": [{"host": "platforms-cache.stage.cure.fit.internal", "port": 6379}], "redisOptions": {}, "isCluster": true}, {"entitySpace": "BUMBLE", "hosts": [{"host": "curefit.y66lea.0001.aps1.cache.amazonaws.com", "port": 6379}], "redisOptions": {}, "isCluster": false}, {"entitySpace": "SEGMENTATION_REDIS_CACHE", "hosts": [{"host": "platforms-segmentation-cache.stage.cure.fit.internal", "port": 6379}], "redisOptions": {}, "poolOptions": {"min": 1, "max": 2}, "isCluster": true}]}, "mysqlConf": {"connections": [{"entitySpace": "DEFAULT", "connection": {"debug": true, "connection": {"host": "cfdb.cw5efoqjpxhj.ap-south-1.rds.amazonaws.com", "port": 3306, "user": "curefit", "password": "cure!123", "database": "cfdb", "connectionLimit": 10, "timezone": "UTC"}}}, {"entitySpace": "REPLICA", "connection": {"debug": true, "connection": {"host": "cfdb.cw5efoqjpxhj.ap-south-1.rds.amazonaws.com", "port": 3306, "user": "curefit", "password": "cure!123", "database": "cfdb", "connectionLimit": 10, "timezone": "UTC"}}}]}, "whatsappConfig": {"accounts": [{"username": "Curefitwa", "password": "cure3456", "phone": "************", "serviceProvider": "VALUE_FIRST", "isDefault": true}]}, "backendServices": {"MOZART": {"url": "http://mozart.stage.cure.fit.internal"}, "segmentationService": {"url": "http://segmentation.stage.cure.fit.internal", "apiKey": "${SEGMENTATION_API_KEY}", "segmentUsageMetricsQueueName": "stage-segmentation-usage-metrics", "disableRollbar": false}, "rashi": {"url": "http://rashi.stage.cure.fit.internal"}, "configStore": {"url": "http://config-store.stage.cure.fit.internal", "apiKey": "iris-stage-access"}}, "mozart": {"iris_schedule_notification_job_config_id": "e2dd0a22-6a86-4329-a2cd-18020291c81a", "iris_campaign_job_config_id": "ba95c584-8c8b-41fd-965c-2961649bfa0f", "iris_notification_job_config_id": "349740b7-0728-4a16-9fe2-f9320e91bd15", "iris_notification_retry_job_config_id": "4ea55898-7589-47af-bad4-ee75311ccc5c"}, "sqsQueues": {"mozartSubmitJob": "stage-platforms-mozart-job-create", "falconEntityRefresh": "stage-platforms-falcon-entity"}, "sqsConf": {"connectTimeout": 5000, "maxRetries": 8, "url": "https://sqs.ap-south-1.amazonaws.com/035243212545", "enableDebugLogs": true, "retryDelayBaseMs": 25, "timeout": 30000, "isMessageBufferingEnabled": true, "bufferMessagePublishIntervalMillis": 1000}, "snsTopicArns": {"rashiUserEvents": "arn:aws:sns:ap-south-1:035243212545:stage-rashi-user-events"}, "appEventsConsumerConf": {"brokers": [], "clientId": "iris", "sessionTimeoutInMs": 300000, "heartbeatInterval": 3000, "maxBytes": 262144, "maxWaitTimeInMs": 200, "dlq": "stage-iris-app-events-dlq"}, "eventsConf": {"arnPrefix": "arn:aws:sns:ap-south-1:035243212545:", "sqsPrefix": "https://sqs.ap-south-1.amazonaws.com/035243212545/", "isMessageBufferingEnabled": true, "bufferMessagePublishIntervalMillis": 1000, "snsBatchPublishMaxSize": 10}, "notificationEventsKafkaConf": {"brokers": ["b-2.dataplatformstgkafka.8fdckx.c3.kafka.ap-south-1.amazonaws.com:9092", "b-1.dataplatformstgkafka.8fdckx.c3.kafka.ap-south-1.amazonaws.com:9092"], "clientId": "iris", "topic": "cfprodplatforms_iris_events", "dlq": "stage-iris-notification-events-dlq"}, "port": 3010, "serverConfigOptions": {"enableCors": false, "skipLogin": true, "clusterModeEnabled": false, "metricsPort": 3009, "keepAliveTimeoutInSec": 120, "requestBodySizeLimit": "2mb", "cookieSalt": "preventive", "accessLogsEnabled": true, "disableApiKeyMappingCache": true, "disableServiceAccessAuth": true, "enableConfigStore": true}, "s3": {"attachmentBucket": "cf-iris-stage", "attachmentBucketPublic": "cf-iris-media-stage"}, "cfAPIConfig": {"baseUrl": "http://cfapi-internal.stage.cure.fit.internal", "headers": {"Authorization": "Bearer 0a040ec34abbfb7f3030345244a913c9"}}, "branchBackendConfig": {"url": "https://api2.branch.io/v2/event/standard", "headers": {"branch_key": "key_test_hmyzFVcx3J7FmDDyfKmj8fhowqcouZkl"}}, "sentryConf": {"enabled": true, "dsn": "https://<EMAIL>/6653966"}, "datalakeMetricBackendConfig": {"baseUrl": "http://localhost:50010", "apiKey": "<PERSON><PERSON><PERSON><PERSON>"}, "taskManagement": {"baseUrl": "http://audit.stage.cure.fit.internal/"}, "campaignManagerConfig": {"baseUrl": "http://campaign-manager.stage.cure.fit.internal"}, "leadCategorisation": {"baseUrl": "https://dataplatform-webhook.curefit.co/"}, "firebaseAnalytics": {"measurementId": "G-3RJLC6H331", "apiSecret": "gV35OID6QW-AcRUAlr981w", "baseUrl": "https://www.google-analytics.com/mp/collect", "debugUrl": "https://www.google-analytics.com/debug/mp/collect", "timeout": 10000, "retries": 3, "debugMode": false}}