{"appName": "iris", "loggingDir": "/logs/iris", "rollbar": {"enabled": true, "key": "8e1ad44906134e2a8d665e962acc00b2"}, "identityURL": "http://identity.production.cure.fit.internal", "mongoConf": {"connections": [{"entitySpace": "DEFAULT", "instance": {"replicaSetName": "prod-shard-0", "host": "prod-pl-0.vtjqj.mongodb.net", "children": [{"host": "prod-shard-00-00-vtjqj.mongodb.net"}, {"host": "prod-shard-00-01-vtjqj.mongodb.net"}, {"host": "prod-shard-00-02-vtjqj.mongodb.net"}]}, "credentials": {"dbName": "curefit-prod", "replicaSetName": "prod-shard-0", "username": "iris-rw", "password": "65Dv2Z9bOqtKElEy", "ssl": true, "authSource": "admin", "connectionOptions": {"minPoolSize": 1, "maxPoolSize": 3}}}], "isLeanQueryEnabled": true}, "redisConf": {"connections": [{"entitySpace": "DEFAULT", "hosts": [{"host": "iris-cache.production.cure.fit.internal", "port": 6379}], "redisOptions": {}, "poolOptions": {"min": 3, "max": 3}, "isCluster": true}, {"entitySpace": "NOTIFICATION_WRAPPER_CACHE", "hosts": [{"host": "iris-cache.production.cure.fit.internal", "port": 6379}], "redisOptions": {}, "poolOptions": {"min": 3, "max": 3}, "isCluster": true, "enableCompression": false}, {"entitySpace": "BUMBLE", "hosts": [{"host": "iris-cache.production.cure.fit.internal", "port": 6379}], "redisOptions": {}, "poolOptions": {"min": 3, "max": 3}, "isCluster": true}, {"entitySpace": "RASHI_REDIS_CACHE", "hosts": [{"host": "platforms-cache.production.cure.fit.internal", "port": 6379}], "redisOptions": {}, "poolOptions": {"min": 3, "max": 3}, "isCluster": true}, {"entitySpace": "REDIS_CACHE", "hosts": [{"host": "prod-eatfit-redis-primary.production.cure.fit.internal", "port": 6379}], "redisOptions": {}, "poolOptions": {"min": 3, "max": 3}, "isCluster": false}, {"entitySpace": "SEGMENTATION_REDIS_CACHE", "hosts": [{"host": "platforms-segmentation-cache.production.cure.fit.internal", "port": 6379}], "redisOptions": {}, "poolOptions": {"min": 1, "max": 15}, "isCluster": true}]}, "mysqlConf": {"connections": [{"entitySpace": "DEFAULT", "connection": {"debug": false, "connection": {"host": "curefit-iris-master.production.cure.fit.internal", "user": "iris_rw", "password": "tm9KZsVX5PgSdf4X", "database": "cfdb", "connectionLimit": 10, "timezone": "UTC"}, "pool": {"min": 2, "max": 25, "idleTimeoutMillis": 100000, "acquireTimeoutMillis": 100000}}}, {"entitySpace": "REPLICA", "connection": {"debug": false, "connection": {"host": "curefit-iris-read.production.cure.fit.internal", "user": "iris_read", "password": "6QiUdOgs0aZCfxMq", "database": "cfdb", "connectionLimit": 10, "timezone": "UTC"}, "pool": {"min": 2, "max": 25, "idleTimeoutMillis": 100000, "acquireTimeoutMillis": 100000}}}]}, "whatsappConfig": {"accounts": [{"username": "Curefitwa", "password": "cure3456", "phone": "************", "serviceProvider": "VALUE_FIRST", "isDefault": true}, {"username": "Curefitwa", "password": "cure3456", "phone": "************", "serviceProvider": "VALUE_FIRST"}]}, "backendServices": {"MOZART": {"url": "http://mozart.production.cure.fit.internal"}, "segmentationService": {"url": "http://segmentation.production.cure.fit.internal", "apiKey": "${SEGMENTATION_API_KEY}", "segmentUsageMetricsQueueName": "production-segment-usage-metrics", "disableRollbar": false}, "rashi": {"url": "http://rashi.production.cure.fit.internal"}, "configStore": {"url": "http://config-store.production.cure.fit.internal", "apiKey": "6daae085-31f8-402b-8f67-f3973250b560"}}, "mozart": {"iris_schedule_notification_job_config_id": "2a84697f-3e0d-4f45-9136-5a0668723493", "iris_campaign_job_config_id": "ba95c584-8c8b-41fd-965c-2961649bfa0f", "iris_notification_job_config_id": "349740b7-0728-4a16-9fe2-f9320e91bd15", "iris_notification_retry_job_config_id": "6b3ab820-1836-42e2-94a9-4136c5909a1f"}, "sqsQueues": {"mozartSubmitJob": "prod-platforms-mozart-job-create"}, "snsTopicArns": {"rashiUserEvents": "arn:aws:sns:ap-south-1:035243212545:production-rashi-user-events"}, "sqsConf": {"connectTimeout": 5000, "maxRetries": 8, "url": "https://sqs.ap-south-1.amazonaws.com/035243212545", "enableDebugLogs": false, "retryDelayBaseMs": 25, "timeout": 30000, "isTracingEnabled": true, "isMessageBufferingEnabled": false, "bufferMessagePublishIntervalMillis": 200}, "eventsConf": {"arnPrefix": "arn:aws:sns:ap-south-1:035243212545:", "sqsPrefix": "https://sqs.ap-south-1.amazonaws.com/035243212545/", "isMessageBufferingEnabled": true, "bufferMessagePublishIntervalMillis": 200, "snsBatchPublishMaxSize": 10}, "appEventsConsumerConf": {"brokers": ["b-2.data-platform-prod-ka.876v8u.c3.kafka.ap-south-1.amazonaws.com:9092", "b-1.data-platform-prod-ka.876v8u.c3.kafka.ap-south-1.amazonaws.com:9092", "b-3.data-platform-prod-ka.876v8u.c3.kafka.ap-south-1.amazonaws.com:9092"], "clientId": "iris", "sessionTimeoutInMs": 300000, "heartbeatInterval": 3000, "maxBytes": 262144, "maxWaitTimeInMs": 200, "dlq": "production-iris-app-events-dlq"}, "notificationEventsKafkaConf": {"brokers": ["b-4.data-platform-prod-ka.876v8u.c3.kafka.ap-south-1.amazonaws.com:9092", "b-3.data-platform-prod-ka.876v8u.c3.kafka.ap-south-1.amazonaws.com:9092", "b-2.data-platform-prod-ka.876v8u.c3.kafka.ap-south-1.amazonaws.com:9092"], "clientId": "iris", "topic": "cfprodplatforms_iris_events", "dlq": "production-iris-notification-events-dlq"}, "port": 3010, "serverConfigOptions": {"enableCors": false, "skipLogin": true, "clusterModeEnabled": false, "metricsPort": 3009, "keepAliveTimeoutInSec": 120, "requestTimeoutInSec": 120, "requestBodySizeLimit": "2mb", "cookieSalt": "preventive", "accessLogsEnabled": true, "disableApiKeyMappingCache": true, "disableServiceAccessAuth": true, "enableConfigStore": true}, "s3": {"attachmentBucket": "cf-iris", "attachmentBucketPublic": "cf-iris-media-prod"}, "cfAPIConfig": {"baseUrl": "http://cfapi-internal.production.cure.fit.internal", "headers": {"Authorization": "Bearer a662f45cd97df7e30abd3036ab2f9fe3"}}, "branchBackendConfig": {"url": "https://api2.branch.io/v2/event/standard", "headers": {"branch_key": "key_live_anzEyViCZLYFjFyzpJgf5eedEqgnCP25"}}, "sentryConf": {"enabled": true, "dsn": "https://<EMAIL>/6653966"}, "datalakeMetricBackendConfig": {"baseUrl": "https://dataplatform-webhook.curefit.co/prod/commons/iris_custom_analytics", "apiKey": "81341430e2a296b54c7e908749612dbf"}, "taskManagement": {"baseUrl": "http://audit.production.cure.fit.internal/"}, "campaignManagerConfig": {"baseUrl": "http://campaign-manager.production.cure.fit.internal"}, "leadCategorisation": {"baseUrl": "https://dataplatform-webhook.curefit.co/"}}