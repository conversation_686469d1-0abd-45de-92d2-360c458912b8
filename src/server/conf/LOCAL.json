{"appName": "iris", "loggingDir": "logs/iris", "mongoConf": {"connections": [{"entitySpace": "DEFAULT", "instance": {"replicaSetName": "stage-shard-0", "host": "stage-pl-0.tfwfr.mongodb.net", "children": [{"host": "stage-shard-00-00-tfwfr.mongodb.net"}, {"host": "stage-shard-00-01-tfwfr.mongodb.net"}, {"host": "stage-shard-00-02-tfwfr.mongodb.net"}]}, "credentials": {"dbName": "curefit-stage", "replicaSetName": "stage-shard-0", "username": "service-access", "password": "xkcD32PCnGOfdril", "ssl": true, "authSource": "admin", "connectionOptions": {"minPoolSize": 1, "maxPoolSize": 6}}}], "isLeanQueryEnabled": true}, "redisConf": {"connections": [{"entitySpace": "DEFAULT", "hosts": [{"host": "localhost", "port": 6379}], "redisOptions": {}, "isCluster": false}, {"entitySpace": "NOTIFICATION_WRAPPER_CACHE", "hosts": [{"host": "localhost", "port": 6379}], "redisOptions": {}, "isCluster": false}, {"entitySpace": "REDIS_CACHE", "hosts": [{"host": "localhost", "port": 6379}], "redisOptions": {}, "isCluster": false}, {"entitySpace": "RASHI_REDIS_CACHE", "hosts": [{"host": "localhost", "port": 6379}, {"host": "localhost", "port": 6379}], "redisOptions": {}, "isCluster": false}, {"entitySpace": "BUMBLE", "hosts": [{"host": "localhost", "port": 6379}], "redisOptions": {}, "isCluster": false}]}, "mysqlConf": {"connections": [{"entitySpace": "DEFAULT", "connection": {"debug": true, "connection": {"host": "localhost", "port": 3306, "user": "root", "password": "", "database": "cfdb", "connectionLimit": 10, "timezone": "UTC"}}}, {"entitySpace": "REPLICA", "connection": {"debug": true, "connection": {"host": "localhost", "port": 3306, "user": "root", "password": "", "database": "cfdb", "connectionLimit": 10, "timezone": "UTC"}}}]}, "identityURL": "http://identity.stage.curefit.co", "whatsappConfig": {"accounts": [{"username": "Curefitwa", "password": "cure3456", "phone": "************", "serviceProvider": "VALUE_FIRST", "isDefault": true}]}, "eventsConf": {"arnPrefix": "arn:aws:sns:ap-south-1:************:", "sqsPrefix": "https://sqs.ap-south-1.amazonaws.com/************/", "isMessageBufferingEnabled": true, "bufferMessagePublishIntervalMillis": 200, "snsBatchPublishMaxSize": 10}, "backendServices": {"iris": {"url": "http://localhost:3010", "apiKey": ""}, "MOZART": {"url": "https://mozart.stage.curefit.co"}, "segmentationService": {"url": "https://segmentation.stage.curefit.co"}, "rashi": {"url": "https://rashi.stage.curefit.co"}, "configStore": {"url": "http://localhost:8091", "apiKey": "iris-stage-access"}}, "mozart": {"iris_schedule_notification_job_config_id": "e2dd0a22-6a86-4329-a2cd-18020291c81a", "iris_campaign_job_config_id": "ba95c584-8c8b-41fd-965c-2961649bfa0f", "iris_notification_job_config_id": "349740b7-0728-4a16-9fe2-f9320e91bd15", "iris_notification_retry_job_config_id": "6b3ab820-1836-42e2-94a9-4136c5909a1f"}, "sqsQueues": {"mozartSubmitJob": "stage-platforms-mozart-job-create", "falconEntityRefresh": "stage-platforms-falcon-entity"}, "sqsConf": {"connectTimeout": 5000, "maxRetries": 8, "url": "https://sqs.ap-south-1.amazonaws.com/************", "enableDebugLogs": false, "retryDelayBaseMs": 25}, "appEventsConsumerConf": {"brokers": ["localhost:9092"], "clientId": "iris", "sessionTimeoutInMs": 300000, "heartbeatInterval": 3000, "maxBytes": 262144, "maxWaitTimeInMs": 200, "dlq": "stage-iris-app-events-dlq"}, "notificationEventsKafkaConf": {"brokers": ["b-2.dataplatformstgkafka.8fdckx.c3.kafka.ap-south-1.amazonaws.com:9092", "b-1.dataplatformstgkafka.8fdckx.c3.kafka.ap-south-1.amazonaws.com:9092"], "clientId": "iris", "topic": "cfprodplatforms_iris_events", "dlq": "stage-iris-app-events-dlq"}, "port": 3010, "serverConfigOptions": {"enableCors": false, "skipLogin": true, "clusterModeEnabled": false, "metricsPort": 3009, "keepAliveTimeoutInSec": 120, "requestBodySizeLimit": "2mb", "cookieSalt": "preventive", "accessLogsEnabled": true, "disableApiKeyMappingCache": true, "disableServiceAccessAuth": true, "enableConfigStore": false}, "s3": {"attachmentBucket": "cf-iris-stage", "attachmentBucketPublic": "cf-iris-media-stage"}, "cfAPIConfig": {"baseUrl": "http://cfapi-internal.stage.cure.fit.internal", "headers": {"Authorization": "Bearer 0a040ec34abbfb7f3030345244a913c9"}}, "branchBackendConfig": {"url": "https://api2.branch.io/v2/event/standard", "headers": {"branch_key": "key_test_hmyzFVcx3J7FmDDyfKmj8fhowqcouZkl"}}, "datalakeMetricBackendConfig": {"baseUrl": "http://localhost:50010", "apiKey": "<PERSON><PERSON><PERSON><PERSON>"}, "taskManagement": {"baseUrl": "http://audit.stage.cure.fit.internal/"}, "campaignManagerConfig": {"baseUrl": "http://campaign-manager.stage.cure.fit.internal"}}