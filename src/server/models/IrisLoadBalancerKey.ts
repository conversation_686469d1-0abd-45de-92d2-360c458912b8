import {Key} from "@curefit/cache-utils"

export class IrisLoadBalancerKey implements Key {

    taskId: string
    key: string

    constructor(taskId: string, prefix: string) {
        this.taskId = taskId
        this.key = `iris_lb_${prefix}_${taskId}`
    }

    lookupKey(): string {
        return this.key
    }

    hash(): string {
        return undefined
    }

    static from(taskId: string, prefix?: string) {
        switch (prefix) {
            case "counter" :
                //to increase/reset a counter determining serving status of a provider
                return new IrisLoadBalancer<PERSON>ey(taskId, "counter")
                break
            case "provider_status":
                //to find current serving status of a provider
                return new IrisLoadBalancerKey(taskId, "provider_status")
                break
            default:
                return new IrisLoadBalancer<PERSON>ey(taskId, "id")
                break
        }
        return new IrisLoadBalancer<PERSON>ey(taskId, "id")
    }
}

export class IrisLoadBalancerValue {
    providersTried: ProviderTry[]

    static getValue(value: any) {
        return value
    }
}

export class ProviderTry {

    constructor(provider: string) {
        this.provider = provider
    }

    provider: string
}

