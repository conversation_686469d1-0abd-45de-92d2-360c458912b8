import { MultiMongooseSchema, IRead, IWrite, MultiMongooseAccess, MONGO_TYPES, MongoReadonlyDao, MongoReadWriteDao } from "@curefit/mongo-utils"
import * as Mongoose from "mongoose"
import { inject, injectable } from "inversify"
import { BASE_TYPES, ILogger } from "@curefit/base"
import TYPES from "../ioc/types"
export interface SlackAuthMapping {
    userId: String
    slackUserId: String
    botUserId: String
    botAccessToken: String
    channelId: String
}

export interface SlackPostMessagePayload {
    token: String
    channel: String
    text: String
    blocks: any
}
export interface SlackAuthMappingModel extends SlackAuthMapping, Mongoose.Document { }
export interface ISlackAuthMappingReadWriteDao extends IWrite<SlackAuthMapping>, IRead<SlackAuthMapping> { }
export interface ISlackAuthMappingReadOnlyDao extends IRead<SlackAuthMapping> { }

@injectable()
export class SlackAuthMappingSchema extends MultiMongooseSchema<SlackAuthMappingModel> {

    constructor(@inject(MONGO_TYPES.MultiMongooseAccess) multiMongooseAccess: MultiMongooseAccess) {
        super(multiMongooseAccess, "slackauthmappings", "DEFAULT")
    }

    protected schema() {
        return {
            userId: {
                type: String,
                unique: true,
                index: true,
                required: true,
            },
            slackUserId: {
                type: String,
                required: true,
            },
            botUserId: {
                type: String,
                required: true,
            },
            botAccessToken: {
                type: String,
                required: true,
            },
            channelId: {
                type: String,
                required: true,
            }
        }
    }
}

@injectable()
export class SlackAuthMappingReadOnlyDaoMongoImpl extends MongoReadonlyDao<SlackAuthMappingModel, SlackAuthMapping> implements ISlackAuthMappingReadOnlyDao {
    constructor(@inject(TYPES.SlackAuthMappingSchema) slackAuthMappingSchema: SlackAuthMappingSchema,
        @inject(BASE_TYPES.ILogger) logger: ILogger) {
        super(slackAuthMappingSchema.mongooseModel, logger)
    }
}

@injectable()
export class SlackAuthMappingReadWriteDaoMongoImpl extends MongoReadWriteDao<SlackAuthMappingModel, SlackAuthMapping> implements ISlackAuthMappingReadWriteDao {
    constructor(@inject(TYPES.SlackAuthMappingSchema) slackAuthMappingSchema: SlackAuthMappingSchema,
        @inject(TYPES.SlackAuthMappingReadOnlyDao) readonlyDao: SlackAuthMappingReadOnlyDaoMongoImpl,
        @inject(BASE_TYPES.ILogger) logger: ILogger) {
        super(slackAuthMappingSchema.mongooseModel, readonlyDao, logger)
    }
}