import {Key} from "@curefit/cache-utils"

export class IANNotification<PERSON>ey implements Key {

    userId: string
    appId: string
    key: string

    constructor(userId: string, appId: string) {
        this.userId = userId
        this.appId = appId
        this.key = `iris_ian__${userId}__${appId}`
    }

    lookupKey(): string {
        return this.key
    }

    hash(): string {
        return undefined
    }

    static from(userId: string, appId: string) {
        return new IANNotificationKey(userId, appId)
    }
}
