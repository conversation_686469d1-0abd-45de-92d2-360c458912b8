import {inject, injectable} from "inversify"
import {
    IR<PERSON>,
    I<PERSON><PERSON>,
    <PERSON><PERSON>G<PERSON>_TYPES,
    MongoReadonlyDao,
    MongoReadWriteDao,
    MultiMongooseAccess,
    MultiMongooseSchema
} from "@curefit/mongo-utils"
import * as Mongoose from "mongoose"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {ApiKeyMapping} from "@curefit/access-common"
import TYPES from "../ioc/types"


export interface ApiKeyMappingModel extends ApiKeyMapping, Mongoose.Document { }
export interface IApiKeyMappingReadWriteDao extends IWrite<ApiKeyMapping>, IRead<ApiKeyMapping> {}
export interface IApiKeyMappingReadonlyDao extends IRead<ApiKeyMapping> {}

const SingleKeySchema = {
    apiKeyHash: {
        type: String,
        index: true,
        required: true
    },
    clientName: {
        type: String,
        index: true,
        required: true
    }
}


@injectable()
export class ApiKeyMappingSchema extends MultiMongooseSchema<ApiKeyMappingModel> {

    constructor( @inject(MONGO_TYPES.MultiMongooseAccess) multiMongooseAccess: MultiMongooseAccess) {
        super(multiMongooseAccess, "apikeymappings", "DEFAULT")
    }

    protected schema() {
        return {
            serviceName: {
                type: String,
                index: true,
                required: true,
                unique: true
            },
            keys : {
                type: [SingleKeySchema],
                index: true,
                required: true
            }
        }
    }
}

@injectable()
export class ApiKeyMappingReadonlyDaoMongoImpl extends MongoReadonlyDao<ApiKeyMappingModel, ApiKeyMapping> implements IApiKeyMappingReadonlyDao {
    constructor(@inject(TYPES.ApiKeyMappingSchema) apiKeyMappingSchema: ApiKeyMappingSchema,
                @inject(BASE_TYPES.ILogger) logger: ILogger) {
        super(apiKeyMappingSchema.mongooseModel, logger)
    }
}

@injectable()
export class ApiKeyMappingReadWriteDaoMongoImpl extends MongoReadWriteDao<ApiKeyMappingModel, ApiKeyMapping> implements IApiKeyMappingReadWriteDao {
    constructor(@inject(TYPES.ApiKeyMappingSchema) apiKeyMappingSchema: ApiKeyMappingSchema,
                @inject(TYPES.ApiKeyMappingReadonlyDao) readonlyDao: ApiKeyMappingReadonlyDaoMongoImpl,
                @inject(BASE_TYPES.ILogger) logger: ILogger) {
        super(apiKeyMappingSchema.mongooseModel, readonlyDao, logger)
    }
}