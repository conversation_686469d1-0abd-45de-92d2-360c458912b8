/*
 * Doc link:
 * https://firebase.google.com/docs/reference/dynamic-links/link-shortener
 */

type FirebaseLinkOption = "SHORT" | "UNGUESSABLE"

export interface AndroidInfo {
  androidPackageName: string
  androidFallbackLink?: string
  androidMinPackageVersionCode?: string
}

export interface IOSInfo {
  iosBundleId: string
  iosFallbackLink?: string
  iosCustomScheme?: string
  iosIpadFallbackLink?: string
  iosIpadBundleId?: string
  iosAppStoreId: string
}

export interface IFirebaseDynamicPayload {
    dynamicLinkInfo: {
      domainUriPrefix: string,
      link: string,
      androidInfo: AndroidInfo,
      iosInfo: IOSInfo,
      navigationInfo?: {
        enableForcedRedirect: boolean,
      },
      analyticsInfo?: {
        googlePlayAnalytics?: {
          utmSource?: string,
          utmMedium?: string,
          utmCampaign?: string,
          utmTerm?: string,
          utmContent?: string,
          gclid?: string
        },
        itunesConnectAnalytics?: {
          at?: string,
          ct?: string,
          mt?: string,
          pt?: string
        }
      },
      socialMetaTagInfo?: {
        socialTitle?: string,
        socialDescription?: string,
        socialImageLink?: string
      }
    },
    suffix?: {
      option: FirebaseLinkOption
    }
  }