import {Key} from "@curefit/cache-utils"

/*
    We use this constant key to track number of active send to segment jobs
    This count allows us to scale out preemptively
 */
export class ScaleHistoryKey implements Key {

    key: string

    constructor(channel: string) {
        this.key = `iris_scale_${channel}`
    }

    hash(): string {
        return undefined
    }

    lookupKey(): string {
        return this.key
    }
}