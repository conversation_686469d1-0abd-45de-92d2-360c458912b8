import * as _ from "lodash"
import * as Handlebars from "handlebars"
import * as mustache from "mustache"

export class MustacheUtils {

    public static typeName = "MustacheUtils"

    /**
    /**
    /**
     * Returns a list of text matches for {{tag}} in the text
     * @param text The text to be matches
     */
    private static getTagsUsed(text: string): string[] {
        if (!text)
            return []
        // Find {{tag}} and {{{tag}}}
        const threes = text.match(/{{{.*?}}}/gi) || []
        const twos = text.match(/{{.*?}}/gi) || []
        // remove all two tags if its present in the threes
        return _.concat(threes,
            twos.filter(t2 => _.isEmpty(threes.filter(t3 => t3.includes(t2)))))
    }

    /**
     * All unique tags used in the texts
     * @param texts
     */
    static allTagsUsed(texts: string[]): string[] {
        const uniqTags = _.uniq(_.flatten(_.map(texts, t => MustacheUtils.getTagsUsed(t))))
        // send without the {{ }}
        return _.map(uniqTags, t =>
            t.startsWith("{{{") ? t.substring(3, t.length - 3) : t.substring(2, t.length - 2))
    }

    /**
     * Check if the context has all tags provided
     * Checks for both - Mustache and FunctionalTags
     */
    static contextHasAllTags(tags: string[], context: { [tagName: string]: string }): boolean {
        for (let t of tags) {
            if (t.startsWith("#") || t.startsWith("/") || t.startsWith("^")) {
                continue
            }
            if (_.isUndefined(context[t])) {
                return false
            }
        }
        return true
    }

    /**
     * Check if the context has all tags provided (including nested tags)
     */
    static ifTagNotInContext(tag: string, context: { [tagName: string]: string }): boolean {
        if (_.isUndefined(context[tag])) {
            const splittedTags = tag.split(".")
            let userTagInContext: any = context
            for (let t of splittedTags) {
                userTagInContext = userTagInContext[t]
                if (_.isUndefined(userTagInContext)) return true
            }
        }
        return false
    }

    public static render(template: string, tags: object): string {
        return mustache.render(template, tags)
    }

}