import {inject, injectable} from "inversify"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {
    IUserAttributeCacheClient,
    RASHI_CLIENT_TYPES,
    UserAttributeClient,
    UserAttributesResponse
} from "@curefit/rashi-client/dist"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import Constants from "../constants"
import {CommonUtils} from "./CommonUtils"
import * as _ from "lodash"

@injectable()
class UserProfileAttributeUtils {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(RASHI_CLIENT_TYPES.UserAttributeClient) private userAttributeClient: UserAttributeClient,
        @inject(RASHI_CLIENT_TYPES.UserAttributeCacheClient) private userAttributeCacheClient: IUserAttributeCacheClient
    ) {
    }

    public static USER_PROFILE_ATTRIBUTE_KEY_COMMUNICATION_LIMITS = "communicationslimits"

    // Gets a single attribute
    public async getCachedUserAttribute(userId: string, attribute: string, appId?: string): Promise<any> {
        const response = await this.getCachedUserAttributes(userId, [ attribute ], appId)
        return response.attributes.get(attribute)
}

    // Gets a bunch of attributes; Internally rashi makes individual calls though, so no IO advantage
    public async getCachedUserAttributes(userId: string, attributes?: string [], appId?: string): Promise<UserAttributesResponse> {
        appId = appId || Constants.APP_ID_CUREFIT

        let numericComputedTraits: string[] = await this.userAttributeCacheClient.getCachedNumericComputedTraits()

        const response = await this.userAttributeCacheClient.getInterpretedCachedUserAttributes(+userId, CommonUtils.convertAppIdtoAppTenant(appId), attributes)
        for (const attribute of response.attributes.keys()) {
            const value = response.attributes.get(attribute)

            if (!value) {
                continue
            }

            //Negative computed traits don't exist in curefit ecosystem.
            if (numericComputedTraits.includes(attribute) && (value < 0)) {
                response.attributes.set(attribute, null)
            }

            // TODO: Remove hack after fix from rashi
            if (value.toString().toLowerCase() === "null") {
                response.attributes.set(attribute, null)
                continue
            }
            this.logger.debug(`UserProfileAttributeUtils::getCachedUserAttributes After fixing key ` +
                `${attribute} value ${response.attributes.get(attribute)} and is of type: ${typeof response.attributes.get(attribute)}`)
        }
        return response
    }

    //Fetches All Global Attributes from Rashi for communications
    public async getAllGlobalAttributes(appId: string): Promise<Record<string, string>> {
        try {
            return await this.userAttributeCacheClient.getAllGlobalAttributes(CommonUtils.convertAppIdtoAppTenant(appId))
        } catch (e) {
            this.logger.error(`UserProfileAttributeUtils::getGlobalAttributes error ${e}`)
            throw e
        }

    }

    public async getInterpretedCachedGlobalAttributes(appId: string, attr?: string[]): Promise<Map<string, any>> {
        if (!_.isEmpty(attr)) {
            try {
                // return await this.userAttributeCacheClient
                return await this.userAttributeCacheClient.getInterpretedCachedGlobalAttributes(CommonUtils.convertAppIdtoAppTenant(appId), attr)
            } catch (e) {
                    this.logger.error(`UserProfileAttributeUtils::getInterpretedCachedGlobalAttributes 
                fetching interpreted attributes ${attr} for appId ${appId} error ${e}`)
                throw e
            }
        } else {
            try {
                return await this.userAttributeCacheClient.getInterpretedCachedGlobalAttributes(CommonUtils.convertAppIdtoAppTenant(appId))
            }  catch (e) {
                this.logger.error(`UserProfileAttributeUtils::getInterpretedCachedGlobalAttributes fetching all 
                global attributes for appId ${appId} error ${e}`)
                throw e
            }
        }
    }

    //Fetches Global Attributes from Rashi for communications for the given attributes
    public async getCachedGlobalAttributes(appId: string, attr?: string[]): Promise<Map<string, any>> {
        try {
            return await this.userAttributeCacheClient.getGlobalAttributes(CommonUtils.convertAppIdtoAppTenant(appId), attr)
        } catch (e) {
            this.logger.error(`UserProfileAttributeUtils::getCachedGlobalAttributes unable to fetch Global Attributes error ${e}`)
            this.rollbarService.sendError(new Error(`UserProfileAttributeUtils::getCachedGlobalAttributes unable to 
            fetch Global Attributes for appId: ${appId} and attributes: ${attr} because error: ${e}`))
        }

    }

    public async setUserAttributes(userId: string, attribute: string, attributeValue: any, namespace: string, description: string, dataType: string,  appId?: string) {
        try {
            await this.userAttributeClient.setUserAttributes({
                userId: Number(userId),
                attribute: attribute,
                attrValue: attributeValue,
                namespace: namespace,
                description: description,
                dataType: dataType,
                occuredAt: new Date().getTime()
            }, null, CommonUtils.convertAppIdtoAppTenant(appId) )
        } catch (e) {
            this.logger.error(`UserProfileAttributeUtils::setUserAttributes unable to set Attribute error ${e}`)
            this.rollbarService.sendError(new Error(`UserProfileAttributeUtils::setUserAttributes unable to set Attribute error for appId: ${appId} and attributes: ${attribute} and value ${JSON.stringify(attributeValue)} because error: ${e}`))
        }
    }

}

export default UserProfileAttributeUtils