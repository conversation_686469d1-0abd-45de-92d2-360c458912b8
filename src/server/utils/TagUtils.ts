import {injectable} from "inversify"
import FunctionalTagsUtils from "./FunctionalTagsUtils"
import {MustacheUtils} from "./MustacheUtils"
import * as mustache from "mustache"

@injectable()
class TagUtils {

    public static render(template: string, tags: object, renderLibrary: any): string {
        template = FunctionalTagsUtils.preProcess(template, tags)
        if (renderLibrary === mustache)
            renderLibrary.parse(template)
        template = renderLibrary.render(template, tags)
        return FunctionalTagsUtils.render(template, tags)
    }

    /**
     * All unique tags used in the texts
     * @param texts
     */
    static allTagsUsed(texts: string[]): string[] {
        let allTags = MustacheUtils.allTagsUsed(texts)
        return allTags.concat(FunctionalTagsUtils.allTagsUsed(texts))
    }


}

export default TagUtils