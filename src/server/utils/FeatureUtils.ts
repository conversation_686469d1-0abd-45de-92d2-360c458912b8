import {inject, injectable} from "inversify"
import {BASE_TYPES, ILogger} from "@curefit/base"
import Constants from "../constants"
import * as _ from "lodash"
import {ScaleApplicationParams} from "@curefit/voyager-common"
import { IAppConfigCache, SERVER_TYPES } from "@curefit/server"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

@injectable()
class FeatureUtils {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(SERVER_TYPES.AppConfigCache) private configStore: IAppConfigCache,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService
    ) {
    }

    private static USE_RASHI_FOR_USER_TAGS: string = "useRashiForUserTags"
    private static USE_RASHI_FOR_USER_TIMEZONE: string = "useRashiForUserTimezone"

    private static USER_SPECIFIC_COMM_LIMITS_ENABLED: string = "userSpecificCommLimitsEnabled"

    private static PUSH_NOTIFICATION_DELIVERY_REPORT_VIA_FIREBASE_ENABLED = "pushNotificationDeliveryReportViaFirebaseEnabled"
    private static PUSH_NOTIFICATION_DELIVERY_REPORT_VIA_CF_API_ENABLED = "pushNotificationDeliveryReportViaCFAPIEnabled"
    private static PUSH_NOTIFICATION_DELIVERY_REPORT_VIA_APP_EVENTS_ENABLED = "pushNotificationDeliveryReportViaAppEventsEnabled"

    private static FIREBASE_ADMIN_MESSAGING_MIN_APP_VERSION = "firebaseAdminMessagingMinAppVersion"
    private static NOTIFEE_MESSAGING_MIN_APP_VERSION = "notifeeMessagingMinAppVersion"

    private static SCALE_IN_CONFIG = "scaleInConfig"
    private static SCALE_OUT_CONFIG = "scaleOutConfig"

    private static ENCODE_USER_CONTEXT_TAG_STRING = "encodeUserContextTagString"

    private static FAILURE_ALERT_CONFIG = "failureAlertConfig"

    private static PASS_ONLY_NOTIFICATION_IDS_TO_WORKERS = "passOnlyNotificationIdsToWorkers"

    private static SMS_PROVIDER_CONFIG = "smsProviderConfig"

    private static BLACKLIST_CREATIVE_CONFIG = "blacklistCreativeConfig"

    public getConfigValue(config: string, defaultValue: any): any {
        try {
            return JSON.parse( this.configStore.getConfig(config, {default: JSON.stringify(defaultValue)}))
        } catch (e) {
            const errMsg = `FeatureUtils::getConfigValue unable to fetch ${config} from Config-Store. Using Default.`
            this.logger.error(errMsg)
            this.rollbarService.sendError(new Error(errMsg))
            return defaultValue
        }

    }

    public getFailureAlertConfig(): any {
        return this.getConfigValue(FeatureUtils.FAILURE_ALERT_CONFIG, Constants.getDefaultFailureAlertConfig())
    }

    public passOnlyNotificationIdsToWorkers(): boolean {
        return this.getConfigValue(FeatureUtils.PASS_ONLY_NOTIFICATION_IDS_TO_WORKERS, false)
    }

    public useRashiForUserTags(): boolean {
        return this.getConfigValue(FeatureUtils.USE_RASHI_FOR_USER_TAGS, false)
    }

    public useRashiForUserTimezone(): boolean {
        return this.getConfigValue(FeatureUtils.USE_RASHI_FOR_USER_TIMEZONE, false)
    }

    public isUserSpecificCommLimitEnabled(): boolean {
        return this.getConfigValue(FeatureUtils.USER_SPECIFIC_COMM_LIMITS_ENABLED, false)
    }

    public getThrottledNotificationScheduleDelayInMinutes(): number {
        return this.getConfigValue(
            Constants.THROTTLED_NOTIFICATION_SCHEDULE_DELAY_IN_MINUTES_CONFIG_KEY,
            Constants.THROTTLED_NOTIFICATION_SCHEDULE_DELAY_IN_MINUTES_DEFAULT
        )
    }

    public isPushNotificationDeliveryReportViaFirebaseEnabled(): boolean {
        return this.getConfigValue(
            FeatureUtils.PUSH_NOTIFICATION_DELIVERY_REPORT_VIA_FIREBASE_ENABLED, true)
    }

    public isPushNotificationDeliveryReportViaCFAPIEnabled(): boolean {
        return this.getConfigValue(
            FeatureUtils.PUSH_NOTIFICATION_DELIVERY_REPORT_VIA_CF_API_ENABLED, true)
    }

    public isPushNotificationDeliveryReportViaAppEventsEnabled(): boolean {
        return this.getConfigValue(
            FeatureUtils.PUSH_NOTIFICATION_DELIVERY_REPORT_VIA_APP_EVENTS_ENABLED, true)
    }

    public firebaseAdminMessagingMinAppVersion(appId: string): number {
        const config = this.getConfigValue(FeatureUtils.FIREBASE_ADMIN_MESSAGING_MIN_APP_VERSION, Constants.getDefaultFirebaseAdminMessagingMinAppVersion())
        return _.get(config, appId, Number.MAX_SAFE_INTEGER)
    }

    public notifeeMessagingMinAppVersion(appId: string): number {
        const config = this.getConfigValue(FeatureUtils.NOTIFEE_MESSAGING_MIN_APP_VERSION, Constants.NOTIFEE_MESSAGING_MIN_APP_VERSION_DEFAULT)
        return _.get(config, appId, Number.MAX_SAFE_INTEGER)
    }

    public encodeUserTagString(): boolean {
        return this.getConfigValue(
            FeatureUtils.ENCODE_USER_CONTEXT_TAG_STRING, false)
    }

    private static DEFAULT_SCALE_OUT_CONFIG = {
        SERVER: {
            applicationName: "iris",
            minReplicas: 10,
            maxReplicas: 25,
            targetCPUUtilisation: 200
        },
        EMAIL: {
            applicationName: "iris-worker-email",
            minReplicas: 10,
            maxReplicas: 20,
            targetCPUUtilisation: 250
        },
        PUSH_NOTIFICATION: {
            applicationName: "iris-worker-pn",
            minReplicas: 10,
            maxReplicas: 30,
            targetCPUUtilisation: 250
        },
        SMS: {
            applicationName: "iris-worker-sms",
            minReplicas: 10,
            maxReplicas: 25,
            targetCPUUtilisation: 250
        },
        WHATSAPP: {
            applicationName: "iris-worker-whatsapp",
            minReplicas: 4,
            maxReplicas: 25,
            targetCPUUtilisation: 250
        }
    }
    public getScaleOutConfig(): {[key: string]: ScaleApplicationParams} {
        return this.getConfigValue(FeatureUtils.SCALE_OUT_CONFIG, FeatureUtils.DEFAULT_SCALE_OUT_CONFIG)
    }

    private static DEFAULT_SCALE_IN_CONFIG = {
        SERVER: {
            applicationName: "iris",
            minReplicas: 2,
            maxReplicas: 12,
            targetCPUUtilisation: 200
        },
        EMAIL: {
            applicationName: "iris-worker-email",
            minReplicas: 2,
            maxReplicas: 20,
            targetCPUUtilisation: 250
        },
        PUSH_NOTIFICATION: {
            applicationName: "iris-worker-pn",
            minReplicas: 2,
            maxReplicas: 30,
            targetCPUUtilisation: 250
        },
        SMS: {
            applicationName: "iris-worker-sms",
            minReplicas: 2,
            maxReplicas: 25,
            targetCPUUtilisation: 250
        },
        WHATSAPP: {
            applicationName: "iris-worker-whatsapp",
            minReplicas: 2,
            maxReplicas: 25,
            targetCPUUtilisation: 250
        }
    }
    public getScaleInConfig(): {[key: string]: ScaleApplicationParams} {
        return this.getConfigValue(FeatureUtils.SCALE_IN_CONFIG, FeatureUtils.DEFAULT_SCALE_IN_CONFIG)
    }

    public getSmsProviderConfig(defaultValue: any) {
        return this.getConfigValue(FeatureUtils.SMS_PROVIDER_CONFIG, defaultValue)
    }

    public getBlacklistCreativeConfig(defaultValue: any) {
        return this.getConfigValue(FeatureUtils.BLACKLIST_CREATIVE_CONFIG, defaultValue)
    }


}

export default FeatureUtils