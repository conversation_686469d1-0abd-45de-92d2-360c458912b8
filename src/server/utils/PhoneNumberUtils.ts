import {Country} from "../../iris-common"

export class PhoneNumberUtils {

    static getCountry(phone: string): Country {
        if (phone.startsWith("+971")) {
            return "DUBAI"
        } else if (phone.startsWith("+1")) {
                return "USA-CANADA"
        } else {
            return "INDIA"
        }
    }

    static addCountryCode(phoneNumber: string, countryName?: string) {
        const phone = phoneNumber.replace(/-/g, "")
        const country = countryName ? countryName : this.getCountry(phone)
        switch (country) {
            case "DUBAI":
                if (phone.startsWith("+971")) return phone
                return "+971" + phone
                    break
            case "USA-CANADA":
                if (phone.startsWith("+1")) return phone
                return "+1" + phone
                break
            default:
                if (phone.startsWith("+91")) return phone
                return "+91" + phone
        }
    }

    static getPossibleNumbers(phone: string, countryCode: string): string[] {
        const numbers: string[] = [phone]
        if (countryCode) {
            // +91123456789
            const phoneNumber = countryCode + phone
            numbers.push(phoneNumber)
            // +91-123456789
            const dashNumber = countryCode + "-" + phone
            numbers.push(dashNumber)
        }
        return numbers
    }

    static findPhoneStyle(phone: string): string {
        if (!phone) return "Empty"
        if (phone.length === 10) return "WithoutCountryCode"
        if (phone.length === 14 && phone.startsWith("+91-")) return "CountryCodeWithDash"
        if (phone.length === 13 && phone.startsWith("+91")) return "CountryCode"
        if (phone.startsWith("+971")) return "DUBAI"
        if (phone.startsWith("+1")) return "USA-CANADA"
        return "Invalid"
    }

}