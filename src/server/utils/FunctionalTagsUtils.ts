import {injectable} from "inversify"

import Constants from "../constants"
import _ = require("lodash")
import uuid = require("uuid")

@injectable()
class FunctionalTagsUtils {

    public static render(template: string, tags: object): string {
        const regexp = new RegExp(Constants.PERSONALIZED_ATTRIBUTE_TAG_REG_EXP, "g")
        let match
        const const_template = template
        while (match = regexp.exec(const_template)) {
            if (!(<any>tags)[match[0]]) {
                throw new Error(`Unable to resolve Functional Tag: ${match[0]}`)
            }
            template = template.replace(match[0], (<any>tags)[match[0]])
        }
        return template
    }


    public static preProcess(template: string, tags: object): string {
        /*
        Here, we replace all functional Tags by a uuid(). The reason is simple: there are a lot of restrictions
         with Handlebar variables. Handlebar variables cannot contain:

         Whitespace ! " # % & ' ( ) * + , . / ; < = > @ [ \ ] ^ ` { | } ~

         Hence, we replace the entire tag by a uuid(), so that Handlebars can easily render if-else tags that are
          necessary, once Handlebar rendering is complete, we replace the uuids() with their actual replacements in
           FunctionalTagsUtils.render()
         */
        const regexp = new RegExp(Constants.PERSONALIZED_ATTRIBUTE_TAG_REG_EXP, "g")
        let match
        const const_template = template
        let tagToUuid: Map<string, string> = new Map<string, string>()
        while (match = regexp.exec(const_template)) {
            const key = tagToUuid.has(match[0]) ? tagToUuid.get(match[0]) : uuid()
            tagToUuid.set(match[0], key)
            template = template.replace(match[0], Constants.PERSONALIZED_ATTRIBUTE_TAG_PREFIX + "$$" + key
                + "$$" + Constants.PERSONALIZED_ATTRIBUTE_TAG_SUFFIX)
            if (!(<any>tags)[match[0]]) {
                continue
            }
            (<any>tags)[Constants.PERSONALIZED_ATTRIBUTE_TAG_PREFIX + key
            + Constants.PERSONALIZED_ATTRIBUTE_TAG_SUFFIX] = (<any>tags)[match[0]]
        }
        return template
    }
     /**
     * Returns a list of text matches for $$$tag$$$ in the text
     * @param text The text to be matches
     */
    private static getTagsUsed(text: string): string[] {
        if (!text) {
            return []
        }
        //Find $$$tag$$$
        const regexp = new RegExp(Constants.PERSONALIZED_ATTRIBUTE_TAG_REG_EXP, "gi")
        return text.match(regexp) || []
    }

    /**
     * All unique tags used in the texts
     * @param texts
     */
    static allTagsUsed(texts: string[]): string[] {
        const uniqTags = _.uniq(_.flatten(_.map(texts, t => FunctionalTagsUtils.getTagsUsed(t))))
        return uniqTags
    }


}

export default FunctionalTagsUtils