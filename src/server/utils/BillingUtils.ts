import Constants from "../constants"
import {Country, ServiceProvider} from "../../iris-common"


export class BillingUtils  {

    static getSMSUnitsConsumed(body: string, country: Country, serviceProvider: ServiceProvider): number {
        switch (serviceProvider) {
            case "SOLUTIONS_INFINI":
                return Math.ceil(body.length / Constants.SMS_LENGTH)
            case "GUPSHUP":
                return Math.ceil(body.length / Constants.SMS_LENGTH)
            case "FONADA":
                return Math.ceil(body.length / Constants.SMS_LENGTH)
            default:
                return Math.ceil(body.length / Constants.SMS_LENGTH)
        }
    }

    static getPNUnitsConsumed(): number {
        return 1
    }

    static getOBDUnitsConsumed(): number {
        return 1
    }

    static getCTCUnitsConsumed(billableTimeInSeconds: number): number {
        return billableTimeInSeconds
    }

    static getIANUnitsConsumed(): number {
        return 1
    }

    static getWhatsappUnitsConsumed(): number {
        return 1
    }

    static getEmailUnitsConsumed(): number {
        return 1
    }

    static getSOIPUnitsConsumed(): number {
        return 1
    }
}
