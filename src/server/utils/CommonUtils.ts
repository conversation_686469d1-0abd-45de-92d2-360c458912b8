import {inject, injectable} from "inversify"
import * as _ from "lodash"
import {BASE_TYPES, Logger} from "@curefit/base"
import {UserTags} from "../../iris-common"
import {AppTenant} from "@curefit/base-common"
import Constants from "../constants"

const fetch = require("node-fetch")

interface ICheckFields {
    collection: any,
    fields: string[],
    isThrowError?: boolean,
    errorMsg?: string,
    logMsg?: string
}

@injectable()
export class CommonUtils {
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger
    ) {
    }

    public checkFields({ collection, fields, isThrowError = true, errorMsg, logMsg }: ICheckFields) {
        const paramKeys = _.keys(collection)
        const notFoundFields = _.difference(fields, _.intersection(paramKeys, fields))

        _.each(notFoundFields, (field) => {
            logMsg = (logMsg && logMsg.replace("{field}", field)) || `${field} not found!`
            this.logger.error(logMsg)
        })

        if (!_.isEmpty(notFoundFields) && isThrowError) {
            errorMsg = errorMsg || "Invalid params found!"
            throw new Error(errorMsg)
        }

        return _.isEmpty(notFoundFields) ? { status: "OK", notFoundFields } : { status: "ERROR", notFoundFields }
    }

    public async fetchWithRetry(retryAttempts: number, backOff: number, ...fetchArgs: any[]): Promise<any> {
        if (retryAttempts === 0) {
            throw new Error("Retry failed")
        }

        try {
            const response = await fetch(...fetchArgs)
            if (response.ok) { return response}
            else { throw new Error(`Response not ok ${JSON.stringify(response)}`) }
        } catch (err) {
            this.logger.error(`CommonUtils::fetchWithRetry Failed with ${err.toString()}.` +
                `Params: ${JSON.stringify(fetchArgs)}`)
            const commonUtilsInstance = this

            return new Promise(function (resolve, reject) {
                setTimeout(() => {
                    resolve(commonUtilsInstance.fetchWithRetry(
                        retryAttempts - 1, backOff * 2, ...fetchArgs))
                }, backOff)
            })
        }
    }

    public static hasNotificationExpired(userContext: UserTags): boolean {
        // null expiry means no expiry
        return userContext.expiry ? new Date(userContext.expiry) < new Date() : false
    }

    public static isDateAfterNotificationExpiry(userContext: UserTags, date: Date): boolean {
        // null expiry means no expiry
        return userContext && userContext.expiry ? new Date(userContext.expiry) < date : false
    }

    public static isEnvironmentLocal(): boolean {
        return process.env.ENVIRONMENT.toLowerCase() === "local"
    }

    public static isEnvironmentStage(): boolean {
        return process.env.ENVIRONMENT.toLowerCase() === "stage"
    }

    public static isEnvironmentAlpha(): boolean {
        return process.env.ENVIRONMENT.toLowerCase() === "alpha"
    }

    public static isEnvironmentProduction(): boolean {
        return process.env.ENVIRONMENT.toLowerCase() === "production"
    }

    public static isEnvironmentProductionOrAlpha(): boolean {
        return CommonUtils.isEnvironmentAlpha() || CommonUtils.isEnvironmentProduction()
    }

    //returns integer [1,100] inclusive
    public static getRandomNumber1to100() {
        return Math.floor(Math.random() * 100) + 1
    }

    //For converting Iris AppIds to Rashi App Tenant
    public static convertAppIdtoAppTenant(appId: string): AppTenant {
        switch (appId) {
            case Constants.APP_ID_SUGARFIT : {
                return AppTenant.SUGARFIT
            }
            case Constants.APP_ID_LIVEFIT: {
                return AppTenant.LIVEFIT
            }
            case Constants.APP_ID_CULTSPORT: {
                return AppTenant.CULTSPORT
            }
            case Constants.APP_ID_ULTRAFIT: {
                return AppTenant.ULTRAFIT
            }
            default: {
                return AppTenant.CUREFIT
            }
        }

    }
}