import {inject, injectable} from "inversify"
import {BASE_TYPES, Configuration, ILogger} from "@curefit/base"
import {IQueueService, SQS_CLIENT_TYPES} from "@curefit/sqs-client"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"

@injectable()
export class KafkaUtils {


    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService
    ) {

    }

    async handleMessageFailure(message: any, queueResource: string) {
         this.logger.info(`AppEventsConsumer::handleMessageFailure pushing message to dlq ${queueResource} ` +
            `offset: ${message.offset}`)
        try {
            this.queueService.sendMessageAsync(queueResource, message)
        } catch (error) {
            this.logger.error(`AppEventsConsumer::handleMessageFailure Error while sending message with offset ` +
                `${message.offset} to dlq ${queueResource} ${error.toString()}`)
        }
    }

}

