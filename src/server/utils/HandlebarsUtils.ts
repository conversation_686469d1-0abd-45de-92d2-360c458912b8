import { injectable } from "inversify"

import * as Handlebars from "handlebars"
import { mathHelpers } from "../helpers/handlebar/mathHelper"
import { stringHelpers } from "../helpers/handlebar/stringHelper"
import { comparisonHelper } from "../helpers/handlebar/comparisonHelper"
import { accessHelper } from "../helpers/handlebar/accessHelper"

@injectable()
class HandlebarsUtils {

    public static typeName = "HandlebarsUtils"

    public static render(template: string, tags: object): string {
        const generator = Handlebars.compile(template, { strict: true })
        return generator(tags)
    }

    public static compile(template: string, tags: any): string {
        // register helpers
        Object.entries(mathHelpers).forEach(([name, fn]) => {
            Handlebars.registerHelper(name, fn)
        })
        Object.entries(stringHelpers).forEach(([name, fn]) => {
            Handlebars.registerHelper(name, fn)
        })
        Object.entries(comparisonHelper).forEach(([name, fn]) => {
            Handlebars.registerHelper(name, fn)
        })
        Object.entries(accessHelper).forEach(([name, fn]) => {
            Handlebars.registerHelper(name, fn);
        })

        let templateDelegate = Handlebars.compile(template, { strict: true })
        return templateDelegate(tags)
    }
}

export default HandlebarsUtils