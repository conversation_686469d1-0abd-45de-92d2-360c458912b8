import {Country} from "../../iris-common"
import {Moment} from "moment-timezone"
import Constants from "../constants"

export class TimezoneUtils {

    static getCountryString(country: Country): string {
        switch (country) {
            case "INDIA": {
                return "Asia/Kolkata"
            }
            case "DUBAI": {
                return "Asia/Dubai"
            }
            case "USA-CANADA": {
                return "America/Denver"
            }
            default: {
                return "Asia/Kolkata"
            }
        }
    }

    static getCountry(appId: string): Country {
        if (!appId) return "INDIA"
        switch (appId.toLowerCase()) {
            case Constants.APP_ID_CUREFIT: {
                return "INDIA"
            }
            case Constants.APP_ID_LIVEFIT: {
                return "USA-CANADA"
            }
            default: {
                return "INDIA"
            }
        }
    }

    static adjustDNDBoundaries(now: Moment, dndWindowStart: Moment, dndWindowEnd: Moment)
        : { dndWindowStart: Moment, dndWindowEnd: Moment } {
        // Fix boundaries if start > end
        if (dndWindowStart.isAfter(dndWindowEnd)) {
            if (dndWindowEnd.isAfter(now)) {
                dndWindowStart.add(-1, "day")
            } else if (dndWindowStart.isBefore(now)) {
                dndWindowEnd.add(1, "day")
            }
        }
        return { dndWindowStart, dndWindowEnd }
    }

    static isAppropriateTime(now: Moment, dndWindowStart: Moment, dndWindowEnd: Moment): boolean {
        return now.isBefore(dndWindowStart) || now.isAfter(dndWindowEnd)
    }

}
