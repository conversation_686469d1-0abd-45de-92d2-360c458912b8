import {BASE_TYPES, ILogger} from "@curefit/base"

import {Counter, Histogram} from "prom-client"
const fetch = require("node-fetch")
import {inject, injectable} from "inversify"
import * as _ from "lodash"

import {
    BaseCreative,
    CampaignType,
    CommunicationCampaign,
    CreativeType,
    Notification
} from "../../iris-common"
import Constants from "../constants"
import {CommonUtils} from "./CommonUtils"

export enum ProfilerType {
    PERF = "PERF",
    EXTERNAL_API = "EXTERNAL_API"
}

@injectable()
export class MetricUtils {

    private readonly prometheusEndpoint: string
    // TODO: Remove environment usage in metrics
    private readonly prometheusMetricKeyPrefix: string = process.env.ENVIRONMENT.toUpperCase()

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger
    ) {

        if (CommonUtils.isEnvironmentProduction()) {
            this.prometheusEndpoint = "https://monitoring.curefit.co/prometheus/api/v1/query?query="
        } else if (CommonUtils.isEnvironmentAlpha()) {
            this.prometheusEndpoint = "https://monitoring.alpha.curefit.co/prometheus/api/v1/query?query="
        } else if (CommonUtils.isEnvironmentStage()) {
            this.prometheusEndpoint = "https://monitoring.stage.curefit.co/prometheus/api/v1/query?query="
        }
    }

    private getMetricsKey(eventKey: string): string {
        return `${this.prometheusMetricKeyPrefix}_${Constants.IRIS}_${eventKey}`
    }

    /*
        Prometheus Metrics
    */

    private notificationScheduledCounter = new Counter({
        name: this.getMetricsKey("notification_scheduled"),
        help: "Notification scheduled Count",
        labelNames: ["notificationId"]
    })

    private failReasonCounter = new Counter({
        name: this.getMetricsKey("send_campaign_fail_reason"),
        help: "Send campaign failure reasons",
        labelNames: ["campaignType", "creativeType", "campaignId", "creativeId", "failReason"]
    })

    private callbackErrorCounter = new Counter({
        name: this.getMetricsKey("callback_errors"),
        help: "notification status",
        labelNames: ["creativeType", "campaignId", "creativeId", "failReason", "serviceProvider"]
    })

    private notificationStatusCounter = new Counter({
        name: this.getMetricsKey("notification_status"),
        help: "notification status",
        labelNames: ["campaignId", "creativeId", "status", "source"]
    })

    private apiCallCounter = new Counter({
        name: this.getMetricsKey("api_call"),
        help: "Number of api calls to iris",
        labelNames: ["method", "path"]
    })

    private campaignCreativeDNDUsageCounter = new Counter({
        name: this.getMetricsKey("dnd_creative_usage"),
        help: "Campaign, Creatives used sent during DND",
        labelNames: ["campaign", "creative"]
    })

    private appEventTypeCounter = new Counter({
        name: this.getMetricsKey("app_events"),
        help: "App events",
        labelNames: ["type"]
    })

    private notificationProcessLatencyHistogram = new Histogram({
        name: this.getMetricsKey("notification_process_latency"),
        help: "Notification Process Latency",
        buckets: [0.1, 0.2, 0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 4.0, 5.0, 7.5, 10.0, 15.0, 20.0, 25.0, 50.0, 100.0, 150.0,
            200.0, 250.0, 300.0, 350.0, 400.0, 450.0, 500.0, 550.0, 600.0, 650.0, 700.0, 750.0, 800.0, 900.0, 1000.0],
        labelNames: ["campaignType", "creativeType", "campaignId", "creativeId"]
    })

    private externalAPILatencyHistogram = new Histogram({
        name: this.getMetricsKey("external_api_latency"),
        help: "External API Latency",
        buckets: [0.1, 0.2, 0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 4.0, 5.0, 7.5, 10.0, 15.0, 20.0, 25.0, 50.0, 100.0, 150.0,
            200.0, 250.0, 300.0, 350.0, 400.0, 450.0, 500.0, 550.0, 600.0, 650.0, 700.0, 750.0, 800.0, 900.0, 1000.0],
        labelNames: ["api"]
    })

    private profilerHistogram = new Histogram({
        name: this.getMetricsKey("profiler"),
        help: "Profiler",
        buckets: [0.005, 0.01, 0.02, 0.05, 0.01, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 2.0, 2.5, 3.0, 4.0, 5.0,
            7.5, 10.0, 15.0, 20.0, 25.0, 50.0, 100.0, 150.0, 200.0, 250.0, 300.0, 350.0, 400.0, 500.0],
        labelNames: ["method"]
    })

    private callbackLatencyHistogram = new Histogram({
        name: this.getMetricsKey("callback_latency"),
        help: "Callback Latency",
        buckets: [0.1, 0.2, 0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 4.0, 5.0, 7.5, 10.0, 15.0, 20.0, 25.0, 50.0, 100.0, 150.0,
            200.0, 250.0, 300.0, 350.0, 400.0, 450.0, 500.0, 550.0, 600.0, 650.0, 700.0, 750.0, 800.0, 900.0, 1000.0],
        labelNames: ["channel"]
    })

    private queuePushTimeHistogram = new Histogram({
        name: this.getMetricsKey("queue_push_time"),
        help: "Time taken to push to SQS",
        buckets: [0.005, 0.01, 0.02, 0.05, 0.01, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 2.0, 2.5, 3.0, 4.0, 5.0,
            7.5, 10.0, 15.0, 20.0, 25.0, 50.0, 100.0, 150.0, 200.0, 250.0, 300.0, 350.0, 400.0, 500.0],
        labelNames: ["queueName"]
    })

    private callTriggerLatencyHistogram = new Histogram({
        name: this.getMetricsKey("call_trigger_latency"),
        help: "Call latencies",
        buckets: [1.0, 2.0, 3.0, 4.0, 5.0, 7.5, 10.0, 15.0, 20.0, 25.0, 30.0, 35.0, 40.0, 45.0, 50.0,
            100.0, 150.0, 200.0, 250.0, 300.0, 350.0, 400.0, 500.0],
        labelNames: ["provider"]
    })

    /*
        Methods for logging metrics into prometheus
    */

    public incrementFailReasonCounter(campaignType: CampaignType, creativeType: CreativeType, campaignId: string,
                                      creativeId: string, failReason: string) {
        try {
            this.failReasonCounter.inc({ campaignType, creativeType, campaignId, creativeId, failReason })
        } catch (err) {
            this.logger.error(`MetricUtils::incrementFailReasonCounter Failure while incrementing: ${err.toString()}`)
        }
    }

    public incrementNotificationScheduledCounter(notificationId: string) {
        try {
            this.notificationScheduledCounter.inc({ notificationId })
        } catch (err) {
            this.logger.error(`MetricUtils::incrementNotificationScheduledCounter Failure while incrementing: ${err.toString()}`)
        }
    }

    public incrementCallbackFailReasonCounter(creativeType: CreativeType, campaignId: string,
                                              creativeId: string, failReason: string, serviceProvider: string) {
        try {
            this.callbackErrorCounter.inc({ creativeType, campaignId, creativeId, failReason, serviceProvider })
        } catch (err) {
            this.logger.error(`MetricUtils::incrementCallbackFailReasonCounter Failure while incrementing: ${err.toString()}`)
        }
    }

    public incrementNotificationStatusCounter(campaignId: string, creativeId: string, status: string, source: string) {
        try {
            this.notificationStatusCounter.inc({ campaignId, creativeId, status, source })
        } catch (err) {
            this.logger.error(`MetricUtils::incrementNotificationStatusCounter Failure while incrementing: ${err.toString()}`)
        }
    }

    public incrementApiCallCounter(methodLabel: string, pathLabel: string) {
        try {
            this.apiCallCounter.inc({method: methodLabel, path: pathLabel})
        } catch (err) {
            this.logger.error(`MetricUtils::incrementApiCallCounter Failure while incrementing: ${err.toString()}`)
        }
    }

    public incrementCampaignCreativeDNDUsageCounter(campaignLabel: string, creativeLabel?: string, value?: number) {
        try {
            const valueCount: number = value != null ? value : 1
            this.campaignCreativeDNDUsageCounter.inc({ campaign: campaignLabel, creative: creativeLabel }, valueCount)
        } catch (err) {
            this.logger.error(`MetricUtils::incrementDNDCreativeUsageCounter Failure while incrementing: ${err.toString()}`)
        }
    }

    public incrementAppEventTypeCounter(type: string) {
        try {
            this.appEventTypeCounter.inc({ type })
        } catch (err) {
            this.logger.error(`MetricUtils::incrementAppEventTypeCounter Failure while incrementing: ${err.toString()}`)
        }
    }

    public reportProfilerTime(method: string, timeInMilliseconds: number) {
        try {
            this.profilerHistogram.labels(method).observe(timeInMilliseconds / 1000)
        } catch (e) {
            this.logger.error("MetricUtils::reportProfilerTime Error while reporting", e)
        }
    }

    public reportNotificationProcessLatency(campaignType: CampaignType, creativeType: CreativeType, campaignId: string,
                                            creativeId: string, latencyInMilliseconds: number) {
        try {
            this.notificationProcessLatencyHistogram.labels(campaignType, creativeType, campaignId, creativeId)
                .observe(latencyInMilliseconds / 1000)
        } catch (e) {
            this.logger.error(`MetricUtils::reportNotificationProcessLatency Error while reporting ${e.toString()}`)
        }
    }

    public reportCallbackLatency(channel: string, latencyInMilliseconds: number) {
        try {
            this.callbackLatencyHistogram.labels(channel).observe(latencyInMilliseconds / 1000)
        } catch (e) {
            this.logger.error(`MetricUtils::reportCallbackLatency Error while reporting ${e.toString()}`)
        }
    }

    public reportExternalAPILatency(api: string, latencyInMilliseconds: number) {
        try {
            this.externalAPILatencyHistogram.labels(api).observe(latencyInMilliseconds / 1000)
        } catch (e) {
            this.logger.error(`MetricUtils::reportExternalAPILatency Error while reporting ${e.toString()}`)
        }
    }

    public profileNotification(notification: Notification, campaign: CommunicationCampaign, creative: BaseCreative) {
        // Only use terminal state notifications for metrics
        if (notification.status !== "SCHEDULED") {

            const failReason =
                notification.failReason && notification.failReason !== "" ? notification.failReason : "none"

            // "none" fail reason implies success
            this.incrementFailReasonCounter(campaign.type,
                creative.type, campaign.campaignId,
                creative.creativeId, failReason)

            // If was scheduled, use that time. Else createdAt
            const scheduledAt = notification.scheduledAt || notification.createdAt
            // If no sentAt, use current time (failure case)
            const sentAt = notification.sentAt || new Date()

            this.reportNotificationProcessLatency(campaign.type, creative.type, campaign.campaignId,
                creative.creativeId, (sentAt.getTime() - scheduledAt.getTime()))
        }
    }

    public startProfilingTimer(profilerType: ProfilerType, method: string, weight?: number): any {
        return { profilerType, method, startTime: new Date().getTime(), weight }
    }

    public endProfilingTimer(context: { profilerType: ProfilerType, method: string, startTime: number, weight?: number}) {
        const weight = context.weight || 1

        switch (context.profilerType) {
            case ProfilerType.EXTERNAL_API: {
                this.reportExternalAPILatency(context.method, (new Date().getTime() - context.startTime) / weight)
                break
            }
            case ProfilerType.PERF: {
                this.reportProfilerTime(context.method, (new Date().getTime() - context.startTime) / weight)
                break
            }
            default: {
                this.logger.error(`MetricUtils::endProfilingTimer invalid profiler type ${JSON.stringify(context)}`)
            }
        }
    }

    public reportQueuePushTime(queueName: string, timeInMilliseconds: number) {
        try {
            this.queuePushTimeHistogram.labels(queueName).observe(timeInMilliseconds / 1000)
        } catch (e) {
            this.logger.error("MetricUtils::reportQueuePushTimeHistogram Error while reporting", e)
        }
    }

    public reportCallTriggerLatency(provider: string, timeInMilliseconds: number) {
        try {
            this.callTriggerLatencyHistogram.labels(provider).observe(timeInMilliseconds / 1000)
        } catch (e) {
            this.logger.error("MetricUtils::reportCallTriggerLatency Error while reporting", e)
        }
    }

    /*
        Methods for querying metrics from promtheus
     */

    public async getStatusMetrics(creativeId: string, campaignId: string,
                                  timeRange: string): Promise<{ deliveredCount: number, readCount: number }> {
        const statusMetricQuery = this.prometheusEndpoint + encodeURIComponent(
            `(sum(increase(${this.prometheusMetricKeyPrefix}_iris_notification_status{` +
            (campaignId ? `campaignId="${campaignId}",` : ``) +
            (creativeId ? `creativeId="${creativeId}",` : ``) +
            `source=~"IRIS_EVENTS|SES"` +
            `}[${timeRange}])) by (status))`)

        const statusMetricQueryResponse = await fetch(statusMetricQuery)
        const { data: { result: statusMetricQueryResult } } = await statusMetricQueryResponse.json()

        let deliveredCount = 0, readCount = 0
        for (const metric of statusMetricQueryResult) {
            const status = metric.metric.status
            const value = Math.round(parseFloat(metric.value[1]))
            if (status === "DELIVERED") {
                deliveredCount = value
            } else if (status === "READ") {
                readCount = value
            }
        }

        return { deliveredCount, readCount }
    }

    public async getFailureMetrics(creativeId: string, campaignId: string,
                                   timeRange: string): Promise<{ sentCount: number, failCount: number, failureMetrics: any }> {
        const failureMetricQuery = this.prometheusEndpoint + encodeURIComponent(
            `(sum(increase(${this.prometheusMetricKeyPrefix}_iris_send_campaign_fail_reason{` +
            (campaignId ? `campaignId="${campaignId}",` : ``) +
            (creativeId ? `creativeId="${creativeId}",` : ``) +
            `}[${timeRange}])) by (failReason))`)

        const failureMetricQueryResponse = await fetch(failureMetricQuery)
        const { data: { result: failureMetricQueryResult } } = await failureMetricQueryResponse.json()

        let failureMetrics = []
        let sentCount = 0, failCount = 0
        for (const metric of failureMetricQueryResult) {
            const label = metric.metric.failReason
            const value = Math.round(parseFloat(metric.value[1]))
            failureMetrics.push({ label, value})

            if (label === "none") {
                sentCount = value
            } else {
                failCount += value
            }
        }
        failureMetrics = failureMetrics.filter((metric) => { return metric.value > 0})
        failureMetrics.sort((metric1, metric2) => {
            return metric2.value - metric1.value })

        return { sentCount, failCount, failureMetrics }
    }

    public async getFailureCountForEachCampaignAndCreative(timeRange: string): Promise<Map<string, number>> {
        const failureCountMetricQuery = this.prometheusEndpoint + encodeURIComponent(
            `sum(increase(${this.prometheusMetricKeyPrefix}_iris_send_campaign_fail_reason` +
            `{failReason != "none"}` +
            `[${timeRange}])) by (campaignId, creativeId)`)

        const failureCountMetricQueryResponse = await fetch(failureCountMetricQuery)
        const { data: { result: campaignFailureCountMetricQueryResult } } =
            await failureCountMetricQueryResponse.json()

        const failureCountMap = new Map<string, number>()
        _.forEach(campaignFailureCountMetricQueryResult, (failureCount) => {
            let { metric: { campaignId, creativeId }, value: [ _, count ] } = failureCount
            count = Math.round(count)
            if (count > 0) {
                failureCountMap.set([campaignId, creativeId].join("$$"), Math.round(count))
            }
        })

        return failureCountMap
    }

    public async getProcessCountForEachCampaignAndCreative(timeRange: string): Promise<Map<string, number>> {
        const processCountMetricQuery = this.prometheusEndpoint + encodeURIComponent(
            `sum(increase(${this.prometheusMetricKeyPrefix}_iris_send_campaign_fail_reason` +
            `[${timeRange}])) by (campaignId, creativeId)`)

        const processCountMetricQueryResponse = await fetch(processCountMetricQuery)
        const { data: { result: campaignProcessCountMetricQueryResult } } =
            await processCountMetricQueryResponse.json()

        const processCountMap = new Map<string, number>()
        _.forEach(campaignProcessCountMetricQueryResult, (processCount) => {
            let { metric: { campaignId, creativeId },  value: [ _, count ] } = processCount
            count = Math.round(count)
            if (count > 0) {
                processCountMap.set([campaignId, creativeId].join("$$"), Math.round(count))
            }
        })

        return processCountMap
    }



}
