import * as _ from "lodash"
import {inject, injectable} from "inversify"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {PhoneNumberUtils} from "./PhoneNumberUtils"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
@injectable()
export class UserUtils {


    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
    ) {}

    /*
    * fetches user identities from userCache
     */
    public async getUserIdentities(userIds: string[]) {
        const response = await this.userService.getUsersCached(userIds)
        this.logger.debug("userCache response:" + JSON.stringify(response))
        if (_.isEmpty(response)) return userIds
        const userIdentities: string[] = []
        userIdentities.push(...userIds)
        for (let user of response) {
            if (user.email) userIdentities.push(user.email)
            if (user.phone) {
                const numbers = PhoneNumberUtils.getPossibleNumbers(user.phone, user.countryCallingCode)
                userIdentities.push(...numbers)
            }
        }
        return userIdentities
    }

}