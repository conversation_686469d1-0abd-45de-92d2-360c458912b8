import {inject, injectable} from "inversify"
import * as AWS from "aws-sdk"
import {BASE_TYPES, ILogger} from "@curefit/base"
import {MessageAttributeMap} from "aws-sdk/clients/sns"
import Constants from "../constants"
import {snsTopic} from "../enums"
import {CommonUtils} from "./CommonUtils"
import {EventData, EVENTS_TYPES, EventsService} from "@curefit/events-util"

@injectable()
export class AWSUtils {
    private SNS: AWS.SNS
    private arnPrefix: string = Constants.AWS_CONFIG.ARN_PREFIX_ONE
    private s3: AWS.S3
    private SQS: AWS.SQS

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(EVENTS_TYPES.EventsService) private eventsService: EventsService
    ) {
        this.SNS = new AWS.SNS({
            region: "ap-south-1"
        })
        this.s3 = new AWS.S3({
            region: "ap-south-1"
        })
        this.SQS = new AWS.SQS({
            region: "ap-south-1"
        })
    }

    private static getSNSARN(arnPrefix: string, topicName: string) {
        return arnPrefix + topicName
    }

    public static getAwsSNSTopicName(topic: snsTopic, isAlphaIsolated: boolean): string {
        /*
            isAlphaIsolated is true if there exists a separate topic for alpha
            if false, we publish to production topic
         */
        if (CommonUtils.isEnvironmentAlpha() && isAlphaIsolated) {
            return `alpha-${topic}`
        } else if (CommonUtils.isEnvironmentProductionOrAlpha()) {
            return `production-${topic}`
        } else if (CommonUtils.isEnvironmentStage()) {
            return `stage-${topic}`
        } else {
            return `dev-${topic}`
        }
    }

    public async getSignedUrlForGetObject(bucket: string, key: string): Promise<string> {
        return this.s3.getSignedUrlPromise("getObject", {
            Bucket: bucket,
            Key: key
        })
    }
}
