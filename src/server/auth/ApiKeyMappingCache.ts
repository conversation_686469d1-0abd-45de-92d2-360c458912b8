import {<PERSON><PERSON><PERSON><PERSON>} from "@curefit/access-common"
import {inject, injectable} from "inversify"
import {BASE_TYPES, ILogger} from "@curefit/base"
import TYPES from "../ioc/types"
import {IApiKeyMappingReadonlyDao} from "../models/ApiKey"

const memoize = require("memoizee")


@injectable()
export class ApiKeyMappingCache {
    //120 seconds of clearing put
    public fetchKeyMapping = memoize(this.loadData, {promise: true, maxAge: 120000})
    constructor(@inject(BASE_TYPES.ILogger) protected logger: ILogger,
                @inject(BASE_TYPES.AppName) private appName: string,
                @inject(TYPES.ApiKeyMappingReadonlyDao) private apiKeyMappingReadonlyDao: IApiKeyMappingReadonlyDao) {
    }

    async loadData(): Promise<ApiKey[]> {
        const apiKeyMapping = await this.apiKeyMappingReadonlyDao.findOne({serviceName: this.appName})
        if (!apiKeyMapping) {
            this.logger.error("No API Keys found for " + this.appName + " . Please add api keys through doorman")
            return []
        }
        this.logger.info("CALL MADE MEMOIZE")
        return apiKeyMapping.keys
    }

    public async isAuthorized(req: any): Promise<boolean> {
        const apiKey = req && req.header("apiKey")
        if (!apiKey) return false
        const clientName = req.header("clientName")
        const apiKeyMappings = await this.fetchKeyMapping()
        if (apiKeyMappings) {
            for (const apiKeyMapping of apiKeyMappings) {
                if (apiKeyMapping.clientName === clientName && apiKeyMapping.apiKeyHash === apiKey) return true
            }
        }
        return false
    }
}