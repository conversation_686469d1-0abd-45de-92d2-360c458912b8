{"name": "@curefit/iris-models", "version": "3.5.0", "description": "models for iris", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/curefit/iris-models.git"}, "license": "ISC", "bugs": {"url": "https://github.com/curefit/iris-models/issues"}, "homepage": "https://github.com/curefit/iris-models#readme", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "generate-barrels": "rm -rf dist; barrelsby --delete -e node_modules/*", "build": "tsc", "tslint": "tslint --project tsconfig.json", "tslint-fix": "tslint --project tsconfig.json --fix", "prepublishOnly": "tsc", "postversion": "git push origin master && git push origin master --tags && npm publish"}, "devDependencies": {"@types/bookshelf": "0.9.9", "@types/knex": "0.16.1", "@types/mongodb": "^3.1.23", "@types/node": "^12.0.2", "barrelsby": "^1.0.2", "tslint": "5.16.0", "typescript": "^4.7.2"}, "dependencies": {"@curefit/base": "6.13.2", "@curefit/base-common": "^2.42.0", "@curefit/base-mongo": "1.3.5", "@curefit/error-client": "^1.1.1", "@curefit/iris-common": "1.79.0", "@curefit/mongo-utils": "4.5.0", "@curefit/mysql-utils": "^1.2.0", "@curefit/offer-common": "^2.5.0", "@curefit/payment-common": "^1.8.0", "@curefit/product-common": "5.10.3", "bookshelf": "0.12.1", "knex": "0.16.5", "memoizee": "^0.4.14"}, "resolutions": {"@curefit/base": "6.13.2", "@curefit/mongo-utils": "4.5.0", "@curefit/iris-common": "1.79.0", "knex": "0.16.5", "inversify": "6.0.1", "inversify-express-utils": "6.4.3", "inversify-logger-middleware": "^3.1.0", "bookshelf": "0.12.1", "@types/knex": "0.16.1", "@types/bookshelf": "0.9.9", "@curefit/user-common": "2.3.0", "@curefit/diy-common": "4.42.0", "@curefit/eat-common": "^7.48.0", "@curefit/aztec-models": "1.3.0", "@curefit/order-common": "5.40.1", "@curefit/care-common": "2.34.0", "@curefit/product-common": "5.10.3", "@curefit/apps-common": "^2.47.0", "class-validator": "0.14.0", "mongoose": "6.13.8"}, "keywords": ["iris", "mongo", "models"], "author": "<PERSON><PERSON> <<EMAIL>>"}