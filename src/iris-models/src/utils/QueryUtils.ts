import { BASE_TYPES, Logger } from "@curefit/base"

import { inject, injectable } from "inversify"
import { QueryBuilder } from "knex"

@injectable()
export class QueryUtils {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger
    ) {}

    async getTotalCount(query: QueryBuilder): Promise<number> {
        query.count({ count: "*" })
        const rowCount = await query
        return rowCount[0].count
    }
}