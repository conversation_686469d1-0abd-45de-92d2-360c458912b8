import { inject, injectable } from "inversify"
import * as _ from "lodash"
import { BASE_TYPES, Logger } from "@curefit/base"

interface ICheckFields {
    collection: any,
    fields: string[],
    isThrowError?: boolean,
    errorMsg?: string,
    logMsg?: string
}

@injectable()
export class CommonUtils {
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger
    ) {}

    public checkFields({ collection, fields, isThrowError = true, errorMsg, logMsg }: ICheckFields) {
        const paramKeys = _.keys(collection)
        const notFoundFields = _.difference(fields, _.intersection(paramKeys, fields))

        _.each(notFoundFields, (field) => {
            logMsg = (logMsg && logMsg.replace("{field}", field)) || `${field} not found!`
            this.logger.error(logMsg)
        })

        if (!_.isEmpty(notFoundFields) && isThrowError) {
            errorMsg = errorMsg || "Invalid params found!"
            throw new Error(errorMsg)
        }

        return _.isEmpty(notFoundFields) ? { status: "OK", notFoundFields } : { status: "ERROR", notFoundFields }
    }
}