export const IRIS_MODELS_TYPES = {

    CreativeReadonlyDao: "iris-models:CreativeReadonlyDao",
    CreativeReadWriteDao: "iris-models:CreativeReadWriteDao",
    CreativeSchema: "iris-models:CreativeSchema",

    NotificationReadonlyDao: "iris-models:NotificationReadonlyDao",
    NotificationReadWriteDao: "iris-models:NotificationReadWriteDao",
    NotificationSchema: "iris-models:NotificationSchema",
    BasicNotificationDao: "iris-models:BasicNotificationDao",
    BasicNotificationMetaDao: "iris-models:BasicNotificationMetaDao",

    InAppNotificationReadOnlyDao: "iris-models:InAppNotificationReadOnlyDao",
    InAppNotificationReadWriteDao: "iris-models:InAppNotificationReadWriteDao",
    InAppNotificationSchema: "iris-models:InAppNotificationSchema",

    NotificationTaskReadonlyDao: "iris-models:NotificationTaskReadonlyDao",
    NotificationTaskReadWriteDao: "iris-models:NotificationTaskReadWriteDao",
    NotificationTaskSchema: "iris-models:NotificationTaskSchema",
    SimpleNotificationTaskSchema: "iris-models:SimpleNotificationTaskSchema",

    NotificationSegmentTaskReadonlyDao: "iris-models:NotificationSegmentTaskReadonlyDao",
    NotificationSegmentTaskReadWriteDao: "iris-models:NotificationSegmentTaskReadWriteDao",
    NotificationSegmentTaskSchema: "iris-models:NotificationSegmentTaskSchema",

    CampaignReadWriteDao: "iris-models:CampaignReadWriteDao",
    CampaignReadOnlyDao: "iris-models:CampaignReadOnlyDao",
    CampaignSchema: "iris-models:CampaignSchema",

    CommunicationCampaignReadWriteDao: "iris-models:CommunicationCampaignReadWriteDao",
    CommunicationCampaignReadonlyDao: "iris-models:CommunicationCampaignReadonlyDao",
    CommunicationCampaignSchema: "iris-models:CommunicationCampaignSchema",

    NotificationReportReadonlyDao: "iris-models:NotificationReportReadonlyDao",
    NotificationReportReadWriteDao: "iris-models:NotificationReportReadWriteDao",
    NotificationReportSchema: "iris-models:NotificationReportSchema",
    NotificationAttemptDao: "iris-models:NotificationAttemptDao",

    TemplateReadonlyDao: "iris-models:TemplateReadonlyDao",
    TemplateReadWriteDao: "iris-models:TemplateReadWriteDao",
    TemplateSchema: "iris-models:TemplateSchema",

    BasicNotificationReadOnlyDao: "iris-models:BasicNotificationReadOnlyDao",
    CommonUtils: "iris-models:CommonUtils",
    QueryUtils: "iris-models:QueryUtils",

    FirebaseReportDao: "iris-models:FirebaseReportDao",

    CommValidationsDao: "iris-models:CommValidationsDao"

}
