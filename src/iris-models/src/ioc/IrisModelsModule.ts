import * as Inversify from "inversify"
import { ContainerModule } from "inversify"
import { INotificationTaskReadonlyDao, INotificationTaskReadWriteDao } from "../daos/INotificationTaskDaos"
import { NotificationTaskSchema } from "../models/notifications/NotificationTaskSchema"
import { BasicNotificationDaoMysqlImpl } from "../daos/BasicNotificationDaoMysqlImpl"
import { NotificationReadonlyDaoMongoImpl } from "../daos/NotificationReadonlyDaoMongoImpl"
import { ICreativeReadonlyDao, ICreativeReadWriteDao } from "../daos/ICreativeDao"
import { BaseCreativeSchema } from "../models/creatives/BaseCreativeSchema"
import { IRIS_MODELS_TYPES } from "./IrisModelsTypes"
import {
    NotificationTaskReadonlyDaoMongoImpl,
    NotificationTaskReadWriteDaoMongoImpl
} from "../daos/NotificationTaskDaos"
import {
    IBasicNotificatioMetaDao,
    IBasicNotificationDao, IBasicNotificationReadOnlyDao,
    INotificationReadonlyDao,
    INotificationReadWriteDao
} from "../daos/INotificationDao"
import { CreativeReadonlyDaoMongoImpl } from "../daos/CreativeReadonlyDaoMongoImpl"
import { CreativeReadWriteDaoMongoImpl } from "../daos/CreativeReadWriteDaoMongoImpl"
import { NotificationSchema } from "../models/notifications/NotificationSchema"
import { NotificationReadWriteDaoMongoImpl } from "../daos/NotificationReadWriteDaoMongoImpl"
import { BasicNotificationMetaDaoMysqlImpl } from "../daos/BasicNotificationMetaDaoMysqlImpl"
import { CommunicationCampaignSchema } from "../models/campaigns/CommunicationCampaignSchema"
import { CampaignSchema } from "../models/campaign/CampaignSchema"
import {
    ICommunicationCampaignReadonlyDao,
    ICommunicationCampaignReadWriteDao
} from "../daos/ICommunicationCampaignDao"
import { CommunicationCampaignReadonlyDaoMongoImpl } from "../daos/CommunicationCampaignReadonlyDaoMongoImpl"
import { ICampaignReadonlyDao, ICampaignReadWriteDao } from "../daos/ICampaignDao"
import { CampaignReadonlyDaoMongoImpl } from "../daos/CampaignReadonlyDaoMongoImpl"
import { CampaignReadWriteDaoMongoImpl } from "../daos/CampaignReadWriteDaoMongoImpl"
import { CommunicationCampaignReadWriteDaoMongoImpl } from "../daos/CommunicationCampaignReadWriteDaoMongoImpl"
import { NotificationReportSchema } from "../models/notifications/NotificationReportSchema"
import { INotificationReportReadonlyDao, INotificationReportReadWriteDao } from "../daos/INotificationReportDao"
import { NotificationReportReadonlyDaoMongoImpl } from "../daos/NotificationReportReadonlyDaoMongoImpl"
import { NotificationReportReadWriteDaoMongoImpl } from "../daos/NotificationReportReadWriteDaoMongoImpl"
import { INotificationAttemptDao } from "../daos/INotificationAttemptDao"
import { NotificationAttemptDaoMysqlImpl } from "../daos/NotificationAttemptDaoMysqlImpl"
import { TemplateSchema } from "../models/attachment/TemplateSchema"
import { ITemplateReadonlyDao, ITemplateReadWriteDao } from "../daos/ITemplateDao"
import { TemplateReadonlyDaoMongoImpl } from "../daos/TemplateReadonlyDaoMongoImpl"
import { TemplateReadWriteDaoMongoImpl } from "../daos/TemplateReadWriteDaoMongoImpl"
import { BasicNotificationReadonlyDaoMysqlImpl } from "../daos/BasicNotificationReadonlyDaoMysqlImpl"
import { CommonUtils } from "../utils/CommonUtils"
import { QueryUtils } from "../utils/QueryUtils"
import { NotificationSegmentTaskSchema } from "../models/notifications/NotificationSegmentTaskSchema"
import {
    NotificationSegmentTaskReadonlyDaoMongoImpl,
    NotificationSegmentTaskReadWriteDaoMongoImpl
} from "../daos/NotificationSegmentTaskDaos"
import {
    INotificationSegmentTaskReadonlyDao,
    INotificationSegmentTaskReadWriteDao
} from "../daos/INotificationSegmentTaskDaos"
import { FirebaseReportDaoMySqlImpl } from "../daos/FirebaseReportDaoMysqlImpl"
import { IFirebaseReportDao } from "../daos/IFirebaseReportDao"
import { ICommValidationsDao } from "../daos/ICommValidationsDao"
import { CommValidationsDaoMySqlImpl } from "../daos/CommValidationsDaoMySqlImpl"

export function IrisModelsModule(kernel: Inversify.Container) {
    return new ContainerModule((bind: Inversify.interfaces.Bind) => {

        bind<BaseCreativeSchema>(IRIS_MODELS_TYPES.CreativeSchema).to(BaseCreativeSchema).inSingletonScope()
        bind<ICreativeReadonlyDao>(IRIS_MODELS_TYPES.CreativeReadonlyDao).to(CreativeReadonlyDaoMongoImpl).inSingletonScope()
        bind<ICreativeReadWriteDao>(IRIS_MODELS_TYPES.CreativeReadWriteDao).to(CreativeReadWriteDaoMongoImpl).inSingletonScope()

        bind<NotificationSchema>(IRIS_MODELS_TYPES.NotificationSchema).to(NotificationSchema).inSingletonScope()
        bind<INotificationReadonlyDao>(IRIS_MODELS_TYPES.NotificationReadonlyDao).to(NotificationReadonlyDaoMongoImpl).inSingletonScope()
        bind<INotificationReadWriteDao>(IRIS_MODELS_TYPES.NotificationReadWriteDao).to(NotificationReadWriteDaoMongoImpl).inSingletonScope()
        bind<IBasicNotificationDao>(IRIS_MODELS_TYPES.BasicNotificationDao).to(BasicNotificationDaoMysqlImpl).inSingletonScope()
        bind<IBasicNotificatioMetaDao>(IRIS_MODELS_TYPES.BasicNotificationMetaDao).to(BasicNotificationMetaDaoMysqlImpl).inSingletonScope()

        bind<NotificationTaskSchema>(IRIS_MODELS_TYPES.NotificationTaskSchema).to(NotificationTaskSchema).inSingletonScope()
        bind<INotificationTaskReadonlyDao>(IRIS_MODELS_TYPES.NotificationTaskReadonlyDao).to(NotificationTaskReadonlyDaoMongoImpl).inSingletonScope()
        bind<INotificationTaskReadWriteDao>(IRIS_MODELS_TYPES.NotificationTaskReadWriteDao).to(NotificationTaskReadWriteDaoMongoImpl).inSingletonScope()
        bind<INotificationAttemptDao>(IRIS_MODELS_TYPES.NotificationAttemptDao).to(NotificationAttemptDaoMysqlImpl).inSingletonScope()

        bind<NotificationSegmentTaskSchema>(IRIS_MODELS_TYPES.NotificationSegmentTaskSchema).to(NotificationSegmentTaskSchema).inSingletonScope()
        bind<INotificationSegmentTaskReadonlyDao>(IRIS_MODELS_TYPES.NotificationSegmentTaskReadonlyDao).to(NotificationSegmentTaskReadonlyDaoMongoImpl)
        bind<INotificationSegmentTaskReadWriteDao>(IRIS_MODELS_TYPES.NotificationSegmentTaskReadWriteDao).to(NotificationSegmentTaskReadWriteDaoMongoImpl)

        bind<NotificationReportSchema>(IRIS_MODELS_TYPES.NotificationReportSchema).to(NotificationReportSchema).inSingletonScope()
        bind<INotificationReportReadonlyDao>(IRIS_MODELS_TYPES.NotificationReportReadonlyDao).to(NotificationReportReadonlyDaoMongoImpl).inSingletonScope()
        bind<INotificationReportReadWriteDao>(IRIS_MODELS_TYPES.NotificationReportReadWriteDao).to(NotificationReportReadWriteDaoMongoImpl).inSingletonScope()


        bind<CampaignSchema>(IRIS_MODELS_TYPES.CampaignSchema).to(CampaignSchema).inSingletonScope()
        bind<ICampaignReadonlyDao>(IRIS_MODELS_TYPES.CampaignReadOnlyDao).to(CampaignReadonlyDaoMongoImpl).inSingletonScope()
        bind<ICampaignReadWriteDao>(IRIS_MODELS_TYPES.CampaignReadWriteDao).to(CampaignReadWriteDaoMongoImpl).inSingletonScope()

        bind<TemplateSchema>(IRIS_MODELS_TYPES.TemplateSchema).to(TemplateSchema).inSingletonScope()
        bind<ITemplateReadonlyDao>(IRIS_MODELS_TYPES.TemplateReadonlyDao).to(TemplateReadonlyDaoMongoImpl).inSingletonScope()
        bind<ITemplateReadWriteDao>(IRIS_MODELS_TYPES.TemplateReadWriteDao).to(TemplateReadWriteDaoMongoImpl).inSingletonScope()

        bind<IBasicNotificationReadOnlyDao>(IRIS_MODELS_TYPES.BasicNotificationReadOnlyDao).to(BasicNotificationReadonlyDaoMysqlImpl).inSingletonScope()
        bind<CommonUtils>(IRIS_MODELS_TYPES.CommonUtils).to(CommonUtils).inSingletonScope()
        bind<QueryUtils>(IRIS_MODELS_TYPES.QueryUtils).to(QueryUtils).inSingletonScope()


        bind<CommunicationCampaignSchema>(IRIS_MODELS_TYPES.CommunicationCampaignSchema)
            .to(CommunicationCampaignSchema).inSingletonScope()
        bind<ICommunicationCampaignReadonlyDao>(IRIS_MODELS_TYPES.CommunicationCampaignReadonlyDao)
            .to(CommunicationCampaignReadonlyDaoMongoImpl).inSingletonScope()
        bind<ICommunicationCampaignReadWriteDao>(IRIS_MODELS_TYPES.CommunicationCampaignReadWriteDao)
            .to(CommunicationCampaignReadWriteDaoMongoImpl).inSingletonScope()

        bind<IFirebaseReportDao>(IRIS_MODELS_TYPES.FirebaseReportDao)
            .to(FirebaseReportDaoMySqlImpl).inSingletonScope()

        bind<ICommValidationsDao>(IRIS_MODELS_TYPES.CommValidationsDao)
            .to(CommValidationsDaoMySqlImpl).inSingletonScope()

    })
}
