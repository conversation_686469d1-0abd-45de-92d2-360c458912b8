import {
    Notification,
    PaginatedResults,
    UserNotificationFilterRequest,
} from "@curefit/iris-common"
import { MYSQL_TYPES, MultiMysqlAccess } from "@curefit/mysql-utils"
import { BASE_TYPES, Logger } from "@curefit/base"

import * as knex from "knex"
import * as _ from "lodash"
const memoize = require("memoizee")

import { inject, injectable } from "inversify"
import { QueryBuilder } from "knex"
import { IBasicNotificationReadOnlyDao } from "./INotificationDao"
import { IRIS_MODELS_TYPES } from "../ioc/IrisModelsTypes"
import { ICommunicationCampaignReadonlyDao } from "./ICommunicationCampaignDao"
import { CommonUtils } from "../utils/CommonUtils"
import { QueryUtils } from "../utils/QueryUtils"

@injectable()
export class BasicNotificationReadonlyDaoMysqlImpl implements IBasicNotificationReadOnlyDao {

    private connection: knex
    private static NOTIFICATION_TABLE: string = "notification"
    // 300 seconds of static loading
    public loadMemoizedCampaigns = memoize(this.loadCampaigns, {promise: true, maxAge: 300000})

    private getQueryBuilder(): QueryBuilder {
        return this.connection.table(BasicNotificationReadonlyDaoMysqlImpl.NOTIFICATION_TABLE)
    }

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(IRIS_MODELS_TYPES.CommonUtils) private commonUtils: CommonUtils,
        @inject(IRIS_MODELS_TYPES.QueryUtils) private queryUtils: QueryUtils,
        @inject(MYSQL_TYPES.MultiMysqlAccess) private mysqlConn: MultiMysqlAccess,
        @inject(IRIS_MODELS_TYPES.CommunicationCampaignReadonlyDao) private communicationCampaignReadonlyDao: ICommunicationCampaignReadonlyDao,
    ) {
        this.connection = mysqlConn.getMysqlConnection("REPLICA")
    }

    async getUserNotifications(request: UserNotificationFilterRequest): Promise<PaginatedResults<Notification>> {
        this.commonUtils.checkFields({ collection: request, fields: [ "userIds", "startDate", "endDate", "limit", "skip"] })

        /*
        Suggesting MySQL to use userId as index here since it searches in lesser number of rows as compared to the
         default index that is used
        */
        const query = this.connection.select("*").from(this.connection.raw(`${BasicNotificationReadonlyDaoMysqlImpl.NOTIFICATION_TABLE} USE INDEX(userId)`))
            .whereIn("userId", request.userIds)
            .whereBetween("createdAt", [request.startDate, request.endDate])

        if (!_.isEmpty(request.creativeIds)) {
            query.whereIn("creativeId", request.creativeIds)
        }
        else {
            if (!_.isEmpty(request.creativeType)) {
                query.where("creativeId", "LIKE", `${request.creativeType}%`)
            }
        }

        if (!_.isEmpty(request.campaignIds)) {
            query.whereIn("campaignId", request.campaignIds)
        }
        else {
            if (!_.isEmpty(request.vertical)) {
                query.where("campaignId", "LIKE", `${request.vertical}%`)
            }
            if (!_.isEmpty(request.campaignType)) {
                const campaignIds: string[] = await this.loadMemoizedCampaigns(request.campaignType)
                query.whereIn("campaignId", campaignIds )
            }
        }

        const fetchCountQuery = query.clone()
        query.orderBy("id", "desc").limit(request.limit).offset(request.skip)
        const notifications: Notification[] = await query
        const totalCount = await this.queryUtils.getTotalCount(fetchCountQuery)
        return {
            results: notifications,
            metadata: {
                limit: request.limit,
                skip: request.skip,
                totalCount: totalCount
            }
        }
    }

    async loadCampaigns(campaignType: string): Promise<string[]> {
        const campaigns = await this.communicationCampaignReadonlyDao.find({condition: {"type": campaignType}})
        this.logger.info("Call to reload data made")
        return campaigns.map(campaign => campaign.campaignId)
    }
}
