import { injectable, inject } from "inversify"
import { MongoReadonlyDao } from "@curefit/mongo-utils"

import { ClickToCallReportModel, ClickToCallReportMongooseModel } from "../models/notifications/NotificationReportModels"
import { INotificationReportReadonlyDao } from "./INotificationReportDao"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { IRIS_MODELS_TYPES } from "../ioc/IrisModelsTypes"
import { NotificationReportSchema } from "../models/notifications/NotificationReportSchema"

@injectable()
export class NotificationReportReadonlyDaoMongoImpl extends MongoReadonlyDao<ClickToCallReportMongooseModel, ClickToCallReportModel>
    implements INotificationReportReadonlyDao {
    constructor(
        @inject(IRIS_MODELS_TYPES.NotificationReportSchema) schemaModel: NotificationReportSchema,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(schemaModel.mongooseModel, logger, schemaModel.isLeanQueryEnabled)
    }
}
