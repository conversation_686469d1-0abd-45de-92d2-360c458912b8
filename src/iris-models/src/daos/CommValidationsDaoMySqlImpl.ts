import * as knex from "knex"
import { QueryBuilder } from "knex"
import { ICommValidationsDao } from "./ICommValidationsDao"
import { inject, injectable } from "inversify"
import { MultiMysqlAccess, MYSQL_TYPES } from "@curefit/mysql-utils"
import {
    CommValidationType, CommValidation,
    CommValidationModel,
    ExpireValidationRequest
} from "../../../iris-common/src/commValidations"

@injectable()
export class CommValidationsDaoMySqlImpl implements ICommValidationsDao {
    private connection: knex
    private static COMM_VALIDATIONS_TABLE: string = "comm_validations"

    private getQueryBuilder(): QueryBuilder {
        return this.connection.table(CommValidationsDaoMySqlImpl.COMM_VALIDATIONS_TABLE)
    }
    constructor(
        @inject(MYSQL_TYPES.MultiMysqlAccess) private mysqlConn: MultiMysqlAccess
    ) {
        this.connection = mysqlConn.getMysqlConnection()
    }

    async create(commValidation: CommValidationModel): Promise<Boolean> {
        const { type, entityId, expireAt } = commValidation
        const currentTime = new Date()

        // Try to find an active creative with the same creativeId
        const isPresent = await this.isBlockingValidation(type, entityId)

        // If an active creative exists, update its expireAt; otherwise, insert a new one
        const result = isPresent
            ? await this.getQueryBuilder().where({type}).andWhere({entityId}).andWhere("expireAt", ">", currentTime).update({
                entityId,
                expireAt: expireAt
            })
            : await this.getQueryBuilder().insert(commValidation)

        // Return true if at least one record was affected
        return (Array.isArray(result) && result.length > 0) || (typeof result === "number" && result > 0)
    }

    async bulkCreate(commValidation: CommValidationModel[]): Promise<Boolean[]> {
        // Use the `create` method for each creative
        return await Promise.all(commValidation.map(commValidation => this.create(commValidation)))
    }

    async expireValidation(expireValidations: ExpireValidationRequest[]): Promise<Boolean[]> {
        const currentTime = new Date()
        return await Promise.all(expireValidations.map(async (expireValidation) => {
            const {entityId, type} = expireValidation
            const exist = await this.isBlockingValidation(type, entityId)
            return exist ? await this.getQueryBuilder().where({type}).andWhere({entityId}).andWhere("expireAt", ">", currentTime).update({
                entityId,
                expireAt: currentTime
            }) : false
        }))
    }

    async isBlockingValidation(type: CommValidationType, entityId: string): Promise<Boolean> {
        const result = await this.getQueryBuilder().where({ type }).andWhere({ entityId }).andWhere("expireAt", ">", new Date())
        return result && result.length > 0
    }

    async findAllBlockingValidations(type: CommValidationType): Promise<CommValidation[]> {
        const result = await this.getQueryBuilder().where({type}).andWhere("expireAt", ">", new Date())
        return result && result.length > 0 ? result : null
    }


}