import { FirebaseReport, FirebaseReportModel } from "@curefit/iris-common"

export interface IFirebaseReportDao {
    create(firebaseReport: FirebaseReportModel) : Promise<Boolean>
    bulkCreate(firebaseReports: FirebaseReport[]) : Promise<Boolean[]>
    getFirebaseReportByDate(date: String) : Promise<FirebaseReportModel>
    updateFirebaseReportByDate(firebaseReport: FirebaseReportModel) : Promise<Boolean>
}


