import { injectable, inject } from "inversify"

import { BaseCreative } from "@curefit/iris-common"
import { BaseCreativeModel } from "../models/creatives/BaseCreativeModel"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { MongoReadWriteDao } from "@curefit/mongo-utils"
import { ICreativeReadonlyDao } from "./ICreativeDao"
import { BaseCreativeSchema } from "../models/creatives/BaseCreativeSchema"
import { CreativeReadonlyDaoMongoImpl } from "./CreativeReadonlyDaoMongoImpl"
import { IRIS_MODELS_TYPES } from "../ioc/IrisModelsTypes"


@injectable()
export class CreativeReadWriteDaoMongoImpl extends MongoReadWriteDao<BaseCreativeModel, BaseCreative>
    implements ICreativeReadonlyDao {
    constructor(
        @inject(IRIS_MODELS_TYPES.CreativeSchema) schemaModel: BaseCreativeSchema,
        @inject(IRIS_MODELS_TYPES.CreativeReadonlyDao) readonlyDao: CreativeReadonlyDaoMongoImpl,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(schemaModel.mongooseModel, readonlyDao, logger)
    }
}
