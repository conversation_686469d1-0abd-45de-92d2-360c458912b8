import { injectable, inject } from "inversify"

import { BaseCreative, CreativeType } from "@curefit/iris-common"
import { BaseCreativeModel } from "../models/creatives/BaseCreativeModel"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { IFindQuery, MongoReadonlyDao } from "@curefit/mongo-utils"
import { ICreativeReadonlyDao } from "./ICreativeDao"
import { BaseCreativeSchema } from "../models/creatives/BaseCreativeSchema"
import { IRIS_MODELS_TYPES } from "../ioc/IrisModelsTypes"

@injectable()
export class CreativeReadonlyDaoMongoImpl extends MongoReadonlyDao<BaseCreativeModel, BaseCreative>
    implements ICreativeReadonlyDao {
    constructor(
        @inject(IRIS_MODELS_TYPES.CreativeSchema) schemaModel: BaseCreativeSchema,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(schemaModel.mongooseModel, logger, schemaModel.isLeanQueryEnabled)
    }

    async findByType(findQuery: IFindQuery, type: CreativeType): Promise<BaseCreative[]> {
        findQuery.condition["type"] = type
        return this.find(findQuery)
    }

    async retrieveByType(type: CreativeType): Promise<BaseCreative[]> {
        const findQuery: IFindQuery = {
            condition: {
                type: type
            }
        }
        return this.find(findQuery)
    }
}
