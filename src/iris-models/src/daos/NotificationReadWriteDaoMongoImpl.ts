import { injectable, inject } from "inversify"

import { NotificationModel } from "../models/notifications/NotificationModel"
import { MongoReadWriteDao } from "@curefit/mongo-utils"
import { INotificationReadWriteDao } from "./INotificationDao"
import { NotificationSchema } from "../models/notifications/NotificationSchema"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { NotificationReadonlyDaoMongoImpl } from "./NotificationReadonlyDaoMongoImpl"
import { IRIS_MODELS_TYPES } from "../ioc/IrisModelsTypes"
import { Notification } from "@curefit/iris-common"


@injectable()
export class NotificationReadWriteDaoMongoImpl extends MongoReadWriteDao<NotificationModel, Notification>
    implements INotificationReadWriteDao {
    constructor(
        @inject(IRIS_MODELS_TYPES.NotificationSchema) schemaModel: NotificationSchema,
        @inject(IRIS_MODELS_TYPES.NotificationReadonlyDao) readonlyDao: NotificationReadonlyDaoMongoImpl,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(schemaModel.mongooseModel, readonlyDao, logger)
    }
}
