import { NotificationAttempt } from "@curefit/iris-common"
import { IRead, IWrite } from "@curefit/mongo-utils"

export interface INotificationAttemptReadonlyDao extends IRead<NotificationAttempt> { }
export interface INotificationAttemptReadWriteDao extends IWrite<NotificationAttempt>, INotificationAttemptReadonlyDao { }

export interface INotificationAttemptDao {
    getAttemptByNotificationId(notificationId: string): Promise<NotificationAttempt>

    create(notificationAttempt: NotificationAttempt): Promise<NotificationAttempt>

    updateProps(notificationId: string, externalId: string, props: { [key: string]: any }): Promise<boolean>
}
