import { injectable, inject } from "inversify"
import { NotificationSegmentTaskModel } from "../models/notifications/NotificationSegmentTaskModel"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { MongoReadonlyDao, MongoReadWriteDao } from "@curefit/mongo-utils"
import { INotificationSegmentTaskReadonlyDao, INotificationSegmentTaskReadWriteDao } from "./INotificationSegmentTaskDaos"
import { NotificationSegmentTaskSchema } from "../models/notifications/NotificationSegmentTaskSchema"
import { IRIS_MODELS_TYPES } from "../ioc/IrisModelsTypes"
import { NotificationSegmentTask } from "../../../iris-common/src/notificationsegmenttask"

@injectable()
export class NotificationSegmentTaskReadonlyDaoMongoImpl extends MongoReadonlyDao<NotificationSegmentTaskModel, NotificationSegmentTask>
    implements INotificationSegmentTaskReadonlyDao {
    constructor(
        @inject(IRIS_MODELS_TYPES.NotificationSegmentTaskSchema) notificationSegmentTaskSchema: NotificationSegmentTaskSchema,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(notificationSegmentTaskSchema.mongooseModel, logger, notificationSegmentTaskSchema.isLeanQueryEnabled)
    }
}

@injectable()
export class NotificationSegmentTaskReadWriteDaoMongoImpl extends MongoReadWriteDao<NotificationSegmentTaskModel, NotificationSegmentTask>
    implements INotificationSegmentTaskReadWriteDao {
    constructor(
        @inject(IRIS_MODELS_TYPES.NotificationSegmentTaskSchema) notificationSegmentTaskSchema: NotificationSegmentTaskSchema,
        @inject(IRIS_MODELS_TYPES.NotificationSegmentTaskReadonlyDao) readonlyDao: NotificationSegmentTaskReadonlyDaoMongoImpl,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(notificationSegmentTaskSchema.mongooseModel, readonlyDao, logger)
    }
}