import { injectable, inject } from "inversify"

import { NotificationModel } from "../models/notifications/NotificationModel"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { MongoReadonlyDao } from "@curefit/mongo-utils"
import { INotificationReadonlyDao } from "./INotificationDao"
import { IRIS_MODELS_TYPES } from "../ioc/IrisModelsTypes"
import { NotificationSchema } from "../models/notifications/NotificationSchema"
import { Notification } from "@curefit/iris-common"


@injectable()
export class NotificationReadonlyDaoMongoImpl extends MongoReadonlyDao<NotificationModel, Notification>
    implements INotificationReadonlyDao {
    constructor(
        @inject(IRIS_MODELS_TYPES.NotificationSchema) schemaModel: NotificationSchema,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(schemaModel.mongooseModel, logger, schemaModel.isLeanQueryEnabled)
    }
}
