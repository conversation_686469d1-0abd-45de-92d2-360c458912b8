import { injectable, inject } from "inversify"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { MongoReadonlyDao } from "@curefit/mongo-utils"
import { IRIS_MODELS_TYPES } from "../ioc/IrisModelsTypes"
import {  TemplateModel } from "../models/attachment/TemplateModel"
import { ITemplateReadonlyDao } from "./ITemplateDao"
import { TemplateSchema } from "../models/attachment/TemplateSchema"
import { Template } from "@curefit/iris-common"

@injectable()
export class TemplateReadonlyDaoMongoImpl extends MongoReadonlyDao<TemplateModel, Template> implements ITemplateReadonlyDao {
    constructor(
        @inject(IRIS_MODELS_TYPES.TemplateSchema) schemaModel: TemplateSchema,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(schemaModel.mongooseModel, logger, schemaModel.isLeanQueryEnabled)
    }
}
