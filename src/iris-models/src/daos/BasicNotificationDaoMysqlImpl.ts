import { Notification, NotificationModel} from "@curefit/iris-common"
import { ConflictError, NotFoundError } from "@curefit/error-client"
import { MYSQL_TYPES, MultiMysqlAccess } from "@curefit/mysql-utils"

import * as knex from "knex"
import * as _ from "lodash"
import { inject, injectable } from "inversify"
import { QueryBuilder } from "knex"

import { IBasicNotificationDao } from "./INotificationDao"
import * as moment from "moment"
@injectable()
export class BasicNotificationDaoMysqlImpl implements IBasicNotificationDao {


    private connection: knex
    private static NOTIFICATION_TABLE: string = "notification"

    private getQueryBuilder(): QueryBuilder {
        return this.connection.table(BasicNotificationDaoMysqlImpl.NOTIFICATION_TABLE)
    }

    constructor(
        @inject(MYSQL_TYPES.MultiMysqlAccess) private mysqlConn: MultiMysqlAccess
    ) {
        this.connection = mysqlConn.getMysqlConnection()
    }

    private static convertToNotification(notificationModelObject: NotificationModel): Notification {
        const notification: Notification = _.omit(notificationModelObject, [ "deviceInfo "]) as Notification
        if (notificationModelObject.deviceInfo) {
            notification.deviceInfo = JSON.parse(notificationModelObject.deviceInfo)
        }
        return notification
    }

    private static convertFromNotification(notification: Notification): NotificationModel {
        const notificationModel: NotificationModel = _.omit(notification, [ "deviceInfo" ])
        if (notification.deviceInfo) {
            notificationModel.deviceInfo = JSON.stringify(notification.deviceInfo)
        }
        return notificationModel
    }

    private static convertUpdateProps(props: { [key: string]: any }): { [key: string]: any } {
        const updatedProps = _.omit(props, [ "deviceInfo" ])
        if (props.deviceInfo) {
            updatedProps.deviceInfo = JSON.stringify(props.deviceInfo)
        }
        return updatedProps
    }

    async get(notificationId: string): Promise<Notification> {
        const notifications: NotificationModel[] = await this.getQueryBuilder()
            .where({ notificationId: notificationId })
            .limit(1)

        if (notifications && notifications.length > 0) {
            return BasicNotificationDaoMysqlImpl.convertToNotification(notifications[0])
        } else {
            // throw new NotFoundError(`Notification with id ${notificationId} not found.`)
            return null
        }
    }

    async getNotificationsById(notificationIds: string[]): Promise<Notification[]> {
        const notificationObjects: NotificationModel[] = await this.getQueryBuilder()
            .whereIn("notificationId", notificationIds)
        return notificationObjects.map(BasicNotificationDaoMysqlImpl.convertToNotification)
    }

    async update(notification: Notification): Promise<boolean> {
        return ((await this.getQueryBuilder()
            .where({ notificationId: notification.notificationId })
            .update(BasicNotificationDaoMysqlImpl.convertFromNotification(notification))) > 0)
    }

    async bulkCreate(notifications: Notification[]): Promise<boolean> {
        try {
            const id = (await this.getQueryBuilder()
                .insert(notifications.map(BasicNotificationDaoMysqlImpl.convertFromNotification)))
            return id > 0
        } catch (err) {
            console.log("Error IS ", err)
            if (err.code === "ER_DUP_ENTRY") {
                throw new ConflictError("Notification already exists")
            }
        }
    }

    async updateProps(notificationId: string, props: { [key: string]: any }): Promise<boolean> {
        return ((await this.getQueryBuilder()
            .where({ notificationId: notificationId })
            .update(BasicNotificationDaoMysqlImpl.convertUpdateProps(props))) > 0)
    }

    async updatePropsByExternalId(externalId: string, props: { [key: string]: any; }): Promise<boolean> {
        return ((await this.getQueryBuilder()
            .where({ externalId: externalId })
            .update(BasicNotificationDaoMysqlImpl.convertUpdateProps(props))) > 0)
    }

    async getLatestForUser(campaignId: string, creativeId: string, userId: string): Promise<Notification> {
        const query = { userId, campaignId, creativeId }

        const notificationObjects: NotificationModel[] = await this.getQueryBuilder()
            .where(query)
            .whereNotNull("sentAt")
            .orderBy("id", "desc")
            .limit(1)

        if (notificationObjects && notificationObjects.length > 0) {
            return BasicNotificationDaoMysqlImpl.convertToNotification(notificationObjects[0])
        }

        throw new NotFoundError(`No notification found for ${JSON.stringify(query)}`)
    }

    async getNotificationsByTaskId(taskId: string): Promise<Notification[]> {
        const query = { taskId }

        return (await this.getQueryBuilder().where(query)).map(BasicNotificationDaoMysqlImpl.convertToNotification)
    }

    async getNotificationTaskReport(taskId: string): Promise<any> {
        const query = { taskId }

        return this.getQueryBuilder()
            .select("status")
            .count({count: "*"})
            .where(query)
            .groupBy("status")
    }

    async getNotificationsByExternalId(externalId: string): Promise<Notification> {
        const query = { externalId: externalId }

        const notifications: Notification[] = (await this.getQueryBuilder().where(query))
            .map(BasicNotificationDaoMysqlImpl.convertToNotification)

        if (notifications && notifications.length > 0) {
            return notifications[0]
        } else {
            return null
        }
    }

    // Returns true if any mail was successfully sent to the given emailId in the past
    async validateEmail(emailId: string, checkLastXDays: number): Promise<boolean> {
        const query = { userId: emailId }
        const dateXDaysBefore: string = moment().utc().subtract(checkLastXDays, "days").format("YYYY-MM-DD")
        const notificationObjects: NotificationModel[] = await this.getQueryBuilder()
            .where(query)
            .where("creativeId", "like", "EMAIL_%")
            .whereIn("status", ["READ", "DELIVERED"])
            .where("createdAt", ">", dateXDaysBefore)
            .limit(1)
        return !(_.isEmpty(notificationObjects))
    }

    async fetchRecentlySentCampaigns(campaignIds: string[], startDate: string, endDate: string): Promise<string[]> {
        const recentlySentCampaigns: Notification[] = await this.getQueryBuilder()
            .distinct()
            .select("campaignId")
            .whereIn("campaignId", campaignIds)
            .whereBetween("createdAt", [startDate, endDate])
            .limit(campaignIds.length)
        const recentlySentCampaignsString: string[] = recentlySentCampaigns.map(obj => obj.campaignId)
        return recentlySentCampaignsString
    }

}
