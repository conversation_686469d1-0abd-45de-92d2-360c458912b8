import {
    NotificationMeta,
    NotificationState,
    PaginatedResults
} from "@curefit/iris-common"
import { InAppNotificationType } from "@curefit/iris-common"
import { MYSQL_TYPES, MultiMysqlAccess } from "@curefit/mysql-utils"
import { ConflictError } from "@curefit/error-client"

import { inject, injectable } from "inversify"
import { QueryBuilder } from "knex"
import * as knex from "knex"
import * as _ from "lodash"

import { IBasicNotificatioMetaDao } from "./INotificationDao"
import { IRIS_MODELS_TYPES } from "../ioc/IrisModelsTypes"
import { QueryUtils } from "../utils/QueryUtils"

export const NUDGE_PRIORITY_MAP = new Map<InAppNotificationType, number>()
NUDGE_PRIORITY_MAP.set("META", 1)
NUDGE_PRIORITY_MAP.set("PERMISSION", 2)
NUDGE_PRIORITY_MAP.set("PACK_RENEWAL", 3)
NUDGE_PRIORITY_MAP.set("SINGLE", 4)
NUDGE_PRIORITY_MAP.set("ONBOARDING", 5)
NUDGE_PRIORITY_MAP.set("POST_CLASS", 6)
NUDGE_PRIORITY_MAP.set("PACK_PURCHASED", 7)
NUDGE_PRIORITY_MAP.set("NPS_SURVEY", 8)

@injectable()
export class BasicNotificationMetaDaoMysqlImpl implements IBasicNotificatioMetaDao {

    private connection: knex
    private static NOTIFICATION_META_TABLE: string = "NotificationMeta"

    private getQueryBuilder(): QueryBuilder {
        return this.connection.table(BasicNotificationMetaDaoMysqlImpl.NOTIFICATION_META_TABLE)
    }

    constructor(
        @inject(MYSQL_TYPES.MultiMysqlAccess) private mysqlConn: MultiMysqlAccess,
        @inject(IRIS_MODELS_TYPES.QueryUtils) private queryUtils: QueryUtils
    ) {
        this.connection = mysqlConn.getMysqlConnection()
    }

    async get(notificationId: string): Promise<NotificationMeta> {
        const notificationMetas: NotificationMeta[] = await this.getQueryBuilder().where({ notificationId: notificationId }).limit(1)
        if (notificationMetas && notificationMetas.length > 0) {
            return notificationMetas[0]
        } else {
            // throw new NotFoundError(`Notification with id ${notificationId} not found.`)
            return null
        }
    }

    async getBulk(notificationIds: string[]): Promise<NotificationMeta[]> {
        return this.getQueryBuilder().whereIn("notificationId", notificationIds)
    }

    async update(notificationMeta: NotificationMeta): Promise<boolean> {
        return (await this.getQueryBuilder().where({ notificationId: notificationMeta.notificationId }).update(notificationMeta)) > 0
    }

    async updatePropsByTaskId(taskId: string, props: { [key: string]: any }): Promise<boolean> {
        return ((await this.getQueryBuilder().where({ taskId: taskId }).update(props)) > 0)
    }

    async updatePropsByNotificationId(notificationId: string, props: { [key: string]: any }): Promise<boolean> {
        return ((await this.getQueryBuilder().where({ notificationId: notificationId }).update(props)) > 0)
    }

    async bulkCreate(notificationMetas: NotificationMeta[]): Promise<boolean> {
        try {
            const id = (await this.getQueryBuilder().insert(notificationMetas))
            return id > 0
        } catch (err) {
            console.log("Error IS ", err)
            if (err.code === "ER_DUP_ENTRY") {
                throw new ConflictError("Notification already exists")
            }
        }
    }

    async getNotificationsMetasCountsForUser(userId: string, appId: string, expiryAfter?: string): Promise<any> {
        const query = this.getQueryBuilder()
            .select("state")
            .where({ userId, appId })
            .where("expiry", ">", expiryAfter || new Date())
            .where("scheduledAt", "<", new Date())
            .count("* as count")
            .groupBy("state")
        return query
    }

    async getNotificationMetasForUser(userId: string, appId: string, states: NotificationState[],
                                      limit: number, skip: number): Promise<PaginatedResults<NotificationMeta>> {
        const query = this.getQueryBuilder().where({ userId, appId })
            .where("expiry", ">", new Date())
            .where("scheduledAt", "<", new Date())
        !_.isEmpty(states) && query.whereIn("state", states)

        const fetchCountQuery = query.clone()
        query.orderBy("id", "desc").limit(limit).offset(skip)

        const notificationMetas: NotificationMeta[] = await query
        const totalCount = await this.queryUtils.getTotalCount(fetchCountQuery)

        return {
            results: notificationMetas,
            metadata: { limit, skip, totalCount }
        }
    }

    async getActiveNotificationMetasForUser(userId: string, appId: string): Promise<NotificationMeta[]> {
        const activeState: NotificationState = "RECEIVED"
        const query = {
            userId: userId,
            state: activeState,
            appId: appId
        }
        const notificationMetas: NotificationMeta[] = await this.getQueryBuilder()
            .where(query)
            .where("expiry", ">", new Date())
            .where("scheduledAt", "<", new Date())
        const filteredNotificationMetas = _.filter(notificationMetas,
                notificationMeta => {
            return NUDGE_PRIORITY_MAP.get(notificationMeta.type) && NUDGE_PRIORITY_MAP.get(notificationMeta.type) !== -1 })
        const sortedNotificationMetas = _.sortBy(filteredNotificationMetas,
                notificationMeta => { return NUDGE_PRIORITY_MAP.get(notificationMeta.type) })
        return sortedNotificationMetas
    }

    async updateNotificationMetaBulk(notificationIds: string[], props: { [key: string]: any }): Promise<boolean> {
        const query = this.getQueryBuilder().whereIn("notificationId", notificationIds).update(props)
        return ((await query) > 0)
    }

    async getNotificationMetasByTaskId(taskId: string): Promise<NotificationMeta[]> {
        const query = {
            taskId: taskId
        }
        const notificationMetas: NotificationMeta[] = await this.getQueryBuilder().where(query)
        return notificationMetas
    }
}
