import { QueryBuilder } from "knex";
import { IFirebaseReportDao } from "./IFirebaseReportDao";
import { inject, injectable } from "inversify";
import { MultiMysqlAccess, MYSQL_TYPES } from "@curefit/mysql-utils";
import { FirebaseReport, FirebaseReportModel } from "@curefit/iris-common";
import * as knex from "knex"

@injectable()
export class FirebaseReportDaoMySqlImpl implements IFirebaseReportDao{
    private connection: knex
    private static FIREBASE_REPORTS_TABLE: string = "firebase_report"

    private getQueryBuilder(): QueryBuilder {
        return this.connection.table(FirebaseReportDaoMySqlImpl.FIREBASE_REPORTS_TABLE)
    }
    constructor(
        @inject(MYSQL_TYPES.MultiMysqlAccess) private mysqlConn: MultiMysqlAccess
    ) {
        this.connection = mysqlConn.getMysqlConnection()
    }

    private static convertToFirebaseReport(firebaseReport: FirebaseReport): FirebaseReportModel {
        const {appId, date, data} = firebaseReport;
        const dateString: string = `${date.year}-${String(date.month).padStart(2, '0')}-${String(date.day).padStart(2, '0')}`
        return {
            appId,
            date: dateString,
            data: JSON.stringify(data)
        }
    }

    async create(firebaseReport: FirebaseReportModel) : Promise<Boolean>{
        let result=  await this.getQueryBuilder().insert(firebaseReport)
        return (Array.isArray(result) && result.length > 0) || (typeof result === 'number' && result > 0)
    }

    async bulkCreate(firebaseReports: FirebaseReport[]): Promise<Boolean[]> {
        const results = await Promise.all(firebaseReports.map(async (dayReport)=>{
            let convertedDayReport = FirebaseReportDaoMySqlImpl.convertToFirebaseReport(dayReport)
            let exist = await this.getFirebaseReportByDate(convertedDayReport.date)
            if(!exist) {
                return await this.create(convertedDayReport)
            }
            else {
                if(Object.keys(JSON.parse(convertedDayReport.data)).length > 0){
                    return await this.updateFirebaseReportByDate(convertedDayReport)
                }
                return false;
            }
        }))
        return results;
    }

    async getFirebaseReportByDate(date: String): Promise<FirebaseReportModel> {
        const result = await this.getQueryBuilder()
        .where({ date: date })
        if(result && result.length > 0){
            return result[0]
        }
        else return null

    }

    async updateFirebaseReportByDate(firebaseReport: FirebaseReportModel): Promise<Boolean> {
        return (await this.getQueryBuilder()
            .where({ date: firebaseReport.date })
            .update(firebaseReport)) > 0
    }

}