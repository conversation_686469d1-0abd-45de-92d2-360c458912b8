import { injectable, inject } from "inversify"

import { CommunicationCampaignModel } from "../models/campaigns/CommunicationCampaignModel"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { MongoReadonlyDao } from "@curefit/mongo-utils"
import { CommunicationCampaign } from "@curefit/iris-common"
import { CommunicationCampaignSchema } from "../models/campaigns/CommunicationCampaignSchema"
import { IRIS_MODELS_TYPES } from "../ioc/IrisModelsTypes"
import { ICommunicationCampaignReadonlyDao } from "./ICommunicationCampaignDao"

@injectable()
export class CommunicationCampaignReadonlyDaoMongoImpl extends MongoReadonlyDao<CommunicationCampaignModel, CommunicationCampaign>
    implements ICommunicationCampaignReadonlyDao {
    constructor(
        @inject(IRIS_MODELS_TYPES.CommunicationCampaignSchema) schemaModel: CommunicationCampaignSchema,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(schemaModel.mongooseModel, logger, schemaModel.isLeanQueryEnabled)
    }
}
