import { injectable, inject } from "inversify"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { MongoReadWriteDao } from "@curefit/mongo-utils"
import { IRIS_MODELS_TYPES } from "../ioc/IrisModelsTypes"
import { ClickToCallReportModel, ClickToCallReportMongooseModel } from "../models/notifications/NotificationReportModels"
import { INotificationReportReadWriteDao } from "./INotificationReportDao"
import { NotificationReportSchema } from "../models/notifications/NotificationReportSchema"
import { NotificationReportReadonlyDaoMongoImpl } from "./NotificationReportReadonlyDaoMongoImpl"

@injectable()
export class NotificationReportReadWriteDaoMongoImpl extends MongoReadWriteDao<ClickToCallReportMongooseModel, ClickToCallReportModel>
    implements INotificationReportReadWriteDao {
        constructor(
            @inject(IRIS_MODELS_TYPES.NotificationReportSchema) schemaModel: NotificationReportSchema,
            @inject(IRIS_MODELS_TYPES.NotificationReportReadonlyDao) readonlyDao: NotificationReportReadonlyDaoMongoImpl,
            @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
            super(schemaModel.mongooseModel, readonlyDao, logger)
        }
}
