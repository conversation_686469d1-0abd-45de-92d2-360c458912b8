import { injectable, inject } from "inversify"
import { NotificationTaskModel } from "../models/notifications/NotificationTaskModel"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { MongoReadonlyDao, MongoReadWriteDao } from "@curefit/mongo-utils"
import { NotificationTask } from "@curefit/iris-common"
import { INotificationTaskReadonlyDao, INotificationTaskReadWriteDao } from "./INotificationTaskDaos"
import { NotificationTaskSchema } from "../models/notifications/NotificationTaskSchema"
import { IRIS_MODELS_TYPES } from "../ioc/IrisModelsTypes"

@injectable()
export class NotificationTaskReadonlyDaoMongoImpl extends MongoReadonlyDao<NotificationTaskModel, NotificationTask> implements INotificationTaskReadonlyDao {
    constructor(
        @inject(IRIS_MODELS_TYPES.NotificationTaskSchema) NotificationTaskSchema: NotificationTaskSchema,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(NotificationTaskSchema.mongooseModel, logger, NotificationTaskSchema.isLeanQueryEnabled)
    }
}

@injectable()
export class NotificationTaskReadWriteDaoMongoImpl extends MongoReadWriteDao<NotificationTaskModel, NotificationTask> implements INotificationTaskReadWriteDao {
    constructor(
        @inject(IRIS_MODELS_TYPES.NotificationTaskSchema) NotificationTaskSchema: NotificationTaskSchema,
        @inject(IRIS_MODELS_TYPES.NotificationTaskReadonlyDao) readonlyDao: NotificationTaskReadonlyDaoMongoImpl,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(NotificationTaskSchema.mongooseModel, readonlyDao, logger)
    }
}
