import { NotificationAttempt } from "@curefit/iris-common"
import * as knex from "knex"
import { inject, injectable } from "inversify"
import { QueryBuilder } from "knex"
import { INotificationAttemptDao } from "./INotificationAttemptDao"
import { MYSQL_TYPES, MultiMysqlAccess } from "@curefit/mysql-utils"


@injectable()
export class NotificationAttemptDaoMysqlImpl implements INotificationAttemptDao {


    private connection: knex
    private static NOTIFICATION_ATTEMPT_TABLE: string = "notificationAttempt"

    private getQueryBuilder(): QueryBuilder {
        return this.connection.table(NotificationAttemptDaoMysqlImpl.NOTIFICATION_ATTEMPT_TABLE)
    }

    constructor(
        @inject(MYSQL_TYPES.MultiMysqlAccess) private mysqlConn: MultiMysqlAccess
    ) {
        this.connection = mysqlConn.getMysqlConnection()
    }

    async getAttemptByNotificationId(notificationId: string): Promise<NotificationAttempt> {
        const notificationAttempts: NotificationAttempt[] = await this.getQueryBuilder().where({ notificationId: notificationId }).limit(1)
        if (notificationAttempts && notificationAttempts.length > 0) {
            return notificationAttempts[0]
        } else {
            return null
        }
    }

    async create(notificationAttempt: NotificationAttempt): Promise<NotificationAttempt> {
        const attempt = await this.getQueryBuilder().insert(notificationAttempt)
        return attempt
    }

    async updateProps(notificationId: string, externalId: string, props: { [key: string]: any }): Promise<boolean> {
        return ((await this.getQueryBuilder().where({ notificationId: notificationId, externalId: externalId }).update(props)) > 0)
    }

}
