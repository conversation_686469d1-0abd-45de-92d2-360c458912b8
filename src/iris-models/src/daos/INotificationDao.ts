import {
    Notification,
    NotificationMeta,
    NotificationState,
    PaginatedResults,
    UserNotificationFilterRequest
} from "@curefit/iris-common"
import { IRead, IWrite } from "@curefit/mongo-utils"

export interface INotificationReadonlyDao extends IRead<Notification> { }
export interface INotificationReadWriteDao extends IWrite<Notification>, INotificationReadonlyDao { }

export interface IBasicNotificationDao {
    get(notificationId: string): Promise<Notification>

    getNotificationsById(notificationIds: string[]): Promise<Notification[]>

    update(notification: Notification): Promise<boolean>

    updateProps(notificationId: string, props: { [key: string]: any }): Promise<boolean>

    updatePropsByExternalId(externalId: string, props: { [key: string]: any }): Promise<boolean>

    bulkCreate(notifications: Notification[]): Promise<boolean>

    getLatestForUser(campaignId: string, creativeId: string, userId: string): Promise<Notification>

    getNotificationsByTaskId(taskId: string): Promise<Notification[]>

    getNotificationTaskReport(taskId: string): Promise<any>

    getNotificationsByExternalId(externalId: string): Promise<Notification>

    validateEmail(emailId: string, checkLastXDays: number): Promise<boolean>

    fetchRecentlySentCampaigns(campaignIds: string[], startDate: string, endDate: string): Promise<string[]>

}

export interface IBasicNotificatioMetaDao {
    get(notificationId: string): Promise<NotificationMeta>

    getBulk(notificationIds: string[]): Promise<NotificationMeta[]>

    update(notificationMeta: NotificationMeta): Promise<boolean>

    bulkCreate(notificationMetas: NotificationMeta[]): Promise<boolean>

    getNotificationMetasForUser(userId: string, appId: string, states: NotificationState [],
                                limit: number, skip: number): Promise<PaginatedResults<NotificationMeta>>

    getActiveNotificationMetasForUser(userId: string, appId: string): Promise<NotificationMeta[]>

    updateNotificationMetaBulk(notificationIds: string[], props: { [key: string]: any }): Promise<boolean>

    updatePropsByTaskId(taskId: string, props: { [key: string]: any }): Promise<boolean>

    updatePropsByNotificationId(notificationId: string, props: { [key: string]: any }): Promise<boolean>

    getNotificationMetasByTaskId(taskId: string): Promise<NotificationMeta[]>

    getNotificationsMetasCountsForUser(userId: string, appId: string, expiryAfter?: string): Promise<any>

}

export interface IBasicNotificationReadOnlyDao {
    getUserNotifications(request: UserNotificationFilterRequest): Promise<PaginatedResults<Notification>>
}