import { injectable, inject } from "inversify"
import { MongoReadWriteDao } from "@curefit/mongo-utils"
import { CommunicationCampaignModel } from "../models/campaigns/CommunicationCampaignModel"
import { CommunicationCampaign } from "@curefit/iris-common"
import { ICommunicationCampaignReadWriteDao } from "./ICommunicationCampaignDao"
import { CommunicationCampaignSchema } from "../models/campaigns/CommunicationCampaignSchema"
import { CommunicationCampaignReadonlyDaoMongoImpl } from "./CommunicationCampaignReadonlyDaoMongoImpl"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { IRIS_MODELS_TYPES } from "../ioc/IrisModelsTypes"

@injectable()
export class CommunicationCampaignReadWriteDaoMongoImpl extends MongoReadWriteDao<CommunicationCampaignModel, CommunicationCampaign>
    implements ICommunicationCampaignReadWriteDao {
    constructor(
        @inject(IRIS_MODELS_TYPES.CommunicationCampaignSchema) schemaModel: CommunicationCampaignSchema,
        @inject(IRIS_MODELS_TYPES.CommunicationCampaignReadonlyDao) readonlyDao: CommunicationCampaignReadonlyDaoMongoImpl,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(schemaModel.mongooseModel, readonlyDao, logger)
    }
}
