import { injectable, inject } from "inversify"
import { Campaign } from "@curefit/offer-common"
import { CampaignModel } from "../models/campaign/CampaignModel"
import { CampaignSchema } from "../models/campaign/CampaignSchema"
import { IRIS_MODELS_TYPES } from "../ioc/IrisModelsTypes"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { MongoReadonlyDao } from "@curefit/mongo-utils"

@injectable()
export class CampaignReadonlyDaoMongoImpl extends MongoReadonlyDao<CampaignModel, Campaign> {
    constructor(
        @inject(IRIS_MODELS_TYPES.CampaignSchema) campaignSchema: CampaignSchema,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(campaignSchema.mongooseModel, logger, campaignSchema.isLeanQueryEnabled)
    }
}
