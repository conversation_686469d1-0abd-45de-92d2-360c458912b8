import {
    CommValidationModel,
    CommValidation,
    ExpireValidationRequest,
    CommValidationType
} from "../../../iris-common/src/commValidations"
export interface ICommValidationsDao {
    create(commValidation: CommValidationModel): Promise<Boolean>
    bulkCreate(commValidations: CommValidationModel[]): Promise<Boolean[]>
    expireValidation(expireValidation: ExpireValidationRequest[]): Promise<Boolean[]>
    isBlockingValidation(type: CommValidationType, entityId: string): Promise<Boolean>
    findAllBlockingValidations(type: CommValidationType): Promise<CommValidation[]>
}