import { injectable, inject } from "inversify"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { MongoReadWriteDao } from "@curefit/mongo-utils"
import { CampaignModel } from "../models/campaign/CampaignModel"
import { Campaign } from "@curefit/offer-common"
import { IRIS_MODELS_TYPES } from "../ioc/IrisModelsTypes"
import { CampaignSchema } from "../models/campaign/CampaignSchema"
import { CampaignReadonlyDaoMongoImpl } from "./CampaignReadonlyDaoMongoImpl"

@injectable()
export class CampaignReadWriteDaoMongoImpl extends MongoReadWriteDao<CampaignModel, Campaign> {
    constructor(
        @inject(IRIS_MODELS_TYPES.CampaignSchema) campaignSchema: CampaignSchema,
        @inject(IRIS_MODELS_TYPES.CampaignReadOnlyDao) readonlyDao: CampaignReadonlyDaoMongoImpl,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(campaignSchema.mongooseModel, readonlyDao, logger)
    }
}
