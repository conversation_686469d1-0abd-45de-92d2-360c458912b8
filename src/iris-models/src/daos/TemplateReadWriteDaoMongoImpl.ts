import { injectable, inject } from "inversify"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { MongoReadWriteDao } from "@curefit/mongo-utils"
import { IRIS_MODELS_TYPES } from "../ioc/IrisModelsTypes"
import { TemplateModel } from "../models/attachment/TemplateModel"
import { ITemplateReadWriteDao } from "./ITemplateDao"
import { TemplateSchema } from "../models/attachment/TemplateSchema"
import { TemplateReadonlyDaoMongoImpl } from "./TemplateReadonlyDaoMongoImpl"
import { Template } from "@curefit/iris-common"


@injectable()
export class TemplateReadWriteDaoMongoImpl extends MongoReadWriteDao<TemplateModel, Template>
    implements ITemplateReadWriteDao {
    constructor(
        @inject(IRIS_MODELS_TYPES.TemplateSchema) schemaModel: TemplateSchema,
        @inject(IRIS_MODELS_TYPES.TemplateReadonlyDao) readonlyDao: TemplateReadonlyDaoMongoImpl,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(schemaModel.mongooseModel, readonlyDao, logger)
    }
}
