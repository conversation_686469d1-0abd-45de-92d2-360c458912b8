import { inject, injectable } from "inversify"
import { MONGO_TYPES, MultiMongooseAccess, MultiMongooseSchema } from "@curefit/mongo-utils"
import { TemplateModel } from "./TemplateModel"
import { Template, TemplateTypes } from "@curefit/iris-common"


@injectable()
export class TemplateSchema extends MultiMongooseSchema<TemplateModel> {

    constructor(
        @inject(MONGO_TYPES.MultiMongooseAccess) mongooseAccess: MultiMongooseAccess
    ) {
        super(mongooseAccess, "Template", "DEFAULT")
    }

    protected schema() {
        return {
            templateId: {
                type: String,
                required: true,
                index: true,
                unique: true
            },
            name: {
                type: String,
                required: true,
                unique: true
            },
            type: {
                type: String,
                enum: TemplateTypes,
                required: true
            },
            defaultFileName: {
                type: String,
                required: true,
            },
            templateBody: {
                type: String,
                required: false,
            }
        }
    }
}