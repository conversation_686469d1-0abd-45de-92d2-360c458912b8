import { MONGO_TYPES, MultiMongooseAccess, MultiMongooseSchema } from "@curefit/mongo-utils"
import { injectable, inject } from "inversify"
import { NotificationTaskModel } from "./NotificationTaskModel"
import { NotificationTaskTypes } from "@curefit/iris-common"
import { DateTimezoneSchema } from "@curefit/base-mongo"
import { Countries } from "@curefit/iris-common"

const mongoose = require("mongoose")
const MongooseMap = require("mongoose-map")(mongoose)

@injectable()
export class NotificationTaskSchema extends MultiMongooseSchema<NotificationTaskModel> {

    constructor(
        @inject(MONGO_TYPES.MultiMongooseAccess) mongooseAccess: MultiMongooseAccess
    ) {
        super(mongooseAccess, "notificationTasks", "DEFAULT")
    }

    protected schema() {
        const UserContextSchema = {
            userId: {
                type: String,
                required: false
            },
            tags: {
                type: MongooseMap,
                required: false
            }
        }

        return {
            creativeIds: {
                type: [String],
                required: true
            },
            campaignId: {
                type: String,
                index: true,
                required: true,
            },
            taskType: {
                type: String,
                enum: NotificationTaskTypes,
                required: true
            },
            userContexts: {
                type: [UserContextSchema],
                required: false
            },
            schedule: {
                type: DateTimezoneSchema,
                required: false
            },
            globalTags: {
                type: MongooseMap,
                required: false
            },
            status: {
                type: String
            },
            progress: {
                type: Number
            },
            hanselSegmentIds: {
                type: [String],
                required: false
            },
            agentId: {
                type: String,
                required: true
            },
            country: {
                type: String,
                enum: Countries,
                required: true,
                default: "INDIA"
            },
            dndWindow: {
                type: {
                    startingHours: {
                        type: {
                            hour: {
                                type: Number,
                                required: false
                            },
                            min: {
                                type: Number,
                                required: false
                            }
                        },
                        required: false
                    },
                    closingHours: {
                        type: {
                            hour: {
                                type: Number,
                                required: false
                            },
                            min: {
                                type: Number,
                                required: false
                            }
                        },
                        required: false
                    }
                },
                required: false
            },
            forceSend: {
                type: Boolean,
                required: true
            },
            fallbackCreativeIds: {
                type: MongooseMap,
                required: false
            }
        }
    }
}
