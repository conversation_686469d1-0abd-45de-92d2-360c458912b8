import { injectable, inject } from "inversify"
import { MONGO_TYPES, MultiMongooseAccess, MultiMongooseSchema } from "@curefit/mongo-utils"
import { ClickToCallReportMongooseModel } from "./NotificationReportModels"

@injectable()
export class NotificationReportSchema extends MultiMongooseSchema<ClickToCallReportMongooseModel> {

    constructor(
        @inject(MONGO_TYPES.MultiMongooseAccess) mongooseAccess: MultiMongooseAccess
    ) {
        super(mongooseAccess, "notificationreport", "DEFAULT")
    }

    protected schema() {

        return {
            notificationId: {
                type: String,
                required: true,
                index: true,
                unique: true
            },
            creativeType: {
                type: String,
                required: true,
                index: true,
                unique: false
            },
            serviceProvider: {
                type: String,
                required: true,
                index: true,
                unique: false
            },
            caller: {
                type: String,
                required: true
            },
            receiver: {
                type: String,
                required: true
            },
            startTime: {
                type: Date,
                required: false
            },
            duration: {
                type: Number,
                required: false,
            },
            status: {
                type: String,
                required: false
            },
            statusCaller: {
                type: String,
                required: false
            },
            statusReceiver: {
                type: String,
                required: false
            },
            recordPath: {
                type: String,
                required: false
            },
            externalId: {
                type: String,
                required: false
            },
            metadata: {
                type: String,
                required: false
            }
        }
    }
}
