import { injectable, inject } from "inversify"
import { CompositeIndex, MONGO_TYPES, MultiMongooseAccess, MultiMongooseSchema } from "@curefit/mongo-utils"
import { NotificationSegmentTaskModel } from "./NotificationSegmentTaskModel"
import { getEnumValues } from "@curefit/base"
import { Schema } from "mongoose"
import { NotificationSegmentTaskJobTypeEnum, NotificationSegmentTaskStatusEnum } from "../../../../iris-common/src/notificationsegmenttask"

const mongoose = require("mongoose")
const MongooseMap = require("mongoose-map")(mongoose)

@injectable()
export class NotificationSegmentTaskSchema extends MultiMongooseSchema<NotificationSegmentTaskModel> {

    constructor(
        @inject(MONGO_TYPES.MultiMongooseAccess) mongooseAccess: MultiMongooseAccess
    ) {
        super(mongooseAccess, "notificationsegmenttasks", "DEFAULT")
    }

    protected schema() {

        const NotificationSegmentTaskJobSchema = new Schema({
            jobId: {
                type: String,
                required: true,
                index: true
            },
            type: {
                type: String,
                enum: getEnumValues(NotificationSegmentTaskJobTypeEnum),
                required: true,
                index: true
            }
        }, { _id: false })

        const S3InfoSchema = new Schema({
            bucket: {
                type: String,
                required: true
            },
            key: {
                type: String,
                required: true
            }
        }, { _id: false })

        const AttachmentSchema = new Schema({
            s3Info: {
                type: S3InfoSchema,
                required: false
            },
            fileUrl: {
                type: String,
                required: false,
            },
            customFileName: {
                type: String,
                required: true
            },
            publicFileUrl: {
                type: String,
                required: false
            }
        }, { _id: false })

        return {
            taskId: {
                type: String,
                index: true,
                required: true,
            },
            userId: {
                type: String,
                index: true,
                required: true
            },
            segmentId: {
                type: String,
                index: true,
                required: true
            },
            segmentExecutionType: {
                type: String,
                required: false
            },
            creativeIds: {
                type: [ String ],
                required: true
            },
            campaignId: {
                type: String,
                index: true,
                required: true
            },
            forceSend: {
                type: Boolean,
                required: false
            },
            expiry: {
                type: Date,
                required: false
            },
            appId: {
                type: String,
                required: false
            },
            globalTags: {
                type: Object,
                required: false
            },
            jobs: {
                type: [ NotificationSegmentTaskJobSchema ],
                required: false,
                default: [] as any[]
            },
            status: {
                type: String,
                index: true,
                enum: getEnumValues(NotificationSegmentTaskStatusEnum),
                required: true
            },
            totalBatches: {
                type: Number,
                required: false
            },
            batchesProcessed: {
                type: Number,
                required: false
            },
            globalAttachments: {
                type: [ AttachmentSchema ],
                required: false
            },
            metaTags: { // TODO: Deprecated, remove this
                type: Object,
                required: false
            },
            globalMetaTags: {
                type: Object,
                required: false
            },
            creativeSpecificMetaTags: {
                type: Object,
                required: false
            },
            fallbackCreativeIds: {
                type: Object,
                required: false
            }
        }
    }

    protected getAllCompositeIndexes(): CompositeIndex[] {
        return [
            { "jobs.type": 1, "jobs.jobId": 1 }
        ]
    }
}
