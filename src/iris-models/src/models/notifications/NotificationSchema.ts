import { injectable, inject } from "inversify"
import { MONGO_TYPES, MultiMongooseAccess, MultiMongooseSchema } from "@curefit/mongo-utils"
import { NotificationModel } from "./NotificationModel"

@injectable()
export class NotificationSchema extends MultiMongooseSchema<NotificationModel> {

    constructor(
        @inject(MONGO_TYPES.MultiMongooseAccess) mongooseAccess: MultiMongooseAccess
    ) {
        super(mongooseAccess, "Notification", "DEFAULT")
    }

    protected schema() {

        return {
            notificationId: {
                type: String,
                required: true,
                index: true,
                unique: true
            },
            campaignId: {
                type: String,
                required: true,
                index: true
            },
            creativeId: {
                type: String,
                required: true
            },
            userId: {
                type: String,
                required: true,
                index: true
            },
            bumbleTokenId: {
                type: String,
                required: false
            },
            failReason: {
                type: String,
                required: false
            },
            sentAt: {
                type: Date,
                required: false
            },
            receivedAt: {
                type: Date,
                required: false
            },
            processedAt: {
                type: Date,
                required: false
            },
            openedAt: {
                type: Date,
                required: false
            },
            convertedAt: {
                type: Date,
                required: false
            },
            convertedEntityId: {
                type: String,
                required: false
            },
            convertedEntityType: {
                type: String,
                required: false
            },
            externalId: {
                type: String,
                required: false
            },
            status: {
                type: String,
                required: false
            },
            userTags: {
                type: String,
                required: false
            },
            scheduledAt: {
                type: Date,
                required: false
            }
        }
    }
}
