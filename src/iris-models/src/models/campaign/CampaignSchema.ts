import { injectable, inject } from "inversify"

import { MONGO_TYPES, MultiMongooseAccess, MultiMongooseSchema } from "@curefit/mongo-utils"
import { CampaignTypes, OfferItemTypes } from "@curefit/offer-common"
import { Schema } from "mongoose"
import { ProductTypes } from "@curefit/product-common"
import { PaymentChannels } from "@curefit/payment-common"
import { CampaignModel } from "./CampaignModel"

@injectable()
export class CampaignSchema extends MultiMongooseSchema<CampaignModel> {

    constructor(
        @inject(MONGO_TYPES.MultiMongooseAccess) mongooseAccess: MultiMongooseAccess
    ) {
        super(mongooseAccess, "campaign", "DEFAULT")
    }

    protected schema() {
        const CultMemberShipExpirySchema = new Schema({
            min: {type: Number, required: true},
            max: {type: Number, required: true}
        })
        const OfferConstraintSchema = new Schema({
            daysOfWeek: {type: [Number], required: false},
            cultMemberOnly: {type: Boolean, required: false},
            cultMemberShipExpiry: {type: CultMemberShipExpirySchema, required: false},
            paymentChannel: {type: String, enum: PaymentChannels, required: false},
            minimumAmount: {type: Number, required: false},
            numTickets: {type: Number, required: false}
        })
        return {
            campaignId: {type: String, required: true, index: true, unique: true},
            campaignType: {type: String, enum: CampaignTypes, required: true},
            title: {type: String, required: true},
            description: {type: String, required: true},
            count: {type: Number, required: true},
            userCount: {type: Number, required: true},
            startDate: {type: Date, required: true},
            endDate: {type: Date, required: true},
            discount: {type: Number, required: false},
            price: {type: Number, required: false},
            priceDiscount: {type: Number, required: false},
            cashbackAmount: {type: Number, required: false},
            cashbackWalletId: {type: String, required: false},
            numberOfCoupons: {type: Number, required: false},
            createdDays: {type: [String], required: false},
            active: {type: Boolean, required: true},
            productIds: {type: [String], required: false},
            constraints: {type: OfferConstraintSchema, required: false},
            packOffer: {type: Boolean, required: false},
            priority: {type: Number, required: false},
            categoryIds: {type: [String], required: false},
            autoApply: {type: Boolean, required: false},
            productType: {type: String, enum: ProductTypes, required: false},
            paytmPromoCampaignId: {type: String, required: false},
            mealSlots: {type: [String], required: false},
            offerItemType: {type: String, enum: OfferItemTypes, required: true},
            noDeliveryCharge: {type: Boolean, required: false},
            minimumItemPrice: {type: Number, required: false}
        }
    }

}
