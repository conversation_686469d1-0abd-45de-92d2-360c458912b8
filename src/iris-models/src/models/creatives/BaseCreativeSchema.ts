import { injectable, inject } from "inversify"
import { MONGO_TYPES, MultiMongooseAccess, MultiMongooseSchema } from "@curefit/mongo-utils"
import { BaseCreativeModel } from "./BaseCreativeModel"
import { Schema } from "mongoose"
import {
    CreativeTypes,
    EmailServiceProviders,
    SMSServiceProviders,
    CallServiceProviders,
    NotificationTypes,
    InAppNotificationTypes,
    OBDServiceProviders,
    TemplateEngines,
    WhatsappServiceProviders, registrationStatusTypes,
    C2CServiceAccount
} from "@curefit/iris-common"

@injectable()
export class BaseCreativeSchema extends MultiMongooseSchema<BaseCreativeModel> {

    constructor(
        @inject(MONGO_TYPES.MultiMongooseAccess) mongooseAccess: MultiMongooseAccess
    ) {
        super(mongooseAccess, "Creative", "DEFAULT")
    }

    protected schema() {

        // ==========================================================
        // EMAIL
        // ==========================================================

        const EmailCreativeSchema = new Schema({
            from: {
                type: String,
                required: false,
            },
            fromName: {
                type: String,
                required: false,
            },
            serviceProvider: {
                type: String,
                enum: EmailServiceProviders,
                required: true
            },
            templateEngine: {
                type: String,
                enum: TemplateEngines,
                required: false
            }
        }, { _id: false })

        // ==========================================================
        // OBD
        // ==========================================================
        const KaleyraProviderSchema = new Schema({
        }, { _id: false })

        const OBDServiceProviderSchema = new Schema({
            solutionsInfini: {
                type: KaleyraProviderSchema,
                required: false
            },
        }, { _id: false })

        const OBDCreativeSchema = new Schema({
            sender: {
                type: String,
                required: false,
            },
            serviceProvider: {
                type: String,
                enum: OBDServiceProviders,
                required: true
            },
            useCaseId: {
                type: String,
                required: true
            },
            serviceProviderInfo: {
                type: OBDServiceProviderSchema,
                required: false
            }
        }, { _id: false })


        // ==========================================================
        // PUSH NOTIFICATION
        // ==========================================================
        const ActionSchema = new Schema({
            title: {
                type: String,
                required: false
            },
            actionType: {
                type: String,
                required: false
            },
            meta: {
                type: Object,
                required: false
            },
            url: {
                type: String,
                required: false
            }
        }, { _id: false })

        const chronometerPropertiesSchema = new Schema({
            showChronometer : {
                type: Boolean,
                required: true
            },
            chronometerDirection : {
                type: String,
                required: true
            },
            chronometerTimestamp : {
                type: Number,
                required: true
            },
            overrideTimestampValue : {
                type: String,
                required: false
            }
        }, {_id: false})

        const PNCreativeSchema = new Schema({
            sound: {
                type: String,
                required: true,
            },
            androidChannelId: {
                type: String,
                required: false,
            },
            icon: {
                type: String,
                required: true,
            },
            action: {
                type: String,
                required: true,
            },
            image: {
                type: String,
                required: false,
            },
            minAppVersion: {
                type: Number,
                required: false,
            },
            maxAppVersion: {
                type: Number,
                required: false,
            },
            notificationType: {
                type: String,
                enum: NotificationTypes,
                default: "DEFAULT",
                required: false
            },
            templateEngine: {
                type: String,
                enum: TemplateEngines,
                required: false
            },
            timeToLiveInSeconds: {
                type: Number,
                required: false
            },
            subTitle: {
                type: String,
                required: false
            },
            smallIcon: {
                type: String,
                required: false
            },
            groupKey: {
                type: String,
                required: false
            },
            actions: {
                type: [ ActionSchema ],
                required: false
            },
            sortKey: {
                type: String,
                required: false
            },
            overwriteKey: {
                type: String,
                required: false
            },
            category: {
                type: String,
                required: false
            },
            chronometerProperties: {
                type: chronometerPropertiesSchema,
                required: false
            },
            silentNotification: {
                type: Boolean,
                required: false
            },
            osName: {
                type: String,
                required: false
            },
            timeoutAfter: {
                type: Number,
                required: false
            },
            ongoing: {
                type: Boolean,
                required: false
            }
        }, { _id: false })

        // ==========================================================
        // WHATSAPP
        // ==========================================================

        const ValueFirstProviderSchema = new Schema({
        }, { _id: false })

        const WhatsappServiceProviderSchema = new Schema({
            valueFirst: {
                type: ValueFirstProviderSchema,
                required: false
            },
        }, { _id: false })

        const WhatsappCreativeSchema = new Schema({
            fromNumber: {
                type: String,
                required: false,
            },
            serviceProvider: {
                type: String,
                enum: WhatsappServiceProviders,
                required: true
            },
            templateId: {
                type: String,
                required: true
            },
            backupTemplateId: {
                type: String,
                required: false
            },
            backupBody: {
                type: String,
                required: false
            },
            kaleyraHeaderType: {
                type: String,
                required: false
            },
            website_url_dynamic: { 
                type: String,
                required: false
            },
            backup_website_url_dynamic: {
                type: String,
                required: false
            }
        }, { _id: false })

        // ==========================================================
        // SMS
        // ==========================================================
        const SolInfiniProviderSchema = new Schema({
        }, { _id: false })

        const SMSServiceProviderSchema = new Schema({
            solutionsInfini: {
                type: SolInfiniProviderSchema,
                required: false
            },
        }, { _id: false })

        const SOIPProperties = new Schema({
            title: {
                type: String,
                required: true
            },
            category: {
                type: String,
                required: true
            },
            imageUrl: {
                type: String,
                required: true
            },
            actionUrl: {
                type: String,
                required: true
            },
            actionText: {
                type: String,
                required: true
            }
        })

        const SMSCreativeSchema = new Schema({
            sender: {
                type: String,
                required: false,
            },
            serviceProvider: {
                type: String,
                enum: SMSServiceProviders,
                required: true
            },
            serviceProviderInfo: {
                type: SMSServiceProviderSchema,
                required: false
            },
            templateEngine: {
                type: String,
                enum: TemplateEngines,
                required: false
            },
            templateId: {
                type: String,
                required: false
            },
            soipProperties: {
                type: SOIPProperties,
                required: false
            }
        }, { _id: false })

        // ==========================================================
        // IN APP NOTIFICATION
        // ==========================================================
        const InAppNotificationCreativeSchema = new Schema({
            actionTitle: {
                type: String,
                required: true,
                maxlength: 20
            },
            actionUrl: {
                type: String,
                required: true,
            },
            expiry: {
                type: String,
                required: true,
            },
            scheduledAt: {
                type: String,
                required: false,
            },
            type: {
                type: String,
                enum: InAppNotificationTypes,
                required: true
            },
            image: {
                type: String,
                required: false
            },
            textColor: {
                type: String,
                required: true,
                default: "#505050"
            },
            backgroundColor: {
                type: String,
                required: true,
                default: "#f9f9f9"
            },
            templateEngine: {
                type: String,
                enum: TemplateEngines,
                required: false
            }
        }, { _id: false })


        // ==========================================================
        // CLICK TO CALL NOTIFICATION
        // ==========================================================
        const CallServiceProviderSchema = new Schema({
            solutionsInfini: {
                type: SolInfiniProviderSchema,
                required: false
            },
        }, { _id: false })

        const CallCreativeSchema = new Schema({
            serviceProvider: {
                type: String,
                enum: CallServiceProviders,
                required: true
            },
            serviceProviderInfo: {
                type: CallServiceProviderSchema,
                required: false
            },
            isConfidential: {
                type: Boolean,
                required: false
            },
            serviceAccount : {
                type: String,
                enum: C2CServiceAccount,
                required: false
            }
        }, { _id: false })

        // ==========================================================
        // SLACK
        // ==========================================================

        const SlackCreativeSchema = new Schema({
            blocksJsonAsString: {
                type: String,
                required: true
            }

        }, { _id: false })

        return {
            creativeId: {
                type: String,
                required: true,
                index: true,
                unique: true
            },
            name: {
                type: String,
                required: true,
                unique: true,
                sparse: true
            },
            type: {
                type: String,
                enum: CreativeTypes,
                required: true
            },
            title: {
                type: String,
                required: true,
            },
            body: {
                type: String,
                required: false,
            },
            registrationStatus: {
                type: String,
                enum: registrationStatusTypes,
                required: false,
            },
            author: {
                type: String,
                required: false
            },
            objective: {
                type: String,
                required: false
            },
            utmCampaignIds: {
                type: [String],
                required: false
            },
            pushNotificationParams: {
                type: PNCreativeSchema,
                required: false
            },

            emailParams: {
                type: EmailCreativeSchema,
                required: false
            },

            smsParams: {
                type: SMSCreativeSchema,
                required: false
            },

            obdParams: {
                type: OBDCreativeSchema,
                required: false
            },

            whatsappParams: {
                type: WhatsappCreativeSchema,
                required: false
            },

            inAppNotificationParams: {
                type: InAppNotificationCreativeSchema,
                required: false
            },

            clickToCallParams: {
                type: CallCreativeSchema,
                required: false
            },

            slackParams: {
                type: SlackCreativeSchema,
                required: false
            }

        }
    }
}
