import { injectable, inject } from "inversify"
import { MONGO_TYPES, MultiMongooseAccess, MultiMongooseSchema } from "@curefit/mongo-utils"
import { CommunicationCampaignModel } from "./CommunicationCampaignModel"
import {CampaignTypes, Verticals} from "@curefit/iris-common"
import {Schema} from "mongoose"

@injectable()
export class CommunicationCampaignSchema extends MultiMongooseSchema<CommunicationCampaignModel> {

    constructor(
        @inject(MONGO_TYPES.MultiMongooseAccess) mongooseAccess: MultiMongooseAccess
    ) {
        super(mongooseAccess, "CommunicationCampaign", "DEFAULT")
    }

    protected schema() {
        const MinMaxAppVersionSchema = new Schema({
            minAppVersion: {
                type: Number,
                required: false,
            },
            maxAppVersion: {
                type: Number,
                required: false,
            }
        }, { _id: false })

        return {
            campaignId: {
                type: String,
                required: true,
                index: true,
                unique: true
            },
            name: {
                type: String,
                required: true
            },
            description: {
                type: String,
                required: false
            },
            type: {
                type: String,
                enum: CampaignTypes,
                required: true
            },
            vertical: {
                type: String,
                enum: Verticals,
                required: true
            },
            startDate: {
                type: Date,
            },
            endDate: {
                type: Date,
            },
            requiredAppVersion: {
                type: MinMaxAppVersionSchema,
                required: false
            }
        }
    }
}
