import {inject, injectable} from "inversify"
import { CounterDataLayer, TokenData } from "../services/CounterDataLayer"
import { Redis, Cluster } from "ioredis"
import {MultiRedisAccess, REDIS_TYPES} from "@curefit/redis-utils"
import {BASE_TYPES, ILogger} from "@curefit/base"

@injectable()
export class RedisCounterDataLayerImpl implements CounterDataLayer {

    constructor(
        @inject(REDIS_TYPES.MultiRedisAccess) multiRedisAccess: MultiRedisAccess,
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
    ) {
        this.redisClient = multiRedisAccess.getConnection("BUMBLE")
    }

    private redisClient: Redis | Cluster

    private exists(key: string): Promise<boolean> {
        return new Promise((resolve, reject) => {
            this.redisClient.exists(key, (err: any, data: any) => {
                if (err) {
                    return reject(err)
                } else {
                    return resolve(!!data)
                }
            })
        })
    }
    createTokenData(key: string, hsh: TokenData, expireTime: number): Promise<boolean> {
        return new Promise((resolve, reject) => {
            let multi = this.redisClient.multi()
            multi.hset(key, "namespace", hsh.namespace)
            multi.hset(key, "entityId", hsh.entityId)
            multi.hset(key, "day", hsh.day + "")
            multi.hset(key, "keys", hsh.keys.join(","))
            multi.expireat(key, expireTime)
            multi.exec((err, data: any) => {
                if (err) {
                    return reject(err)
                } else {
                    return resolve(data)
                }
            })
        })
    }

    getCounters(key: string): Promise<{ [p: string]: number }> {
        return new Promise((resolve, reject) => {
            this.redisClient.hgetall(key, (err: any, data: any) => {
                if (err) {
                    return reject(err)
                } else {
                    if (!data) {
                        return resolve({})
                    }
                    let ret: { [p: string]: number } = {}
                    for (let key of Object.keys(data)) {
                        ret[key] = +(data[key])
                    }
                    return resolve(ret)
                }
            })
        })
    }

    getTokenData(key: string): Promise<TokenData> {
        return new Promise((resolve, reject) => {
            this.redisClient.hgetall(key, (err: any, data: any) => {
                if (err) {
                    return reject(err)
                } else {
                    if (!data) {
                        return resolve(<TokenData>{})
                    }
                    return resolve({
                        namespace: data["namespace"],
                        entityId: data["entityId"],
                        day: +data["day"],
                        keys: !!data["keys"] ? data["keys"].split(",") : null
                    })
                }
            })
        })
    }

    delete(key: string): Promise<boolean> {
        return new Promise((resolve, reject) => {
            this.redisClient.del(key, (err: any, data: any) => {
                if (err) {
                    return reject(err)
                } else {
                    return resolve(data)
                }
            })
        })
    }

    incrementCounters(hashName: string, hashKeys: string[], expireTime: number): Promise<boolean> {
        return new Promise((resolve, reject) => {
            let multi = this.redisClient.multi()
            for (let key of hashKeys) {
                multi.hincrby(hashName, key, 1)
            }
            multi.expireat(hashName, expireTime)
            multi.exec((err, data: any) => {
                if (err) {
                    return reject(err)
                } else {
                    return resolve(data)
                }
            })
        })
    }


    decrementCounters(hashName: string, hashKeys: string[]): Promise<boolean> {
        return new Promise(async (resolve, reject) => {
            let multi = this.redisClient.multi()
            for (let key of hashKeys) {
                multi.hincrby(hashName, key, -1)
            }
            if (await this.exists(hashName)) {
                multi.exec((err, data) => {
                    if (err) {
                        return reject(err)
                    } else {
                        return resolve(true)
                    }
                })
            } else {
                return resolve(false)
            }
        })
    }


}
