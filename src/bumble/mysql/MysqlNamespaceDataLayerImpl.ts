import { inject, injectable } from "inversify"
import {DailyWeeklyLimits, NamespaceDataLayer} from "../services/NamespaceDataLayer"
import {Namespace} from "../services/BumbleService"
import {ConflictError} from "@curefit/error-client"
import {MYSQL_TYPES, MultiMysqlAccess} from "@curefit/mysql-utils"
import * as moment from "moment"
import * as knex from "knex"

@injectable()
export class MysqlNamespaceDataLayerImpl implements NamespaceDataLayer {

    private connection: knex
    private namespaceCache: {[key: string]: {expiry: number, val: Namespace}} = {}
    private dimensionsCache: {[key: string]: {expiry: number, val: string[]}} = {}
    private limitsCache: {[key: string]: {expiry: number, val: DailyWeeklyLimits}} = {}

    private static CACHE_EXPIRY_TIME = 60

    constructor(@inject(MYSQL_TYPES.MultiMysqlAccess) private mysqlConn: MultiMysqlAccess) {
        this.connection = mysqlConn.getMysqlConnection()
    }

    async saveNamespace(namespace: Namespace): Promise<number> {
        try {
            return (await this.connection.table("BumbleNamespace").insert({name: namespace.name, type: namespace.type.toString()}))
        } catch (err) {
            if (err.code === "ER_DUP_ENTRY") {
                throw new ConflictError("Namespace " + namespace.name + " already exists")
            } else {
                throw (err)
            }
        }
    }

    async getNamespaces(): Promise<Namespace[]> {
        return this.connection.table("BumbleNamespace").where({})
    }

    async getNamespace(namespace: string): Promise<Namespace> {
        if (this.namespaceCache[namespace] != null && this.namespaceCache[namespace].expiry > (moment().unix())) {
            return this.namespaceCache[namespace].val
        }
        let ns = (await this.connection.table("BumbleNamespace").where({name: namespace}).limit(1))[0]
        this.namespaceCache[namespace] = {expiry: moment().unix()  + MysqlNamespaceDataLayerImpl.CACHE_EXPIRY_TIME, val: ns}
        return ns
    }

    async getDimensions(namespace: string): Promise<string[]> {
        if (this.dimensionsCache[namespace] != null && this.dimensionsCache[namespace].expiry > (moment().unix())) {
            return this.dimensionsCache[namespace].val
        }
        let dims = (<{name: string}[]>(await this.connection.table("BumbleDimension").select("name").where({"namespaceName": namespace}).orderBy("id", "asc"))).map(d => d["name"])
        this.dimensionsCache[namespace] = {expiry: moment().unix() +  + MysqlNamespaceDataLayerImpl.CACHE_EXPIRY_TIME, val: dims}
        return dims
    }

    async addDimensions(namespace: string, dimensions: string[]): Promise<boolean> {
        try {
            let dimObjs = []
            for (const dim of dimensions) {
                dimObjs.push({namespaceName: namespace, name: dim})
            }
            await this.connection.table("BumbleDimension").insert(dimObjs)
            return Promise.resolve(true)
        } catch (err) {
            if (err.code === "ER_DUP_ENTRY") {
                throw new ConflictError("Dimension already exists")
            }
        }
    }

    async setWeeklyLimits(namespace: string, limits: DailyWeeklyLimits): Promise<boolean> {
        return this.connection.table("BumbleLimits").insert({namespaceName: namespace, limits: JSON.stringify(limits)})
    }

    async getDefaultDailyWeeklyLimits(namespace: string): Promise<DailyWeeklyLimits> {
        if (this.limitsCache[namespace] != null && this.limitsCache[namespace].expiry > (moment().unix())) {
            return this.limitsCache[namespace].val
        }
        let limits: DailyWeeklyLimits
        let databaseLimits =
            await this.connection.table("BumbleLimits")
                .select("limits")
                .where({namespaceName: namespace})
                .orderBy("id", "desc")
                .limit(1)

        if (databaseLimits.length === 0) {
            limits = {daily: {}, weekly: {}}
        } else {
            limits = JSON.parse(databaseLimits[0]["limits"])
        }
        this.limitsCache[namespace] = {expiry: moment().unix() + MysqlNamespaceDataLayerImpl.CACHE_EXPIRY_TIME, val: limits}
        return limits
    }

}
