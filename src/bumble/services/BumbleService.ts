export enum NamespaceType {
    D<PERSON><PERSON>Y_WEEKLY_ROLLING
}

export enum TimeType {
    DAY,
    WEEK
}

export class Namespace {
    name: string
    type: NamespaceType.DAILY_WEEKLY_ROLLING
}


export interface DimensionValue {
    dimensionName: string,
    dimensionValue: string
}

export interface Usage {
    type: TimeType.DAY | TimeType.WEEK,
    description: DimensionValue[],
    value: number
}

export interface BumbleService {

    /**
     *
     * @param namespace
     * @return whether successfully created
     */
    createNamespace(namespace: Namespace): Promise<boolean>

    getNamespaces(): Promise<Namespace[]>

    getNamespace(namespace: string): Promise<Namespace>

    getDimensions(namespace: string): Promise<string[]>

    addDimensions(namespace: string, dimensions: string[]): Promise<boolean>

    addLimits(namespace: string, limits: Usage[]): Promise<boolean>

    getLimits(namespace: string): Promise<Usage[]>

    fetchUnsubscribedStatusAndToken(namespace: string, entityId: string, description:
        DimensionValue[], dryRun?: boolean, onlyCheckUnsubscribed?: boolean, appId?: string): Promise<{ token: string, unsubscribed: boolean }>

    tokenConsumed(tokenID: string): Promise<boolean>

    tokenUnused(tokenID: string): Promise<boolean>

    getCounters(namespace: string, entityId: string): Promise<Usage[]>

}