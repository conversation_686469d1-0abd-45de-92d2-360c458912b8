export interface TokenData {
    namespace: string
    entityId: string
    day: number
    keys: string[]
}

export interface CounterDataLayer {

    createTokenData(key: string, hsh: TokenData, expireTime: number): Promise<boolean>

    getCounters(key: string): Promise<{[ key: string]: number}>

    getTokenData(key: string): Promise<TokenData>

    delete(key: string): Promise<boolean>

    incrementCounters(hashName: string, hashKeys: string[], expireTime: number): Promise<boolean>

    decrementCounters(hashName: string, hashKeys: string[]): Promise<boolean>


}
