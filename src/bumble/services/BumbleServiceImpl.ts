import {DataError, NotFoundError} from "@curefit/error-client"
import { <PERSON>ogger, BASE_TYPES } from "@curefit/base"

import { inject, injectable } from "inversify"
import * as moment from "moment"
import * as _ from "lodash"
import {v4 as uuid} from "uuid"

import TYPES from "../../server/ioc/types"
import Constants from "../constants/constants"
import {DailyWeeklyLimits, NamespaceDataLayer} from "./NamespaceDataLayer"
import {BumbleService, Usage, DimensionValue, NamespaceType, Namespace, TimeType} from "./BumbleService"
import {CounterDataLayer} from "./CounterDataLayer"
import PowersetGenerator from "../utils/PowersetGenerator"
import UserProfileAttributeUtils from "../../server/utils/UserProfileAttributeUtils"
import {ERROR_COMMON_TYPES, RollbarService} from "@curefit/error-common"
import FeatureUtils from "../../server/utils/FeatureUtils"
import ServerConstants from "../../server/constants"

//TODO fix non reflecting http error codes
@injectable()
export class BumbleServiceImpl implements  BumbleService {

    constructor(
        @inject(TYPES.NamespaceDataLayer) private namespaceDataLayer: NamespaceDataLayer,
        @inject(TYPES.CounterDataLayer) private counterDataLayer: CounterDataLayer,
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(TYPES.FeatureUtils) private featureUtils: FeatureUtils,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(TYPES.UserProfileAttributeUtils) private userProfileAttributeUtils: UserProfileAttributeUtils,
    ) {}

    private concatKeys(keys: string[]): string {
        return keys.join(Constants.SEPARATOR)
    }

    private addDimensionValueToKey(key: string, dimVal: string, dimIndex: number): string {
        let dimIndexedKey = dimIndex + Constants.INDEX_SEPARATOR + dimVal
        return this.concatKeys([key, dimIndexedKey])

    }

    getEntityKey(namespace: string, entity: string, identifier: number) {
        return this.concatKeys([namespace, Constants.ENTITY, entity, Constants.IDENTIFIER, identifier + ""])
    }

    getTokenKey(token: string): string {
        return this.concatKeys([Constants.TOKEN, token])
    }

    async createNamespace(namespace: Namespace): Promise<boolean> {
        if ((await this.namespaceDataLayer.saveNamespace(namespace)) > 0) {
            return Promise.resolve(true)
        }
        return Promise.resolve(false)
    }

    async getNamespaces(): Promise<Namespace[]> {
        return this.namespaceDataLayer.getNamespaces()
    }

    async getNamespace(namespace: string): Promise<Namespace> {
        return this.namespaceDataLayer.getNamespace(namespace)
    }

    async checkNamespaceExists(namespace: string): Promise<boolean> {
        return !!(await this.namespaceDataLayer.getNamespace(namespace) )
    }

    async getDimensions(namespace: string): Promise<string[]> {
        if (await this.checkNamespaceExists(namespace)) {
            return (await this.namespaceDataLayer.getDimensions(namespace))
        }
        return Promise.resolve(null)
    }

    async addDimensions(namespace: string, dimensions: string[]): Promise<boolean> {
        if (!(await this.checkNamespaceExists(namespace))) {
            throw new NotFoundError("Namespace " + namespace + " not found")
        }
        return this.namespaceDataLayer.addDimensions(namespace, dimensions)
    }

    async addLimits(namespace: string, limits: Usage[]): Promise<boolean> {
        if (!(await this.checkNamespaceExists(namespace))) {
            throw new NotFoundError("Namespace " + namespace + " not found")
        }

        let daily: { [key: string]: number } = {}
        let weekly: { [key: string]: number } = {}
        let dimensions = await this.getDimensions(namespace)
        let positionMap: {[ key: string]: number} = {}

        for (let i = 0; i < dimensions.length; i++) {
            positionMap[dimensions[i]] = i
        }

        for (const limit of limits) {
            let sortedDimensions = _.sortBy(limit.description, function (dim) {
                return positionMap[dim.dimensionName]
            })

            let k: string = this.getCounterKeyFromSortedDimensions(positionMap, sortedDimensions)
            if (limit.type === TimeType.WEEK) {
                weekly[k] = limit.value
            } else if (limit.type === TimeType.DAY) {
                daily[k] = limit.value
            } else {
                throw new DataError("Unknown type " + limit.type)
            }
        }
        return this.namespaceDataLayer.setWeeklyLimits(namespace, {daily: daily, weekly: weekly})
    }

    async getLimits(namespace: string): Promise<Usage[]> {
        const {daily, weekly} = await this.namespaceDataLayer.getDefaultDailyWeeklyLimits(namespace)
        const dimensions: string[] = await this.getDimensions(namespace)

        let limits: Usage[] = []

        for (let counterKey of Object.keys(daily)) {
            const dimensionsArray: DimensionValue[] = await this.getDimensionsFromCounterKey(counterKey, dimensions)
            limits.push({type: TimeType.DAY, description: dimensionsArray, value: daily[counterKey]})
        }

        for (let counterKey of Object.keys(weekly)) {
            const dimensionsArray: DimensionValue[] = await this.getDimensionsFromCounterKey(counterKey, dimensions)
            limits.push({type: TimeType.WEEK, description: dimensionsArray, value: weekly[counterKey]})
        }

        return limits
    }

    /*
    use this version to support weekly counters

    private async getCounterDetails(namespace: string, entityId: string, day: number): Promise<{
        day: number,
        dailyCounters: {[ key: string]: number},
        weeklyCounters: {[ key: string]: number}
    }> {

        let counters: {[ key: string]: number}[] = []
        let counterPromises: Promise<{[ key: string]: number}>[] = []
        for (let i = 0; i < 7; i++ ) {
            counterPromises.push(this.counterDataLayer.getCounters(this.getEntityKey(namespace, entityId, i)))
        }

        await Promise.all(counterPromises).then(values => {
            counters.push.apply(counters, values)
        })
        const weeklyCounters = counters.reduce(function(ret, hsh) {
            return Object.keys(Object.assign({}, ret, hsh))
                .reduce(function(obj: {[key: string]: any}, k) {
                    obj[k] = (ret[k] || 0) + (hsh[k] || 0)
                    return obj
                }, {})
        }, {})
        const dailyCounters = counters[day] || {}

        return {day: day, weeklyCounters: weeklyCounters, dailyCounters: dailyCounters}
    }

     */

    private async getCounterDetails(namespace: string, entityId: string, day: number): Promise<{
        day: number,
        dailyCounters: {[ key: string]: number},
        weeklyCounters: {[ key: string]: number}
    }> {
        const dailyCounters = (await this.counterDataLayer.getCounters(this.getEntityKey(namespace, entityId, day))) || {}
        return {day: day, weeklyCounters: {}, dailyCounters: dailyCounters}
    }

    async fetchUnsubscribedStatusAndToken(namespace: string, entityId: string, description: DimensionValue[],
                                          dryRun?: boolean, onlyCheckUnsubscribed: boolean = false, appId?: string):
                                                Promise<{ token: string, unsubscribed: boolean }> {

        if (![ServerConstants.APP_ID_CUREFIT, ServerConstants.APP_ID_SUGARFIT, ServerConstants.APP_ID_CULTSPORT].includes(namespace)) {
            return {token: `invalidNamespace${namespace}`, unsubscribed: false}
        }
        const ns = await this.getNamespace(namespace)

        if (!ns) {
            const message = `BumbleService::fetchUnsubscribedStatusAndToken invalid namespace found ${namespace}`
            this.logger.error(message)
            const error = new Error(message)
            this.rollbarService.sendError(error)
            throw error
        }

        if (ns.type !== NamespaceType.DAILY_WEEKLY_ROLLING) {
            return null
        }

        let dimensions: string[] = await this.getDimensions(namespace)
        const today = new Date()
        const day = (Math.floor(moment.duration(today.getTime()).asDays()) % 7)

        // Fetch entity specific limits from user attribute store
        let entitySpecificLimits: DailyWeeklyLimits = { daily: {},  weekly: {} }
        if (this.featureUtils.isUserSpecificCommLimitEnabled()) {
            try {
                entitySpecificLimits = await this.getEntitySpecificLimits(entityId, dimensions, appId)
            } catch (err) {
                const errorString = `BumbleServiceImpl::fetchToken Failed to fetch entity specific limits for ` +
                    `entityId: ${entityId}. Error: ${err.toString()}`
                this.logger.error(errorString)
                this.rollbarService.sendError(new Error(errorString))
            }
        }

        let defaultLimits: DailyWeeklyLimits = { daily: {},  weekly: {} }
        let weeklyCounters: {[ key: string]: number} = {}
        let dailyCounters: {[ key: string]: number} = {}

        //fetch counter value and limits
        if (!onlyCheckUnsubscribed) {
            // Fetch default limits from sql
            try {
                defaultLimits = await this.namespaceDataLayer.getDefaultDailyWeeklyLimits(namespace)
            } catch (err) {
                const errorString = `BumbleServiceImpl::fetchToken Failed to fetch default limits for ` +
                    `entityId: ${entityId}. Error: ${err.toString()}`
                this.logger.error(errorString)
                this.rollbarService.sendError(new Error(errorString))
            }

            let counterDetails = await this.getCounterDetails(namespace, entityId, day)
            weeklyCounters = counterDetails.weeklyCounters
            dailyCounters = counterDetails.dailyCounters
        }

        let relevantKeys: string[] = this.getRelevantLimitKeys(dimensions, description)

        for (const relevantKey of relevantKeys) {
            const weeklyValue = weeklyCounters[relevantKey] || 0
            const dailyValue = dailyCounters[relevantKey] || 0

            // Unsubscription is marked as -1
            if (entitySpecificLimits.weekly[relevantKey] === -1 || entitySpecificLimits.daily[relevantKey] === -1) {
                this.logger.info(`BumbleServiceImpl::fetchToken User has unsubscribed for ${relevantKey}. ` +
                    `Weekly Limit: ${entitySpecificLimits.weekly[relevantKey]} ` +
                    `Daily limit: ${entitySpecificLimits.daily[relevantKey]}`)
                return { token: null, unsubscribed: true }
            }

            //check counter limits for all keys
            if (!onlyCheckUnsubscribed) {
                // For both limits, preference order: entity specific limit > default limit > no limit
                const weeklyLimit = dryRun ? Number.MAX_VALUE :
                    entitySpecificLimits.weekly[relevantKey] || defaultLimits.weekly[relevantKey] || Number.MAX_VALUE
                const dailyLimit = dryRun ? Number.MAX_VALUE :
                    entitySpecificLimits.daily[relevantKey] || defaultLimits.daily[relevantKey] || Number.MAX_VALUE

                if (weeklyValue >= weeklyLimit) {
                    this.logger.info(`BumbleServiceImpl::fetchToken Weekly counter exceeded for: ` +
                        `${relevantKey}. Weekly Limit: ${weeklyLimit} Counter value: ${weeklyValue}`)
                    return {token: null, unsubscribed: false}
                }

                if (dailyValue >= dailyLimit) {
                    this.logger.info(`BumbleServiceImpl::fetchToken Daily counter exceeded for: ` +
                        `${relevantKey}. Weekly Limit: ${dailyLimit} Counter value: ${dailyValue}`)
                    return {token: null, unsubscribed: false}
                }
            }
        }

        if (onlyCheckUnsubscribed) {
            return {token: null, unsubscribed: false}
        }

        const tokenId = uuid()
        const expireTime = moment.tz(today, "Asia/Kolkata").endOf("day").unix()
        this.logger.debug(`Granting token ${tokenId} for namespace: ${namespace} entity:${entityId} expiry: ${expireTime}`)

        await Promise.all([
            this.counterDataLayer.incrementCounters(
                this.getEntityKey(namespace, entityId, day), relevantKeys, expireTime),
            this.counterDataLayer.createTokenData(this.getTokenKey(tokenId),
                {namespace: namespace, entityId: entityId, day: day, keys: relevantKeys}, expireTime)
        ])
        return { token: tokenId, unsubscribed: false }
    }

    async tokenConsumed(tokenId: string): Promise<boolean> {
        this.logger.debug(`Consumed token: ${tokenId}`)
        return this.counterDataLayer.delete(this.getTokenKey(tokenId))
    }

    async tokenUnused(tokenId: string): Promise<boolean> {
        this.logger.debug(`BumbleServiceImpl::tokenUnused Unused token: ${tokenId}`)
        const tokenData = await this.counterDataLayer.getTokenData(this.getTokenKey(tokenId))
        this.logger.debug(`BumbleServiceImpl::tokenUnused tokenData: ${JSON.stringify(tokenData)}`)

        let {namespace, entityId, day, keys} = tokenData

        if (entityId) {
            this.logger.debug(`Processing unused token: ${tokenId} for ` +
                `${JSON.stringify({ namespace, entityId, day, keys })}`)
            this.counterDataLayer.decrementCounters(this.getEntityKey(namespace, entityId, day), keys)
            return this.counterDataLayer.delete(this.getTokenKey(tokenId))
        } else {
            this.logger.debug(`Unused token: ${tokenId} not found. Expired?`)
            return Promise.resolve(false)
        }
    }

    async getCounters(namespace: string, entityId: string): Promise<Usage[]> {
        const today = new Date()
        const day = (Math.floor(moment.duration(today.getTime()).asDays()) % 7)
        let {weeklyCounters, dailyCounters }  = await this.getCounterDetails(namespace, entityId, day)
        let dimensions = await this.getDimensions(namespace)
        let usages: Usage[] = []
        for (const key of Object.keys(weeklyCounters)) {
            usages.push({
                type: TimeType.WEEK,
                value: weeklyCounters[key],
                description: this.getDimensionsFromCounterKey(key, dimensions)
            })
        }
        for (const key of Object.keys(dailyCounters)) {
            usages.push({
                type: TimeType.DAY,
                value: dailyCounters[key],
                description: this.getDimensionsFromCounterKey(key, dimensions)
            })
        }

        return usages
    }


    private getDimensionsFromCounterKey(counterKey: string, dimensions: string[]): DimensionValue[] {
        let dimensionValues: DimensionValue[] = []

        if (counterKey !== Constants.GLOBAL) {
            for (let dimIndexedKey of counterKey.split(Constants.SEPARATOR)) {
                let split: string[] = dimIndexedKey.split(Constants.INDEX_SEPARATOR)
                dimensionValues.push({dimensionName: dimensions[+split[0]], dimensionValue: split[1]})
            }
        }

        return dimensionValues
    }

    private getCounterKeyFromSortedDimensions(positionMap: {[ key: string]: number},
                                              sortedDimensions: DimensionValue[]): string {
        let key = ""
        if (sortedDimensions.length === 0) {
            key = Constants.GLOBAL
        } else {
            for (let dim of sortedDimensions) {
                const index = positionMap[dim.dimensionName]
                if (index === -1) {
                    throw new DataError("Unknown dimension " + dim.dimensionName + ":" + dim.dimensionValue)
                }
                key = this.addDimensionValueToKey(key, dim.dimensionValue, index)
            }
            key = key.substring(2)
        }

        return key
    }

    private getRelevantLimitKeys(dimensions: string[], description: DimensionValue[]): string[] {
        let positionMap: {[ key: string]: number} = {}

        for (let i = 0; i < dimensions.length; i++) {
            positionMap[dimensions[i]] = i
        }

        let sortedDimensions = _.sortBy(description, function (dim) {
            return positionMap[dim.dimensionName]
        })
        //Preserves sorted order
        let subsets: DimensionValue[][] = PowersetGenerator.generatePowerSets(sortedDimensions)
        let keys = []

        for (let subset of subsets) {
            keys.push(this.getCounterKeyFromSortedDimensions(positionMap, subset))
        }

        return keys

    }

    private async getEntitySpecificLimits(entityId: string, dimensions: string[], appId?: string): Promise<DailyWeeklyLimits> {

        /* User attribute service stores entity level limits in the following structure.
            {
                "daily": [
                    {
                        "description": [],
                        "limit": 5
                    },
                    {
                        "description": [
                            {"dimensionName": "vertical", "dimensionValue": "CULT"},
                            {"dimensionName": "channel", "dimensionValue": "EMAIL"}
                        ],
                        "limit": 5,
                        "unsubscribed": true
                    },
                    {
                        "description": [
                            {"dimensionName": "channel", "dimensionValue": "PUSH_NOTIFICATION"}
                        ],
                        "limit": 10
                    }
                ],
                "weekly": [
                    {
                        "description": [],
                        "limit": 5
                    },
                    {
                        "description": [
                            {"dimensionName": "channel", "dimensionValue": "PUSH_NOTIFICATION"}
                        ],
                        "limit": 10
                    }
                ]
            }

            We fetch and convert to type consistent with bumble:
            {
                "daily": {
                    "GLOBAL": 10,
                    "0:SMS": 2,
                    "0:PUSH_NOTIFICATION": 2,
                    "0:EMAIL": 2
                },
                "weekly": {
                    "1:CULT__0:EMAIL": 3    // Cross product across dimensions are stored along dimensionName
                }
            }
         */

        const userLimits = await this.userProfileAttributeUtils.getCachedUserAttribute(
            entityId, UserProfileAttributeUtils.USER_PROFILE_ATTRIBUTE_KEY_COMMUNICATION_LIMITS, appId)

        this.logger.debug(`BumbleServiceImpl::getEntitySpecificLimits daily entity specific limits found: ${userLimits} string ${JSON.stringify(userLimits)}`)
        let positionMap: {[ key: string]: number} = {}
        for (let i = 0; i < dimensions.length; i++) {
            positionMap[dimensions[i]] = i
        }

        let dailyLimits, weeklyLimits

        try {
            dailyLimits = this.convertEntitySpecificLimits(
                _.get(userLimits, "daily", []), positionMap)
            this.logger.debug(`BumbleServiceImpl::getEntitySpecificLimits daily entity specific limits found: ` +
                `${JSON.stringify(_.get(userLimits, "daily", []))}. ` +
                `Converted to: ${JSON.stringify(dailyLimits)}`)
        } catch (err) {
            const errorString = `BumbleServiceImpl::getEntitySpecificLimits failed to convert daily limits. ` +
                `Entity: ${entityId}. Limits: ${JSON.stringify(_.get(userLimits, "daily", []))} ` +
                `Error: ${err.toString()}`
            this.logger.error(errorString)
            this.rollbarService.sendError(new Error(errorString))
            dailyLimits = {}
        }

        try {
            /*
            weekly limits from Rashi are not populated
            calling convertEntitySpecificLimits will use default values for unsubscription even if daily values are set
             */
            // weeklyLimits = this.convertEntitySpecificLimits(
            //     _.get(userLimits, "weekly", []), positionMap)
            weeklyLimits = {}
            this.logger.debug(`BumbleServiceImpl::getEntitySpecificLimits weekly entity specific limits found: ` +
                `${JSON.stringify(_.get(userLimits, "weekly", []))}. ` +
                `Converted to: ${JSON.stringify(weeklyLimits)}`)
        } catch (err) {
            const errorString = `BumbleServiceImpl::getEntitySpecificLimits failed to convert weekly limits. ` +
                `Entity: ${entityId}. Limits: ${JSON.stringify(_.get(userLimits, "weekly", []))} ` +
                `Error: ${err.toString()}`
            this.logger.error(errorString)
            this.rollbarService.sendError(new Error(errorString))
            weeklyLimits = {}
        }

        return {
            daily: dailyLimits,
            weekly: weeklyLimits
        }
    }

    private convertEntitySpecificLimits(userLimitExpressions: any, positionMap: {[key: string]: number}) {
        /*
            userLimitExpressions is of the following type
            P.S. Rashi does a lower on all keys
            [
                {
                    "description": [], // Empty implies global
                    "limit": 5,
                    "unsubscribed": true
                },
                {
                    "description": [
                        {"dimensionname": "vertical", "dimensionvalue": "CULT"},
                        {"dimensionname": "channel", "dimensionvalue": "EMAIL"}
                    ],
                    "limit": 5
                },
                {
                    "description": [
                        {"dimensionname": "channel", "dimensionvalue": "PUSH_NOTIFICATION"}
                    ],
                    "limit": 10
                }
            ]
            returned object is of type:
            {
                "GLOBAL": 5,
                "0:EMAIL__1:CULT": 5,
                "0:PUSH_NOTIFICATION": 10
            }
         */

        const limits: any = {}

        for (const limitExpressions of userLimitExpressions) {
            const { description, limit, unsubscribed } = limitExpressions

            const dimensionValues: DimensionValue[] = []
            for (const desc of description) {
                const { dimensionname: dimensionName, dimensionvalue: dimensionValue } = desc
                dimensionValues.push({ dimensionName, dimensionValue })
            }

            let sortedDimensionValues = _.sortBy(dimensionValues, function (dim) {
                return positionMap[dim.dimensionName]
            })

            const key = this.getCounterKeyFromSortedDimensions(positionMap, sortedDimensionValues)

            // We mark unsubscribed as -1
            limits[key] = unsubscribed ? -1 : limit
        }

        this.logger.debug(`BumbleServiceImpl::convertEntitySpecificLimits limits before defaults: ${JSON.stringify(limits)}`)

        //populating default values
        //these should ideally flow from Rashi
        for (const [key, value] of Object.entries(BumbleServiceImpl.ENTITY_SPECIFIC_DEFUALT_LIIMITS)) {
            if (!(key in limits))
                limits[key] = value
        }

        this.logger.debug(`BumbleServiceImpl::convertEntitySpecificLimits limits after defaults: ${JSON.stringify(limits)}`)
        return limits
    }

    static ENTITY_SPECIFIC_DEFUALT_LIIMITS: {[key: string]: number} = {
        // "0:WHATSAPP__2:Reminders": -1
    }
}