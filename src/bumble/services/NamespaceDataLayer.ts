import {Namespace} from "./BumbleService"

export interface DailyWeeklyLimits {
    daily: { [key: string]: number }
    weekly: { [key: string]: number }
}

export interface NamespaceDataLayer {

    saveNamespace(namespace: Namespace): Promise<number>

    getNamespaces(): Promise<Namespace[]>

    getNamespace(namespace: string): Promise<Namespace>

    getDimensions(namespace: string): Promise<string[]>

    addDimensions(namespace: string, dimensions: string[]): Promise<boolean>

    setWeeklyLimits(namespace: string, limits: DailyWeeklyLimits): Promise<boolean>

    getDefaultDailyWeeklyLimits(namespace: string): Promise<DailyWeeklyLimits>

}