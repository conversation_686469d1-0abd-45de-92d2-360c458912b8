export default class PowersetGenerator {

    public static generatePowerSets<T>(input: T[]): T[][] {
        let results = []
        let result = []
        let total = Math.pow(2, input.length)
        for (let mask = 0; mask < total; mask++) {
            result = []
            let i = 0
            do {
                if ( (mask & (1 << i)) !== 0) {
                    result.push(input[i])
                }
                i += 1
            } while (i < input.length)
            results.push(result)
        }

        return results
    }
}