-- updated on 23rd July
ALTER TABLE notification ADD COLUMN status varchar(15)
ALTER TABLE notification ADD COLUMN externalId varchar(128)
CREATE INDEX external_id_index ON notification (externalId);

-- updated on 30th december
-- added serviceProvider attempts table
CREATE TABLE `notificationAttempt` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `notificationId` varchar(128) NOT NULL DEFAULT '',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `lastUpdatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(16) DEFAULT NULL,
  `serviceProvider` varchar(64) DEFAULT NULL,
  `externalId` varchar(128) DEFAULT NULL,
  `metadata` text,
  PRIMARY KEY (`id`),
  KEY `createdAt` (`createdAt`),
  KEY `notificationId` (`notificationId`),
  <PERSON>EY `serviceProvider` (`serviceProvider`),
  KEY `externalId` (`externalId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;