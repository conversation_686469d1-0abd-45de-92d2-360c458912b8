{"name": "@curefit/iris-common", "version": "1.88.0", "types": "dist/index.d.ts", "description": "Pojos for all iris models", "main": "dist/index.js", "repository": {"type": "git", "url": "git+https://github.com/curefit/iris-common.git"}, "license": "ISC", "bugs": {"url": "https://github.com/curefit/iris-common/iriss"}, "homepage": "https://github.com/curefit/iris-common#readme", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "generate-barrels": "rm -rf dist; barrelsby --delete -e node_modules/*", "build": "tsc", "tslint": "tslint --project tsconfig.json", "tslint-fix": "tslint --project tsconfig.json --fix", "prepublishOnly": "tsc", "postversion": "git push origin master && git push origin master --tags && npm publish"}, "devDependencies": {"@types/node": "^16.11.38", "barrelsby": "^1.0.2", "tslint": "^5.16.0", "typescript": "^4.7.2"}, "dependencies": {"@curefit/base-common": "^2.42.0", "@curefit/eat-common": "^7.48.0", "@curefit/fulfilment-common": "^1.7.1", "@curefit/logging-common": "^1.3.2", "@curefit/user-common": "2.3.0", "@curefit/util-common": "^3.1.5", "@curefit/apps-common": "2.94.0"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "resolutions": {"@curefit/base": "6.14.0", "@curefit/mongo-utils": "4.5.0", "@curefit/user-common": "2.3.0", "@curefit/diy-common": "4.42.0", "@curefit/eat-common": "^7.48.0", "@curefit/aztec-models": "1.3.0", "@curefit/order-common": "5.40.1", "@curefit/care-common": "2.34.0", "@curefit/product-common": "5.10.3", "inversify": "6.0.1", "class-validator": "0.14.0", "mongoose": "6.13.8"}}