export const enum AdFormat {
    APP_INSTALL = "App Install",
    DISPLAY_LINK = "Display Link",
    CUSTOM_LINK = "Search Link"
}

export const AdsPlatforms = [AdFormat.APP_INSTALL, AdFormat.CUSTOM_LINK, AdFormat.DISPLAY_LINK]

export const enum AdsPartner {
    FACEBOOK = "facebook",
    COLLECTCENT_DIGITAL_MEDIA = "collectcent_digital_media",
    GOOGLE_ADWORDS = "google_adwords",
    HTTPOOL_DIGITAL = "httpool_digital",
    DOUBLE_CLICK = "doubleclick",
    GOOGLE_MARKETING_TEAM = "google_marketing_platform",
    IN_MOBI = "inmobi",
    IN_MOBI_OEM = "inmobi_oem",
    SNAP = "snap",
    VIA_AI = "vid_ai"
}

export const AdsPartners = {
    [AdsPartner.FACEBOOK]: {
        title: "Facebook",
        value: AdsPartner.FACEBOOK,
        params: {
            app: {
                "$3p": AdsPartner.FACEBOOK,
                deeplink_no_attribution: true
            },
            disaply: {
                "$3p": AdsPartner.FACEBOOK,
                "~ad_id": "{{ad.id}}",
                "~ad_name": "{{ad.name}}",
                "~ad_set_id": "{{adset.id}}",
                "~ad_set_name": "{{adset.name}}",
                "~campaign": "{{campaign.name}}",
                "~campaign_id": "{{campaign.id}}"
            }
        }
    },
    [AdsPartner.GOOGLE_ADWORDS]: {
        title: "Google adwords",
        value: AdsPartner.GOOGLE_ADWORDS,
        params: {
            app: {
                "$3p": AdsPartner.GOOGLE_ADWORDS,
                "always_deeplink": "false",
                "gclid": "{gclid}",
                "lpurl": "{lpurl}",
                "~ad_set_id": "{adgroupid}",
                "~campaign_id": "{campaignid}",
                "~channel": "{network}",
                "~keyword": "{keyword}",
                "~placement": "{placement}"
            },
            disaply: {
                "$3p": AdsPartner.GOOGLE_ADWORDS,
                "always_deeplink": "false",
                "gclid": "{gclid}",
                "lpurl": "{lpurl}",
                "~ad_set_id": "{adgroupid}",
                "~campaign_id": "{campaignid}",
                "~channel": "{network}",
                "~keyword": "{keyword}",
                "~placement": "{placement}"
            }
        }
    },
    [AdsPartner.COLLECTCENT_DIGITAL_MEDIA]: {
        title: "Collectcent digital media",
        value: AdsPartner.COLLECTCENT_DIGITAL_MEDIA,
        params: {
            app: {
                "$3p": AdsPartner.COLLECTCENT_DIGITAL_MEDIA,
                "~click_id": "{click_id}",
                "~cost_value": "{cost_value}"
            },
            disaply: {
                "$3p": AdsPartner.COLLECTCENT_DIGITAL_MEDIA,
                "~click_id": "{click_id}",
                "~cost_value": "{cost_value}"
            }
        }
    },
    [AdsPartner.HTTPOOL_DIGITAL]: {
        title: "Httpool digital",
        value: AdsPartner.HTTPOOL_DIGITAL,
        params: {
            app: {
                "$3p": AdsPartner.HTTPOOL_DIGITAL,
                "~click_id": "{clickid}"
            },
            disaply: {
                "$3p": AdsPartner.HTTPOOL_DIGITAL,
                "~click_id": "{clickid}"
            }
        }
    },
    [AdsPartner.DOUBLE_CLICK]: {
        title: "Double click",
        value: AdsPartner.DOUBLE_CLICK,
        params: {
            app: {
                "$3p": AdsPartner.DOUBLE_CLICK,
                "cat": "{cat}",
                "src": "{src}",
                "t_url": "{unescapedlpurl}",
                "type": "{type}"
            },
            disaply: {
                "$3p": AdsPartner.DOUBLE_CLICK,
                "cat": "{cat}",
                "src": "{src}",
                "t_url": "{unescapedlpurl}",
                "type": "{type}"
            }
        }
    },
    [AdsPartner.GOOGLE_MARKETING_TEAM]: {
        title: "Google marketing team",
        value: AdsPartner.GOOGLE_MARKETING_TEAM,
        params: {
            app: {
                "$3p": AdsPartner.GOOGLE_MARKETING_TEAM,
            },
            disaply: {
                "$3p": AdsPartner.GOOGLE_MARKETING_TEAM,
            }
        }
    },
    [AdsPartner.IN_MOBI]: {
        title: "In mobi",
        value: AdsPartner.IN_MOBI,
        params: {
            app: {
                "$3p": AdsPartner.IN_MOBI,
                "$aaid": "$GPID",
                "$idfa": "$IDA",
                "$s2s": "true",
                "device_ip": "$USER_IP",
                "subSiteId": "$SITE_APP_ID",
                "user_agent": "$UA",
                "~ad_set_id": "$ADGROUP_ID",
                "~ad_set_name": "$ADGROUP_NAME",
                "~campaign": "$CAMPAIGN_NAME",
                "~campaign_id": "$CAMPAIGN_ID",
                "~click_id": "$IMP_ID",
                "~creative_id": "$CREATIVE_ID",
                "~creative_name": "$CREATIVE_NAME",
                "~secondary_publisher": "$SITE_NAME"
            },
            disaply: {
                "$3p": AdsPartner.IN_MOBI,
                "$aaid": "$GPID",
                "$idfa": "$IDA",
                "$s2s": "true",
                "device_ip": "$USER_IP",
                "subSiteId": "$SITE_APP_ID",
                "user_agent": "$UA",
                "~ad_set_id": "$ADGROUP_ID",
                "~ad_set_name": "$ADGROUP_NAME",
                "~campaign": "$CAMPAIGN_NAME",
                "~campaign_id": "$CAMPAIGN_ID",
                "~click_id": "$IMP_ID",
                "~creative_id": "$CREATIVE_ID",
                "~creative_name": "$CREATIVE_NAME",
                "~secondary_publisher": "$SITE_NAME"
            }
        }
    },
    [AdsPartner.IN_MOBI_OEM]: {
        title: "In mobi oem",
        value: AdsPartner.IN_MOBI_OEM,
        params: {
            app: {
                "$3p": AdsPartner.IN_MOBI_OEM,
                "$aaid": "$GPID",
                "$idfa": "$IDA",
                "$s2s": "true",
                "device_ip": "$USER_IP",
                "user_agent": "$UA",
                "~ad_set_id": "$ADGROUP_ID",
                "~ad_set_name": "$ADGROUP_NAME",
                "~campaign": "$CAMPAIGN_NAME",
                "~campaign_id": "$CAMPAIGN_ID",
                "~click_id": "$IMP_ID",
                "~creative_id": "$CREATIVE_ID",
                "~creative_name": "$CREATIVE_NAME",
                "~secondary_publisher": "$SITE_NAME",
                "~sub_site_name": "$SITE_APP_ID"
            },
            disaply: {
                "$3p": AdsPartner.IN_MOBI_OEM,
                "$aaid": "$GPID",
                "$idfa": "$IDA",
                "$s2s": "true",
                "device_ip": "$USER_IP",
                "user_agent": "$UA",
                "~ad_set_id": "$ADGROUP_ID",
                "~ad_set_name": "$ADGROUP_NAME",
                "~campaign": "$CAMPAIGN_NAME",
                "~campaign_id": "$CAMPAIGN_ID",
                "~click_id": "$IMP_ID",
                "~creative_id": "$CREATIVE_ID",
                "~creative_name": "$CREATIVE_NAME",
                "~secondary_publisher": "$SITE_NAME",
                "~sub_site_name": "$SITE_APP_ID"
            }
        }
    },
    [AdsPartner.SNAP]: {
        title: "Snap",
        value: AdsPartner.SNAP,
        params: {
            app: {
                "$3p": AdsPartner.SNAP,
                "$deeplink_no_attribution": "true"
            },
            disaply: {
                "$3p": AdsPartner.SNAP,
            }
        }
    },
    [AdsPartner.VIA_AI]: {
        title: "Via.AI",
        value: AdsPartner.VIA_AI,
        params: {
            app: {
                "$3p": AdsPartner.VIA_AI,
                "~ad_set_id": "{adsetId}",
                "~campaign_id": "{campaignId}",
                "~click_id": "%%trackerId%%",
                "~cost_currency": "{currencyCode}",
                "~creative_id": "{creativeId}",
                "~secondary_publisher": "%%affiliateId%%"
            },
            disaply: {
                "$3p": AdsPartner.VIA_AI,
                "~ad_set_id": "{adsetId}",
                "~campaign_id": "{campaignId}",
                "~click_id": "%%trackerId%%",
                "~cost_currency": "{currencyCode}",
                "~creative_id": "{creativeId}",
                "~secondary_publisher": "%%affiliateId%%"
            }
        }
    }
}