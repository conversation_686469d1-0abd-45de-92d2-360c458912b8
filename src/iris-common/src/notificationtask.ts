import { SendCampaignNotificationsRequest } from "./campaign"
import { DateTimezone } from "@curefit/util-common"

export type NotificationTaskType = "SIMPLE" | "TEMPLATIZED"
export const NotificationTaskTypes = ["SIMPLE", "TEMPLATIZED"]

export type NotificationTaskStatus = "CREATED" | "SCHEDULED" | "STARTED" | "COMPLETED" | "DUPLICATE" | "INVALID" | "FAILED" | "CANCELLED"
export const NotificationTaskStatuses = ["CREATED", "SCHEDULED", "STARTED", "COMPLETED", "DUPLICATE", "INVALID", "FAILED", "CANCELLED"]

export interface SimpleNotificationTag { key: string, value: string }

export interface SimpleNotificationTask extends SegmentNotificationTask {
    creativeId: string
    campaignId: string
    userIds: string[]
    globalTags?: [SimpleNotificationTag]
}

export interface NotificationTask extends SendCampaignNotificationsRequest, SegmentNotificationTask {
    taskType: NotificationTaskType,
    status: NotificationTaskStatus,
    progress?: number,
}

export interface SegmentNotificationTask {
    schedule?: DateTimezone
    hanselSegmentIds?: string[]
    agentId: String
}