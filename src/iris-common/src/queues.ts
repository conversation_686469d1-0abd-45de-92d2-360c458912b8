export type IRIS_QUEUE_NAMES = "SES-DELIVERY-REPORT" |  "IRIS_CAMPAIGN" | "IRIS_SMS_JOBS" | "IRIS_PN_JOBS"
| "IRIS_EMAIL_JOBS" | "IRIS_OBD_JOBS" | "IRIS_IAN_JOBS" | "IRIS_CTC_JOBS" | "IRIS_WHATSAPP_JOBS" | "IRIS_EMAIL_OPEN_EVENTS" |
    "IRIS_PN_DELIVERY_EVENTS" | "MOZART_JOB_STATUS_UPDATES" | "MOZART_SEGMENT_BATCH" | "IRIS_SCHEDULED_NOTIFICATIONS" |
    "IRIS_EVENTS" | "NOTIFICATION_PROCESSOR" | "IRIS_NOTIFICATION_STATUS_UPDATE" | "IRIS_FIREBASE_APP_REMOVE_EVENTS" |
    "IRIS_CAMPAIGN_EXECUTION_NOTIFICATION" | "IRIS_EMAIL_BOUNCE_ALL" | "IRIS_SMS_CALLBACKS"

export class QueueConstants {
    private static IRIS_QUEUES = {
        "SES-DELIVERY-REPORT": "ses-delivery-report",
        "IRIS_EMAIL_OPEN_EVENTS": "iris-email-open-events",
        "IRIS_CAMPAIGN": "iris-campaign",
        "IRIS_SMS_JOBS": "iris-sms-jobs",
        "IRIS_PN_JOBS": "iris-pn-jobs",
        "IRIS_EMAIL_JOBS": "iris-email-jobs",
        "IRIS_OBD_JOBS": "iris-obd-jobs",
        "IRIS_IAN_JOBS": "iris-ian-jobs",
        "IRIS_CTC_JOBS": "iris-ctc-jobs",
        "IRIS_WHATSAPP_JOBS": "iris-whatsapp-jobs",
        "IRIS_PN_DELIVERY_EVENTS": "iris-pn-deliver-events",
        "IRIS_FIREBASE_APP_REMOVE_EVENTS": "iris-firebase-app-remove-events",
        "MOZART_JOB_STATUS_UPDATES": "iris-mozart-job-status-updates",
        "MOZART_SEGMENT_BATCH": "iris-segment-batches",
        "IRIS_SCHEDULED_NOTIFICATIONS": "iris-scheduled-notifications",
        "IRIS_EVENTS": "iris-events",
        "NOTIFICATION_PROCESSOR": "notification-processor",
        "IRIS_NOTIFICATION_STATUS_UPDATE": "iris-notification-status-update",
        "IRIS_CAMPAIGN_EXECUTION_NOTIFICATION": "iris-campaign-execution-notification-subscription",
        "IRIS_EMAIL_BOUNCE_ALL": "iris-all-bounced-emails",
        "IRIS_SMS_CALLBACKS": "iris-sms-callbacks"
    }
    public static getQueueName(queueName: IRIS_QUEUE_NAMES) {
        if (process.env.ENVIRONMENT.toLowerCase() === "production") {
            return "production-" + (<any>QueueConstants.IRIS_QUEUES)[queueName]
        } else if (process.env.ENVIRONMENT.toLowerCase() === "alpha") {
            return "alpha-" + (<any>QueueConstants.IRIS_QUEUES)[queueName]
        } else if (process.env.ENVIRONMENT.toLowerCase() === "stage") {
            return "stage-" + (<any>QueueConstants.IRIS_QUEUES)[queueName]
        } else if (process.env.ENVIRONMENT.toLowerCase() === "integration") {
            return "integration-" + (<any>QueueConstants.IRIS_QUEUES)[queueName]
        } else {
            return "dev-" + (<any>QueueConstants.IRIS_QUEUES)[queueName]
        }
    }
}
