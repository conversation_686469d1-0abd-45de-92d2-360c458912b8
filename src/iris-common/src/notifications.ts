import { CreativeType, InAppNotificationType, ServiceProvider } from "./creatives"
import { CampaignType, Vertical } from "./communicationcampaigns"
import { UserTags } from "./campaign"

// all internal fail reasons should be included in notificationFailureReason list
export type NotificationFailureReason = "RATE_LIMIT" | "SEND_FAIL" | "DELIVER_FAIL" | "NOT_OPENED" | "NO_DEVICE" |
    "TEMPLATE_FAIL" | "DND" | "ATTACHMENT_FAIL" | "UNAUTHORIZED" | "UNSUBSCRIBED" | "PHONE_NOT_FOUND" |
    "EMAIL_NOT_FOUND" | "CANCELLED" | "NOT_REGISTERED" | "MESSAGE_ID_INVALID" | "UNKNOWN" | "USER_ID_MISSING" |
    "TITLE_TOO_LONG" | "THROTTLED" | "TAG_LENGTH_EXCEEDED"

export type NotificationStatus = "CREATED" | "QUEUED" | "SCHEDULED" | "SENT" | "DELIVERED" | "READ" | "FAILED" | "UNKNOWN"

export type NotificationState = "RECEIVED" | "CANCELLED" | "CLOSED" | "READ"
export const NotificationStates = ["RECEIVED", "CANCELLED", "CLOSED", "READ"]

export interface DeviceInfo {
    deviceId?: string
    advertiserId?: string
    appId?: string
    appVersion?: string
    deviceModel?: string
    osName?: string
    osVersion?: string
}

export interface Notification {
    notificationId: string
    campaignId: string
    creativeId: string
    userId: string
    taskId?: string
    bumbleTokenId?: string
    externalId?: string
    failReason?: NotificationFailureReason | string
    status?: NotificationStatus
    sentAt?: Date
    receivedAt?: Date
    processedAt?: Date
    openedAt?: Date
    convertedAt?: Date
    userIdType: string
    convertedEntityId?: string
    convertedEntityType?: string
    createdAt?: Date
    userTags?: string
    scheduledAt?: Date
    billableUnits?: number
    deviceInfo?: DeviceInfo
    appId: string,
    fallbackNotificationId?: string
    whatsappTestRunMessage?: string
}

export interface NotificationModel {
    notificationId: string
    campaignId: string
    creativeId: string
    userId: string
    taskId?: string
    bumbleTokenId?: string
    externalId?: string
    failReason?: NotificationFailureReason | string
    status?: NotificationStatus
    sentAt?: Date
    receivedAt?: Date
    processedAt?: Date
    openedAt?: Date
    convertedAt?: Date
    userIdType: string
    convertedEntityId?: string
    convertedEntityType?: string
    createdAt?: Date
    userTags?: string
    scheduledAt?: Date
    billableUnits?: number
    deviceInfo?: string
    appId: string,
    fallbackNotificationId?: string
}

export interface NotificationAttempt {
    notificationId: string
    externalId: string
    status: string
    serviceProvider: ServiceProvider
    metadata: string
}

export interface NotificationMeta {
    notificationId: string
    appId: string,
    taskId: string
    userId: string
    expiry: Date
    state: NotificationState
    type: InAppNotificationType
    scheduledAt: Date
    vertical: Vertical
    dataBlob: string
}

export interface InAppNotificationData {
    title: string
    action: {
        title: string
        url: string
    },
    image?: string,
    textColor: string,
    backgroundColor: string
}

export interface NotificationWrapper {
    notification: Notification
    userContext: UserTags
}

export interface TaskReport {
    taskId: string
    total: number
    taskStatus: {
        [status in keyof NotificationStatus]: string
    }
}

export interface UserNotificationFilterRequest {
    userIds: string[],
    startDate: Date,
    endDate: Date,
    creativeType?: CreativeType,
    vertical?: Vertical,
    campaignType?: CampaignType,
    campaignIds?: string[],
    creativeIds?: string[],
    limit?: number,
    skip?: number
}

export interface PaginatedResults<T> {
    results: T[],
    metadata: {
        limit: number,
        skip: number,
        totalCount: number
    }
}