import { Action } from "@curefit/apps-common"

export type CreativeType = "PUSH_NOTIFICATION" | "SMS" | "EMAIL" | "IN_APP_NOTIFICATION" | "OBD" | "CLICK_TO_CALL" | "WHATSAPP" | "SLACK"
export const CreativeTypes: CreativeType[] = ["PUSH_NOTIFICATION", "SMS", "EMAIL", "IN_APP_NOTIFICATION", "OBD", "CLICK_TO_CALL", "WHATSAPP", "SLACK"]

export type ServiceProvider = "SOLUTIONS_INFINI" | "KNOWLARITY" | "SES" | "KALEYRA " | "VALUE_FIRST" | "FIREBASE" | "GUPSHUP" | "KALEYRA" | "SPRINKLR" | "FONADA"
export const ServiceProviders = ["SOLUTIONS_INFINI", "KNOWLARITY", "SES", "<PERSON><PERSON><PERSON>Y<PERSON>" , "VALUE_FIRST", "FIREBASE", "GUPSHUP", "SPRINKLR"]

export type EmailServiceProvider = "SES"
export const EmailServiceProviders = ["SES"]

export type TemplateEngine = "mustache" | "handlebars"
export const TemplateEngines = ["mustache", "handlebars"]

export type SMSServiceProvider = "SOLUTIONS_INFINI" | "GUPSHUP" | "FONADA"
export const SMSServiceProviders = ["SOLUTIONS_INFINI", "GUPSHUP", "FONADA"]

export type CallServiceProvider = "SOLUTIONS_INFINI" | "KNOWLARITY"
export const CallServiceProviders = ["SOLUTIONS_INFINI", "KNOWLARITY"]

export type OBDServiceProvider = "KALEYRA"
export const OBDServiceProviders = ["KALEYRA"]

export type WhatsappServiceProvider = "VALUE_FIRST" | "KALEYRA" | "SPRINKLR"
export const WhatsappServiceProviders = ["VALUE_FIRST", "KALEYRA", "SPRINKLR"]

export type registrationStatusType = "ACTIVE" | "PENDING" | "CREATED" | "REJECTED"
export const registrationStatusTypes = ["ACTIVE", "PENDING", "CREATED", "REJECTED"]

export type C2CServiceAccount = "CUREFIT" | "SUGARFIT" | "SUGARFIT_EMR_CALLING" | "SUGARFIT_OPS_CALLING" | "SUGARFIT_EMR_CALLING_DOCTOR" | "ONEFITPLUS"
export const C2CServiceAccount = ["CUREFIT", "SUGARFIT", "SUGARFIT_EMR_CALLING", "SUGARFIT_OPS_CALLING", "SUGARFIT_EMR_CALLING_DOCTOR", "ONEFITPLUS"]

export type KaleyraWhatsAppHeaderType = "TEXT_IMAGE_DOCUMENT_VIDEO" | "NONE"
export const KaleyraWhatsAppHeaderType = ["TEXT_IMAGE_DOCUMENT_VIDEO", "NONE"]
/**
 * Common for all creatives in the system
 */
export interface BaseCreative {
    creativeId: string
    name: string
    type: CreativeType
    title: string
    body?: string
    registrationStatus?: registrationStatusType
    author?: string
    objective?: string
    // BranchIO CampaignIds associated with the creative
    utmCampaignIds?: string[]
}

export type NotificationType = "DEFAULT" | "AUTO_ACTION"
export const NotificationTypes = ["DEFAULT", "AUTO_ACTION"]
export type InAppNotificationType = "PERMISSION" | "META" | "PACK_RENEWAL" | "SINGLE" | "ONBOARDING" | "POST_CLASS" | "PACK_PURCHASED" | "NPS_SURVEY"
export const InAppNotificationTypes = ["PERMISSION", "META", "PACK_RENEWAL", "SINGLE", "ONBOARDING", "POST_CLASS", "PACK_PURCHASED", "NPS_SURVEY"]

export interface PNCreativeParams {
    sound: string

    androidChannelId?: string

    // Large icon besides the body
    icon: string

    // Deep link when user presses notification
    action: string

    // Large image with the PN
    image?: string

    minAppVersion?: number

    maxAppVersion?: number

    notificationType?: NotificationType

    templateEngine?: TemplateEngine

    timeToLiveInSeconds?: number

    subTitle?: string

    // Small icon rendered on top
    smallIcon?: string

    groupKey?: string

    actions?: Action[]

    // Used for sorting PN in notification tray
    sortKey?: string

    // Used for overwriting sent PNs
    overwriteKey?: string,

    category?: string,

    // for displaying an ongoing counting timer in the PN
    chronometerProperties?: {
        showChronometer: boolean,
        chronometerDirection: "up" | "down",
        // Epoch Time upto/from which we need to count to/from
        chronometerTimestamp: number,
        overrideTimestampValue?: string,
    },

    silentNotification?: boolean,

    // Strictly send to only one particular OS (android/ios).
    osName?: string,

    // Auto collapse time
    timeoutAfter?: number,

    // Set true for Sticky Notification
    ongoing?: boolean
}

export interface PNCreative extends BaseCreative {
    pushNotificationParams?: PNCreativeParams
}

export interface InAppNotificationParams {
    actionTitle: string
    actionUrl: string
    expiry: string
    scheduledAt: string
    type: InAppNotificationType
    image?: string
    textColor: string
    backgroundColor: string
    templateEngine?: TemplateEngine
}

export interface InAppNotificationCreative extends BaseCreative {
    inAppNotificationParams?: InAppNotificationParams
}

export interface EmailCreativeParams {
    from?: string
    fromName?: string

    serviceProvider: EmailServiceProvider
    templateEngine?: TemplateEngine
}

export interface SlackCreativeParams {
    blocksJsonAsString: string
}

export interface EmailCreative extends BaseCreative {
    emailParams?: EmailCreativeParams
}

export interface SlackCreative extends BaseCreative {
    slackParams?: SlackCreativeParams
}

export interface WhatsappCreativeParams {
    fromNumber: string
    templateId?: string
    backupTemplateId?: string
    backupBody?: string
    serviceProvider: WhatsappServiceProvider
    kaleyraHeaderType?: KaleyraWhatsAppHeaderType
    website_url_dynamic?: string
    backup_website_url_dynamic? : string
}

export interface WhatsappCreative extends BaseCreative {
    whatsappParams?: WhatsappCreativeParams
}

export interface KaleyraSPInfo {
    useClickToCallAPI?: boolean
}

export interface OBDServiceProviderInfo {
    kaleyra?: KaleyraSPInfo
}

export interface SolInfiniSPInfo {
}

export interface SMSServiceProviderInfo {
    solutionsInfini?: SolInfiniSPInfo
}

export interface SMSCreativeParams {
    sender?: string
    serviceProvider: SMSServiceProvider
    serviceProviderInfo: SMSServiceProviderInfo
    templateEngine?: TemplateEngine
    templateId?: string
    soipProperties?: SOIPProperties
}

export interface SOIPProperties {
    title: string,
    category: string,
    imageUrl: string,
    actionUrl: string,
    actionText: string
}

export interface ClickToCallCreativeParams {
    serviceProvider: CallServiceProvider,
    serviceAccount?: C2CServiceAccount,
    isConfidential: boolean
}

export interface OBDCreativeParams {
    sender?: string
    serviceProvider: OBDServiceProvider
    serviceProviderInfo: OBDServiceProviderInfo,
    useCaseId: string
}

export interface SMSCreative extends BaseCreative {
    smsParams?: SMSCreativeParams
}

export interface OBDCreative extends BaseCreative {
    obdParams?: OBDCreativeParams
}

export interface ClickToCallCreative extends BaseCreative {
    clickToCallParams?: ClickToCallCreativeParams
}
