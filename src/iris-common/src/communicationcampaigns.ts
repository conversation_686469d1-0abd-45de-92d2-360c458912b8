export type CampaignType = "TRANSACTION" | "PROMO"
export const CampaignTypes = ["TRANSACTION", "PROMO"]


export type Vertical = "EAT" | "CULT" | "MIND" | "CARE" | "CUREFIT" | "GYMFIT" | "LIVE" | "CULT<PERSON>AR" | "TRANSFORM" | "CULTSPORT"
export const Verticals = ["EAT", "CULT", "MIND", "CARE", "CUREFIT", "GY<PERSON><PERSON>", "LIVE", "CULT<PERSON><PERSON>", "TRANSFORM", "CULTSPORT"]

export type ExternalCommunicationCampaign = "INBOUND_CENTER_INQUIRY"
export const ExternalCommunicationCampaigns = ["INBOUND_CENTER_INQUIRY"]

export interface CommunicationCampaign {
    campaignId: string
    name: string
    description: string
    vertical: Vertical
    type: CampaignType
    startDate: Date
    endDate: Date
    requiredAppVersion?: MinMaxAppVersion
}

export interface MinMaxAppVersion {
    minAppVersion?: number
    maxAppVersion?: number
}
