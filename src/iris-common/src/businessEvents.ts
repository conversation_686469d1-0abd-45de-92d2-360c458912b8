import { ActivityTypeDS, ActivityState } from "@curefit/logging-common"
import { SubscriptionType } from "@curefit/base-common"
import { MealSlot, DeliveryChannel } from "@curefit/eat-common"
import { OrderSource } from "@curefit/fulfilment-common"

export type EventName = "EatOrderConfirmedEvent" | "EatOrderOffferEvent" | "EatSinglePurchasedEvent" |
    "EatPackPurchasedEvent" | "EatCartDeliveredEvent" | "EatShipmentDeliveredEvent" |
    "EatPackCompletedEvent" | "EatUserProfileUpdated" | "MyReferralAccepted" | "MyReferralConverted" |
    "LOGGING-EATFIT_MEAL" | "LOGGING-CULT_CLASS" | "LOGGING-MIND_CLASS" | "LOGGING-DIY_FITNESS" | "LOGGING-DIY_MEDITATION" |
    "LOGGING-FOOD_CONSUMPTION" | "LOGGING-WORKOUT_ACTIVITY" | "LOGGING-MEDICINE_INTAKE" | "LOGGING-MIND_WORKOUT_ACTIVITY" |
    "LOGGING-CONSULTATION" | "LOGGING-DIAGNOSTIC_TEST" | "LOGGING-GMF_FOOD_RECOMMENDATION" | "LOGGING-GMF_FITNESS_RECOMMENDATION" |
    "LOGGING-GMF_MIND_RECOMMENDATION" | "LOGGING-GMF_MEDICINE_RECOMMENDATION" | "StepsOfferActivatedEvent" | "FitclubMembershipSync" | "WorldCupOfferOrder" |
    "RewardAssigned" | "RewardRedeemed" | "CaloriesSavedEvent" | "ActivityDSEvent" | "QuestUserRewardEvent"


export type EventType = "USER_PROFILE_UPDATE" | "USER_ACTIVITY_EVENT" | "RAW" | "BULK" | "ACTIVITY_DS_EVENT" | "QUEST_USER_REWARD_EVENT"

export type MealType = "VEG" | "NON_VEG"

export type ReferralAcceptedEventStatus = "SUCCESS" | "FAILED"

export type ReferralAcceptedEventSubStatus = "ReferrerBlacklisted" | "ReferrerDisabled" | "BookingUsed" | "WalkinUser"
    | "SpecialDiscountApplied" | "ExistingUser" | "Accepted" | "ReferralAlreadyAccepted" | "GiftCardAlreadyAccepted"
    | "ReferrerPhoneNotApplicable"

export interface EatOrderConfirmedEvent extends IEventProps {
    areaId: string
    orderId: string
    slot: MealSlot
    amountPaid: number
    deliverySlot: string
    purchasedAt: Date
    startDate: Date
    numItems: number
    orderSource: OrderSource
    channel: DeliveryChannel
}

export interface EatOrderOffferEvent extends IEventProps {
    slot: MealSlot,
    areaId: string,
    orderId: string,
    deliverySlot: string,
    purchasedAt: Date,
    startDate: Date,
    offerId: string,
    campaignId: string,
    campaignType: string
}

export interface EatSinglePurchasedEvent extends IEventProps {
    areaId: string,
    slot: MealSlot,
    cuisine: string,
    isVeg: MealType,
    amountPaid: number,
    paymentChannel: string,
    purchasedAt: Date,
    productId: string
}

export interface EatPackPurchasedEvent extends IEventProps {
    areaId: string,
    slot: MealSlot,
    cuisine: string,
    isVeg: MealType,
    amountPaid: number,
    paymentChannel: string,
    packStart: Date,
    packEnd: Date,
    packValidity: number,
    numTickets: number,
    purchasedAt: Date,
    productId: string
    subscription: boolean
    autorenew: boolean
    period: SubscriptionType
}

export interface EatCartDeliveredEvent extends IEventProps {
    centerId: string,
    slot: MealSlot,
    deliveredAt: Date,
    numItems: number,
    containsPack: boolean
}

export interface EatShipmentDeliveredEvent extends IEventProps {
    centerId: string,
    shipmentId: string,
    slot: MealSlot,
    cuisine: string,
    isVeg: MealType,
    deliveredAt: Date,
    productId: string,
    isPartOfPack: boolean,
    orderSource: OrderSource
    channel: DeliveryChannel
}

export interface EatPackCompletedEvent extends IEventProps {
    centerId: string,
    slot: MealSlot,
    cuisine: string,
    isVeg: MealType,
    price: number,
    packStart: Date,
    packEnd: Date,
    packValidity: number,
    numTickets: number,
    completedAt: Date,
    productId: string,
    productTitle: string
}

export interface UserRewardEvent extends IEventProps {
    rewardType: string
    amount: number
}

export interface MyReferralAccepted extends IEventProps {
    phone: string,
    status: ReferralAcceptedEventStatus,
    response: string,
    subStatus?: ReferralAcceptedEventSubStatus
}

export interface MyReferralConverted extends IEventProps {
    conversionEvent: string,
    category: string,
    metaData: any
}

export interface BaseActivityLoggedEvent extends IEventProps {
    date: string,
    activityType: ActivityTypeDS,
    subActivityType?: string,
    status?: ActivityState,
    activityName?: string,
    score?: number
}

export interface StepsOfferActivatedEvent extends IEventProps {
    milestone: number
    discount: number
}

export interface WorldCupOfferActivatedEvent extends IEventProps {
    runsScored: number
}

export interface CaloriesSavedEvent extends IEventProps {
    caloriesSaved: number
}

export interface PurchaseEvent extends IEventProps{
    price: string,
    productId: string,
    productName: string
}

// Firebase Analytics Event Interfaces
export interface FirebaseAnalyticsEvent {
    name: string
    params: { [key: string]: any }
}

export interface FirebasePurchaseEvent extends IEventProps {
    transaction_id: string
    value: number
    currency: string
    items: FirebaseEventItem[]
    coupon?: string
    shipping?: number
    tax?: number
}

export interface FirebaseEventItem {
    item_id: string
    item_name: string
    item_category?: string
    item_category2?: string
    item_category3?: string
    item_category4?: string
    item_category5?: string
    item_brand?: string
    item_variant?: string
    price: number
    quantity: number
    currency: string
    coupon?: string
    affiliation?: string
    location_id?: string
    item_list_id?: string
    item_list_name?: string
    promotion_id?: string
    promotion_name?: string
    creative_name?: string
    creative_slot?: string
}

export interface FirebaseConversionEvent extends IEventProps {
    event_name: string
    user_id: string
    timestamp_micros: number
    user_properties?: { [key: string]: any }
    event_params?: { [key: string]: any }
}

export interface FirebaseCustomEvent extends IEventProps {
    event_name: string
    parameters: { [key: string]: any }
    user_id?: string
    session_id?: string
    engagement_time_msec?: number
}

export interface FirebaseNotificationEvent extends IEventProps {
    notification_id: string
    campaign_id: string
    creative_id: string
    event_type: 'notification_sent' | 'notification_opened' | 'notification_dismissed'
    timestamp: number
    user_id: string
    app_id: string
}

export type EventProps = EatSinglePurchasedEvent | EatPackPurchasedEvent | EatShipmentDeliveredEvent | EatPackCompletedEvent
    | MyReferralAccepted | MyReferralConverted | BaseActivityLoggedEvent | StepsOfferActivatedEvent | WorldCupOfferActivatedEvent | UserRewardEvent | CaloriesSavedEvent

export interface UserEvent {
    walletBalance?: number
    eatHasActiveBreakfastPack?: boolean
    eatHasActiveLunchPack?: boolean
    eatHasActiveSnacksPack?: boolean
    eatHasActiveDinnerPack?: boolean
    gmfLivePlanStartDateV2?: Date
    steps?: number,
    fitclubMembershipStartDate?: Date,
    fitclubMembershipEndDate?: Date,
    fitclubSavingsFitcash?: number,
    fitclubSavingsDeliveryCharges?: number,
    fitclubSavingsNumDeliveries?: number,
    fitclubTotalSavings?: number,
    fitclubNumberOfFitThalisSaved?: number,
    fitclubCultMindTotalFitcash?: number,
    runs?: number
    totalCaloriesSaved?: number,
    eatPNCoveredTill?: Date
    unredeemedPasses?: number,
    isEatSuperUser?: boolean,
    isCultSuperUser?: boolean,
    isEatAndCultSuperUser?: boolean
}

export interface ActivityEvent {
    userId: string,
    eventName: EventName,
    eventAt: number,
    eventProperties: EventProps
}

export interface UserProfileEvent extends UserEvent {
    userId: string
}

export interface BulkEvent {
    activityEvents?: ActivityEvent[],
    userEvents?: UserProfileEvent[]
}

export interface IEventProps {
}

export class BusinessEventsConstants {

    public static getEventNameKey() {
        return "EVENT_NAME"
    }

    public static getEventTypeKey() {
        return "EVENT_TYPE"
    }

    public static getEventAtKey() {
        return "EVENT_AT"
    }

    public static getUserIdKey() {
        return "USER_ID"
    }

    public static getQueueName() {
        return "BUSINESS_EVENTS"
    }

}
