export type TemplateType = "HTML_TO_PDF" | "CSV"
export const TemplateTypes = ["HTML_TO_PDF", "CSV"]

export interface Template {
    templateId: string
    name: string
    type: TemplateType
    defaultFileName: string
    templateBody?: string
}

export interface TemplateAttachments {
    templateId: string,
    tags?: { [tagName: string]: string },
    fields?: any[],
    customFileName?: string,
    doNotUploadToS3?: boolean,
    pdfProperties?: { [key: string]: string }
}

export interface PatternForAttributes {
    prefix: string,
    suffix: string,
    regex: RegExp
}