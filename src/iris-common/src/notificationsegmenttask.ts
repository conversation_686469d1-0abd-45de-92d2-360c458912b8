import { Attachment} from "./campaign"

export enum NotificationSegmentTaskStatusEnum {
    CREATED = "CREATED",
    PROCESSING = "PROCESSING",
    SENDING = "SENDING",
    COMPLETED = "COMPLETED",
    FAILED = "FAILED",
    KILLED = "KILLED",
    EXPIRED = "EXPIRED"
}

export enum NotificationSegmentTaskJobTypeEnum {
    TRANSIENT_SEGMENT_DOWNLOAD_ATHENA = "TRANSIENT_SEGMENT_DOWNLOAD_ATHENA",
    SEGMENT_DOWNLOAD = "SEGMENT_DOWNLOAD",
    BATCH_PROCESS_QUEUE = "BATCH_PROCESS_QUEUE"
}

export interface NotificationSegmentTaskJob {
    jobId?: string
    type: NotificationSegmentTaskJobTypeEnum
}

export interface NotificationSegmentTask {
    taskId: string
    userId: string
    segmentId: string
    segmentExecutionType?: string
    creativeIds: string []
    campaignId: string
    forceSend?: boolean
    expiry?: Date
    appId?: string
    globalTags?: { [tagName: string]: string }
    globalAttachments: Attachment[]
    jobs: NotificationSegmentTaskJob[]
    status: NotificationSegmentTaskStatusEnum
    totalBatches?: number
    batchesProcessed?: number
    globalMetaTags?: any
    creativeSpecificMetaTags?: any
    metaTags?: any // TODO: deprecate this,
    fallbackCreativeIds?: { [creativeId: string]: string }
}