export type ClickToCallStatus = "ATTEMPTED" | "RINGED" | "CONNECTED" | "FAILED"
export type ClickToCallLegStatus = "ANSWER" | "DISCONNECTED" | "MISSED" | "BUSY" | "FAILED"

export interface ClickToCallReport {
    caller: string
    receiver: string
    status: ClickToCallStatus
    statusCaller?: ClickToCallLegStatus
    statusReceiver?: ClickToCallLegStatus
    startTime?: Date
    duration?: number
    recordPath?: string
    externalId?: string
    metadata?: string
}

export interface InboundCallReport {
    caller: string
    receiver?: string
    virtualNumber: string
    status: string
    statusCaller: string
    statusReceiver?: string
    startTime: Date
    duration: number
    recordPath?: string
    metadata?: string
}

export interface NotificationReport {
    notificationId: string,
    clickToCallReport?: ClickToCallReport
}

export interface EmailValidResponse {
    email: string,
    isValidEmail: boolean,
    deepEmailStatusCheck: boolean
}