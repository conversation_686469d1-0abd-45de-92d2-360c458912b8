import { TemplateAttachments } from "./templates"
import { StartEndTime } from "@curefit/base-common"
import { Country } from "./generic"
import { NotificationState, NotificationStatus } from "./notifications"

export interface GetLastSentMessageRequest {
    campaignId: string
    creativeId: string
    userIds: [string]
}

export interface GetLastSentMessageRespose {
    [userId: string]: Date
}

export interface Attachment {
    s3Info?: {
        bucket: string,
        key: string
    }
    publicFileUrl?: string
    fileUrl?: string,
    customFileName: string,
    signedUrl?: string
}

export interface UserTags {
    userId?: string
    phone?: string
    emailId?: string
    tags: { [tagName: string]: string }
    emailCC?: string[]
    emailBcc?: string[]
    attachments?: Attachment[]
    templateAttachments?: TemplateAttachments[]
    appId?: string
    expiry?: Date
    deviceId?: string,
    metaTags?: any
    atSchedule?: Date,
    metaData?: {[data: string]: string}
}

export interface SendCampaignNotificationsToSegmentRequest {
    userId: string
    campaignId: string,
    creativeIds: string[]
    segmentId: string
    globalTags?: { [tagName: string]: string }
    globalAttachments?: Attachment[]
    forceSend?: boolean
    expiry?: Date
    appId?: string
    segmentExecutionType?: string
    globalMetaTags?: any
    creativeSpecificMetaTags?: any
    fallbackCreativeIds?: { [creativeId: string]: string }
}

export interface SendCampaignNotificationsRequest {
    taskId?: string
    expiry?: Date
    campaignId: string
    creativeIds: string[]
    userContexts: UserTags[]
    creativeFreshnessThreshold?: number
    globalTags?: { [tagName: string]: string }
    globalAttachments?: Attachment[]
    atSchedule?: Date
    appId?: string
    forceSend?: boolean,
    dndWindow?: StartEndTime
    country?: Country
    globalMetaTags?: any
    creativeSpecificMetaTags?: any
    fallbackCreativeIds?: { [creativeId: string]: string }
}


export interface SendCampaignWithCustomTags {
    campaignId: string
    creativeId: string
    emailId: string
    customTag1: string
    customTag2: string
    customTag3: string
    customTag4: string
    customTag5: string
    customTag6: string
    customTag7: string
    customTag8: string
    customTag9: string
    customTag10: string
    appId?: string
}

export interface SendNotificationsRequest {
    campaignId: string
    creativeId: string
    notificationIds: string[]
    atSchedule?: Date
    appId?: string
}

export interface CancelCampaignNotificationsRequest {
    taskId: string
}

export interface CancelCampaignNotificationsResponse {
    success: boolean
}

export interface SendCampaignNotificationsResponse {
    [userId: string]: {
        creativeId: string,
        notificationId: string,
        status: NotificationStatus
        sent: boolean,
        failReason?: string,
        scheduled?: boolean
        whatsappTestRunMessage?: string 
    }[]
}

export interface UserNotificationStatus {
    "userId": string
    "creativeId": string,
    "notificationId": string,
    "sent": boolean
    "failedReason"?: string,
    "externalId"?: string
}

export interface ConvertedEventRequest {
    entityType: string,
    entityId: string
}

export type EVENT_TYPE = "PROCESSED" | "RECEIVED" | "OPENED" | "CONVERTED"
