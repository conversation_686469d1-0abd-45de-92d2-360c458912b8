import { Tenant } from "@curefit/user-common"
import { AdsPartner, AdFormat } from "./adsPartner"

export interface DeeplinkResponse {
  url: string
  applink: string
  universallink?: string
  weblink?: string
}

export interface Dynamiclink extends LinkPayload {
  dynamiclink: string
  analyticsInfo?: {
    googlePlayAnalytics?: {
      utmSource?: string
      utmMedium?: string
      utmCampaign?: string
      utmTerm?: string
      utmContent?: string
      gclid?: string
    },
    itunesConnectAnalytics?: {
      at?: string
      ct?: string
      mt?: string
      pt?: string
    }
  }
}

export interface BranchLink extends LinkPayload {
  analyticsInfo?: {
    canonicalUrl?: string
    marketingTitle?: string
    feature?: string
    channel?: string
    campaign?: string
    type?: number
    campaign_id?: string
    customer_campaign?: string
    stage?: string
    tags?: string[]
    secondary_publisher?: string
    customer_secondary_publisher?: string
    creative_name?: string
    creative_id?: string
    ad_set_name?: string
    customer_ad_set_name?: string
    ad_id?: string
    customer_ad_name?: string
    keyword?: string
    customer_keyword?: string
    placement?: string
    placement_id?: string
    customer_placement?: string
    sub_site_name?: string
    customer_sub_site_name?: string
    ad_name?: string
  },
  linkRedirection?: {
    uri_redirect_mode?: number
    android_deepview?: string
    ios_deepview?: string
    desktop_deepview?: string
    ios_passive_deepview?: string
    android_passive_deepview?: string
  }
}

export const enum DeeplinkServices {
  BRANCH = "branch",
  FIREBASE = "firebase"
}

export const enum LinkTypes {
  QUICK = "Quick",
  ADS = "Ads"
}

export interface LinkPayload {
  tenant?: Tenant
  link?: string
  linkType?: DeeplinkServices
  linkCategory?: LinkTypes
  adPartner?: AdsPartner
  adFormat?: AdFormat
  applink: string
  weblink?: string
  androidUrl?: string
  iosUrl?: string
  redirectMWeb?: boolean
  enableForcedRedirect?: boolean
  media_source?: string
  campaign_name?: string
  campaign_objective?: string
  campaign_audience?: string
  campaign_vertical?: string
  medium?: string
  ad_name?: string
  analyticsInfo?: any
  socialMetaTagInfo?: {
    socialTitle?: string
    socialDescription?: string
    socialImageLink?: string
    socialType?: string
  },
  linkRedirection?: any
  universallink?: string
}

export interface AppInfo {
  appVersion?: number
  osName?: string
  minAndroidVersionCode?: string
}

export interface BranchDeeplinkAnalyticsRequest {
  branch_key: string,
  branch_secret: string,
  start_date: string,
  end_date: string,
  data_source: string,
  dimensions: string[],
  filters?: {[key: string]: string[]},
  ordered_by?: string,
  ordered?: string,
  aggregation?: string,
  zero_fill?: boolean,
}

export interface BranchDeeplinkAnalyticsResponse {
  results?: any[],
  timestamp?: Date,
  paging?: {
    next_url: string,
    total_count: number
  },
  code?: number,
  message?: string
}

export interface GetAnalyticsResponse {
  response: [string, string, number][]
}