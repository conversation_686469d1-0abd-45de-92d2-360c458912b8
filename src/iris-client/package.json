{"name": "@curefit/iris-client", "version": "5.4.0", "types": "dist/index.d.ts", "description": "Http client to send PN and business events through IRIS", "main": "dist/index.js", "repository": {"type": "git", "url": "git+https://github.com/curefit/iris-client.git"}, "author": "AG", "license": "ISC", "bugs": {"url": "https://github.com/curefit/iris-client/issues"}, "homepage": "https://github.com/curefit/iris-client#readme", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "generate-barrels": "rm -rf dist; barrelsby --delete -e node_modules/*", "build": "tsc", "tslint": "tslint --project tsconfig.json", "tslint-fix": "tslint --project tsconfig.json --fix", "prepublishOnly": "yarn run build && yarn run tslint-fix && yarn run tslint", "postversion": "git push origin master && git push origin master --tags && npm publish"}, "devDependencies": {"@types/node": "^16.11.38", "barrelsby": "^1.0.2", "tslint": "^5.16.0", "typescript": "^4.7.2"}, "dependencies": {"@curefit/base-common": "^2.42.0", "@curefit/eat-common": "^7.48.0", "@curefit/iris-common": "1.79.0", "@curefit/logging-common": "^1.3.2", "lodash": "^4.17.4", "@curefit/sqs-client": "^1.2.1", "@curefit/user-common": "2.3.0", "moment": "^2.22.2", "node-fetch": "^2.6.0"}, "resolutions": {"@curefit/order-common": "5.40.1", "@curefit/eat-common": "^7.48.0", "@curefit/base": "6.13.2", "@curefit/mongo-utils": "4.5.0", "@curefit/base-common": "^2.42.0", "@curefit/user-common": "2.3.0", "@curefit/diy-common": "4.42.0", "@curefit/aztec-models": "1.3.0", "@curefit/care-common": "2.34.0", "@curefit/product-common": "5.10.3", "@curefit/iris-common": "1.79.0", "dd-trace": "2.3.0", "inversify": "6.0.1", "class-validator": "0.14.0", "mongoose": "6.13.8"}}