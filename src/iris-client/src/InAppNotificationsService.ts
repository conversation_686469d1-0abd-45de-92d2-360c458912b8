import { BackendConf, BASE_TYPES, FetchUtil, UrlUtil } from "@curefit/base"
import { NotificationMeta, NotificationState, PaginatedResults } from "@curefit/iris-common"
import { GenericResponse } from "@curefit/iris-common/dist/src/generic"

import { inject, injectable, Container } from "inversify"

import { BaseIrisService } from "./BaseIrisService"
import { IRIS_CLIENT_TYPES } from "./ioc/IrisClientTypes"
import { InAppNotifications } from "./InAppNotifications"

@injectable()
export class InAppNotificationsService extends BaseIrisService implements InAppNotifications {


    constructor( @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
                 @inject(IRIS_CLIENT_TYPES.IrisBackendConf) protected backendConf: BackendConf,
                 @inject(BASE_TYPES.UrlUtil) protected urlUtil: UrlUtil) {
        super(fetchHelper, backendConf, urlUtil)
    }

    public async getInAppNotificationsForUser(userId: string, appId: string, states?: NotificationState[],
                                              pageSize?: number, pageNumber?: number): Promise<PaginatedResults<NotificationMeta>> {
        const params = [
            { key: "userId", value: userId },
            { key: "appId", value: appId },
        ]
        states && states.length && params.push({ key: "states", value: states.join()})
        pageSize && params.push({ key: "pageSize", value: pageSize.toString()})
        pageNumber && params.push({ key: "pageNumber", value: pageNumber.toString()})
        return this.makeGetRequest<any>("/inAppNotification",
            "Fetch in app notification for user", params)
    }

    public async getInAppNotificationCountsForUser(userId: string, appId: string, expiryAfter?: string): Promise<any> {
        const params = [
            { key: "userId", value: userId },
            { key: "appId", value: appId }
        ]
        expiryAfter && params.push({ key: "expiryAfter", value: expiryAfter })
        return this.makeGetRequest<any>("/inAppNotification/counts",
            "Fetch in app notification counts for user", params)
    }

    public async getActiveInAppNotificationsForUser(userId: string, appId: string): Promise<any> {
        return this.makeGetRequest<any>("/inAppNotification/active/" + userId + "/" + appId, "Fetch active in app notification for user")
    }

    public async updatePropsByNotificationId(notificationId: string, props: { [key: string]: any }): Promise<GenericResponse> {
        return this.makePutRequest<any>("/inAppNotification/" + notificationId, props, "Update app notification")
    }

    public async updatePropsForNotificationIds(notificationIds: string[], updateProps: {[ key: string]: any}): Promise<GenericResponse> {
        return this.makePostRequest<any>("/inAppNotification/update", {
            notificationIds,
            updateProps
        })
    }
}


export function InAppNotificationsServiceFactory(kernel: Container) {
    return InAppNotificationsService
}
