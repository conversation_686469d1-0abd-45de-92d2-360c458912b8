import {
    CancelCampaignNotificationsRequest,
    CancelCampaignNotificationsResponse,
    ConvertedEventRequest,
    EVENT_TYPE,
    GetLastSentMessageRequest,
    GetLastSentMessageRespose,
    NotificationSegmentTask,
    SendCampaignNotificationsRequest,
    SendCampaignNotificationsResponse,
    SendCampaignNotificationsToSegmentRequest,
    SendNotificationsRequest
} from "@curefit/iris-common"
import { Tenant } from "@curefit/base-common"
import { CommunicationCampaign } from "@curefit/iris-common"

export {
    GetLastSentMessageRequest,
    GetLastSentMessageRespose,
    UserTags,
    SendCampaignNotificationsRequest,
    CancelCampaignNotificationsRequest,
    CancelCampaignNotificationsResponse,
    SendCampaignNotificationsResponse,
    UserNotificationStatus,
    ConvertedEventRequest,
    EVENT_TYPE,
} from "@curefit/iris-common"

export interface ICampaignService {

    /**
     * Get the last sent message time for the given set of users for the given campaign and creative
     */
    getLastSentCampaignMessages(req: GetLastSentMessageRequest): Promise<GetLastSentMessageRespose>

    /**
     * Send notifications to the listed users under the campaign for the listed creatives
     */
    sendCampaignMessages(req: SendCampaignNotificationsRequest, tenant: Tenant): Promise<SendCampaignNotificationsResponse>

    /**
     * Send notifications to a segment of users
     */
    sendCampaignMessagesToSegment(req: SendCampaignNotificationsToSegmentRequest, tenant: Tenant): Promise<NotificationSegmentTask>

    /**
     * Send Notification Segment Task
     */
    getNotificationSegmentTask(taskId: string): Promise<NotificationSegmentTask>

    /**
     * Kill Notification Segment Task
     */
    killNotificationSegmentTask(taskId: string): Promise<NotificationSegmentTask>

    /**
     * Push notifications to the listed users under the campaign for the listed creatives in the iris_campaign queue
     */
    sendCampaignMessagesToQueue(req: SendCampaignNotificationsRequest, tenant: Tenant): Promise<boolean>

    /**
     * Send notifications to the listed users under the campaign through the first successful creative
     */
    sendCampaignMessagesThroughOneCreative(req: SendCampaignNotificationsRequest, tenant: Tenant): Promise<SendCampaignNotificationsResponse>

    /**
     * Send notification event
     */
    sendNotificationEvent(eventType: EVENT_TYPE, notificationId: string, req?: ConvertedEventRequest): Promise<any>

    /**
     * Cancel the notification sent. Currently supported only for InAppNotification.
     */
    cancelCampaignMessages(req: CancelCampaignNotificationsRequest): Promise<CancelCampaignNotificationsResponse>

    /**
     * Send notification by Ids if they are queued.
     */
    processScheduledNotifications(req: SendNotificationsRequest, callbackReceivedAt?: Date): Promise<SendCampaignNotificationsResponse>

    /**
     * Get all the communication campaigns from Iris
     */
    getAllCommunicationCampaigns(request: any): Promise<CommunicationCampaign[]>
}
