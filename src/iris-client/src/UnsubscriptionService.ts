import { BackendConf, BASE_TYPES, Fetch<PERSON>til, Url<PERSON>til } from "@curefit/base"

import { Container, inject, injectable } from "inversify"

import { BaseIrisService } from "./BaseIrisService"
import { IRIS_CLIENT_TYPES } from "./ioc/IrisClientTypes"
import { IUnsubscriptionService } from "./IUnsubscriptionService"

@injectable()
class UnsubscriptionService extends BaseIrisService implements IUnsubscriptionService {

    constructor(
        @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
        @inject(IRIS_CLIENT_TYPES.IrisBackendConf) protected backendConf: BackendConf,
        @inject(BASE_TYPES.UrlUtil) protected urlUtil: UrlUtil) {
            super(fetchHelper, backendConf, urlUtil)
    }

    public async decodeEmailToken(token: string): Promise<any> {
        return this.makeGetRequest<any>(`/unsubscribe/email/decodeToken/${token}`, "Decode email token")
    }
}

export function UnsubscriptionServiceFactory(kernel: Container) {
    return UnsubscriptionService
}