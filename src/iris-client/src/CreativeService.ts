import { BaseIrisService } from "./BaseIrisService"
import {
    ICampaignService, GetLastSentMessageRespose, GetLastSentMessageRequest,
    SendCampaignNotificationsRequest, SendCampaignNotificationsResponse,
    ConvertedEventRequest, EVENT_TYPE, CancelCampaignNotificationsRequest, CancelCampaignNotificationsResponse
} from "./ICampaignService"
import { inject, injectable, Container } from "inversify"
import { BackendConf, BASE_TYPES, FetchUtil, UrlUtil } from "@curefit/base"
import { IRIS_CLIENT_TYPES } from "./ioc/IrisClientTypes"
import { RequestHelper } from "./util/RequestHelper"
import { IQueueService, SQS_CLIENT_TYPES } from "@curefit/sqs-client"
import { BaseCreative, QueueConstants, SendNotificationsRequest, TemplateAttachments } from "@curefit/iris-common"
import { ICreativeService } from "./ICreativeService"

@injectable()
export class CreativeService extends BaseIrisService implements ICreativeService {


    constructor( @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
                 @inject(IRIS_CLIENT_TYPES.IrisBackendConf) protected backendConf: BackendConf,
                 @inject(BASE_TYPES.UrlUtil) protected urlUtil: UrlUtil,
                 @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,
                 @inject(IRIS_CLIENT_TYPES.RequestHelper) protected requestHelper: RequestHelper) {
        super(fetchHelper, backendConf, urlUtil)
    }

    addCreative(creative: BaseCreative): Promise<BaseCreative> {
        return this.makePostRequest<BaseCreative>("/creatives/",
            creative)
    }

    getAllCreatives(): Promise<BaseCreative[]> {
        return this.makeGetRequest("/creatives/all")
    }

    getAllNonActiveCreatives(): Promise<BaseCreative[]> {
        return this.makeGetRequest("/creatives/allNonActive")
    }

    getCreative(creativeId: string): Promise<BaseCreative> {
        return this.makeGetRequest("/creatives/" + creativeId)
    }

    markCreativeActive(creativeId: string, templateId?: string): Promise<boolean> {
        const path = `/creatives/activateCreative/${creativeId}`
        let params = null
        if (templateId) { params = [{key: "templateId", value: templateId}] }
        return this.makePostRequest<boolean>(path, {}, null, params)
    }

    markCreativeSubmitted(creativeId: string): Promise<boolean> {
        return this.makePostRequest<boolean>("/creatives/submitCreative/" + creativeId, {})
    }

    markCreativeRejected(creativeId: string): Promise<boolean> {
        return this.makePostRequest<boolean>("/creatives/rejectCreative/" + creativeId, {})
    }

    updateCreative(creativeId: string, creative: BaseCreative): Promise<BaseCreative> {
        return this.makePutRequest<BaseCreative>("/creatives/" + creativeId, creative)
    }
}


export function CreativeServiceFactory(kernel: Container) {
    return CreativeService
}
