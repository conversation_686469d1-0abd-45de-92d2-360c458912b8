import { injectable, inject } from "inversify"

import { BASE_TYPES, ILogger } from "@curefit/base"
import { IQueueService, SQS_CLIENT_TYPES } from "@curefit/sqs-client"
import { IRIS_CLIENT_TYPES } from "./ioc/IrisClientTypes"

import {
    EventName,
    EventType,
    MealType,
    ReferralAcceptedEventStatus,
    EatOrderConfirmedEvent,
    EatOrderOffferEvent,
    EatSinglePurchasedEvent,
    EatPackPurchasedEvent,
    EatCartDeliveredEvent,
    EatShipmentDeliveredEvent,
    EatPackCompletedEvent,
    MyReferralAccepted,
    MyReferralConverted,
    BaseActivityLoggedEvent,
    EventProps,
    UserEvent,
    IEventProps,
    BusinessEventsConstants,
    ActivityEvent,
    UserProfileEvent,
    BulkEvent
} from "@curefit/iris-common"

export {
    EventName,
    EventType,
    MealType,
    ReferralAcceptedEventStatus,
    EatOrderConfirmedEvent,
    EatOrderOffferEvent,
    EatSinglePurchasedEvent,
    EatPackPurchasedEvent,
    EatCartDeliveredEvent,
    EatShipmentDeliveredEvent,
    EatPackCompletedEvent,
    MyReferralAccepted,
    MyReferralConverted,
    BaseActivityLoggedEvent,
    EventProps,
    UserEvent,
    IEventProps,
    BusinessEventsConstants,
    ActivityEvent,
    UserProfileEvent,
    BulkEvent
} from "@curefit/iris-common"

export interface IBusinessEventsService {
    pushEvent(userId: string, eventName: EventName, eventAt: Date, eventProperties: IEventProps): Promise<boolean>
    pushUserProfileEvent(userId: string, userProperties: UserEvent): Promise<boolean>
    pushBulkEvents(activityEvents: ActivityEvent[], userEvents: UserProfileEvent[]): Promise<boolean>
}

@injectable()
export class BusinessEventsService implements IBusinessEventsService {

    constructor(@inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,
        @inject(IRIS_CLIENT_TYPES.BusinessEventsQueueName) private queueName: string) {

    }

    pushEvent(userId: string, eventName: EventName, eventAt: Date, eventProperties: EventProps): Promise<boolean> {
        const attributes: Map<string, string> = new Map<string, string>()
        attributes.set(BusinessEventsConstants.getUserIdKey(), userId)
        attributes.set(BusinessEventsConstants.getEventNameKey(), eventName)
        const eventType: EventType = "USER_ACTIVITY_EVENT"
        attributes.set(BusinessEventsConstants.getEventTypeKey(), eventType)
        attributes.set(BusinessEventsConstants.getEventAtKey(), "" + eventAt.getTime())
        return this.queueService.sendMessage(this.queueName, eventProperties, attributes)
    }

    pushUserProfileEvent(userId: string, userProperties: UserEvent): Promise<boolean> {
        const attributes: Map<string, string> = new Map<string, string>()
        attributes.set(BusinessEventsConstants.getUserIdKey(), userId)
        const eventType: EventType = "USER_PROFILE_UPDATE"
        attributes.set(BusinessEventsConstants.getEventTypeKey(), eventType)
        return this.queueService.sendMessage(this.queueName, userProperties, attributes)
    }

    private getGroupEventData(activityEvents: ActivityEvent[], userEvents: UserProfileEvent[]): BulkEvent {
        const bulkData: BulkEvent = {}
        if (userEvents && userEvents.length > 0 ) {
            bulkData.userEvents = userEvents
        }
        if (activityEvents && activityEvents.length > 0) {
            bulkData.activityEvents = activityEvents
        }
        return bulkData


    }

    pushBulkEvents(activityEvents: ActivityEvent[], userEvents: UserProfileEvent[]): Promise<boolean> {
        const attributes: Map<string, string> = new Map<string, string>()
        const eventType: EventType = "BULK"
        attributes.set(BusinessEventsConstants.getEventTypeKey(), eventType)
        const eventData: BulkEvent = this.getGroupEventData(activityEvents, userEvents)
        return this.queueService.sendMessage(this.queueName, eventData, attributes)
    }



}
