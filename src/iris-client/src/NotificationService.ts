import { BaseIrisService } from "./BaseIrisService"

import { inject, injectable, Container } from "inversify"
import { BackendConf, BASE_TYPES, FetchUtil, UrlUtil } from "@curefit/base"
import { IRIS_CLIENT_TYPES } from "./ioc/IrisClientTypes"
import { INotificationService } from "./INotificationService"
import { ClickToCallReport, Notification, PaginatedResults, UserNotificationFilterRequest, EmailValidResponse } from "@curefit/iris-common"
import { SendCampaignNotificationsResponse } from "./ICampaignService"

@injectable()
export class NotificationService extends BaseIrisService implements INotificationService {


    constructor( @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
                 @inject(IRIS_CLIENT_TYPES.IrisBackendConf) protected backendConf: BackendConf,
                 @inject(BASE_TYPES.UrlUtil) protected urlUtil: Url<PERSON>til) {
        super(fetchHelper, backendConf, urlUtil)
    }

    public async getTaskReport(taskId: string): Promise<any> {
        return this.makeGetRequest<any>("/notifications/taskReport/" + taskId, "Fetch report for task")
    }

    public async getClickToCallReport(notificationId: string): Promise<ClickToCallReport> {
        return this.makeGetRequest<any>("/notifications/clickToCallReport/" + notificationId, "Fetch click to call report for notification")
    }

    public async getNotificationDetail(notificationId: string): Promise<Notification> {
        return this.makeGetRequest<any>("/notifications/" + notificationId, "Fetch detail for notification")
    }

    public async getUserNotificationHistory(req: UserNotificationFilterRequest): Promise<PaginatedResults<Notification>> {
        return this.makePostRequest<PaginatedResults<Notification>>("/notifications/user/history",
            req, "Fetch notifications for user based on filter")
    }

    public retriggerOldNotifications(notificationId: string): Promise<SendCampaignNotificationsResponse> {
        return this.makePostRequest<SendCampaignNotificationsResponse>("notifications/retrigger", {notificationId})
    }

    public validateEmail(email: string, skipLocalCheck?: boolean, checkForLastXDays?: number): Promise<EmailValidResponse> {
        return this.makePostRequest<EmailValidResponse>("notifications/email/validate", {email, skipLocalCheck, checkForLastXDays}, "Check if the email is valid")
    }

    public bulkValidateEmail(emails: string[], skipLocalCheck?: boolean, checkForLastXDays?: number): Promise<EmailValidResponse[]> {
        return this.makePostRequest<EmailValidResponse[]>("notifications/email/bulkValidate", {emails, skipLocalCheck, checkForLastXDays},  "Check if the" +
        " emails" + " are valid")
    }
}


export function NotificationServiceFactory(kernel: Container) {
    return NotificationService
}
