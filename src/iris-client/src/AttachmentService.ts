import { BaseIrisService } from "./BaseIrisService"

import { inject, injectable } from "inversify"
import { BackendConf, BASE_TYPES, FetchUtil, UrlUtil } from "@curefit/base"
import { IRIS_CLIENT_TYPES } from "./ioc/IrisClientTypes"
import { TemplateAttachments } from "@curefit/iris-common"
import { IAttachmentService } from "./IAttachmentService"

@injectable()
export class AttachmentService extends BaseIrisService implements IAttachmentService {


    constructor( @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
                 @inject(IRIS_CLIENT_TYPES.IrisBackendConf) protected backendConf: BackendConf,
                 @inject(BASE_TYPES.UrlUtil) protected urlUtil: UrlUtil) {
        super(fetchHelper, backendConf, urlUtil)
    }

    getAttachmentURL(request: TemplateAttachments): Promise<{ url: string }> {
        return this.makePostRequest<{ url: string }>("attachment/urlFromTemplate", request)
    }
}
