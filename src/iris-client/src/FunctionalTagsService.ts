import { BaseIrisService } from "./BaseIrisService"

import { inject, injectable, Container } from "inversify"
import { BackendConf, BASE_TYPES, FetchUtil, UrlUtil } from "@curefit/base"
import { IRIS_CLIENT_TYPES } from "./ioc/IrisClientTypes"
import { IFunctionalTagsService } from "./IFunctionalTagsService"
import { FunctionTag } from "@curefit/iris-common"

@injectable()
export class FunctionalTagsService extends BaseIrisService implements IFunctionalTagsService {


    constructor( @inject(BASE_TYPES.FetchUtil) protected fetchHelper: Fetch<PERSON>til,
                 @inject(IRIS_CLIENT_TYPES.IrisBackendConf) protected backendConf: BackendConf,
                 @inject(BASE_TYPES.UrlUtil) protected urlUtil: UrlUtil) {
        super(fetchHelper, backendConf, urlUtil)
    }

    public async getAllFunctions(): Promise<Record<string, FunctionTag>> {
        return this.makeGetRequest<Record<string, FunctionTag>>("/functionalTags/allFunctions", "Fetch all functions")
    }
}
