import { injectable } from "inversify"
import { BackendConf, FetchUtil, UrlUtil } from "@curefit/base"
const fetch = require("node-fetch")

@injectable()
export class BaseIrisService {
    constructor(
        protected fetchHelper: FetchUtil,
        protected backendConf: BackendConf,
        protected urlUtil: UrlUtil) {
    }

    private createUrl(path: string, params?: { key: string, value: string}[]) {
        if (!params) { params = [] }
        params.push({key: "apiKey", value: this.backendConf.apiKey})
        return this.urlUtil.constructUrl(this.backendConf.url, path, params)
    }

    public makePostRequest<T>(path: string, postData: any, debugString?: any, params?: { key: string, value: string}[],
                              headers?: {[key: string]: string}): Promise<T> {
        return fetch(this.createUrl(path, params), this.fetchHelper.post(postData, headers)).then((response: any) => {
            return this.fetchHelper.parseResponse<T>(response)
        }).catch((err: any) => {
            throw err
        })
    }

    public makePutRequest<T>(path: string, postData: any, debugString?: any): Promise<T> {
        return fetch(this.createUrl(path), this.fetchHelper.put(postData)).then((response: any) => {
            return this.fetchHelper.parseResponse<T>(response)
        }).catch((err: any) => {
            throw err
        })
    }

    public makeGetRequest<T>(path: string, debugString?: any, params?: any[]): Promise<T> {
        return fetch(this.createUrl(path, params), this.fetchHelper.get()).then((response: any) => {
            return this.fetchHelper.parseResponse<T>(response)
        }).catch((err: any) => {
            throw err
        })
    }
}
