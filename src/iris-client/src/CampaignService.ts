import { BaseIrisService } from "./BaseIrisService"
import {
    ICampaignService, GetLastSentMessageRespose, GetLastSentMessageRequest,
    SendCampaignNotificationsRequest, SendCampaignNotificationsResponse,
    ConvertedEventRequest, EVENT_TYPE, CancelCampaignNotificationsRequest, CancelCampaignNotificationsResponse
} from "./ICampaignService"
import { inject, injectable, Container } from "inversify"
import { BackendConf, BASE_TYPES, FetchUtil, UrlUtil } from "@curefit/base"
import { IRIS_CLIENT_TYPES } from "./ioc/IrisClientTypes"
import { RequestHelper } from "./util/RequestHelper"
import { IQueueService, SQS_CLIENT_TYPES } from "@curefit/sqs-client"
import {
    NotificationSegmentTask,
    QueueConstants,
    SendCampaignNotificationsToSegmentRequest,
    SendNotificationsRequest
} from "@curefit/iris-common"
import { Tenant } from "@curefit/base-common"
import { CommunicationCampaign } from "@curefit/iris-common"

@injectable()
export class CampaignService extends BaseIrisService implements ICampaignService {


    constructor( @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
                 @inject(IRIS_CLIENT_TYPES.IrisBackendConf) protected backendConf: BackendConf,
                 @inject(BASE_TYPES.UrlUtil) protected urlUtil: UrlUtil,
                 @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,
                 @inject(IRIS_CLIENT_TYPES.RequestHelper) protected requestHelper: RequestHelper) {
        super(fetchHelper, backendConf, urlUtil)
    }

    public getLastSentCampaignMessages(req: GetLastSentMessageRequest): Promise<GetLastSentMessageRespose> {
        return this.makePostRequest<GetLastSentMessageRespose>("/campaigns/lastSent",
            req, "Fetching last sent messages for the given users")
    }

    public async sendCampaignMessages(req: SendCampaignNotificationsRequest, tenant: Tenant): Promise<SendCampaignNotificationsResponse> {
        await this.requestHelper.updateAttachments(req)
        return this.makePostRequest<SendCampaignNotificationsResponse>("/campaigns/send",
            req, "Send notifications to users under the campaign", null, {"x-tenant-id": tenant})
    }

    public cancelCampaignMessages(req: CancelCampaignNotificationsRequest): Promise<CancelCampaignNotificationsResponse> {
        return this.makePostRequest<CancelCampaignNotificationsResponse>("/campaigns/cancel",
            req, "Cancel notifications to users under the campaign")
    }

    async sendCampaignMessagesThroughOneCreative(req: SendCampaignNotificationsRequest, tenant: Tenant): Promise<SendCampaignNotificationsResponse> {
        await this.requestHelper.updateAttachments(req)
        return this.makePostRequest<SendCampaignNotificationsResponse>("/campaigns/sendOne",
            req, "Send notifications to users under the campaign", null, {"x-tenant-id": tenant})
    }

    public async sendCampaignMessagesToSegment(req: SendCampaignNotificationsToSegmentRequest, tenant: Tenant): Promise<NotificationSegmentTask> {
        req.globalAttachments = req.globalAttachments ?
            await this.requestHelper.updateGlobalAttachments(req.globalAttachments) : undefined
        return this.makePostRequest<NotificationSegmentTask>("/campaigns/sendToSegment",
            req, "Send notifications to segment", null, {"x-tenant-id": tenant})
    }

    public async getNotificationSegmentTask(taskId: string): Promise<NotificationSegmentTask> {
        return this.makeGetRequest<NotificationSegmentTask>(`/campaigns/getNotificationSegmentTask/${taskId}`)
    }

    public async killNotificationSegmentTask(taskId: string): Promise<NotificationSegmentTask> {
        return this.makeGetRequest<NotificationSegmentTask>(`/campaigns/killNotificationSegmentTask/${taskId}`)
    }

    public sendNotificationEvent(eventType: EVENT_TYPE, notificationId: string, req?: ConvertedEventRequest): Promise<any> {
        return this.makePutRequest<any>(`/notifications/${notificationId}/${eventType}/${Date.now()}`,
            req, "Send notifications event")
    }

    sendCampaignMessagesToQueue(req: SendCampaignNotificationsRequest, tenant: Tenant): Promise<boolean> {
        if (tenant)
            req.appId = req.appId || tenant
        return this.queueService.sendMessage(QueueConstants.getQueueName("IRIS_CAMPAIGN"), req)
    }

    processScheduledNotifications(req: SendNotificationsRequest): Promise<SendCampaignNotificationsResponse> {
        return null
    }

    public async getAllCommunicationCampaigns(request: any): Promise<CommunicationCampaign[]> {
        return this.makeGetRequest<CommunicationCampaign[]>(`/communicationcampaigns/all`)
    }
}


export function CampaignServiceFactory(kernel: Container) {
    return CampaignService
}
