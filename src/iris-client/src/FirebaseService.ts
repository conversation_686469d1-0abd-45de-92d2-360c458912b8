import { injectable, inject } from "inversify"
import { BackendConf, BASE_TYPES, FetchUtil, UrlUtil } from "@curefit/base"
import {
    PurchaseEvent
} from "@curefit/iris-common"
import { IRIS_CLIENT_TYPES } from "./ioc/IrisClientTypes"
import { BaseIrisService } from "./BaseIrisService"
import {IFirebaseService} from "./IFirebaseService"

@injectable()
export class FirebaseService extends BaseIrisService implements IFirebaseService {
    constructor(
        @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
        @inject(IRIS_CLIENT_TYPES.IrisBackendConf) protected backendConf: BackendConf,
        @inject(BASE_TYPES.UrlUtil) protected urlUtil: UrlUtil) {
            super(fetchHelper, backendConf, urlUtil)
    }

    async sendFirebasePurchaseEvent(userId: string, purchaseEventData: PurchaseEvent): Promise<void> {
        return this.makePostRequest<void>(
            "/firebase-analytics/sendPurchaseEvent",
        {userId, purchaseEventData},
            `Sending purchaseEvent - ${purchaseEventData} for userId ${userId}`
        )
    }
}
