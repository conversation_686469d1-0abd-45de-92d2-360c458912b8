import { BackendConf, BASE_TYPES } from "@curefit/base"
import * as Inversify from "inversify"
import { ContainerModule, interfaces } from "inversify"
import { IRIS_CLIENT_TYPES } from "./IrisClientTypes"
import { BusinessEventsService, IBusinessEventsService } from "../BusinessEventsService"
import { ICampaignService } from "../ICampaignService"
import { CampaignServiceFactory } from "../CampaignService"
import { BranchioService } from "../BranchioService"
import { IBranchioService } from "../IBranchioService"
import { branchioConfig, businessEventsQueueNameConfig, irisConfig } from "../util/config"
import { S3Helper } from "../util/S3Helper"
import { AttachmentHelper } from "../util/AttachmentHelper"
import { RequestHelper } from "../util/RequestHelper"
import { NotificationServiceFactory } from "../NotificationService"
import { INotificationService } from "../INotificationService"
import { IDeeplinkService } from "../IDeepLinkService"
import { DeepLinkService } from "../DeepLinkService"
import { InAppNotifications } from "../InAppNotifications"
import { InAppNotificationsServiceFactory } from "../InAppNotificationsService"
import { ThrottleConfigService } from "../ThrottleConfigService"
import { CreativeServiceFactory } from "../CreativeService"
import { ICreativeService } from "../ICreativeService"
import { IUnsubscriptionService } from "../IUnsubscriptionService"
import { UnsubscriptionServiceFactory } from "../UnsubscriptionService"
import { IFunctionalTagsService } from "../IFunctionalTagsService"
import { FunctionalTagsService } from "../FunctionalTagsService"
import { IFirebaseService } from "../IFirebaseService"
import { FirebaseService } from "../FirebaseService"


export function IrisClientModule(kernel: Inversify.Container): ContainerModule {
    return new Inversify.ContainerModule((bind: Inversify.interfaces.Bind) => {
        bind<BackendConf>(IRIS_CLIENT_TYPES.IrisBackendConf).toDynamicValue((context: interfaces.Context) => {
            const appConfig = context.container.get<any>(BASE_TYPES.Configuration).getConfiguration()
            return appConfig && appConfig.backendServices && appConfig.backendServices.iris || irisConfig()
        }).inSingletonScope()
        bind<BackendConf>(IRIS_CLIENT_TYPES.BranchioBackendConf).toDynamicValue((context: interfaces.Context) => {
            const appConfig = context.container.get<any>(BASE_TYPES.Configuration).getConfiguration()
            return appConfig && appConfig.backendServices && appConfig.backendServices.branchio || branchioConfig()
        }).inSingletonScope()
        bind<string>(IRIS_CLIENT_TYPES.BusinessEventsQueueName).toDynamicValue((context: interfaces.Context) => {
            const appConfig = context.container.get<any>(BASE_TYPES.Configuration).getConfiguration()
            return appConfig && appConfig.sqsQueues && appConfig.sqsQueues.businessEvents || businessEventsQueueNameConfig()
        }).inSingletonScope()
        bind<IBusinessEventsService>(IRIS_CLIENT_TYPES.BusinessEventsService).to(BusinessEventsService).inSingletonScope()
        bind<ThrottleConfigService>(IRIS_CLIENT_TYPES.ThrottleConfigService).to(ThrottleConfigService).inSingletonScope()
        bind<ICampaignService>(IRIS_CLIENT_TYPES.IrisCampaignService).to(CampaignServiceFactory(kernel)).inSingletonScope()
        bind<ICreativeService>(IRIS_CLIENT_TYPES.IrisCreativeService).to(CreativeServiceFactory(kernel)).inSingletonScope()
        bind<INotificationService>(IRIS_CLIENT_TYPES.IrisNotificationService).to(NotificationServiceFactory(kernel)).inSingletonScope()
        bind<InAppNotifications>(IRIS_CLIENT_TYPES.InAppNotificationsService).to(InAppNotificationsServiceFactory(kernel)).inSingletonScope()
        bind<IBranchioService>(IRIS_CLIENT_TYPES.BranchioService).to(BranchioService).inSingletonScope()
        bind<S3Helper>(IRIS_CLIENT_TYPES.S3Helper).to(S3Helper).inSingletonScope()
        bind<AttachmentHelper>(IRIS_CLIENT_TYPES.AttachmentHelper).to(AttachmentHelper).inSingletonScope()
        bind<RequestHelper>(IRIS_CLIENT_TYPES.RequestHelper).to(RequestHelper).inSingletonScope()
        bind<IDeeplinkService>(IRIS_CLIENT_TYPES.DeepLinkService).to(DeepLinkService).inSingletonScope()
        bind<IUnsubscriptionService>(IRIS_CLIENT_TYPES.UnsubscriptionService).to(UnsubscriptionServiceFactory(kernel)).inSingletonScope()
        bind<IFunctionalTagsService>(IRIS_CLIENT_TYPES.FunctionalTagsService).to(FunctionalTagsService).inSingletonScope()
        bind<IFirebaseService>(IRIS_CLIENT_TYPES.IFirebaseService).to(FirebaseService).inSingletonScope()
    })
}
