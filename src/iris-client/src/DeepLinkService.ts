import { injectable, inject } from "inversify"
import { BackendConf, BASE_TYPES, FetchUtil, UrlUtil } from "@curefit/base"
import {
    LinkPayload,
    AppInfo,
    DeeplinkServices,
    DeeplinkResponse,
    GetAnalyticsResponse,
    PurchaseEvent
} from "@curefit/iris-common"
import { Tenant } from "@curefit/user-common"
import { IDeeplinkService } from "./IDeepLinkService"
import { IRIS_CLIENT_TYPES } from "./ioc/IrisClientTypes"
import { BaseIrisService } from "./BaseIrisService"

@injectable()
export class DeepLinkService extends BaseIrisService implements IDeeplinkService {
    constructor(
        @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
        @inject(IRIS_CLIENT_TYPES.IrisBackendConf) protected backendConf: BackendConf,
        @inject(BASE_TYPES.UrlUtil) protected urlUtil: UrlUtil) {
            super(fetchHelper, backendConf, urlUtil)
    }

    async createUniversalDeepLinkUrl(tenant: Tenant, link: LinkPayload, appInfo?: AppInfo, userId?: string, preferredService?: DeeplinkServices): Promise<DeeplinkResponse> {
        return this.makePostRequest<DeeplinkResponse>(
            "/link/create",
            { tenant, link, appInfo, userId, preferredService },
            `Creating deeplink url ${link.applink}`)
    }

    async editUniversalDeepLinkUrl(tenant: Tenant, link: LinkPayload, appInfo?: AppInfo, userId?: string, preferredService?: DeeplinkServices): Promise<DeeplinkResponse> {
        return this.makePutRequest<DeeplinkResponse>(
            "/link/update",
            { tenant, link, appInfo, userId, preferredService },
            `Editing deeplink url ${link.applink}`)
    }

    async getAnalytics(tenant: Tenant, start_date: string, end_date: string, creativeIdToUtmIdMap: [string, string][]): Promise<GetAnalyticsResponse> {
        return this.makePostRequest<GetAnalyticsResponse>(
            "/link/getAnalytics",
            { tenant, start_date, end_date, creativeIdToUtmIdMap },
            `Getting Analytics for ${creativeIdToUtmIdMap} between ${start_date} and ${end_date}`)
    }

    async sendPurchaseEvent(userId: string, purchaseEventData: PurchaseEvent): Promise<void> {
        return this.makePostRequest<void>(
            "/link/sendPackPurchaseEvent",
        {userId, purchaseEventData},
            `Sending purchaseEvent - ${purchaseEventData} for userId ${userId}`
        )
    }
}
