import { LinkPayload, AppInfo, DeeplinkServices, GetAnalyticsResponse, PurchaseEvent } from "@curefit/iris-common"
import { Tenant } from "@curefit/user-common"

/*
 * create deeplinks for the app
 */
export interface IDeeplinkService {
    createUniversalDeepLinkUrl(tenant: Tenant, link: LinkPayload, appInfo?: AppInfo, userId?: string, preferredService?: DeeplinkServices): Promise<{ url: string, applink: string}>

    getAnalytics(tenant: Tenant, start_date: string, end_date: string, creativeIdToUtmIdMap: [string, string][]): Promise<GetAnalyticsResponse>

    sendPurchaseEvent(userId: string, purchaseEventData: PurchaseEvent): Promise<void>
}
