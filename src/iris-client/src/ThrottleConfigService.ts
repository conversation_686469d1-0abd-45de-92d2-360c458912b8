import { IThrottleConfigService } from "./IThrottleConfigService"
import { BaseIrisService } from "./BaseIrisService"
import { inject, injectable, Container } from "inversify"
import { BackendConf, BASE_TYPES, FetchUtil, UrlUtil } from "@curefit/base"
import { IRIS_CLIENT_TYPES } from "./ioc/IrisClientTypes"

@injectable()
export class ThrottleConfigService extends BaseIrisService implements IThrottleConfigService {

    constructor( @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
                 @inject(IRIS_CLIENT_TYPES.IrisBackendConf) protected backendConf: BackendConf,
                 @inject(BASE_TYPES.UrlUtil) protected urlUtil: UrlUtil) {
        super(fetchHelper, backendConf, urlUtil)
    }

    getThrottleValue(workerTag: string): Promise<any> {
        return this.makeGetRequest<any>("/communicationWorkers/config/" + workerTag)
    }

    setThrottleValue(workerTag: string, throttle: number): Promise<any> {
        return this.makePostRequest<any>("/communicationWorkers/config", {
            workerTag: workerTag,
            reservoirSize: throttle
        })
    }
}