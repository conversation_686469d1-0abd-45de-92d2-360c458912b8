import { injectable, inject } from "inversify"
import { IBranchioService } from "./IBranchioService"
import { BackendConf, BASE_TYPES, FetchUtil, ILogger, UrlUtil } from "@curefit/base"
import { IRIS_CLIENT_TYPES } from "./ioc/IrisClientTypes"
import { LinkPayload } from "@curefit/iris-common"
const fetch = require("node-fetch")

export interface BranchioPayload {
    branch_key: string,
    channel?: string
    feature?: string
    campaign?: string
    data: {
        $desktop_url: string,
        $android_url: string,
        $ios_url: string,
        $deeplink_path: string
    }
}

@injectable()
export class BranchioService implements IBranchioService {
    constructor( @inject(BASE_TYPES.ILogger) private logger: ILogger,
                 @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
                 @inject(IRIS_CLIENT_TYPES.BranchioBackendConf) private backendConf: BackendConf,
                 @inject(BASE_TYPES.UrlUtil) private urlUtil: UrlUtil) {
    }


    private createUrl() {
        return this.urlUtil.constructUrl(this.backendConf.url, "/url")
    }

    createShortLink(link: string): Promise<{ url: string }> {
        const payload: BranchioPayload = {
            branch_key: this.backendConf.apiKey,
            data: {
                $desktop_url: link,
                $android_url: link,
                $ios_url: link,
                $deeplink_path: link
            }
        }

        this.logger.info("request of branchIo " + JSON.stringify(payload))
        return fetch(this.createUrl(), this.fetchHelper.post(payload)).then((response: any) => {
            this.logger.info("response of branchIo " + JSON.stringify(response))
            return this.fetchHelper.parseResponse<any>(response).then((res) => {
                this.logger.info("response of branchIo2 " + JSON.stringify(res))
                return { url: res.url }
            })
        })
    }
}
