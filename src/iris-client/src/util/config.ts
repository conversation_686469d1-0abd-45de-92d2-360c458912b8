import { BackendConf } from "@curefit/base"

export const branchioConfig = (): Partial<BackendConf> => {
    if (process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "ALPHA") {
        return {
            "url": "https://api.branch.io/v1",
            "apiKey": "key_live_anzEyViCZLYFjFyzpJgf5eedEqgnCP25"
        }
    } else {
        return {
            "url": "https://api.branch.io/v1",
            "apiKey": "key_live_anzEyViCZLYFjFyzpJgf5eedEqgnCP25"
        }
    }
}

export const irisConfig = (): Partial<BackendConf> => {
    if (process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "ALPHA") {
        return {
            "url": "http://iris.production.cure.fit.internal"
        }
    } else if (process.env.ENVIRONMENT === "STAGE") {
        return {
            "url": "http://iris.stage.cure.fit.internal"
        }
    } else if (process.env.ENVIRONMENT === "LOCAL") {
        return {
            "url": "http://localhost:3010"
        }
    }
    return {
        "url": "http://iris.stage.cure.fit.internal"
    }
}

export const businessEventsQueueNameConfig = (): string => {
    if (process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "ALPHA") {
        return "production-business-events"
    } else {
        return "stage-business-events"
    }
}

export const irisS3BucketConfig = (): string => {
    if (process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "ALPHA") {
        return "cf-iris"
    } else {
        return "cf-iris-stage"
    }
}

export const irisS3Directory = (): string => {
    return "iris-email-attachments"
}

