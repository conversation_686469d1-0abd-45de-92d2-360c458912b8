import { injectable, inject } from "inversify"
import * as aws from "aws-sdk"
import { BASE_TYPES, ILogger } from "@curefit/base"

@injectable()
export class S3Helper {

    private s3: aws.S3

    constructor(@inject(BASE_TYPES.ILogger) private logger: ILogger) {
        this.s3 = new aws.S3({
            region: "ap-south-1"
        })
    }

    public async upload(bucket: string, file: { s3FileKey: string, buffer: <PERSON>uffer }): Promise<string> {

        const uploadPromise = this.s3.upload({
            Bucket: bucket,
            Key: file.s3FileKey,
            Body: file.buffer
        }).promise()

        return uploadPromise.then(data => {
                return data.Key
            })
            .catch(err => {
                this.logger.error("attachment - file upload failed: " + err)
                throw new Error("file upload failed")
            })
    }

    public async uploadBulk(bucket: string, files: { s3FileKey: string, buffer: Buffer }[]): Promise<string[]> {
        return await Promise.all(files.map(file => {
            return this.upload(bucket, file)
        }))
    }

    public async getSignedUrlWithPutObject(params: any): Promise<string> {
        return new Promise<string>((resolve, reject) => {
            this.s3.getSignedUrl("putObject", params, (err: any, url: string) => {
                if (err) {
                    reject(err)
                } else {
                    this.logger.info(`s3 getSignedUrlWithPutObject gave url:${url}`)
                    resolve(url)
                }
            })
        })
    }
}

