import * as fs from "fs"
import { irisS3BucketConfig, irisS3Directory } from "./config"
import * as path from "path"
import * as moment from "moment"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { inject, injectable } from "inversify"
import { IRIS_CLIENT_TYPES } from "../ioc/IrisClientTypes"
import { Attachment } from "@curefit/iris-common"
import { S3Helper } from "./S3Helper"

@injectable()
export class AttachmentHelper {

    constructor(@inject(BASE_TYPES.ILogger) private logger: ILogger, @inject(IRIS_CLIENT_TYPES.S3Helper) private s3Helper: S3Helper) {
    }

    public async uploadFile(filePath: string): Promise<Attachment> {
        const splits = filePath.split("/")
        const fileName = splits[splits.length - 1]

        const extName = path.extname(fileName)
        const fileBaseName = extName ? fileName.slice(0, fileName.indexOf(extName)) : fileName
        const timeStamp = moment().valueOf()
        let s3FileKey = fileBaseName ? `${fileBaseName}-${timeStamp}${extName}` : `${timeStamp}`
        s3FileKey = irisS3Directory() + "/" + s3FileKey
        this.logger.info("uploading file to s3 with key " + s3FileKey)

        const buffer = await this.getBuffer(filePath).then(buffer => {
            return buffer
        }).catch(ex => {
            throw ex
        })

        await this.s3Helper.upload(irisS3BucketConfig(), {s3FileKey: s3FileKey, buffer: buffer}).then()

        return {
            s3Info: {
                bucket: irisS3BucketConfig(),
                key: s3FileKey
            },
            fileUrl: null,
            customFileName: fileName,
        }
    }

    public async uploadFiles(filePaths: string[]): Promise<Attachment[]> {

        return Promise.all(filePaths.map(filePath => {
            return this.uploadFile(filePath)
        }))
    }

    private async getBuffer(filePath: string): Promise<Buffer> {
        return await new Promise<any>((resolve, reject) => fs.readFile(filePath, (err, data) => {
            if (err) {
                reject(err)
            } else {
                resolve(data)
            }
        })).then(result => {
            return result
        }).catch(ex => {
            throw ex
        })
    }
}