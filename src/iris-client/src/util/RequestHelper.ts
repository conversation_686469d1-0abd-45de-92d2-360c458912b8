import { BASE_TYPES, ILogger } from "@curefit/base"
import { inject, injectable } from "inversify"
import { IRIS_CLIENT_TYPES } from "../ioc/IrisClientTypes"
import { Attachment, SendCampaignNotificationsRequest } from "@curefit/iris-common"
import { AttachmentHelper } from "./AttachmentHelper"

@injectable()
export class RequestHelper {


    constructor(@inject(BASE_TYPES.ILogger) private logger: ILogger, @inject(IRIS_CLIENT_TYPES.AttachmentHelper) private attachmentHelper: AttachmentHelper) {
    }

    public async updateAttachments(request: SendCampaignNotificationsRequest): Promise<any> {
        const globalAttachments = request.globalAttachments

        if (globalAttachments) {
            request.globalAttachments = await this.updateGlobalAttachments(globalAttachments)
        }
        await Promise.all(request.userContexts.map(async (userContext, i) => {
            if (userContext.attachments) {
                this.logger.debug("updating users specific attachments")
                await Promise.all(userContext.attachments.map(async (attachment, j) => {
                    if (attachment.fileUrl && this.isLocalFile(attachment.fileUrl)) {
                        request.userContexts[i].attachments[j] = await this.getS3Attachment(attachment)
                    }
                }))
            }
        }))
    }

    public async updateGlobalAttachments(globalAttachments: Attachment[]): Promise<Attachment []> {
        this.logger.debug("iris-client:RequestHelper::updateGlobalAttachments updating global attachments")
        await Promise.all(globalAttachments.map(async (attachment, i) => {
            if (attachment.fileUrl && this.isLocalFile(attachment.fileUrl)) {
                globalAttachments[i] = await this.getS3Attachment(attachment)
            }
        }))
        return globalAttachments
    }

    private async getS3Attachment(attachment: Attachment): Promise<Attachment> {
        const s3Attachment = await this.attachmentHelper.uploadFile(attachment.fileUrl)
            .then(data => {
                return data
            })
            .catch(ex => {
                throw ex
            })
        if (attachment.customFileName) {
            s3Attachment.customFileName = attachment.customFileName
        }

        return s3Attachment
    }

    private isLocalFile(fileUrl: string): boolean {
        return !fileUrl.includes("http")
    }
}
