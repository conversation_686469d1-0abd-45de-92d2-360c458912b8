import { NotificationMeta, NotificationState, PaginatedResults } from "@curefit/iris-common"
import { GenericResponse } from "@curefit/iris-common/dist/src/generic"

export interface InAppNotifications {

    /**
     * Get active In-App notification for users
     */
    getInAppNotificationsForUser(userId: string, appId: string, states?: NotificationState[],
                                 pageSize?: number, pageNumber?: number): Promise<PaginatedResults<NotificationMeta>>

    getInAppNotificationCountsForUser(userId: string, appId: string, expiryAfter?: string): Promise<any>

    getActiveInAppNotificationsForUser(userId: string, appId: string): Promise<NotificationMeta[]>

    updatePropsByNotificationId(notificationId: string, props: {[key: string]: any}): Promise<GenericResponse>

    updatePropsForNotificationIds(notificationIds: string[], updateProps: {[key: string]: any}): Promise<GenericResponse>
}
