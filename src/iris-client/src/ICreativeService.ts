import {
    BaseCreative,
    CancelCampaignNotificationsRequest, CancelCampaignNotificationsResponse,
    ConvertedEventRequest,
    EVENT_TYPE,
    GetLastSentMessageRequest,
    GetLastSentMessageRespose,
    SendCampaignNotificationsRequest, SendCampaignNotificationsResponse, SendNotificationsRequest, TemplateAttachments
} from "@curefit/iris-common"

export {
    GetLastSentMessageRequest,
    GetLastSentMessageRespose,
    UserTags,
    SendCampaignNotificationsRequest,
    CancelCampaignNotificationsRequest,
    CancelCampaignNotificationsResponse,
    SendCampaignNotificationsResponse,
    UserNotificationStatus,
    ConvertedEventRequest,
    EVENT_TYPE,
} from "@curefit/iris-common"

export interface ICreativeService {


    /**
     * Get all creatives
     */
    getAllCreatives(): Promise<BaseCreative[]>

    /**
     * Get one creative by id
     */
    getCreative(creativeId: string): Promise<BaseCreative>

    /**
     * Get all creatives
     */
    addCreative(creative: BaseCreative): Promise<BaseCreative>

    /**
     * Get all non active creatives
     */
    getAllNonActiveCreatives(): Promise<BaseCreative[]>

    /**
     * mark creative registration status as active
     */
    markCreativeActive(creativeId: string, templateId?: string): Promise<boolean>

    /**
     * mark creative registration status as submitted
     */
    markCreativeSubmitted(creativeId: string): Promise<boolean>

    /**
     * mark creative registration status as rejected
     */
    markCreativeRejected(creativeId: string): Promise<boolean>

    /**
     * update a creative
     */
    updateCreative(creativeId: string, creative: BaseCreative): Promise<BaseCreative>

}
