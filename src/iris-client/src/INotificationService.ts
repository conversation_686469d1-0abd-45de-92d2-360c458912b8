import {
    ClickToCallReport,
    EmailValidResponse,
    Notification,
    PaginatedResults,
    UserNotificationFilterRequest
} from "@curefit/iris-common"

export interface INotificationService {

    /**
     * Get the report based on taskId in iris
     */
    getTaskReport(taskId: string): Promise<any>

    /**
     * Get clickToCall report based on notificationId from iris
     */
    getClickToCallReport(notificationId: string): Promise<ClickToCallReport>

    /**
     * Get notification details based on notificationId from iris
     */
    getNotificationDetail(notificationId: string): Promise<Notification>

    /**
     * Get notifications history for user based on filter params
     */
    getUserNotificationHistory(req: UserNotificationFilterRequest): Promise<PaginatedResults<Notification>>

    /**
     * Re-send Old(>30 days) notifications
     */
    retriggerOldNotifications(notificationId: string): Promise<any>

    /**
     * Check if an email-id is valid
     */
    validateEmail(emailId: string, skipLocalCheck?: boolean, checkForLastXDays?: number): Promise<EmailValidResponse>

    /**
     * Check if an email-id is valid in bulk (max-10)
     */
    bulkValidateEmail(emailIds: string[], skipLocalCheck?: boolean, checkForLastXDays?: number): Promise<EmailValidResponse[]>
}
