deployment:
  tolerations:
    # this toleration is to have the app runnable on arm nodes
    - key: graviton
      operator: Equal
      value: 'true'
      effect: NoSchedule
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 1
        preference:
          matchExpressions:
            - key: kubernetes.io/arch
              operator: In
              values:
                - amd64
      - weight: 50
        preference:
          matchExpressions:
            - key: kubernetes.io/arch
              operator: In
              values:
                - arm64
  env:
  - name: ENVIRONMENT
    value: PRODUCTION
  - name: APP_NAME
    value: iris
  - name: NodeClusterSize
    value: '1'
  - name: DEPLOYMENT_TYPE
    value: ctc
  - name: DD_TRACE_DISABLED_PLUGINS
    value: aws-sdk
  podAnnotations:
    iam.amazonaws.com/role: arn:aws:iam::035243212545:role/k8s-iris-workers-prod
  probePort: 3010
  livenessProbe:
    initialDelaySeconds: 60
  readinessProbe:
    initialDelaySeconds: 60
  logDir: iris
  labels:
    billing: platforms
    sub-billing: iris
  resources:
    limits:
      cpu: 1
      memory: 3Gi
    requests:
      cpu: 200m
      memory: 2Gi

service:
  expose:
  - 3010

metrics:
  enabled: true
  interval: 75s
  path: /metrics
  port: 3009
  metricRelabelings:
    - sourceLabels: [ __name__ ]
      regex: '(PRODUCTION_iris_notification_scheduled|PRODUCTION_iris_notification_process_latency_bucket)'
      action: drop

istio:
  allOutboundInterception: true

externalSecrets:
  enabled: "true"

scaling:
  scaleUpAtCPU: 0.7
  minReplicas: 1
  maxReplicas: 6
  keda:
    triggers:
      - metadata:
          value: "700m"
        type: cpu
        metricType: AverageValue # Allowed types are 'Utilization' or 'AverageValue'
