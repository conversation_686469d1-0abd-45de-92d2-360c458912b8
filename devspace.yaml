version: v1beta10

images:
  app:
    image: ${DOCKER_REGISTRY}/${ORG}/stage/${APP_NAME}
    tags:
      - ${PREVIEW_VERSION}
    entrypoint:
      - "/bin/bash"
    cmd:
      - "-c"
      - "node --inspect dist/server/index.js"
    injectRestartHelper: true
    build:
      kaniko:
        cache: false
        initImage: public.ecr.aws/docker/library/alpine:latest
        options:
          buildArgs:
            ENVIRONMENT: ${ENVIRONMENT}
            APP_NAME: ${APP_NAME}
            NPM_TOKEN: ${NPM_TOKEN}
            GITHUB_NPM_TOKEN: ${GITHUB_NPM_TOKEN}

dev:
  ports:
    - labelSelector:
        app: ${APP_NAME}
        version: ${VIRTUAL_CLUSTER}
      forward:
        - port: 3010
          remotePort: 3010
        - port: 9229
          remotePort: 9229          
  sync:
    - labelSelector:
        app: ${APP_NAME}
        version: ${VIRTUAL_CLUSTER}
      containerName: ${APP_NAME}
      localSubPath: ./dist
      containerPath: /home/<USER>/deployment/iris/dist
      disableDownload: true
      initialSync: preferLocal
      onUpload:
        restartContainer: true
  logs:
    disabled: true
