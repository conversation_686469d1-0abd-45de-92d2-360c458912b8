{"noImplicitAny": false, "compileOnSave": true, "compilerOptions": {"outDir": "dist", "target": "ES2021", "lib": ["ES2015", "ES2016", "ES2017", "ES2018", "ES2019", "ES2020", "ES2021", "DOM"], "module": "commonjs", "moduleResolution": "node", "sourceMap": true, "mapRoot": "", "removeComments": true, "noImplicitAny": true, "skipLibCheck": true, "preserveConstEnums": true, "jsx": "react", "experimentalDecorators": true, "emitDecoratorMetadata": true, "types": ["inversify", "inversify-express-utils", "reflect-metadata"]}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "dist"]}