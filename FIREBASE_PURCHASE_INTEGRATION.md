# Firebase Analytics Purchase Events - Simple Integration

Firebase Analytics purchase events are now integrated into the existing FirebaseService, following the exact same pattern as Branch.io purchase events.

## 🎯 What's Added

- ✅ **`sendPurchaseEvent`** method in FirebaseService (same signature as Branch.io)
- ✅ **Automatic device tracking** (uses user's actual device ID)
- ✅ **Same interface** as Branch.io purchase events

## 🔧 Setup

**Optional:** Add these environment variables (fallback values are provided):

```bash
# Firebase Analytics Configuration (Optional - fallback values available)
FIREBASE_ANALYTICS_MEASUREMENT_ID_CUREFIT=G-XXXXXXXXXX
FIREBASE_ANALYTICS_API_SECRET_CUREFIT=your_api_secret_here

# For Livefit (if needed)
FIREBASE_ANALYTICS_MEASUREMENT_ID_LIVEFIT=G-YYYYYYYYYY
FIREBASE_ANALYTICS_API_SECRET_LIVEFIT=your_api_secret_here
```

**Note:** If these environment variables are not set, the service will automatically use fallback values:
- Measurement ID: `G-03XGFXZPY3`
- API Secret: `ogM9tVtsTTm3AuKxWxyuHQ`

## 🚀 Usage Options

### Option 1: Via REST API Controller

```bash
# Send purchase event
curl -X POST http://localhost:3010/firebase-analytics/sendPurchaseEvent \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "12345",
    "price": "999.99",
    "productId": "MONTHLY_GYM_PLAN",
    "productName": "Monthly Gym Plan"
  }'

# Health check
curl http://localhost:3010/firebase-analytics/health

# Test endpoint (uses sample data)
curl -X POST http://localhost:3010/firebase-analytics/test
```

### Option 2: Direct Service Call (Exactly like Branch.io)

```typescript
// Inject FirebaseService (same as you do for Branch.io)
@inject(TYPES.FirebaseService) private firebaseService: IFirebaseService

// Send purchase event (identical to Branch.io method)
const purchaseEventData = {
    price: "999.99",
    productId: "MONTHLY_GYM_PLAN",
    productName: "Monthly Gym Plan"
};

await this.firebaseService.sendPurchaseEvent(userId, purchaseEventData);
```

### Example Usage with Try-Catch:

```typescript
// Same as Branch.io purchase event with proper error handling
try {
    const result = await this.firebaseService.sendPurchaseEvent("12345", {
        price: "999.99",
        productId: "MONTHLY_GYM_PLAN",
        productName: "Monthly Gym Plan"
    });
    console.log("Purchase event sent successfully:", result);
} catch (error) {
    console.error("Failed to send purchase event:", error);
    // Handle error appropriately - event will be logged and reported to Rollbar
}
```

## 📊 What Gets Sent to Firebase

Your purchase event will be sent to Firebase Analytics with enhanced attribution data:

```json
{
  "client_id": "advertising_id_or_user_id_or_random_uuid",
  "user_id": "12345",
  "timestamp_micros": 1640995200000000,
  "events": [
    {
      "name": "purchase_success",
      "params": {
        "transaction_id": "MONTHLY_GYM_PLAN_1640995200000",
        "value": 999.99,
        "currency": "INR",
        "item_name": "Monthly Gym Plan",
        "platform": "backend",
        "session_id": "1640995200000_abc123",
        "engagement_time_msec": 15000,
        "debug_mode": true
      }
    }
  ],
  "user_properties": {
    "platform": "android",
    "app_version": "1.2.3",
    "device_model": "Samsung Galaxy S21",
    "has_device_info": true,
    "attribution_method": "advertising_id"
  }
}
```

### **Attribution Fallback Strategy:**

1. **With Device Info** (Best Attribution):
   ```json
   {
     "client_id": "advertising_id_abc123",
     "user_properties": {
       "has_device_info": true,
       "attribution_method": "advertising_id"
     }
   }
   ```

2. **Without Device Info** (Fallback Attribution):
   ```json
   {
     "client_id": "random_uuid_def456",
     "user_properties": {
       "has_device_info": false,
       "attribution_method": "uuid_fallback",
       "platform": "unknown"
     }
   }
   ```

## 🔄 Integration Points

### Same as Branch.io:
- ✅ Uses same `PurchaseEvent` interface
- ✅ Gets device info automatically
- ✅ Same method signature
- ✅ Same error handling pattern
- ✅ Same logging pattern

### Differences from Branch.io:
- 🎯 Sends to Firebase Analytics instead of Branch
- 🎯 Uses Firebase Analytics event format
- 🎯 Uses Google Analytics Measurement Protocol

## 🧪 Testing

### Test via Controller:
```bash
node test-firebase-purchase-simple.js
```

### Test via curl:
```bash
# Health check
curl http://localhost:3010/firebase-analytics/health

# Send purchase event
curl -X POST http://localhost:3010/firebase-analytics/sendPurchaseEvent \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "test_user_123",
    "price": "99.99",
    "productId": "TEST_PRODUCT",
    "productName": "Test Product"
  }'

# Test endpoint
curl -X POST http://localhost:3010/firebase-analytics/test
```

## 📈 View Results

Events will appear in:
1. **Firebase Analytics Console** → Events → purchase_success
2. **Google Analytics 4** → Reports → Events
3. **Real-time reports** for immediate verification

## 🛡️ Error Handling

The Firebase Analytics service includes comprehensive error handling:

- ✅ **Try-catch blocks** around all async operations
- ✅ **Configuration validation** (checks for missing measurement ID/API secret)
- ✅ **Device API fallback** (continues if device info unavailable)
- ✅ **Detailed logging** for debugging
- ✅ **Rollbar error reporting** for monitoring
- ✅ **Performance metrics** tracking

## 🎉 Benefits

1. **Same API as Branch.io** - No learning curve
2. **Better tracking** - Uses actual device advertising IDs
3. **Automatic device info** - Platform, app version, device model
4. **Production ready** - Comprehensive error handling, logging, retry logic
5. **Multi-tenant** - Supports different Firebase projects per app
6. **Pre-configured** - Your measurement ID and API secret are already set

Your Firebase Analytics purchase events are now ready to use with the exact same interface as Branch.io! 🚀
