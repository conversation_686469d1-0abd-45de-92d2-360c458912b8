// Test Firebase Analytics purchase event integration via Iris Controller
// This shows how to use the new Firebase Analytics Controller

const axios = require("axios");

// Iris API Configuration
const IRIS_BASE_URL = process.env.IRIS_BASE_URL || "http://localhost:3010";

async function testFirebaseControllerPurchaseEvent() {
    console.log("🧪 Testing Firebase Analytics Purchase Event via Iris Controller");
    console.log("=" * 60);

    // Example purchase event data
    const purchaseEventData = {
        userId: "12345",
        price: "999.99",
        productId: "MONTHLY_GYM_PLAN",
        productName: "Monthly Gym Plan"
    };

    console.log("📦 Purchase Event Data:");
    console.log(JSON.stringify(purchaseEventData, null, 2));

    try {
        console.log("\n🚀 Testing Health Check...");
        const healthResponse = await axios.get(`${IRIS_BASE_URL}/firebase-analytics/health`);
        console.log("✅ Health Check:", healthResponse.data);

        console.log("\n🚀 Sending Purchase Event via Iris Controller...");

        const response = await axios.post(`${IRIS_BASE_URL}/firebase-analytics/sendPurchaseEvent`, purchaseEventData, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log("✅ Event sent successfully via Iris Controller!");
        console.log("Status:", response.status);
        console.log("Response:", JSON.stringify(response.data, null, 2));

        console.log("\n🧪 Testing Test Endpoint...");
        const testResponse = await axios.post(`${IRIS_BASE_URL}/firebase-analytics/test`, {}, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log("✅ Test endpoint response:");
        console.log(JSON.stringify(testResponse.data, null, 2));

    } catch (error) {
        console.error("❌ Error calling Iris Firebase Analytics Controller:");
        if (error.response) {
            console.error("Status:", error.response.status);
            console.error("Data:", JSON.stringify(error.response.data, null, 2));
        } else {
            console.error("Message:", error.message);
        }
    }

    console.log("\n🎯 Controller endpoints available:");
    console.log("POST /firebase-analytics/sendPurchaseEvent - Send purchase event");
    console.log("GET  /firebase-analytics/health - Health check");
    console.log("POST /firebase-analytics/test - Test with sample data");
}

// Run the test
testFirebaseControllerPurchaseEvent().catch(console.error);

// Example of how to use in your actual Iris code:
/*
// In your service/controller:
@inject(TYPES.FirebaseService) private firebaseService: IFirebaseService

// Send purchase event (same as Branch.io) with try-catch:
try {
    const purchaseEventData = {
        price: "999.99",
        productId: "MONTHLY_GYM_PLAN",
        productName: "Monthly Gym Plan"
    };

    const result = await this.firebaseService.sendPurchaseEvent(userId, purchaseEventData);
    console.log("Purchase event sent successfully:", result);
} catch (error) {
    console.error("Failed to send purchase event:", error);
    // Handle error appropriately
}
*/
