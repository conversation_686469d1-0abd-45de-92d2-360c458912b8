# Firebase Analytics Configuration (Purchase Events Only)
# These values are OPTIONAL - fallback values are provided in the service

# Curefit App Firebase Analytics (Optional - fallback values will be used if not set)
# FIREBASE_ANALYTICS_MEASUREMENT_ID_CUREFIT=G-XXXXXXXXXX
# FIREBASE_ANALYTICS_API_SECRET_CUREFIT=your_api_secret_here

# Livefit App Firebase Analytics (if needed)
# FIREBASE_ANALYTICS_MEASUREMENT_ID_LIVEFIT=G-YYYYYYYYYY
# FIREBASE_ANALYTICS_API_SECRET_LIVEFIT=your_api_secret_here

# Fallback values (automatically used if environment variables not set):
# Measurement ID: G-03XGFXZPY3
# API Secret: ogM9tVtsTTm3AuKxWxyuHQ

# You only need to uncomment and set these if you want to override the fallback values
