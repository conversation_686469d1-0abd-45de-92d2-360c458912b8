# Firebase Analytics Configuration
# Get these values from your Firebase project settings

# Curefit App Firebase Analytics
FIREBASE_ANALYTICS_PROJECT_ID_CUREFIT=cult-155917
FIREBASE_ANALYTICS_MEASUREMENT_ID_CUREFIT=G-XXXXXXXXXX
FIREBASE_ANALYTICS_API_SECRET_CUREFIT=your_api_secret_here

# Livefit App Firebase Analytics  
FIREBASE_ANALYTICS_PROJECT_ID_LIVEFIT=livefit-app
FIREBASE_ANALYTICS_MEASUREMENT_ID_LIVEFIT=G-YYYYYYYYYY
FIREBASE_ANALYTICS_API_SECRET_LIVEFIT=your_api_secret_here

# Cultwatch App Firebase Analytics
FIREBASE_ANALYTICS_PROJECT_ID_CULTWATCH=cultwatch-app
FIREBASE_ANALYTICS_MEASUREMENT_ID_CULTWATCH=G-ZZZZZZZZZZ
FIREBASE_ANALYTICS_API_SECRET_CULTWATCH=your_api_secret_here

# How to get these values:
# 1. Go to Firebase Console (https://console.firebase.google.com/)
# 2. Select your project
# 3. Go to Project Settings > General tab
# 4. Scroll down to "Your apps" section
# 5. Find your web app and copy the measurementId from the config
# 6. For API Secret: Go to Admin > Data Streams > Web > Measurement Protocol API secrets
