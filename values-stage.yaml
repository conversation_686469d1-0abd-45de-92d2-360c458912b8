deployment:
  tolerations:
    # this toleration is to have the app runnable on arm nodes
    - key: graviton
      operator: Equal
      value: 'true'
      effect: NoSchedule
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 1
        preference:
          matchExpressions:
            - key: kubernetes.io/arch
              operator: In
              values:
                - amd64
      - weight: 100
        preference:
          matchExpressions:
            - key: kubernetes.io/arch
              operator: In
              values:
                - arm64
  env:
  - name: ENVIRONMENT
    value: STAGE
  - name: APP_NAME
    value: iris
  - name: NodeClusterSize
    value: '1'
  - name: DEPLOYMENT_TYPE
    value: server
  - name: DD_TRACE_DISABLED_PLUGINS
    value: aws-sdk

  podAnnotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::035243212545:role/k8s-iris
  probePort: 3010
  livenessProbe:
    initialDelaySeconds: 60
  readinessProbe:
    initialDelaySeconds: 60
  labels:
    billing: platforms
    sub-billing: iris
  resources:
    limits:
      cpu: 1
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 1Gi
istio:
  allOutboundInterception: true
  external:
    hosts:
      - iris-api.stage.curefit.co
    match:
      - prefix: /notifications/smsDelivery
      - prefix: /notifications/obdCallback
      - prefix: /notifications/clickToCallReport
      - prefix: /notifications/ext/smsDelivery
      - prefix: /campaigns/ext/send
      - prefix: /notifications/whatsappDelivery
      - prefix: /bot/status
      - prefix: /bot/stat
      - prefix: /bot/campaign
      - prefix: /bot/creative
      - prefix: /notifications/smsDelivery/gs
      - prefix: /notifications/soipDelivery
      - prefix: /notifications/whatsappDelivery/sprinklr
      - prefix: /notifications/updateWhatsappPreferencesForUser/sprinklr
      - prefix: /notifications/inboundCallReport
      - prefix: /notifications/smsDelivery/fonada
  internal:
    hosts:
      - iris.stage.cure.fit.internal
metrics:
  enabled: true
  interval: 75s
  path: /metrics
  port: 3009
service:
  expose:
  - 3010

externalSecrets:
  enabled: "true"

scaling:
  scaleUpAtCPU: 0.5
  targetCPUUtilPercentage: 100
  minReplicas: 1
  maxReplicas: 5
