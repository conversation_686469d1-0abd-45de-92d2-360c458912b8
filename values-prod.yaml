# This drain time is for the fluentd container.
# We want this container to go only down after the service container is terminated
drainTime: 110
deployment:
  dnsPolicy: "true"
  drainTime: 80 # This drain time is for the service
  terminationGracePeriodSeconds: 120
  tolerations:
    # this toleration is to have the app runnable on arm nodes
    - key: graviton
      operator: Equal
      value: 'true'
      effect: NoSchedule
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 1
        preference:
          matchExpressions:
            - key: kubernetes.io/arch
              operator: In
              values:
                - amd64
      - weight: 100
        preference:
          matchExpressions:
            - key: kubernetes.io/arch
              operator: In
              values:
                - arm64
  env:
  - name: ENVIRONMENT
    value: PRODUCTION
  - name: APP_NAME
    value: iris
  - name: NodeClusterSize
    value: '1'
  - name: DEPLOYMENT_TYPE
    value: server
  - name: DD_TRACE_DISABLED_PLUGINS
    value: aws-sdk
  - name: LOG_LEVEL
    value: debug
  podAnnotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::035243212545:role/k8s-iris
  probePort: 3010
  livenessProbe:
    initialDelaySeconds: 60
  readinessProbe:
    initialDelaySeconds: 60
  labels:
    billing: platforms
    sub-billing: iris
  resources:
    limits:
      cpu: 1
      memory: 3Gi
    requests:
      cpu: 300m
      memory: 2Gi
istio:
  allOutboundInterception: true
  external:
    hosts:
      - iris-api.curefit.co
    match:
      - prefix: /notifications/smsDelivery
      - prefix: /notifications/obdCallback
      - prefix: /notifications/clickToCallReport
      - prefix: /notifications/ext/smsDelivery
      - prefix: /campaigns/ext/send
      - prefix: /notifications/whatsappDelivery
      - prefix: /bot/status
      - prefix: /bot/stat
      - prefix: /bot/campaign
      - prefix: /bot/creative
      - prefix: /notifications/smsDelivery/gs
      - prefix: /notifications/soipDelivery
      - prefix: /notifications/whatsappDelivery/sprinklr
      - prefix: /notifications/updateWhatsappPreferencesForUser/sprinklr
      - prefix: /notifications/inboundCallReport
      - prefix: /notifications/smsDelivery/fonada
  internal:
    hosts:
      - iris.production.cure.fit.internal
      - iris-load-test.production.cure.fit.internal
metrics:
  enabled: true
  interval: 120s
  path: /metrics
  port: 3009
  metricRelabelings:
    - sourceLabels: [ __name__ ]
      regex: '(PRODUCTION_iris_notification_scheduled|PRODUCTION_iris_notification_process_latency_bucket)'
      action: drop

pager:
  service-name: iris #refer https://curefit.app.opsgenie.com/service
  provider: opsgenie # opsgenie/pagerduty

bugtracker:
  provider: sentry # rollbar/sentry
  service-name: iris #refer https://sentry.io/organizations/curefit/projects/

support:
  slack-channel: iris
  mailing-list: <EMAIL> # refer https://groups.google.com/all-groups

apm:
  provider: datadog # datadog/newrelic
  service-name: iris #refer https://app.datadoghq.com/apm/services

pod-id: platform # refer https://github.com/curefit/curefit-billing/blob/master/pod.yaml

repository:
  url: https://github.com/curefit/iris

tags:
  billing: platform # refer https://github.com/curefit/curefit-billing/blob/master/billing.yaml

service:
  expose:
  - 3010

externalSecrets:
  enabled: "true"

scaling:
  scaleUpAtCPU: 0.5
  targetCPUUtilPercentage: 200
  minReplicas: 5
  maxReplicas: 30
  keda:
    triggers:
      - metadata:
          value: "500m"
        type: cpu
        metricType: AverageValue # Allowed types are 'Utilization' or 'AverageValue'
      - metadata:
          awsRegion: ap-south-1
          identityOwner: operator
          queueLength: "100"
          queueURL: https://sqs.ap-south-1.amazonaws.com/035243212545/production-iris-segment-batches
        type: aws-sqs-queue
      - metadata:
          awsRegion: ap-south-1
          identityOwner: operator
          queueLength: "150"
          queueURL: https://sqs.ap-south-1.amazonaws.com/035243212545/production-iris-campaign
        type: aws-sqs-queue
      - type: cron # scale up from 5:45am-8:00am
        metadata:
          timezone: Asia/Kolkata
          start: 45 5 * * *
          end: 0 8 * * *
          desiredReplicas: "15"
