FROM public.ecr.aws/docker/library/node:16.20.2-bullseye as intermediate

ARG NPM_TOKEN
ARG GITHUB_NPM_TOKEN
ARG APP_NAME
ARG ENVIRONMENT

RUN apt update && apt install libpng-dev git sudo python3 make g++ bzip2 wget openssl -y

RUN echo ${NPM_TOKEN} > /root/.npmrc
RUN echo ${GITHUB_NPM_TOKEN} >> /root/.npmrc
RUN echo '@curefit:registry=https://npm.pkg.github.com/' >> /root/.npmrc
RUN echo 'registry=https://registry.npmjs.org/' >> /root/.npmrc
RUN echo 'unsafe-perm=true' >> /root/.npmrc

RUN mkdir -p /${APP_NAME}-deploy/

WORKDIR /${APP_NAME}

ADD . /${APP_NAME}

RUN deploy/build_k8s.sh /${APP_NAME}-deploy ${ENVIRONMENT}


FROM public.ecr.aws/docker/library/node:16.20.2-bullseye

ARG APP_NAME

ENV OPENSSL_CONF=/dev/null

ENV QT_QPA_PLATFORM=offscreen
ENV XDG_RUNTIME_DIR=/tmp/runtime-dir
RUN mkdir -p /tmp/runtime-dir && chmod 700 /tmp/runtime-dir

RUN mkdir -p /logs/${APP_NAME} /tmp
RUN apt update && apt install -y curl openssl libfontconfig1

# Install phantomjs based on architecture
RUN dpkgArch="$(dpkg --print-architecture)" && \
    if [ "$dpkgArch" = "amd64" ]; then \
        curl -kL https://static.cure.fit/phantomjs/phantomjs-2.1.1-amd64.deb -o /tmp/phantomjs-amd64.deb && \
        apt -f -y install /tmp/phantomjs-amd64.deb; \
    elif [ "$dpkgArch" = "arm64" ]; then \
        curl -kL https://static.cure.fit/phantomjs/phantomjs-2.1.1-arm64.deb -o /tmp/phantomjs-arm64.deb && \
        apt -f -y install /tmp/phantomjs-arm64.deb; \
    fi


WORKDIR /${APP_NAME}

COPY --from=intermediate /${APP_NAME}/  /${APP_NAME}/

CMD ["/bin/bash", "-c", "sleep 15s && npm run -s serve"]
