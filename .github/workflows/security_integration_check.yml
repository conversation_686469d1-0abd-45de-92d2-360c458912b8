name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

on:
  push:
    branches:
      - master
      - alpha
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - master
      - alpha
  pull_request_review:
    types: [submitted]

permissions:
  contents: read
  pull-requests: write
  security-events: read

jobs:
  call-template:
    uses: curefit/actions-template/.github/workflows/security-workflow-template.yml@pause_alphaV1
    with:
      branch: "${{ github.event.pull_request.head.ref || github.ref_name }}"
      require_approval: ${{ github.ref_name != 'alpha' }}
      critical_approval_count: 2
      high_approval_count: 1
    secrets: inherit
