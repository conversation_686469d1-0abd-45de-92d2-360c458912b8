name: Sync oci-stage with stage

on:
  workflow_dispatch:
  schedule:
    - cron: '30 19 * * *'  # Runs daily at 19:30 UTC

jobs:
  sync_oci_stage:
    runs-on: ubuntu-latest

    steps:
      - name: Fail if not triggered from master (for workflow_dispatch)
        if: github.event_name == 'workflow_dispatch' && github.ref != 'refs/heads/master'
        run: |
          echo "This workflow should only be triggered from master"
          exit 1

      - name: Checkout oci-stage branch
        uses: actions/checkout@v3
        with:
          ref: oci-stage
          fetch-depth: 0
          token: ${{ secrets.WORKFLOW_TOKEN }}

      - name: Configure Git
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

      - name: Pull latest changes from stage (merge strategy)
        id: pull
        run: |
          set +e
          git pull --no-rebase --allow-unrelated-histories origin stage --no-edit
          echo "PULL_EXIT_CODE=$?" >> $GITHUB_ENV
          set -e

      - name: Abort merge on conflict
        if: env.PULL_EXIT_CODE != '0'
        run: |
          echo "Merge conflict detected. Attempting to abort..."
          if [ -f .git/MERGE_HEAD ]; then
            git merge --abort
            echo "Merge aborted."
          else
            echo "No merge in progress to abort."
          fi

      - name: Send Slack DM on merge conflict
        if: env.PULL_EXIT_CODE != '0'
        uses: slackapi/slack-github-action@v1.24.0
        with:
          channel-id: 'U02ENL1N7T6'  # Replace with your Slack User ID
          slack-message: |
            :x: *Merge Conflict Alert*
            `git pull origin stage` into `oci-stage` failed due to merge conflicts.
            ➡️ [View Workflow Run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

      - name: Push if pull (merge) was successful
        if: env.PULL_EXIT_CODE == '0'
        run: |
          git push origin oci-stage
