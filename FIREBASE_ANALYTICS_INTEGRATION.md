# Firebase Analytics Integration Guide

This guide explains how to use the Firebase Analytics integration in the Iris communication system, similar to how Branch events work.

## 🎯 What's Integrated

Firebase Analytics events are now automatically sent when:
- ✅ **Notifications are sent** (`notification_sent`)
- ✅ **Notifications are opened** (`notification_opened`) 
- ✅ **Notifications fail/dismissed** (`notification_dismissed`)
- ✅ **Purchase events occur** (`purchase_success`)
- ✅ **Custom events happen** (any custom event name)

## 🔧 Setup

### 1. Environment Variables
Copy `.env.example` and set your Firebase Analytics credentials:

```bash
# Get these from Firebase Console > Project Settings
FIREBASE_ANALYTICS_PROJECT_ID_CUREFIT=cult-155917
FIREBASE_ANALYTICS_MEASUREMENT_ID_CUREFIT=G-XXXXXXXXXX
FIREBASE_ANALYTICS_API_SECRET_CUREFIT=your_api_secret_here
```

### 2. How to Get Firebase Credentials

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. **Project Settings** > **General** tab
4. Scroll to "Your apps" section
5. Copy `measurementId` from web app config
6. **Admin** > **Data Streams** > **Web** > **Measurement Protocol API secrets**

## 🚀 Usage Examples

### 1. Automatic Notification Events

Events are **automatically sent** when notifications are processed:

```typescript
// When you send a notification via Iris, Firebase events are automatically tracked
const response = await campaignHelper.sendCampaignMessages({
    campaignId: "CUREFIT_WORKOUT_REMINDER",
    creativeIds: ["PUSH_WORKOUT_REMINDER"],
    userContexts: [{ userId: "12345", tags: { name: "John" } }]
})

// This automatically sends Firebase Analytics events:
// - notification_sent (when notification is sent)
// - notification_opened (when user opens notification)
// - notification_dismissed (when notification fails)
```

### 2. Manual Purchase Events

Send purchase events directly:

```typescript
// Via API endpoint
POST /firebase-analytics/sendPurchaseSuccessEvent
{
    "userId": "12345",
    "transactionId": "ORDER_123",
    "value": 999.99,
    "currency": "INR",
    "itemName": "Monthly Gym Plan",
    "tenant": "CUREFIT_APP"
}

// Via service (in code)
await firebaseAnalyticsService.sendPurchaseSuccessEvent(
    "12345",           // userId
    "ORDER_123",       // transactionId  
    999.99,           // value
    "INR",            // currency
    "Monthly Gym Plan" // itemName
)
```

### 3. Custom Events

Send any custom event:

```typescript
// Via API endpoint
POST /firebase-analytics/sendCustomEvent
{
    "userId": "12345",
    "customEventData": {
        "event_name": "workout_completed",
        "parameters": {
            "workout_type": "yoga",
            "duration_minutes": 45,
            "calories_burned": 200
        }
    }
}

// Via service (in code)
await firebaseAnalyticsService.sendCustomEvent("12345", {
    event_name: "workout_completed",
    parameters: {
        workout_type: "yoga",
        duration_minutes: 45,
        calories_burned: 200
    }
})
```

## 📊 Event Types Supported

### Notification Events
- `notification_sent` - When notification is sent
- `notification_opened` - When user opens notification  
- `notification_dismissed` - When notification fails

### Purchase Events
- `purchase` - Standard purchase event
- `purchase_success` - Successful purchase event

### Custom Events
- Any event name you define

## 🔄 Integration Points

### 1. **Notification Flow Integration**
Firebase events are automatically sent via `NotificationStatusUpdatePublisher`:

```typescript
// In NotificationStatusUpdatePublisher.ts
public async publish(statusUpdates: StatusUpdate[]): Promise<any> {
    this.publishFirebaseAnalyticsEvents(statusUpdates) // ← Firebase events
    return this.publishRashiEvents(statusUpdates)      // ← Existing events
}
```

### 2. **Similar to Branch Events**
Just like Branch events, Firebase events are:
- ✅ Sent asynchronously (non-blocking)
- ✅ Include user context and device info
- ✅ Mapped to appropriate event types
- ✅ Tenant-aware (different Firebase projects per app)

## 🧪 Testing

### 1. **Test Purchase Event**
```bash
curl -X POST http://localhost:3010/firebase-analytics/sendPurchaseSuccessEvent \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "12345",
    "transactionId": "TEST_ORDER_123",
    "value": 99.99,
    "currency": "INR",
    "itemName": "Test Gym Plan"
  }'
```

### 2. **Test Custom Event**
```bash
curl -X POST http://localhost:3010/firebase-analytics/sendCustomEvent \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "12345",
    "customEventData": {
      "event_name": "test_event",
      "parameters": {
        "test_param": "test_value"
      }
    }
  }'
```

### 3. **Health Check**
```bash
curl http://localhost:3010/firebase-analytics/health
```

## 📈 Monitoring

Events will appear in:
1. **Firebase Analytics Console** - Real-time and historical data
2. **Google Analytics 4** - Advanced reporting and analysis
3. **Iris Logs** - Debug information and error tracking

## 🔧 Configuration

Firebase Analytics is configured per tenant in `constants.ts`:

```typescript
static FirebaseAnalyticsConfigMap = {
    [Tenant.CUREFIT_APP]: {
        projectId: "cult-155917",
        measurementId: "G-XXXXXXXXXX",
        apiSecret: "your_api_secret",
        url: "https://www.google-analytics.com/mp/collect"
    }
}
```

## 🎉 Benefits

1. **Unified Analytics** - Same events in both Branch and Firebase
2. **Rich Insights** - Detailed user behavior tracking
3. **Marketing Attribution** - Track campaign effectiveness
4. **Real-time Data** - Immediate event processing
5. **Cross-platform** - Works across iOS, Android, and web

Your Firebase Analytics integration is now complete and follows the same patterns as Branch events! 🚀
