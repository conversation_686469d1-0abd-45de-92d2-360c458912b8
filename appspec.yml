version: 0.0
os: linux
files:
  - source: /iris
    destination: /home/<USER>/deployment/iris
  - source: /iris/dist/server/conf/logs/awslogs/iris_aws_cw_logs.conf
    destination: /var/awslogs/etc/config
permissions:
  - object: /home/<USER>/deployment/iris
    pattern: "**"
    owner: ubuntu
    mode: 775
    type:
      - directory
  - object: /home/<USER>/deployment/iris
    pattern: "**"
    owner: ubuntu
    type:
      - file
hooks:
  AfterInstall:
    - location: set_permissions.sh
    - location: restart_cwlogs_agent.sh
  ApplicationStop:
    - location: stop_server.sh
  ApplicationStart:
    - location: start_server.sh
      timeout: 3600
