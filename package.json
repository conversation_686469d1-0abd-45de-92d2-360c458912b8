{"name": "iris", "version": "1.0.0", "description": "Communications and Campaigns management in Curefit", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc --project tsconfig.json && yarn run copy-configs", "copy-configs": "mkdir -p dist/server/conf/ && whoami && cp src/server/conf/*.json dist/server/conf/", "clean": "rm -rf dist && mkdir -p dist", "serve": "APP_NAME=iris node --max-old-space-size=4096 --max_semi_space_size=512 dist/server/index.js", "serve-inspect": "APP_NAME=iris APP_ENV=LOCAL ENVIRONMENT=LOCAL DEPLOYMENT_TYPE=server AWS_PROFILE=curefit-platform-devs nodemon --inspect --delay 2000ms dist/server/index.js", "tslint": "tslint -c tslint.json -p tsconfig.json", "tslint-fix": "tslint -c tslint.json -p tsconfig.json --fix", "start-dev": "APP_NAME=iris APP_ENV=LOCAL ENVIRONMENT=LOCAL ts-node-dev --inspect --respawn --transpile-only --ignore-watch node_modules dist/server/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/curefit/iris.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/curefit/iris/issues"}, "private": true, "homepage": "https://github.com/curefit/iris#readme", "dependencies": {"@aws-sdk/client-athena": "^3.25.0", "@curefit/apps-common": "2.94.0", "@curefit/base": "7.15.0", "@curefit/base-common": "2.51.0", "@curefit/base-utils": "9.11.0", "@curefit/cache-utils": "4.1.0", "@curefit/campaign-manager-client": "4.2.0", "@curefit/campaign-manager-common": "4.2.0", "@curefit/cf-commons-middleware": "4.6.0", "@curefit/config-store-client": "2.0.1", "@curefit/device-models": "1.14.0", "@curefit/error-client": "^1.0.1", "@curefit/error-common": "^2.19.1", "@curefit/events-util": "^2.1.0", "@curefit/iris-common": "1.88.0", "@curefit/iris-models": "3.5.0", "@curefit/lock-utils": "^2.3.0", "@curefit/mongo-utils": "^4.21.0", "@curefit/mozart-client": "0.6.0", "@curefit/mysql-utils": "^1.7.0", "@curefit/neo-common": "^1.1.0", "@curefit/rashi-client": "3.17.0", "@curefit/redis-utils": "^6.4.0", "@curefit/segmentation-service-client": "2.37.1", "@curefit/server": "8.0.4", "@curefit/sqs-client": "2.8.1-debug", "@curefit/user-client": "9.20.0", "@curefit/user-common": "2.20.0", "@curefit/util-common": "3.7.0", "@curefit/voyager-client": "3.0.3", "@curefit/voyager-common": "1.1.0", "@curefit/athena-service-client": "1.0.0", "@maselious/bottleneck": "^2.19.8", "@notifee/react-native": "^1.1.1", "@sentry/node": "^7.11.1", "@slack/web-api": "5", "async": "^3.2.6", "aws-sdk": "^2.48.0", "bcrypt": "^5.0.1", "body-parser": "^1.14.2", "bookshelf": "^0.12.1", "continuation-local-storage": "^3.2.0", "cookie-parser": "^1.4.3", "cookie-session": "^2.0.0-alpha.2", "create-react-class": "^15.5.3", "crypto-js": "4.2.0", "dd-trace": "^4.55.0", "express": "^4.14.0", "file-stream-rotator": "^0.2.0", "firebase-admin": "9.12.0", "form-data": "^2.1.1", "google-auth-library": "^9.14.0", "gulp": "^4.0.2", "handlebars": "^4.7.8", "helmet": "^2.1.2", "history": "2.0.0", "html-pdf": "^2.2.0", "html-webpack-plugin": "^2.26.0", "inversify": "6.0.1", "inversify-express-utils": "6.4.3", "inversify-logger-middleware": "^3.1.0", "ioredis": "^5.1.0", "isomorphic-fetch": "^2.2.1", "json-loader": "^0.5.4", "json2csv": "^5.0.0", "kafkajs": "2.2.2", "knex": "^0.16.3", "memoizee": "^0.4.14", "method-override": "^2.3.5", "moment": "^2.27.0", "moment-timezone": "^0.5.31", "mongoose-geojson-schema": "^2.1.2", "mongoose-map": "^1.1.0", "morgan": "^1.7.0", "mustache": "^2.3.0", "mysql": "^2.16.0", "node-cache": "^4.1.1", "node-dogstatsd": "0.0.7", "node-fetch": "^2.6.0", "node-gyp": "^9.0.0", "nodemailer": "^6.4.17", "nodemailer-ses-transport": "^1.5.1", "object-hash": "2.0.3", "prom-client": "^11.3.0", "react": "^15.4.2", "react-bootstrap": "^0.30.7", "react-dom": "^15.4.2", "react-redux": "^5.0.2", "react-router": "^3.0.1", "react-router-bootstrap": "^0.23.1", "react-router-redux": "^4.0.7", "react-select": "^1.0.0-rc.3", "redis": "3.1.2", "redlock": "^3.1.0", "redux": "^3.6.0", "redux-logger": "^2.7.4", "redux-thunk": "^2.1.0", "reflect-metadata": "^0.1.8", "request": "^2.74.0", "shortid": "^2.2.6", "snappy": "6.3.5", "ts-node-dev": "^2.0.0", "turbocommons-ts": "^3.7.0", "uuid": "^3.0.1", "webpack": "^1.14.0", "webpack-stream": "^3.2.0", "winston": "^3.2.1", "winston-daily-rotate-file": "^3.6.0"}, "devDependencies": {"@types/aws-sdk": "^2.7.0", "@types/bcrypt": "^1.0.0", "@types/bluebird": "^3.0.36", "@types/body-parser": "0.0.33", "@types/bookshelf": "^0.13.2", "@types/cls-hooked": "^4.3.0", "@types/continuation-local-storage": "^3.2.0", "@types/cookie-parser": "^1.3.30", "@types/cookie-session": "^2.0.32", "@types/express": "^4.0.33", "@types/express-session": "0.0.32", "@types/fixed-data-table": "^0.6.31", "@types/flake-idgen": "^0.1.29", "@types/form-data": "^0.0.33", "@types/handlebars": "^4.0.36", "@types/helmet": "^0.0.33", "@types/history": "^2.0.41", "@types/html-pdf": "^2.2.0", "@types/ioredis": "^4.27.4", "@types/isomorphic-fetch": "0.0.31", "@types/method-override": "^0.0.29", "@types/mime": "0.0.29", "@types/mocha": "^2.2.32", "@types/moment": "^2.13.0", "@types/moment-timezone": "^0.2.32", "@types/mongoose-promise": "^4.5.12", "@types/morgan": "^1.7.32", "@types/mustache": "^0.8.29", "@types/mysql": "0.0.30", "@types/node": "^18.0.2", "@types/node-cache": "^3.0.31", "@types/node-fetch": "^2.6.11", "@types/node-uuid": "0.0.28", "@types/nodemailer": "^6.4.17", "@types/nodemailer-ses-transport": "^3.1.4", "@types/object-hash": "^0.5.28", "@types/react": "^15.0.0", "@types/react-bootstrap": "^0.0.39", "@types/react-dom": "^0.14.20", "@types/react-redux": "^4.4.35", "@types/react-router": "^2.0.44", "@types/react-router-bootstrap": "^0.0.27", "@types/react-router-redux": "^4.0.38", "@types/react-select": "^1.0.46", "@types/redis": "^2.8.32", "@types/redlock": "^3.0.1", "@types/redux": "^3.6.0", "@types/redux-logger": "^2.6.33", "@types/redux-thunk": "2.1.31", "@types/reflect-metadata": "^0.0.5", "@types/request": "^0.0.36", "@types/shortid": "0.0.28", "@types/uuid": "^2.0.29", "@types/winston": "^0.0.32", "del": "^2.2.1", "fixed-data-table": "^0.6.4", "nodemon": "^2.0.18", "tslint": "^6.1.3", "typescript": "^4.7.4", "vinyl-fs": "^3.0.3"}, "resolutions": {"@aws-sdk/client-sqs": "3.623.0", "@curefit/cache-utils": "4.1.0", "@curefit/base": "7.0.2", "@curefit/base-utils": "9.11.0", "@curefit/delivery-models": "^1.4.0", "@curefit/device-common": "1.4.0", "@curefit/device-models": "1.14.0", "@curefit/iris-common": "1.88.0", "@curefit/iris-models": "3.5.0", "@curefit/redis-utils": "^6.4.0", "@curefit/mongo-utils": "^4.21.0", "@curefit/campaign-manager-common": "3.17.0", "inversify": "6.0.1", "inversify-express-utils": "6.4.3", "inversify-logger-middleware": "^3.1.0", "mongoose": "6.13.8", "phantomjs-prebuilt": "npm:empty-npm-package@*", "ioredis": "^5.1.0", "@curefit/user-client": "9.20.0", "node-fetch": "^2.6.0", "dd-trace": "^4.55.0", "node-dogstatsd": "0.0.7", "@curefit/sqs-client": "2.8.1-debug", "@curefit/user-common": "2.20.0", "@curefit/base-common": "2.67.0", "minimist": "1.2.6", "json-schema": "0.4.0", "loader-utils": "1.4.1", "class-validator": "0.14.0", "protobufjs": "7.2.5", "crypto-js": "4.2.0"}}